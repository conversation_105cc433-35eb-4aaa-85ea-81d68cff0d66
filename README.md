# FixHaat

<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## Requirements

- PHP >= 8.2
- Composer
- Node.js >= 18
- MySQL >= 8.0

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/mobile-parts-db.git
cd mobile-parts-db
```

2. Install PHP dependencies:
```bash
composer install
```

3. Install Node.js dependencies:
```bash
npm install
```

4. Create environment file:
```bash
cp .env.example .env
```

5. Generate application key:
```bash
php artisan key:generate
```

6. Configure your database in `.env` file:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=fixhaat
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

7. Run database migrations and seeders:
```bash
php artisan migrate --seed
```

## Development

1. Start the development server:
```bash
php artisan serve
```

2. Watch for asset changes:
```bash
npm run dev
```

3. Run tests:
```bash
php artisan test
```

4. Run code style fixes:
```bash
./vendor/bin/pint
```

## Build for Production

1. Install production dependencies:
```bash
composer install --no-dev --optimize-autoloader
```

2. Build assets:
```bash
npm ci
npm run build
```

3. Optimize application:
```bash
php artisan optimize
php artisan view:cache
php artisan config:cache
php artisan route:cache
php artisan event:cache
```

4. Set proper permissions:
```bash
chmod -R 755 storage bootstrap/cache
```

5. Update environment for production:
```bash
# Edit .env file
APP_ENV=production
APP_DEBUG=false
```

6. Clear unnecessary files before deployment:
```bash
rm -rf node_modules
rm -rf tests
```

7. Create a deployment archive:
```bash
tar -czf FixHaat-deploy.tar.gz --exclude='.git' --exclude='node_modules' --exclude='tests' .
```

## Available Commands

### User Management
```bash
# Create a new user
php artisan user:create

# List all users
php artisan user:list

# Reset user password
php artisan user:reset-password
```

### Database
```bash
# Fresh migration with demo data
php artisan migrate:fresh --seed

# Create a new migration
php artisan make:migration create_table_name

# Run specific seeder
php artisan db:seed --class=UserSeeder
```

### Cache
```bash
# Clear application cache
php artisan cache:clear

# Clear config cache
php artisan config:clear

# Clear route cache
php artisan route:clear

# Clear view cache
php artisan view:clear
```

### Maintenance
```bash
# Put application in maintenance mode
php artisan down

# Bring application back online
php artisan up

# Clear compiled classes
php artisan clear-compiled

# Create symbolic links
php artisan storage:link
```

## Build Status

[![PharmaDesk CI](https://github.com/firozanam/mobile-parts-db/actions/workflows/ci.yml/badge.svg)](https://github.com/firozanam/mobile-parts-db/actions/workflows/ci.yml)
[![Code Style](https://github.com/firozanam/mobile-parts-db/actions/workflows/ci.yml/badge.svg?branch=main&event=push&label=code-style)](https://github.com/firozanam/mobile-parts-db/actions/workflows/ci.yml)
[![Security Check](https://github.com/firozanam/mobile-parts-db/actions/workflows/ci.yml/badge.svg?branch=main&event=push&label=security)](https://github.com/firozanam/mobile-parts-db/actions/workflows/ci.yml)

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[WebReinvent](https://webreinvent.com/)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Jump24](https://jump24.co.uk)**
- **[Redberry](https://redberry.international/laravel/)**
- **[Active Logic](https://activelogic.com)**
- **[byte5](https://byte5.de)**
- **[OP.GG](https://op.gg)**

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct, and the process for submitting pull requests.

## Security

If you discover any security-related issues, <NAME_EMAIL> instead of using the issue tracker.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
