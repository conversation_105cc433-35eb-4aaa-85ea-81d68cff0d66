#!/bin/bash

# Mobile Parts Database - Test Runner Script
# This script runs comprehensive tests for the application

set -e

echo "🧪 Mobile Parts Database - Test Suite"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_error "Please run this script from the Laravel project root directory"
    exit 1
fi

# Set up test environment
print_status "Setting up test environment..."

# Copy environment file for testing
if [ ! -f ".env.testing" ]; then
    print_warning ".env.testing not found, creating from .env.example"
    cp .env.example .env.testing
    
    # Update test database settings
    sed -i.bak 's/DB_DATABASE=.*/DB_DATABASE=:memory:/' .env.testing
    sed -i.bak 's/DB_CONNECTION=.*/DB_CONNECTION=sqlite/' .env.testing
    rm .env.testing.bak
fi

# Clear and prepare test database
print_status "Preparing test database..."
php artisan config:clear --env=testing
php artisan cache:clear --env=testing

# Run different test suites
echo ""
print_status "Running Unit Tests..."
echo "----------------------"
php artisan test --testsuite=Unit --stop-on-failure

if [ $? -eq 0 ]; then
    print_success "Unit tests passed!"
else
    print_error "Unit tests failed!"
    exit 1
fi

echo ""
print_status "Running Feature Tests..."
echo "-------------------------"
php artisan test --testsuite=Feature --stop-on-failure

if [ $? -eq 0 ]; then
    print_success "Feature tests passed!"
else
    print_error "Feature tests failed!"
    exit 1
fi

# Run specific test categories
echo ""
print_status "Running Search Functionality Tests..."
echo "--------------------------------------"
php artisan test tests/Unit/SearchServiceTest.php tests/Feature/SearchControllerTest.php --stop-on-failure

if [ $? -eq 0 ]; then
    print_success "Search tests passed!"
else
    print_error "Search tests failed!"
    exit 1
fi

echo ""
print_status "Running Subscription Tests..."
echo "------------------------------"
php artisan test tests/Unit/SubscriptionServiceTest.php --stop-on-failure

if [ $? -eq 0 ]; then
    print_success "Subscription tests passed!"
else
    print_error "Subscription tests failed!"
    exit 1
fi

# Run code quality checks
echo ""
print_status "Running Code Quality Checks..."
echo "-------------------------------"

# Check if PHP CS Fixer is available
if command -v php-cs-fixer &> /dev/null; then
    print_status "Running PHP CS Fixer..."
    php-cs-fixer fix --dry-run --diff
    
    if [ $? -eq 0 ]; then
        print_success "Code style check passed!"
    else
        print_warning "Code style issues found. Run 'php-cs-fixer fix' to fix them."
    fi
else
    print_warning "PHP CS Fixer not found. Skipping code style check."
fi

# Check if PHPStan is available
if command -v phpstan &> /dev/null; then
    print_status "Running PHPStan static analysis..."
    phpstan analyse --memory-limit=1G
    
    if [ $? -eq 0 ]; then
        print_success "Static analysis passed!"
    else
        print_warning "Static analysis issues found."
    fi
else
    print_warning "PHPStan not found. Skipping static analysis."
fi

# Generate test coverage report if requested
if [ "$1" = "--coverage" ]; then
    echo ""
    print_status "Generating test coverage report..."
    echo "----------------------------------"
    
    php artisan test --coverage --min=80
    
    if [ $? -eq 0 ]; then
        print_success "Coverage report generated successfully!"
    else
        print_warning "Coverage below minimum threshold."
    fi
fi

# Run performance tests if requested
if [ "$1" = "--performance" ]; then
    echo ""
    print_status "Running performance tests..."
    echo "-----------------------------"
    
    # Add performance test commands here
    print_warning "Performance tests not yet implemented."
fi

# Final summary
echo ""
echo "======================================"
print_success "All tests completed successfully! ✅"
echo ""
print_status "Test Summary:"
echo "- Unit Tests: ✅ Passed"
echo "- Feature Tests: ✅ Passed"
echo "- Search Functionality: ✅ Passed"
echo "- Subscription System: ✅ Passed"
echo ""
print_status "Next steps:"
echo "1. Review any warnings above"
echo "2. Run with --coverage flag for coverage report"
echo "3. Deploy with confidence! 🚀"
echo ""
