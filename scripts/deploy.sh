#!/bin/bash

# Mobile Parts Database - Production Deployment Script
# This script handles production deployment with zero downtime

set -e

echo "🚀 Mobile Parts Database - Production Deployment"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
APP_DIR="/var/www/mobile-parts-db"
BACKUP_DIR="/var/backups/mobile-parts-db"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Check if we're running as the correct user
if [ "$EUID" -eq 0 ]; then
    print_error "Please don't run this script as root"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_error "Please run this script from the Laravel project root directory"
    exit 1
fi

# Check environment
if [ ! -f ".env" ]; then
    print_error ".env file not found"
    exit 1
fi

# Source environment variables
source .env

if [ "$APP_ENV" != "production" ]; then
    print_error "APP_ENV must be set to 'production' in .env file"
    exit 1
fi

print_status "Starting deployment process..."

# 1. Create backup
print_status "Creating backup..."
mkdir -p "$BACKUP_DIR"

# Backup database
print_status "Backing up database..."
mysqldump -u "$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" > "$BACKUP_DIR/database_$TIMESTAMP.sql"

# Backup application files
print_status "Backing up application files..."
tar -czf "$BACKUP_DIR/app_$TIMESTAMP.tar.gz" \
    --exclude=node_modules \
    --exclude=vendor \
    --exclude=storage/logs \
    --exclude=storage/framework/cache \
    --exclude=storage/framework/sessions \
    --exclude=storage/framework/views \
    .

print_success "Backup created successfully"

# 2. Put application in maintenance mode
print_status "Enabling maintenance mode..."
php artisan down --message="Deploying new version..." --retry=60

# 3. Pull latest code
print_status "Pulling latest code..."
git fetch origin
git reset --hard origin/main

# 4. Install/update dependencies
print_status "Installing PHP dependencies..."
composer install --no-dev --optimize-autoloader --no-interaction

print_status "Installing Node.js dependencies..."
npm ci --production

# 5. Build assets
print_status "Building production assets..."
npm run build

# 6. Clear and optimize caches
print_status "Clearing application caches..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

print_status "Optimizing for production..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# 7. Run database migrations
print_status "Running database migrations..."
php artisan migrate --force

# 8. Optimize database
print_status "Optimizing database..."
php artisan db:seed --class=CacheWarmupSeeder --force || true

# 9. Warm up caches
print_status "Warming up application caches..."
php artisan cache:warm || true

# 10. Set proper permissions
print_status "Setting file permissions..."
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

# 11. Restart services
print_status "Restarting services..."

# Restart PHP-FPM
sudo systemctl reload php8.2-fpm

# Restart queue workers
print_status "Restarting queue workers..."
php artisan queue:restart

# Start queue workers if not running
if ! pgrep -f "artisan queue:work" > /dev/null; then
    print_status "Starting queue workers..."
    nohup php artisan queue:work --daemon --sleep=3 --tries=3 > /dev/null 2>&1 &
fi

# Restart scheduler (if using supervisor)
if command -v supervisorctl &> /dev/null; then
    sudo supervisorctl restart laravel-worker:*
fi

# 12. Health check
print_status "Performing health check..."

# Check if application responds
if curl -f -s "$APP_URL/health" > /dev/null; then
    print_success "Application health check passed"
else
    print_warning "Application health check failed, but continuing..."
fi

# Check database connection
if php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database OK';" | grep -q "Database OK"; then
    print_success "Database connection check passed"
else
    print_error "Database connection check failed"
    
    # Rollback on database failure
    print_status "Rolling back due to database failure..."
    git reset --hard HEAD~1
    php artisan migrate:rollback --force
    php artisan up
    exit 1
fi

# 13. Disable maintenance mode
print_status "Disabling maintenance mode..."
php artisan up

# 14. Final verification
print_status "Performing final verification..."
sleep 5

if curl -f -s "$APP_URL" > /dev/null; then
    print_success "Final verification passed"
else
    print_error "Final verification failed"
    
    # Emergency rollback
    print_status "Performing emergency rollback..."
    php artisan down
    
    # Restore database
    mysql -u "$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" < "$BACKUP_DIR/database_$TIMESTAMP.sql"
    
    # Restore application
    tar -xzf "$BACKUP_DIR/app_$TIMESTAMP.tar.gz" -C .
    
    php artisan up
    exit 1
fi

# 15. Cleanup old backups (keep last 5)
print_status "Cleaning up old backups..."
cd "$BACKUP_DIR"
ls -t database_*.sql | tail -n +6 | xargs -r rm
ls -t app_*.tar.gz | tail -n +6 | xargs -r rm

# 16. Send deployment notification
print_status "Sending deployment notification..."
if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"✅ Mobile Parts DB deployed successfully to production at $(date)\"}" \
        "$SLACK_WEBHOOK_URL" || true
fi

# 17. Log deployment
echo "$(date): Deployment completed successfully" >> /var/log/mobile-parts-db-deployments.log

print_success "Deployment completed successfully! 🎉"
echo ""
print_status "Deployment Summary:"
echo "- Backup created: $BACKUP_DIR/database_$TIMESTAMP.sql"
echo "- Application files backed up: $BACKUP_DIR/app_$TIMESTAMP.tar.gz"
echo "- Database migrations: Applied"
echo "- Caches: Optimized"
echo "- Services: Restarted"
echo "- Health checks: Passed"
echo ""
print_status "Application is now live at: $APP_URL"
echo ""

# Optional: Run post-deployment tests
if [ "$1" = "--test" ]; then
    print_status "Running post-deployment tests..."
    php artisan test --testsuite=Feature --stop-on-failure
    
    if [ $? -eq 0 ]; then
        print_success "Post-deployment tests passed!"
    else
        print_warning "Some post-deployment tests failed. Please check manually."
    fi
fi

print_status "Deployment script completed."
