#!/bin/bash

# Search Configuration Test Suite Runner
# This script runs comprehensive tests for the search configuration system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run PHP tests
run_php_tests() {
    local test_file=$1
    local test_name=$2
    
    print_status "Running $test_name..."
    
    if php artisan test "$test_file" --stop-on-failure; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Function to run React tests
run_react_tests() {
    local test_file=$1
    local test_name=$2
    
    print_status "Running $test_name..."
    
    if npm test "$test_file" -- --run --reporter=verbose; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Main function
main() {
    local test_type=${1:-"all"}
    local failed_tests=0
    local total_tests=0
    
    print_status "Starting Search Configuration Test Suite"
    print_status "Test type: $test_type"
    echo ""
    
    # Check prerequisites
    if ! command_exists php; then
        print_error "PHP is not installed or not in PATH"
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed or not in PATH"
        exit 1
    fi
    
    # Set up test environment
    print_status "Setting up test environment..."
    
    # Clear caches
    php artisan cache:clear >/dev/null 2>&1 || true
    php artisan config:clear >/dev/null 2>&1 || true
    
    # Run database migrations for testing
    php artisan migrate:fresh --env=testing --seed >/dev/null 2>&1 || true
    
    print_success "Test environment ready"
    echo ""
    
    # Run tests based on type
    case $test_type in
        "all")
            print_status "=== UNIT TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Unit/SearchConfigurationModelTest.php" "SearchConfiguration Model Tests"; then
                ((failed_tests++))
            fi
            echo ""

            print_status "=== FEATURE TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/Admin/SearchConfigurationControllerTest.php" "SearchConfiguration Controller Tests"; then
                ((failed_tests++))
            fi
            echo ""

            print_status "=== INTEGRATION TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/SearchConfigurationIntegrationTest.php" "SearchConfiguration Integration Tests"; then
                ((failed_tests++))
            fi
            echo ""

            print_status "=== PERFORMANCE TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/SearchConfigurationPerformanceTest.php" "SearchConfiguration Performance Tests"; then
                ((failed_tests++))
            fi
            echo ""

            print_status "=== FRONTEND TESTS ==="
            ((total_tests++))
            if ! run_react_tests "tests/components/SearchConfiguration.test.tsx" "SearchConfiguration React Component Tests"; then
                ((failed_tests++))
            fi
            echo ""
            ;;

        "backend")
            print_status "=== UNIT TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Unit/SearchConfigurationModelTest.php" "SearchConfiguration Model Tests"; then
                ((failed_tests++))
            fi
            echo ""

            print_status "=== FEATURE TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/Admin/SearchConfigurationControllerTest.php" "SearchConfiguration Controller Tests"; then
                ((failed_tests++))
            fi
            echo ""

            print_status "=== INTEGRATION TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/SearchConfigurationIntegrationTest.php" "SearchConfiguration Integration Tests"; then
                ((failed_tests++))
            fi
            echo ""

            print_status "=== PERFORMANCE TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/SearchConfigurationPerformanceTest.php" "SearchConfiguration Performance Tests"; then
                ((failed_tests++))
            fi
            echo ""
            ;;

        "unit")
            print_status "=== UNIT TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Unit/SearchConfigurationModelTest.php" "SearchConfiguration Model Tests"; then
                ((failed_tests++))
            fi
            echo ""
            ;;

        "feature")
            print_status "=== FEATURE TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/Admin/SearchConfigurationControllerTest.php" "SearchConfiguration Controller Tests"; then
                ((failed_tests++))
            fi
            echo ""
            ;;

        "integration")
            print_status "=== INTEGRATION TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/SearchConfigurationIntegrationTest.php" "SearchConfiguration Integration Tests"; then
                ((failed_tests++))
            fi
            echo ""
            ;;

        "performance")
            print_status "=== PERFORMANCE TESTS ==="
            ((total_tests++))
            if ! run_php_tests "tests/Feature/SearchConfigurationPerformanceTest.php" "SearchConfiguration Performance Tests"; then
                ((failed_tests++))
            fi
            echo ""
            ;;

        "frontend")
            print_status "=== FRONTEND TESTS ==="
            ((total_tests++))
            if ! run_react_tests "tests/components/SearchConfiguration.test.tsx" "SearchConfiguration React Component Tests"; then
                ((failed_tests++))
            fi
            echo ""
            ;;
        
        "quick")
            print_status "=== QUICK VALIDATION TESTS ==="
            
            print_status "Running key model test..."
            ((total_tests++))
            if ! php artisan test tests/Unit/SearchConfigurationModelTest.php --filter=test_get_method_returns_configuration_value; then
                ((failed_tests++))
            fi
            
            print_status "Running key controller test..."
            ((total_tests++))
            if ! php artisan test tests/Feature/Admin/SearchConfigurationControllerTest.php --filter=test_admin_can_update_search_configurations; then
                ((failed_tests++))
            fi
            
            print_status "Running key integration test..."
            ((total_tests++))
            if ! php artisan test tests/Feature/SearchConfigurationIntegrationTest.php --filter=test_complete_search_configuration_workflow; then
                ((failed_tests++))
            fi
            ;;
        
        *)
            print_error "Unknown test type: $test_type"
            print_status "Available options: all, backend, frontend, unit, feature, integration, performance, quick"
            exit 1
            ;;
    esac
    
    # Print summary
    echo ""
    print_status "=== TEST SUMMARY ==="
    
    if [[ $failed_tests -eq 0 ]]; then
        print_success "All $total_tests test suite(s) passed! ✅"
        echo ""
        print_status "Search Configuration System is ready for production"
    else
        print_error "$failed_tests out of $total_tests test suite(s) failed ❌"
        echo ""
        print_warning "Please review failed tests before deploying"
        exit 1
    fi
}

# Show usage if no arguments or help requested
if [[ $# -eq 0 || "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Search Configuration Test Suite Runner"
    echo ""
    echo "Usage: $0 [test_type]"
    echo ""
    echo "Test Types:"
    echo "  all          - Run all tests (default)"
    echo "  backend      - Run all backend PHP tests"
    echo "  frontend     - Run all frontend React tests"
    echo "  unit         - Run only unit tests"
    echo "  feature      - Run only feature tests"
    echo "  integration  - Run only integration tests"
    echo "  performance  - Run only performance tests"
    echo "  quick        - Run key validation tests only"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 backend           # Run only backend tests"
    echo "  $0 quick             # Quick validation"
    echo "  $0 performance       # Performance tests only"
    echo ""
    exit 0
fi

# Run main function with arguments
main "$@"
