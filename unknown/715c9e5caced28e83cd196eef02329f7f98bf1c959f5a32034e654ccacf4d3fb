{"version": "0.2", "language": "en", "words": ["inertiajs", "lucide", "tailwindcss", "shadcn", "radix", "clsx", "cmdk", "vaul", "sonner", "recharts", "framer", "hookform", "zod", "tanstack", "headlessui", "heroicons", "phosphor", "tabler", "feather", "octicons", "boxicons", "ionicons", "fontawesome", "mantine", "chakra", "nextui", "antd", "ariakit", "downshift", "reach", "<PERSON><PERSON>t", "styled", "emotion", "stitches", "vanilla", "windicss", "unocss", "twind", "daisyui", "bulma", "semantic", "materialize", "bootstrap", "foundation", "spectre", "milligram", "skeleton", "pure", "primer", "tachyons", "basscss", "picnic", "mini", "turret", "concise", "kube", "siimple", "wing", "chota", "marx", "water", "holiday", "tent", "bahunya", "new", "paper", "sakura", "style", "terminal", "retro", "latex", "tufte", "modest", "splendor", "air", "modest", "github", "markdown", "<PERSON><PERSON>", "mobileparts", "favoritable", "unsuspension", "Segoe", "Roboto", "rgba", "<PERSON><PERSON><PERSON><PERSON>", "sluggable", "tightenco", "fakerphp", "<PERSON><PERSON><PERSON><PERSON>", "phpunit", "phpunit", "dont", "AMOLED", "<PERSON><PERSON>", "p<PERSON><PERSON><PERSON>", "VITE", "ptxn", "codepaths", "pestphp", "Litecoin", "Ven<PERSON>", "Foxconn"], "ignorePaths": ["node_modules/**", "vendor/**", "storage/**", "bootstrap/cache/**", "public/build/**", "*.min.js", "*.min.css"]}