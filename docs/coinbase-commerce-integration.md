# Coinbase Commerce Integration Guide

This document provides a comprehensive guide for the Coinbase Commerce integration in the Mobile Parts Database application.

## Overview

Coinbase Commerce is a cryptocurrency payment gateway that allows merchants to accept payments in various cryptocurrencies with automatic settlement in USDC. This integration provides:

- Support for Bitcoin, Ethereum, Litecoin, Bitcoin Cash, USDC, and DAI
- Automatic USDC settlement to avoid volatility
- Real-time payment confirmation
- Comprehensive logging and debugging for development
- Webhook support for payment status updates
- Admin configuration interface

## Features

### ✅ Implemented Features

- **Database Schema**: Complete database structure for transactions and configuration
- **Service Layer**: CoinbaseCommerceService with comprehensive API integration
- **Controller Layer**: Payment processing and webhook handling
- **Admin Interface**: Configuration page with modern, professional design
- **Model Integration**: Updated PricingPlan, Subscription, and User models
- **Webhook Processing**: Real-time payment status updates
- **Comprehensive Logging**: Detailed logging for development and debugging
- **Test Suite**: Feature tests for integration validation

### 🔧 Configuration

#### Environment Variables

Add the following variables to your `.env` file:

```env
# Coinbase Commerce Configuration
COINBASE_COMMERCE_API_KEY=your_api_key_here
COINBASE_COMMERCE_WEBHOOK_SECRET=your_webhook_secret_here
COINBASE_COMMERCE_BASE_URL=https://api.commerce.coinbase.com
COINBASE_COMMERCE_DEBUG=true
COINBASE_COMMERCE_AUTO_ACTIVATE=true
COINBASE_COMMERCE_DEFAULT_CURRENCY=USD
COINBASE_COMMERCE_CHARGE_EXPIRATION=60
COINBASE_COMMERCE_LOGGING_ENABLED=true
COINBASE_COMMERCE_LOGGING_LEVEL=info
COINBASE_COMMERCE_RATE_LIMITING_ENABLED=true
COINBASE_COMMERCE_MAX_REQUESTS_PER_MINUTE=60
COINBASE_COMMERCE_RETRY_ENABLED=true
COINBASE_COMMERCE_MAX_RETRY_ATTEMPTS=3
COINBASE_COMMERCE_RETRY_DELAY=1
```

#### Admin Configuration

1. Navigate to **Admin Panel > Payment Gateways > Coinbase Commerce**
2. Enter your API key from Coinbase Commerce dashboard
3. Configure webhook secret (optional but recommended)
4. Enable debug mode for development
5. Select supported cryptocurrencies
6. Test the connection
7. Save configuration

### 🗄️ Database Schema

#### New Tables

**coinbase_commerce_transactions**
- Stores all Coinbase Commerce payment transactions
- Includes charge details, payment status, and metadata
- Links to users, subscriptions, and pricing plans

#### Updated Tables

**pricing_plans**
- Added `coinbase_commerce_price_id_monthly`
- Added `coinbase_commerce_price_id_yearly`
- Added `coinbase_commerce_product_id`
- Added `crypto_payment_enabled`

**subscriptions**
- Added `coinbase_commerce_subscription_id`
- Updated `payment_gateway` to support 'coinbase_commerce'

**users**
- Added `coinbase_commerce_customer_id`

### 🔌 API Endpoints

#### Payment Endpoints (Authenticated)

```
POST /coinbase-commerce/charge
GET /coinbase-commerce/charge/{chargeId}/status
GET /coinbase-commerce/transactions
```

#### Admin Endpoints (Admin Only)

```
GET /admin/payment-gateways/coinbase/configure
POST /admin/payment-gateways/coinbase/configure
POST /admin/payment-gateways/coinbase/test
```

#### Webhook Endpoint (Public)

```
POST /webhooks/coinbase-commerce
```

### 🎯 Usage Examples

#### Creating a Charge

```php
use App\Services\CoinbaseCommerceService;

$service = app(CoinbaseCommerceService::class);
$result = $service->createCharge($user, $pricingPlan, [
    'billing_cycle' => 'month'
]);

if ($result) {
    $chargeId = $result['charge_id'];
    $hostedUrl = $result['hosted_url'];
    $transaction = $result['transaction'];
}
```

#### Processing Webhooks

```php
// Webhook processing is handled automatically
// Events: charge:confirmed, charge:failed, charge:delayed, charge:pending, charge:resolved
```

#### Frontend Integration

```javascript
// Create charge
const response = await fetch('/coinbase-commerce/charge', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': csrfToken,
    },
    body: JSON.stringify({
        pricing_plan_id: planId,
        billing_cycle: 'month'
    })
});

const data = await response.json();
if (data.success) {
    window.location.href = data.hosted_url;
}
```

### 🔍 Debugging and Logging

#### Debug Mode

When `COINBASE_COMMERCE_DEBUG=true`:
- All API requests and responses are logged
- Webhook events are logged with full details
- Transaction state changes are tracked
- Error details are preserved

#### Log Locations

- **Application Logs**: `storage/logs/laravel.log`
- **Debug Information**: Search for "Coinbase Commerce" in logs
- **Error Tracking**: All exceptions are logged with full context

#### Common Debug Scenarios

```php
// Check if service is configured
$service = app(CoinbaseCommerceService::class);
if (!$service->isConfigured()) {
    Log::error('Coinbase Commerce not configured');
}

// Test API connection
$config = $service->getConfiguration();
Log::info('Coinbase Commerce config', $config);

// Monitor webhook processing
// Check logs for webhook signature verification
// Verify charge status updates
```

### 🧪 Testing

#### Running Tests

```bash
# Run all Coinbase Commerce tests
php artisan test --filter=CoinbaseCommerceIntegrationTest

# Run specific test
php artisan test tests/Feature/CoinbaseCommerceIntegrationTest.php::test_coinbase_commerce_service_is_configured
```

#### Test Coverage

- Service configuration validation
- Transaction model functionality
- Pricing plan integration
- User relationships
- Admin interface accessibility
- Webhook endpoint availability
- Authentication requirements

### 🔒 Security Considerations

#### Webhook Security

- Webhook signature verification using HMAC-SHA256
- Optional webhook secret configuration
- Request validation and sanitization

#### API Security

- API key stored in environment variables
- Rate limiting on API requests
- Request timeout configuration
- SSL/TLS verification

#### Data Protection

- Sensitive data logging is avoided
- API keys are masked in logs
- Transaction data is encrypted at rest

### 🚀 Deployment Checklist

#### Pre-deployment

- [ ] Configure environment variables
- [ ] Test API connection
- [ ] Verify webhook URL accessibility
- [ ] Run integration tests
- [ ] Check database migrations

#### Post-deployment

- [ ] Verify admin configuration interface
- [ ] Test charge creation flow
- [ ] Confirm webhook processing
- [ ] Monitor logs for errors
- [ ] Validate payment completion flow

### 📊 Monitoring and Maintenance

#### Key Metrics

- Transaction success rate
- Webhook processing time
- API response times
- Error rates by type

#### Regular Maintenance

- Monitor API key expiration
- Review webhook logs
- Update supported currencies
- Check rate limiting thresholds

### 🆘 Troubleshooting

#### Common Issues

1. **API Key Invalid**
   - Verify key in Coinbase Commerce dashboard
   - Check environment variable configuration
   - Test connection in admin interface

2. **Webhook Not Receiving Events**
   - Verify webhook URL is publicly accessible
   - Check webhook secret configuration
   - Review Coinbase Commerce webhook settings

3. **Charge Creation Fails**
   - Check API rate limits
   - Verify pricing plan configuration
   - Review request payload in logs

4. **Payment Not Confirming**
   - Check webhook processing logs
   - Verify charge status in Coinbase Commerce
   - Review transaction timeline

#### Support Resources

- [Coinbase Commerce Documentation](https://docs.cdp.coinbase.com/commerce/introduction/welcome)
- [API Reference](https://docs.cdp.coinbase.com/commerce/introduction/quickstart)
- Application logs and debug information
- Integration test suite for validation

### 🔄 Future Enhancements

#### Planned Features

- Recurring payment support
- Multi-currency pricing
- Advanced analytics dashboard
- Customer payment history
- Refund processing
- Payment method preferences

#### Integration Opportunities

- Mobile app payment integration
- Email notification enhancements
- Advanced reporting features
- Customer support tools
- Fraud detection integration
