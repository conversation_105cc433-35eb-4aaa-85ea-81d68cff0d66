# Paddle Payment Gateway - Deployment Guide

This guide covers production deployment strategies, environment setup, and post-deployment monitoring for Paddle payment gateway integration.

## 🚀 Pre-Deployment Checklist

### 1. Environment Preparation

**Production Environment Variables:**
```env
# Application
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Paddle Production Configuration
PADDLE_ENVIRONMENT=production
PADDLE_API_KEY=live_api_key_from_paddle_dashboard
PADDLE_CLIENT_TOKEN=live_client_token_from_paddle_dashboard
PADDLE_WEBHOOK_SECRET=live_webhook_secret_from_paddle_dashboard
PADDLE_VENDOR_ID=your_paddle_vendor_id

# Security
PADDLE_VERIFY_WEBHOOK_SIGNATURE=true
PADDLE_LOGGING_ENABLED=false

# Database
DB_CONNECTION=mysql
DB_HOST=your_production_db_host
DB_DATABASE=your_production_database
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_db_password

# Cache & Sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Mail
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_smtp_user
MAIL_PASSWORD=your_smtp_password
MAIL_ENCRYPTION=tls
```

**Security Configuration:**
```bash
# Generate secure application key
php artisan key:generate --force

# Set proper file permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

# Secure environment file
chmod 600 .env
chown root:root .env
```

### 2. Database Setup

**Production Migration Strategy:**
```bash
# Backup existing database
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Run migrations with backup
php artisan migrate --force

# Verify migration status
php artisan migrate:status

# Seed production data if needed
php artisan db:seed --class=PaddleConfigurationSeeder
```

**Database Optimization:**
```sql
-- Create production indexes
CREATE INDEX idx_users_paddle_customer_id ON users(paddle_customer_id);
CREATE INDEX idx_paddle_transactions_user_status ON paddle_transactions(user_id, status);
CREATE INDEX idx_paddle_transactions_status_created ON paddle_transactions(status, created_at);
CREATE INDEX idx_paddle_webhooks_event_status ON paddle_webhooks(event_type, status);
CREATE INDEX idx_paddle_webhooks_status_created ON paddle_webhooks(status, created_at);

-- Optimize table settings
ALTER TABLE paddle_transactions ENGINE=InnoDB;
ALTER TABLE paddle_webhooks ENGINE=InnoDB;
```

### 3. Paddle Dashboard Configuration

**Production Setup:**
1. **Switch to Live Environment**
   - Navigate to Paddle Dashboard
   - Switch from Sandbox to Live mode
   - Verify business details and tax settings

2. **API Keys Configuration**
   - Generate production API key
   - Generate production client-side token
   - Set domain restrictions for security

3. **Webhook Configuration**
   - Create webhook endpoint: `https://yourdomain.com/webhooks/paddle`
   - Select all relevant events:
     - `transaction.completed`
     - `transaction.updated`
     - `subscription.created`
     - `subscription.updated`
     - `subscription.cancelled`
   - Generate and save webhook secret

4. **Product & Pricing Setup**
   - Create production products
   - Set up pricing plans with correct billing cycles
   - Configure tax settings
   - Set up discount codes if needed

---

## 🔧 Deployment Process

### 1. Zero-Downtime Deployment

**Using Laravel Envoy:**
```php
// Envoy.blade.php
@servers(['production' => '<EMAIL>'])

@setup
    $repository = '**************:your-username/your-repo.git';
    $releases_dir = '/var/www/releases';
    $app_dir = '/var/www/app';
    $release = date('YmdHis');
    $new_release_dir = $releases_dir .'/'. $release;
@endsetup

@story('deploy')
    clone_repository
    run_composer
    update_symlinks
    optimize_application
    migrate_database
    restart_services
@endstory

@task('clone_repository')
    echo 'Cloning repository'
    [ -d {{ $releases_dir }} ] || mkdir {{ $releases_dir }}
    git clone --depth 1 {{ $repository }} {{ $new_release_dir }}
    cd {{ $new_release_dir }}
    git reset --hard {{ $commit }}
@endtask

@task('run_composer')
    echo "Starting deployment ({{ $release }})"
    cd {{ $new_release_dir }}
    composer install --prefer-dist --no-scripts -q -o --no-dev
@endtask

@task('update_symlinks')
    echo "Linking storage directory"
    rm -rf {{ $new_release_dir }}/storage
    ln -nfs {{ $app_dir }}/storage {{ $new_release_dir }}/storage
    
    echo "Linking .env file"
    ln -nfs {{ $app_dir }}/.env {{ $new_release_dir }}/.env
    
    echo "Linking current release"
    ln -nfs {{ $new_release_dir }} {{ $app_dir }}/current
@endtask

@task('optimize_application')
    echo "Optimizing application"
    cd {{ $app_dir }}/current
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    npm run build
@endtask

@task('migrate_database')
    echo "Running migrations"
    cd {{ $app_dir }}/current
    php artisan migrate --force
@endtask

@task('restart_services')
    echo "Restarting services"
    sudo supervisorctl restart laravel-worker:*
    sudo service nginx reload
    sudo service php8.1-fpm reload
@endtask
```

### 2. Docker Deployment

**Dockerfile:**
```dockerfile
FROM php:8.1-fpm

# Install dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    nodejs \
    npm

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . /var/www

# Install dependencies
RUN composer install --no-dev --optimize-autoloader
RUN npm ci && npm run build

# Set permissions
RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/storage

EXPOSE 9000
CMD ["php-fpm"]
```

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  app:
    build: .
    container_name: paddle-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - paddle-network

  nginx:
    image: nginx:alpine
    container_name: paddle-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx:/etc/nginx/conf.d
      - ./docker/ssl:/etc/ssl/certs
    networks:
      - paddle-network

  mysql:
    image: mysql:8.0
    container_name: paddle-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - paddle-network

  redis:
    image: redis:alpine
    container_name: paddle-redis
    restart: unless-stopped
    networks:
      - paddle-network

networks:
  paddle-network:
    driver: bridge

volumes:
  mysql-data:
```

### 3. CI/CD Pipeline

**GitHub Actions:**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: yes
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv

    - name: Install dependencies
      run: |
        composer install --no-progress --prefer-dist --optimize-autoloader
        npm ci

    - name: Copy environment file
      run: cp .env.testing .env

    - name: Generate key
      run: php artisan key:generate

    - name: Run migrations
      run: php artisan migrate

    - name: Build frontend
      run: npm run build

    - name: Run tests
      run: php artisan test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/app
          git pull origin main
          composer install --no-dev --optimize-autoloader
          npm ci && npm run build
          php artisan migrate --force
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          sudo supervisorctl restart laravel-worker:*
          sudo service nginx reload
```

---

## 🔍 Post-Deployment Monitoring

### 1. Health Checks

**Application Health:**
```php
// routes/web.php
Route::get('/health', function () {
    $checks = [
        'app' => true,
        'database' => true,
        'cache' => true,
        'paddle' => app(PaddleService::class)->isConfigured(),
    ];

    try {
        DB::connection()->getPdo();
    } catch (Exception $e) {
        $checks['database'] = false;
    }

    try {
        Cache::put('health_check', true, 60);
        $checks['cache'] = Cache::get('health_check', false);
    } catch (Exception $e) {
        $checks['cache'] = false;
    }

    $status = collect($checks)->every(fn($check) => $check === true) ? 200 : 503;

    return response()->json([
        'status' => $status === 200 ? 'healthy' : 'unhealthy',
        'timestamp' => now()->toISOString(),
        'checks' => $checks,
    ], $status);
});
```

**Webhook Health:**
```bash
# Monitor webhook delivery
curl -s https://yourdomain.com/health/paddle | jq '.checks.recent_webhooks'

# Check webhook processing queue
php artisan queue:monitor
```

### 2. Performance Monitoring

**Application Performance:**
```php
// Add to AppServiceProvider
public function boot(): void
{
    // Monitor database queries
    DB::listen(function ($query) {
        if ($query->time > 1000) { // Log slow queries
            Log::warning('Slow query detected', [
                'sql' => $query->sql,
                'time' => $query->time,
                'bindings' => $query->bindings,
            ]);
        }
    });

    // Monitor Paddle API calls
    Event::listen('paddle.api.request', function ($event) {
        Log::info('Paddle API request', [
            'method' => $event->method,
            'duration' => $event->duration,
            'status' => $event->status,
        ]);
    });
}
```

**Server Monitoring:**
```bash
# Monitor server resources
htop
iotop
nethogs

# Monitor application logs
tail -f storage/logs/laravel.log | grep -E "(ERROR|CRITICAL)"

# Monitor nginx access logs
tail -f /var/log/nginx/access.log | grep -E "(paddle|webhook)"
```

### 3. Error Tracking

**Sentry Integration:**
```bash
composer require sentry/sentry-laravel
```

```php
// config/sentry.php
'dsn' => env('SENTRY_LARAVEL_DSN'),
'environment' => env('APP_ENV'),
'traces_sample_rate' => env('SENTRY_TRACES_SAMPLE_RATE', 0.1),

// In PaddleService
try {
    return $this->paddle->customers->create($operation);
} catch (ApiError $e) {
    app('sentry')->captureException($e, [
        'tags' => ['component' => 'paddle'],
        'extra' => ['user_id' => $user->id],
    ]);
    throw $e;
}
```

---

## 📊 Monitoring Dashboard

### 1. Key Metrics

**Business Metrics:**
- Daily/Monthly recurring revenue
- Subscription conversion rates
- Churn rates
- Average revenue per user

**Technical Metrics:**
- API response times
- Webhook processing success rate
- Database query performance
- Error rates

**Implementation:**
```php
// Create metrics collection job
class CollectPaddleMetrics implements ShouldQueue
{
    public function handle(): void
    {
        $metrics = [
            'daily_revenue' => $this->getDailyRevenue(),
            'active_subscriptions' => $this->getActiveSubscriptions(),
            'webhook_success_rate' => $this->getWebhookSuccessRate(),
            'api_response_time' => $this->getAverageApiResponseTime(),
        ];

        // Send to monitoring service
        Http::post(config('monitoring.webhook_url'), $metrics);
    }

    private function getDailyRevenue(): float
    {
        return PaddleTransaction::completed()
            ->whereDate('completed_at', today())
            ->sum('amount');
    }

    private function getActiveSubscriptions(): int
    {
        return Subscription::where('status', 'active')->count();
    }

    private function getWebhookSuccessRate(): float
    {
        $total = PaddleWebhook::whereDate('created_at', today())->count();
        $successful = PaddleWebhook::whereDate('created_at', today())
            ->where('status', 'processed')
            ->count();

        return $total > 0 ? ($successful / $total) * 100 : 100;
    }
}
```

### 2. Alerting

**Critical Alerts:**
```php
// Monitor webhook failures
if ($webhookFailureRate > 10) {
    Mail::to(config('monitoring.alert_email'))
        ->send(new WebhookFailureAlert($webhookFailureRate));
}

// Monitor API errors
if ($apiErrorRate > 5) {
    Slack::send('Paddle API error rate is high: ' . $apiErrorRate . '%');
}

// Monitor revenue drops
if ($dailyRevenue < $expectedRevenue * 0.8) {
    PagerDuty::trigger('Revenue drop detected');
}
```

---

## 🔒 Security Hardening

### 1. Server Security

**Firewall Configuration:**
```bash
# Allow only necessary ports
ufw allow 22/tcp   # SSH
ufw allow 80/tcp   # HTTP
ufw allow 443/tcp  # HTTPS
ufw enable

# Block direct access to sensitive files
# In nginx configuration
location ~ /\.env {
    deny all;
    return 404;
}

location ~ /storage/logs {
    deny all;
    return 404;
}
```

### 2. Application Security

**Rate Limiting:**
```php
// In RouteServiceProvider
RateLimiter::for('paddle', function (Request $request) {
    return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
});

RateLimiter::for('webhooks', function (Request $request) {
    return Limit::perMinute(1000)->by($request->ip());
});
```

**Input Validation:**
```php
// Strict validation for all Paddle endpoints
class PaddleCheckoutRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'plan_id' => 'required|integer|exists:pricing_plans,id|min:1',
            'billing_cycle' => 'required|string|in:month,year',
        ];
    }

    public function authorize(): bool
    {
        return $this->user() && !$this->user()->isPremium();
    }
}
```

---

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] Production environment variables configured
- [ ] Database backup created
- [ ] SSL certificates installed and valid
- [ ] Paddle dashboard configured for production
- [ ] Webhook endpoints tested
- [ ] Performance testing completed

### During Deployment
- [ ] Application deployed successfully
- [ ] Database migrations completed
- [ ] Cache cleared and rebuilt
- [ ] Services restarted
- [ ] Health checks passing

### Post-Deployment
- [ ] Webhook delivery confirmed
- [ ] Payment flow tested end-to-end
- [ ] Monitoring alerts configured
- [ ] Error tracking active
- [ ] Performance metrics baseline established
- [ ] Team notified of successful deployment

---

**Next:** Your Paddle payment gateway integration is now complete and production-ready! 🎉
