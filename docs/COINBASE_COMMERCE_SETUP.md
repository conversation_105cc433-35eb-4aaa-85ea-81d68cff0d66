# Coinbase Commerce Integration Setup

This document explains how to set up and configure Coinbase Commerce for cryptocurrency payments in the Mobile Parts Database application.

## Overview

The Coinbase Commerce integration allows users to pay for subscriptions using various cryptocurrencies including Bitcoin, Ethereum, USDC, DAI, Litecoin, and Bitcoin Cash. The integration uses Coinbase's Onchain Payment Protocol for secure, instant transactions with auto USDC settlement.

## Features

- **Multiple Cryptocurrencies**: Support for Bitcoin, Ethereum, USDC, DAI, Litecoin, Bitcoin Cash
- **Onchain Payment Protocol**: Auto USDC settlement to avoid volatility
- **Instant Confirmation**: Low-cost transactions on Base & Polygon networks
- **Professional UI**: Modern, responsive checkout interface
- **Development Debugging**: Comprehensive logging and error handling
- **Webhook Support**: Real-time payment status updates

## Configuration

### 1. Environment Variables

Add the following variables to your `.env` file:

```env
# Coinbase Commerce Configuration
COINBASE_COMMERCE_API_KEY=your_actual_api_key_here
COINBASE_COMMERCE_WEBHOOK_SECRET=your_webhook_secret_here
COINBASE_COMMERCE_DEBUG=true
COINBASE_COMMERCE_AUTO_ACTIVATE=true
COINBASE_COMMERCE_AUTO_USDC_SETTLEMENT=true
COINBASE_COMMERCE_INSTANT_CONFIRMATION=true
```

### 2. Getting API Credentials

1. **Create Coinbase Commerce Account**:
   - Visit [commerce.coinbase.com](https://commerce.coinbase.com)
   - Sign up or log in to your account

2. **Generate API Key**:
   - Go to Settings → API Keys
   - Click "Create an API Key"
   - Copy the generated API key
   - Replace `your_actual_api_key_here` in your `.env` file

3. **Set Up Webhook**:
   - Go to Settings → Webhook subscriptions
   - Add webhook URL: `https://yourdomain.com/webhooks/coinbase-commerce`
   - Copy the webhook secret
   - Replace `your_webhook_secret_here` in your `.env` file

### 3. Configure Pricing Plans

Ensure your pricing plans have crypto payments enabled:

```php
// In your database seeder or admin panel
$plan = PricingPlan::find(1);
$plan->crypto_payment_enabled = true;
$plan->coinbase_commerce_product_id = 'your_product_id'; // Optional
$plan->save();
```

## Development Setup

### 1. Using Placeholder Credentials

For development, you can use placeholder credentials, but the integration will show appropriate error messages:

```env
COINBASE_COMMERCE_API_KEY=test_coinbase_commerce_api_key_placeholder
COINBASE_COMMERCE_WEBHOOK_SECRET=test_coinbase_commerce_webhook_secret_placeholder
```

### 2. Error Handling

The system provides helpful error messages in development:

- **Configuration Missing**: "Coinbase Commerce is not configured. Please set COINBASE_COMMERCE_API_KEY in your .env file."
- **Plan Not Supported**: "Cryptocurrency payments are not available for this plan."
- **Service Unavailable**: "Service temporarily unavailable. Please try again later or use another payment method."

### 3. Debug Logging

Enable debug logging to see detailed information:

```env
COINBASE_COMMERCE_DEBUG=true
```

This will log:
- Charge creation requests
- API responses
- Webhook events
- Error details

## Testing

### 1. Frontend Integration

The Coinbase Commerce checkout appears as a "Pay with Crypto" option in the payment method selector for plans that have crypto payments enabled.

### 2. Backend Testing

Test the API endpoints directly:

```bash
# Test charge creation (requires authentication)
curl -X POST http://localhost:8000/coinbase-commerce/charge \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: your_csrf_token" \
  -d '{"pricing_plan_id": 2, "billing_cycle": "month"}'
```

### 3. Webhook Testing

Test webhook handling:

```bash
# Test webhook endpoint
curl -X POST http://localhost:8000/webhooks/coinbase-commerce \
  -H "Content-Type: application/json" \
  -H "X-CC-Webhook-Signature: your_signature" \
  -d '{"event": {"type": "charge:confirmed"}}'
```

## Production Deployment

### 1. Security Considerations

- Use HTTPS for all webhook URLs
- Validate webhook signatures
- Store API keys securely
- Enable rate limiting

### 2. Monitoring

Monitor the following:
- Payment success rates
- Webhook delivery status
- Error rates
- Transaction confirmations

### 3. Backup Payment Methods

Always provide alternative payment methods (Paddle, ShurjoPay) in case Coinbase Commerce is unavailable.

## Troubleshooting

### Common Issues

1. **"No such API key" Error**:
   - Verify API key is correct
   - Check if API key has proper permissions
   - Ensure no extra spaces in environment variable

2. **Webhook Signature Validation Failed**:
   - Verify webhook secret is correct
   - Check webhook URL is accessible
   - Ensure proper HTTPS configuration

3. **Crypto Payments Not Showing**:
   - Verify `crypto_payment_enabled` is true for the plan
   - Check if Coinbase Commerce is configured
   - Ensure plan has proper integration settings

### Debug Steps

1. Check Laravel logs: `tail -f storage/logs/laravel.log`
2. Verify environment variables: `php artisan config:show`
3. Test API connectivity: Use Coinbase Commerce API directly
4. Check webhook delivery in Coinbase Commerce dashboard

## Support

For additional support:
- Coinbase Commerce Documentation: [docs.cloud.coinbase.com](https://docs.cloud.coinbase.com)
- Coinbase Commerce Support: [help.coinbase.com](https://help.coinbase.com)
- Application Issues: Check application logs and error messages

## Integration Files

Key files in the integration:

- **Backend**:
  - `app/Services/CoinbaseCommerceService.php` - Main service class
  - `app/Http/Controllers/CoinbaseCommerceController.php` - API endpoints
  - `app/Models/CoinbaseCommerceTransaction.php` - Transaction model
  - `config/coinbase_commerce.php` - Configuration file

- **Frontend**:
  - `resources/js/components/CoinbaseCommerceCheckout.tsx` - Checkout component
  - `resources/js/components/PaymentMethodSelector.tsx` - Payment method selection
  - `resources/js/utils/checkout-helpers.ts` - Utility functions
  - `resources/js/types/index.d.ts` - TypeScript definitions
