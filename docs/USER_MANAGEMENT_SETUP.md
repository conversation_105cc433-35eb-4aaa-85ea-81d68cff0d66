# User Management System Setup Guide

## 🚀 Quick Setup

### 1. Run Database Migrations
```bash
php artisan migrate
```

### 2. Run Setup Script (Recommended)
```bash
php setup-user-management.php
```

This script will:
- ✅ Run all necessary migrations
- ✅ Create an admin user
- ✅ Create sample test data
- ✅ Verify the setup

### 3. Manual Setup (Alternative)

If you prefer manual setup:

#### Create Admin User
```bash
php artisan tinker
```

Then run:
```php
$admin = App\Models\User::create([
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'email_verified_at' => now(),
    'subscription_plan' => 'premium',
    'status' => 'active',
    'approval_status' => 'approved',
]);
```

#### Create Test User
```php
$testUser = App\Models\User::create([
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'email_verified_at' => now(),
    'subscription_plan' => 'free',
    'status' => 'pending',
    'approval_status' => 'pending',
]);
```

#### Create Sample Payment Request
```php
$payment = App\Models\PaymentRequest::create([
    'user_id' => $testUser->id,
    'amount' => 99.99,
    'currency' => 'USD',
    'payment_method' => 'bank_transfer',
    'subscription_plan' => 'premium',
    'status' => 'pending',
    'notes' => 'Sample payment request for testing',
    'requested_at' => now(),
]);
```

## 🔑 Login Credentials

After setup, you can login with:

- **Admin**: `<EMAIL>` / `password`
- **Test User**: `<EMAIL>` / `password`

## 🌐 Access Points

### Admin Dashboard
- **URL**: `/admin/dashboard`
- **Description**: Main admin dashboard with overview

### User Management
- **URL**: `/admin/users`
- **Features**:
  - View all users
  - Filter and search users
  - Approve/suspend users
  - User detail pages
  - Impersonation

### Payment Requests
- **URL**: `/admin/payment-requests`
- **Features**:
  - View all payment requests
  - Filter by status, payment method
  - Approve/reject payments
  - Bulk operations
  - Payment detail pages

## 🧪 Testing the System

### 1. Test User Management
1. Login as admin
2. Go to `/admin/users`
3. See the test user in "pending" status
4. Click on the test user to view details
5. Approve the user
6. Test impersonation feature

### 2. Test Payment Management
1. Go to `/admin/payment-requests`
2. See the sample payment request
3. Click to view payment details
4. Approve the payment request
5. Verify user subscription is updated

### 3. Test Impersonation
1. From user detail page, click "Login as User"
2. See the orange impersonation banner
3. Experience the site as the user
4. Click "Return to Admin" to go back

## 🔧 Troubleshooting

### Database Issues
If you see "Column not found" errors:
```bash
php artisan migrate:fresh
php setup-user-management.php
```

### Permission Issues
Make sure your web server has write permissions to:
- `storage/` directory
- `bootstrap/cache/` directory

### Cache Issues
Clear all caches:
```bash
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear
```

## 📋 Features Overview

### ✅ Completed Features
- **User Management**: Complete CRUD with approval workflow
- **Payment Requests**: Full payment processing system
- **Impersonation**: Secure user impersonation with audit trails
- **Activity Logging**: Complete audit trails for all actions
- **Statistics Dashboard**: Real-time metrics and insights
- **Advanced Filtering**: Search and filter across all data
- **Responsive Design**: Works on all devices
- **Security**: Proper authorization and validation

### 🎯 Key Capabilities
- **User Approval Workflow**: Pending → Approved → Active
- **Subscription Management**: Free ↔ Premium transitions
- **Payment Processing**: Offline payment approval system
- **Bulk Operations**: Process multiple items at once
- **Audit Trails**: Complete history of all admin actions
- **Real-time Updates**: Live status updates and notifications

## 🚀 Production Deployment

### Environment Setup
1. Set proper environment variables
2. Configure database connections
3. Set up email for notifications
4. Configure file storage for payment proofs

### Security Checklist
- [ ] Change default admin password
- [ ] Set up proper SSL certificates
- [ ] Configure rate limiting
- [ ] Set up backup procedures
- [ ] Enable audit logging

The User Management System is now ready for production use! 🎉
