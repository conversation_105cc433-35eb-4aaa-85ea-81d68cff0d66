# Admin Rate Limiting Guide

## Overview

The Mobile Parts DB admin rate limiting system provides comprehensive protection against abuse of administrative actions while maintaining usability for legitimate admin operations. This guide covers configuration, monitoring, and best practices.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Configuration](#configuration)
3. [Admin Dashboard](#admin-dashboard)
4. [Rate Limit Types](#rate-limit-types)
5. [Monitoring & Analytics](#monitoring--analytics)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)
8. [API Reference](#api-reference)

## System Architecture

### Components

- **AdminRateLimit Middleware**: Core rate limiting logic
- **RateLimitController**: Admin interface for configuration
- **Cache-based Storage**: Redis/file-based rate limit tracking
- **Activity Logging**: Integration with UserActivityLog system
- **Real-time Monitoring**: Live status updates and violation tracking

### How It Works

1. **Request Interception**: Middleware intercepts admin actions
2. **Rate Limit Check**: Validates against configured limits
3. **Action Logging**: Records all admin actions for audit
4. **Violation Handling**: Blocks requests and logs violations
5. **Real-time Updates**: Updates dashboard with current status

## Configuration

### Accessing Rate Limiting Settings

Navigate to **Admin Dashboard → Rate Limiting** (`/admin/rate-limit`)

### Global Settings

#### Enable/Disable Rate Limiting

```
Toggle: Rate Limiting [ON/OFF]
```

- **Enabled**: All configured rate limits are active
- **Disabled**: Rate limiting is bypassed (not recommended for production)

### Action-Specific Configuration

Each admin action type has individual rate limit settings:

#### Default Configuration

| Action Type | Max Attempts | Window | Description |
|-------------|--------------|---------|-------------|
| **General Admin Actions** | 60 | 60 seconds | Basic admin operations |
| **User Management** | 30 | 60 seconds | User approval, suspension, etc. |
| **Payment Approval** | 20 | 60 seconds | Payment request approvals |
| **Bulk Operations** | 10 | 5 minutes | Bulk approve, bulk import, etc. |
| **Impersonation** | 5 | 5 minutes | User impersonation actions |
| **Data Export** | 10 | 5 minutes | Data export operations |
| **Email Configuration** | 10 | 60 seconds | Email settings changes |
| **System Configuration** | 15 | 5 minutes | System settings changes |

#### Customizing Limits

1. **Access Settings Tab** in Rate Limiting dashboard
2. **Modify Values** for each action type:
   - **Max Attempts**: Number of allowed actions
   - **Window (seconds)**: Time period for the limit
   - **Description**: Human-readable description
3. **Save Configuration** to apply changes
4. **Reset to Defaults** if needed

## Admin Dashboard

### Overview Tab

Displays system-wide rate limiting status:

- **Current Status**: Enabled/Disabled with visual indicator
- **Violations (7 days)**: Total rate limit violations
- **Protected Actions**: Number of different action types
- **Top Violators**: Users with most violations

### Settings Tab

Configure rate limits for each action type:

- **Action-specific settings** with real-time validation
- **Bulk configuration** updates
- **Reset to defaults** functionality
- **Configuration validation** and error handling

### Current Status Tab

Shows your personal rate limit status:

- **Real-time remaining attempts** for each action
- **Status indicators**: OK, Warning, Limited
- **Retry timers** for limited actions
- **Visual status badges** for quick assessment

### Statistics Tab

Comprehensive violation analytics:

- **Violations by Action**: Which actions are most limited
- **Top Violating Users**: Users exceeding limits most often
- **Recent Violations**: Timeline of recent limit breaches
- **Trend Analysis**: 7-day violation patterns

## Rate Limit Types

### 1. User Management Actions

**Applied to:**
- User approval (`/admin/users/{user}/approve`)
- User rejection (`/admin/users/{user}/reject`)
- User suspension (`/admin/users/{user}/suspend`)
- User unsuspension (`/admin/users/{user}/unsuspend`)

**Default Limit:** 30 attempts per 60 seconds

### 2. Payment Approval Actions

**Applied to:**
- Payment approval (`/admin/payment-requests/{payment}/approve`)
- Payment rejection (`/admin/payment-requests/{payment}/reject`)

**Default Limit:** 20 attempts per 60 seconds

### 3. Bulk Operations

**Applied to:**
- Bulk user approval (`/admin/users/bulk-approve`)
- Bulk payment approval (`/admin/payment-requests/bulk-approve`)
- Bulk import operations

**Default Limit:** 10 attempts per 5 minutes

### 4. Impersonation Actions

**Applied to:**
- Start impersonation (`/admin/impersonate/{user}`)

**Default Limit:** 5 attempts per 5 minutes

### 5. System Configuration

**Applied to:**
- Rate limit configuration changes
- Email configuration updates
- System settings modifications

**Default Limit:** 15 attempts per 5 minutes

## Monitoring & Analytics

### Real-time Monitoring

#### Current Status Indicators

- 🟢 **OK**: Plenty of attempts remaining
- 🟡 **Warning**: Low attempts remaining (≤5)
- 🔴 **Limited**: Rate limit exceeded
- ⚪ **Disabled**: Rate limiting not active

#### Violation Tracking

All rate limit violations are automatically logged with:

- **User Information**: ID, name, email
- **Action Details**: Type, route, method
- **Network Information**: IP address, user agent
- **Timestamp**: Exact time of violation
- **Context**: Additional metadata

### Analytics Dashboard

#### Violation Statistics

- **Total violations** over configurable periods
- **Violations by action type** breakdown
- **Top violating users** with counts
- **Recent violations** timeline

#### Export Capabilities

- **CSV Export**: All violation data
- **Filtered Reports**: By date range, user, or action
- **Scheduled Reports**: Automated violation summaries

### Activity Integration

Rate limiting integrates with the existing UserActivityLog system:

- **All admin actions** are logged with rate limit context
- **Violation events** create dedicated log entries
- **Audit trail** maintains complete action history
- **Search and filter** capabilities in activity logs

## Troubleshooting

### Common Issues

#### 1. Rate Limit Exceeded

**Symptoms:**
- HTTP 429 responses
- "Too many requests" error messages
- Blocked admin actions

**Solutions:**
1. **Wait for window to reset** (check retry timer)
2. **Contact super admin** to clear your limits
3. **Review action frequency** and adjust workflow

#### 2. Configuration Not Applying

**Symptoms:**
- Changes not taking effect
- Old limits still active

**Solutions:**
1. **Clear application cache**: `php artisan cache:clear`
2. **Verify configuration save** in admin dashboard
3. **Check for validation errors** in settings

#### 3. False Positives

**Symptoms:**
- Legitimate actions being blocked
- Limits too restrictive for normal use

**Solutions:**
1. **Increase limits** for affected action types
2. **Extend time windows** for better distribution
3. **Review usage patterns** in analytics

### Emergency Procedures

#### Disable Rate Limiting

If rate limiting is causing critical issues:

1. **Admin Dashboard**: Toggle rate limiting OFF
2. **Direct Database**: Update cache key `admin_rate_limiting_enabled` to `false`
3. **Code Override**: Temporarily modify middleware

#### Clear All Rate Limits

To reset all rate limits for all users:

```php
// In tinker or custom command
Cache::flush(); // Clears all rate limit data
```

#### Clear Specific User Limits

```php
// Via admin dashboard or API
AdminRateLimit::clearUserRateLimits($userId);
```

## Best Practices

### Configuration Guidelines

1. **Start Conservative**: Begin with lower limits and increase as needed
2. **Monitor Regularly**: Review violation patterns weekly
3. **Adjust Seasonally**: Account for busy periods or maintenance windows
4. **Document Changes**: Keep track of configuration modifications

### Security Recommendations

1. **Enable Logging**: Ensure all violations are logged
2. **Regular Audits**: Review violation patterns for suspicious activity
3. **Alert Thresholds**: Set up monitoring for excessive violations
4. **Access Control**: Limit who can modify rate limiting settings

### Performance Optimization

1. **Use Redis**: For high-traffic environments, use Redis for rate limit storage
2. **Monitor Cache**: Ensure rate limit cache is performing well
3. **Cleanup Old Data**: Regularly clean up old violation logs
4. **Load Testing**: Test rate limits under expected load

### User Experience

1. **Clear Messaging**: Provide helpful error messages when limits are hit
2. **Reasonable Limits**: Don't make limits so strict they hinder productivity
3. **Status Visibility**: Let admins see their current rate limit status
4. **Grace Periods**: Consider implementing grace periods for critical actions

## API Reference

### Middleware Usage

```php
// Apply to specific routes
Route::post('/admin/action', [Controller::class, 'method'])
    ->middleware('admin.rate_limit:action_type');

// Available action types:
// - default
// - user_management
// - payment_approval
// - bulk_operations
// - impersonation
// - data_export
// - email_config
// - system_config
```

### Programmatic Access

```php
// Check rate limit status
$status = AdminRateLimit::getRateLimitStatus($userId, $action, $ip);

// Clear user rate limits
AdminRateLimit::clearUserRateLimits($userId);

// Get current configuration
$configs = Cache::get('admin_rate_limit_configs');

// Check if rate limiting is enabled
$enabled = Cache::get('admin_rate_limiting_enabled', true);
```

### Configuration Structure

```php
[
    'action_type' => [
        'max_attempts' => 30,
        'decay_seconds' => 60,
        'description' => 'Action description'
    ]
]
```

### Response Format (429 Error)

```json
{
    "message": "Too many requests. Please slow down.",
    "retry_after": 45,
    "action": "user_management",
    "max_attempts": 30,
    "window_seconds": 60
}
```

## Support

For additional support with rate limiting:

1. **Check Logs**: Review application logs for detailed error information
2. **Analytics Dashboard**: Use built-in analytics for troubleshooting
3. **Documentation**: Refer to this guide and inline help text
4. **System Admin**: Contact your system administrator for configuration changes

---

*Last Updated: December 2024*
*Version: 1.0*
