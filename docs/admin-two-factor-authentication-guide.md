# Admin Two-Factor Authentication Guide

## Overview

The Mobile Parts DB admin two-factor authentication (2FA) system provides email-based verification for sensitive administrative actions. This guide covers setup, configuration, and usage of the 2FA system.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Getting Started](#getting-started)
3. [Admin Dashboard](#admin-dashboard)
4. [Protected Actions](#protected-actions)
5. [Email OTP Process](#email-otp-process)
6. [Security Features](#security-features)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)
9. [API Reference](#api-reference)

## System Architecture

### Components

- **OtpService**: Core service for OTP generation, verification, and session management
- **RequireOtpVerification Middleware**: Protects admin routes with 2FA requirements
- **TwoFactorController**: Admin interface for 2FA management
- **Email System**: Sends verification codes via configured email provider
- **Session Management**: Tracks verified actions to avoid repeated verification

### How It Works

1. **Action Trigger**: Admin attempts a protected action
2. **2FA Check**: Middleware checks if 2FA is required and if user has valid session
3. **OTP Generation**: System generates 6-digit code and sends via email
4. **User Verification**: Admin enters code in interface
5. **Session Creation**: Successful verification creates temporary session (30 minutes)
6. **Action Execution**: Original action proceeds with verified session

## Getting Started

### Enabling 2FA for Your Account

1. **Navigate to 2FA Settings**: Go to Admin Dashboard → Two-Factor Auth
2. **Enable 2FA**: Click "Enable Two-Factor Authentication"
3. **Test Setup**: Use the "Test" tab to verify email delivery
4. **Confirm Working**: Ensure you receive and can verify test codes

### Global 2FA Settings

Administrators can control system-wide 2FA requirements:

- **Required**: All admins must use 2FA for protected actions
- **Optional**: Individual admins can choose to enable 2FA
- **Disabled**: 2FA is not enforced (not recommended for production)

## Admin Dashboard

### Settings Tab

#### Personal 2FA Settings
- **Enable/Disable**: Toggle 2FA for your account
- **Status Display**: Current 2FA status with visual indicators
- **Disable Confirmation**: Requires typing "DISABLE_2FA" to confirm

#### Global Settings
- **System-wide Toggle**: Enable/disable 2FA requirement for all admins
- **Configuration Display**: Shows current OTP settings
  - Code expiry: 10 minutes
  - Max attempts: 3 attempts
  - Lockout duration: 30 minutes

### Test Tab

#### Email Verification Testing
- **Send Test Code**: Generates and sends test OTP to your email
- **Code Verification**: Interface to test OTP verification process
- **Status Feedback**: Real-time feedback on test results

### Sessions Tab

#### Active Verification Sessions
- **Session List**: Shows actions you've recently verified
- **Expiry Times**: When each session expires
- **Clear Sessions**: Manually clear all verification sessions

### Activity Tab

#### Verification History
- **Recent Attempts**: Last 10 verification attempts
- **Success/Failure Status**: Visual indicators for each attempt
- **Timestamp and IP**: When and where verification occurred

## Protected Actions

### User Management (Action: `user_management`)
- User approval/rejection
- User suspension/unsuspension
- User account modifications

### Payment Approval (Action: `payment_approval`)
- Payment request approval/rejection
- Payment processing actions

### Bulk Operations (Action: `bulk_operations`)
- Bulk user approval
- Bulk payment approval
- Mass data operations

### Impersonation (Action: `impersonation`)
- Starting user impersonation sessions
- Accessing user accounts as admin

### System Configuration (Action: `system_config`)
- Email configuration changes
- Rate limiting settings
- Global 2FA settings
- Other system-wide settings

## Email OTP Process

### OTP Generation
- **6-digit numeric code**: Easy to read and type
- **10-minute expiry**: Balances security and usability
- **Secure generation**: Cryptographically secure random numbers

### Email Delivery
- **HTML and Text versions**: Compatible with all email clients
- **Clear formatting**: Easy-to-read code presentation
- **Security information**: Includes IP address and timestamp
- **Action context**: Shows which admin action triggered the code

### Verification Process
- **Real-time validation**: Immediate feedback on code entry
- **Attempt tracking**: Counts failed attempts toward lockout
- **Session creation**: Creates 30-minute verification session on success

## Security Features

### Lockout Protection
- **3 failed attempts**: Triggers temporary account lockout
- **30-minute lockout**: Prevents brute force attacks
- **Progressive delays**: Increasing delays for repeated failures

### Session Management
- **30-minute sessions**: Verified actions remain valid for 30 minutes
- **Action-specific**: Each action type has separate verification
- **Automatic expiry**: Sessions expire automatically for security

### Audit Logging
- **Complete audit trail**: All 2FA events logged to UserActivityLog
- **Success and failure tracking**: Both outcomes recorded
- **IP and timestamp**: Full context for security analysis
- **Action correlation**: Links verification to specific admin actions

### Rate Limiting Integration
- **Combined protection**: Works with existing rate limiting system
- **Layered security**: Multiple protection mechanisms
- **Coordinated logging**: Unified audit trail

## Troubleshooting

### Common Issues

#### 1. Not Receiving OTP Emails

**Symptoms:**
- No verification code email received
- Email takes too long to arrive

**Solutions:**
1. **Check spam folder**: OTP emails may be filtered
2. **Verify email configuration**: Ensure SMTP/SendGrid is working
3. **Test email system**: Use Email Config → Test Email feature
4. **Check email address**: Verify your account email is correct

#### 2. Invalid Verification Code

**Symptoms:**
- "Invalid verification code" error
- Code doesn't work despite being correct

**Solutions:**
1. **Check code expiry**: Codes expire after 10 minutes
2. **Verify code accuracy**: Ensure all 6 digits are correct
3. **Request new code**: Use "Resend" if code is expired
4. **Check for typos**: Common mistake with similar-looking digits

#### 3. Account Lockout

**Symptoms:**
- "Account temporarily locked" message
- Cannot request new OTP codes

**Solutions:**
1. **Wait for lockout expiry**: 30-minute automatic unlock
2. **Contact administrator**: For emergency access
3. **Check attempt count**: Review recent verification attempts

#### 4. 2FA Not Required

**Symptoms:**
- Admin actions work without 2FA
- No OTP prompt appears

**Solutions:**
1. **Check global settings**: Verify 2FA is enabled system-wide
2. **Verify personal settings**: Ensure your account has 2FA enabled
3. **Clear browser cache**: Refresh application state
4. **Check action protection**: Verify action requires 2FA

### Emergency Procedures

#### Disable 2FA Globally

If 2FA is causing critical issues:

1. **Admin Dashboard**: Navigate to Two-Factor Auth → Settings
2. **Toggle Global Setting**: Disable "Require 2FA for Admin Actions"
3. **Confirm Change**: System will log the change

#### Clear User Sessions

To reset all verification sessions:

1. **Sessions Tab**: Go to Two-Factor Auth → Sessions
2. **Clear All Sessions**: Click "Clear All Sessions"
3. **Confirm Action**: Will require re-verification for all actions

## Best Practices

### For Administrators

1. **Enable 2FA Early**: Set up 2FA as soon as you have admin access
2. **Test Regularly**: Periodically test OTP delivery and verification
3. **Monitor Activity**: Review verification logs for suspicious activity
4. **Keep Email Secure**: Ensure your admin email account is secure
5. **Use Strong Passwords**: Combine 2FA with strong account passwords

### For System Administrators

1. **Enable Globally**: Require 2FA for all admin accounts
2. **Monitor Logs**: Regularly review 2FA activity logs
3. **Email Reliability**: Ensure email system is reliable and fast
4. **Backup Access**: Have emergency access procedures
5. **User Training**: Train admins on 2FA usage and troubleshooting

### Security Recommendations

1. **Regular Audits**: Review 2FA logs and settings monthly
2. **Email Security**: Use secure email providers and encryption
3. **Session Monitoring**: Monitor active verification sessions
4. **Incident Response**: Have procedures for 2FA-related security incidents
5. **Backup Procedures**: Maintain emergency access methods

## API Reference

### Middleware Usage

```php
// Apply 2FA to specific routes
Route::post('/admin/action', [Controller::class, 'method'])
    ->middleware('admin.2fa:action_type');

// Available action types:
// - user_management
// - payment_approval
// - bulk_operations
// - impersonation
// - system_config
```

### OTP Service Methods

```php
// Generate and send OTP
$otpService->generateAndSendOtp($user, $action);

// Verify OTP
$verified = $otpService->verifyOtp($user, $otpCode, $action);

// Check session
$hasSession = $otpService->hasValidOtpSession($user, $action);

// Create session
$otpService->createOtpSession($user, $action, $durationMinutes);

// Get status
$status = $otpService->getOtpStatus($user);
```

### API Endpoints

```
GET    /admin/two-factor              # 2FA dashboard
POST   /admin/two-factor/enable       # Enable 2FA
POST   /admin/two-factor/disable      # Disable 2FA
POST   /admin/two-factor/send-test    # Send test OTP
POST   /admin/two-factor/verify-test  # Verify test OTP
POST   /admin/two-factor/toggle-global # Toggle global 2FA
POST   /admin/two-factor/clear-sessions # Clear sessions
GET    /admin/two-factor/status       # Get 2FA status
```

### Response Formats

#### OTP Required Response (403)
```json
{
    "error": "Two-factor authentication required",
    "message": "Please check your email for a verification code.",
    "requires_otp": true,
    "action": "user_management",
    "otp_status": {
        "has_otp_pending": true,
        "remaining_attempts": 3,
        "is_locked_out": false
    }
}
```

#### Invalid OTP Response (422)
```json
{
    "error": "Invalid verification code",
    "message": "The verification code you entered is incorrect.",
    "remaining_attempts": 2,
    "requires_otp": true,
    "action": "user_management"
}
```

#### Lockout Response (429)
```json
{
    "error": "Account temporarily locked",
    "message": "Too many failed attempts. Try again in 25 minutes.",
    "lockout_remaining_minutes": 25,
    "requires_otp": true,
    "action": "user_management"
}
```

## Support

For additional support with two-factor authentication:

1. **Test Email System**: Use built-in email testing tools
2. **Check Activity Logs**: Review verification attempts in dashboard
3. **Documentation**: Refer to this guide and inline help
4. **System Administrator**: Contact your system administrator for configuration issues

---

*Last Updated: December 2024*
*Version: 1.0*
