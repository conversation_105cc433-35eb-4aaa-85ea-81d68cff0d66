# Mobile Parts Database - Testing Guide

## 🧪 Testing Overview

This guide provides comprehensive instructions for testing the Mobile Parts Database application, including manual testing scenarios and automated test execution.

---

## 🚀 Quick Start Testing

### 1. Setup Test Environment
```bash
# Copy environment file for testing
cp .env.example .env.testing

# Configure test database
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Run migrations and seed test data
php artisan migrate --env=testing
php artisan db:seed --env=testing
```

### 2. Run Automated Tests
```bash
# Run all tests
./scripts/run-tests.sh

# Run specific test suites
php artisan test --testsuite=Unit
php artisan test --testsuite=Feature

# Run with coverage
php artisan test --coverage
```

---

## 👥 Test User Accounts

### Admin Accounts
| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| Super Admin | `<EMAIL>` | `admin123` | Full admin access |
| Content Manager | `<EMAIL>` | `content123` | Content management |

### User Accounts
| Type | Email | Password | Search Count | Status |
|------|-------|----------|--------------|--------|
| Premium User | `<EMAIL>` | `password` | 45 | Active subscription |
| Free User | `<EMAIL>` | `password` | 15 | Within limit |
| Free Near Limit | `<EMAIL>` | `password` | 19 | Near daily limit |
| Free Over Limit | `<EMAIL>` | `password` | 25 | Exceeded limit |

---

## 🔍 Manual Testing Scenarios

### Authentication & User Management

#### Test Case 1: User Registration
1. Navigate to `/register`
2. Fill in registration form
3. Verify email verification process
4. Check default free plan assignment
5. Verify daily search limit initialization

#### Test Case 2: Login & Session Management
1. Test login with valid credentials
2. Test login with invalid credentials
3. Test "Remember Me" functionality
4. Test logout functionality
5. Test session timeout

#### Test Case 3: Password Reset
1. Navigate to password reset
2. Enter email address
3. Check email for reset link
4. Reset password with new credentials
5. Login with new password

### Search Functionality

#### Test Case 4: Basic Search
1. Login as any user
2. Enter search query: "iPhone 15 display"
3. Verify search results appear
4. Check search count increment
5. Test different search types (all, category, model, part)

#### Test Case 5: Advanced Filtering
1. Perform search with filters:
   - Category: "Display"
   - Brand: "Apple"
   - Release Year: "2023"
2. Verify filtered results
3. Test filter combinations
4. Test filter reset functionality

#### Test Case 6: Search Suggestions
1. Start typing in search box
2. Verify autocomplete suggestions appear
3. Test suggestion selection
4. Verify suggestion accuracy

#### Test Case 7: Search Limits
1. Login as `<EMAIL>`
2. Perform one search (should work)
3. Try another search (should be blocked)
4. Verify upgrade prompt appears
5. Login as `<EMAIL>`
6. Verify unlimited searches work

### Part Details & Related Parts

#### Test Case 8: Part Details Page
1. Search for any part
2. Click on a part from results
3. Verify comprehensive part information
4. Check image gallery functionality
5. Verify compatibility information
6. Test related parts suggestions

#### Test Case 9: Part Compatibility
1. View part details
2. Check compatible models list
3. Verify compatibility notes
4. Test compatibility search

### Subscription Management

#### Test Case 10: Plan Comparison
1. Navigate to `/subscription/plans`
2. Verify plan features comparison
3. Test upgrade button (should redirect to Stripe - not implemented yet)
4. Check current plan highlighting

#### Test Case 11: Subscription Dashboard
1. Login as premium user
2. Navigate to subscription dashboard
3. Verify subscription status
4. Check usage statistics
5. Test plan management options

### Admin Panel Testing

#### Test Case 12: Admin Dashboard
1. Login as admin user
2. Navigate to `/admin/dashboard`
3. Verify statistics display correctly
4. Check recent activity feed
5. Test quick action buttons

#### Test Case 13: Category Management
1. Navigate to `/admin/categories`
2. Test category creation
3. Test hierarchical category structure
4. Test category editing
5. Test category deletion (with/without parts)
6. Test bulk operations

#### Test Case 14: Brand Management
1. Navigate to `/admin/brands`
2. Test brand creation with image upload
3. Test brand editing
4. Test brand activation/deactivation
5. Test brand deletion

#### Test Case 15: Model Management
1. Navigate to `/admin/models`
2. Test model creation with brand association
3. Test specifications JSON editing
4. Test model editing
5. Test model deletion

#### Test Case 16: Parts Management
1. Navigate to `/admin/parts`
2. Test part creation with:
   - Category association
   - Model compatibility
   - Image gallery
   - Specifications
3. Test part editing
4. Test part deletion
5. Test bulk operations

#### Test Case 17: Bulk Import
1. Navigate to bulk import section
2. Download CSV templates
3. Test CSV import for each entity type
4. Verify error handling for invalid data
5. Check progress tracking

### Security Testing

#### Test Case 18: Content Protection
1. Open application on mobile device
2. Test screenshot prevention
3. Test right-click disable
4. Test text selection prevention
5. Verify developer tools detection

#### Test Case 19: Rate Limiting
1. Make rapid API requests
2. Verify rate limiting kicks in
3. Check error messages
4. Test rate limit reset

#### Test Case 20: Access Control
1. Try accessing admin routes as regular user
2. Try accessing user routes without authentication
3. Test CSRF protection
4. Verify proper error handling

### Performance Testing

#### Test Case 21: Search Performance
1. Perform complex searches with multiple filters
2. Measure response times
3. Test with large result sets
4. Verify pagination performance

#### Test Case 22: Cache Performance
1. Perform same search multiple times
2. Verify cache hit improvements
3. Test cache invalidation
4. Check cache warming

---

## 🤖 Automated Test Coverage

### Unit Tests
- **SearchService**: Search logic, filtering, suggestions
- **SubscriptionService**: Plan management, limits, billing
- **CacheService**: Cache operations, performance
- **SecurityService**: Security features, validation

### Feature Tests
- **SearchController**: Search endpoints, filtering
- **Admin Controllers**: CRUD operations, validation
- **Authentication**: Login, registration, password reset
- **Subscription**: Plan management, upgrades

### Integration Tests
- **Database**: Model relationships, constraints
- **API**: Endpoint responses, error handling
- **Cache**: Cache integration, performance
- **Security**: Middleware, protection

---

## 📊 Performance Benchmarks

### Expected Performance Metrics
- **Search Response Time**: < 200ms
- **Page Load Time**: < 2 seconds
- **Database Query Time**: < 50ms
- **Cache Hit Ratio**: > 80%

### Load Testing
```bash
# Install Apache Bench for load testing
apt-get install apache2-utils

# Test search endpoint
ab -n 1000 -c 10 http://localhost:8000/search/results?q=iPhone

# Test part details
ab -n 500 -c 5 http://localhost:8000/parts/1
```

---

## 🐛 Common Issues & Troubleshooting

### Database Issues
- **Migration Errors**: Check database permissions
- **Seeding Failures**: Verify foreign key constraints
- **Connection Issues**: Check database configuration

### Search Issues
- **No Results**: Check database seeding
- **Slow Searches**: Verify database indexing
- **Cache Issues**: Clear and warm caches

### Authentication Issues
- **Login Failures**: Check user seeding
- **Session Issues**: Verify session configuration
- **Email Issues**: Check mail configuration

### Performance Issues
- **Slow Responses**: Check query optimization
- **Memory Issues**: Verify cache configuration
- **Asset Loading**: Check asset compilation

---

## ✅ Testing Checklist

### Pre-Deployment Testing
- [ ] All automated tests pass
- [ ] Manual test scenarios completed
- [ ] Performance benchmarks met
- [ ] Security tests passed
- [ ] Cross-browser compatibility verified
- [ ] Mobile responsiveness tested
- [ ] Error handling verified
- [ ] Cache performance optimized

### Post-Deployment Testing
- [ ] Production environment health check
- [ ] Database connectivity verified
- [ ] Search functionality working
- [ ] Admin panel accessible
- [ ] User registration/login working
- [ ] Performance monitoring active
- [ ] Error logging functional
- [ ] Backup systems operational

---

## 📝 Test Reporting

### Test Results Documentation
- Record test execution results
- Document any issues found
- Track performance metrics
- Note browser/device compatibility
- Report security test outcomes

### Issue Tracking
- Use GitHub Issues for bug tracking
- Categorize issues by severity
- Assign priority levels
- Track resolution progress
- Document fixes and workarounds

---

## 🎯 Continuous Testing

### Automated Testing Pipeline
- Run tests on every commit
- Performance regression testing
- Security vulnerability scanning
- Dependency update testing
- Cross-platform compatibility testing

### Monitoring & Alerts
- Set up application monitoring
- Configure performance alerts
- Monitor error rates
- Track user behavior
- Monitor system resources

---

**Happy Testing! 🧪✨**
