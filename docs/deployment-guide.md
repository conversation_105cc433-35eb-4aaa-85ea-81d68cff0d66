# Mobile Parts Database - Deployment Guide

## 🚀 Production Deployment Guide

This guide covers the complete deployment process for the Mobile Parts Database application to a production environment.

---

## 📋 Pre-Deployment Checklist

### System Requirements
- [ ] **Server**: Ubuntu 20.04+ or CentOS 8+
- [ ] **PHP**: 8.2 or higher
- [ ] **Node.js**: 18.0 or higher
- [ ] **MySQL**: 8.0 or higher
- [ ] **Redis**: 6.0+ (recommended for caching)
- [ ] **Nginx**: 1.18+ or Apache 2.4+
- [ ] **SSL Certificate**: Valid SSL certificate
- [ ] **Domain**: Configured domain name

### Required Software
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.2 and extensions
sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-redis \
    php8.2-mbstring php8.2-xml php8.2-curl php8.2-zip \
    php8.2-gd php8.2-intl php8.2-bcmath

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs

# Install MySQL 8.0
sudo apt install mysql-server-8.0

# Install Redis
sudo apt install redis-server

# Install Nginx
sudo apt install nginx

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

---

## 🔧 Server Configuration

### 1. MySQL Configuration
```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE mobile_parts_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'mobileparts'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON mobile_parts_db.* TO 'mobileparts'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. Redis Configuration
```bash
# Configure Redis
sudo nano /etc/redis/redis.conf

# Set password (uncomment and modify)
requirepass your_redis_password_here

# Restart Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

### 3. PHP-FPM Configuration
```bash
# Configure PHP-FPM pool
sudo nano /etc/php/8.2/fpm/pool.d/www.conf

# Key settings to modify:
user = www-data
group = www-data
listen = /run/php/php8.2-fpm.sock
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35

# Restart PHP-FPM
sudo systemctl restart php8.2-fpm
sudo systemctl enable php8.2-fpm
```

### 4. Nginx Configuration
```bash
# Create site configuration
sudo nano /etc/nginx/sites-available/mobileparts
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    root /var/www/mobile-parts-db/public;
    index index.php;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# Enable site and restart Nginx
sudo ln -s /etc/nginx/sites-available/mobileparts /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
sudo systemctl enable nginx
```

---

## 📦 Application Deployment

### 1. Clone and Setup Application
```bash
# Create application directory
sudo mkdir -p /var/www/mobile-parts-db
sudo chown $USER:www-data /var/www/mobile-parts-db

# Clone repository
cd /var/www
git clone https://github.com/your-username/mobile-parts-db.git
cd mobile-parts-db

# Set permissions
sudo chown -R $USER:www-data .
sudo chmod -R 755 .
sudo chmod -R 775 storage bootstrap/cache
```

### 2. Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env
```

```env
APP_NAME="Mobile Parts Database"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://your-domain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mobile_parts_db
DB_USERNAME=mobileparts
DB_PASSWORD=secure_password_here

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password_here
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Stripe Configuration (for future billing)
STRIPE_KEY=your_stripe_publishable_key
STRIPE_SECRET=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Plugin Configuration
PLUGIN_AUTO_DISCOVERY=true
PLUGIN_CACHE_ENABLED=true

# Security Configuration
PLUGIN_VERIFY_SIGNATURES=false
PLUGIN_SANDBOX_MODE=false
```

### 3. Install Dependencies and Build
```bash
# Install PHP dependencies
composer install --no-dev --optimize-autoloader

# Generate application key
php artisan key:generate

# Install Node.js dependencies
npm ci --production

# Build production assets
npm run build

# Run database migrations
php artisan migrate --force

# Seed initial data
php artisan db:seed --force

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Optimize performance
php artisan app:optimize-performance --warm-cache
```

### 4. Set Final Permissions
```bash
# Set proper ownership
sudo chown -R www-data:www-data storage bootstrap/cache

# Set proper permissions
sudo chmod -R 755 storage bootstrap/cache
sudo chmod -R 644 storage/logs
```

---

## 🔄 Process Management

### 1. Queue Workers (Supervisor)
```bash
# Install Supervisor
sudo apt install supervisor

# Create worker configuration
sudo nano /etc/supervisor/conf.d/laravel-worker.conf
```

```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/mobile-parts-db/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/mobile-parts-db/storage/logs/worker.log
stopwaitsecs=3600
```

```bash
# Update Supervisor configuration
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start laravel-worker:*
```

### 2. Cron Jobs
```bash
# Add Laravel scheduler to crontab
sudo crontab -e

# Add this line:
* * * * * cd /var/www/mobile-parts-db && php artisan schedule:run >> /dev/null 2>&1
```

---

## 🔒 Security Configuration

### 1. Firewall Setup
```bash
# Configure UFW firewall
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### 3. Security Hardening
```bash
# Hide server information
sudo nano /etc/nginx/nginx.conf
# Add: server_tokens off;

# Configure fail2ban
sudo apt install fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# Restart services
sudo systemctl restart nginx
sudo systemctl restart fail2ban
```

---

## 📊 Monitoring & Logging

### 1. Log Rotation
```bash
# Configure log rotation
sudo nano /etc/logrotate.d/laravel
```

```
/var/www/mobile-parts-db/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. Health Checks
```bash
# Create health check endpoint test
curl -f https://your-domain.com/health || echo "Health check failed"

# Add to monitoring system
# Monitor response time, error rates, and uptime
```

---

## 🚀 Deployment Script Usage

### Automated Deployment
```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Run deployment
./scripts/deploy.sh

# Run with post-deployment tests
./scripts/deploy.sh --test
```

### Manual Deployment Steps
```bash
# 1. Put in maintenance mode
php artisan down

# 2. Pull latest code
git pull origin main

# 3. Update dependencies
composer install --no-dev --optimize-autoloader
npm ci --production
npm run build

# 4. Run migrations
php artisan migrate --force

# 5. Clear and cache
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 6. Restart services
sudo systemctl reload php8.2-fpm
php artisan queue:restart

# 7. Exit maintenance mode
php artisan up
```

---

## 🔧 Troubleshooting

### Common Issues
1. **Permission Errors**: Check file ownership and permissions
2. **Database Connection**: Verify credentials and firewall
3. **Cache Issues**: Clear all caches and restart Redis
4. **Queue Problems**: Restart Supervisor workers
5. **SSL Issues**: Check certificate validity and configuration

### Performance Optimization
1. **Enable OPcache**: Configure PHP OPcache
2. **Database Tuning**: Optimize MySQL configuration
3. **CDN Setup**: Configure CDN for static assets
4. **Monitoring**: Set up application performance monitoring

---

## ✅ Post-Deployment Checklist

- [ ] Application loads successfully
- [ ] Database connection working
- [ ] Search functionality operational
- [ ] Admin panel accessible
- [ ] User registration/login working
- [ ] SSL certificate valid
- [ ] Queue workers running
- [ ] Cron jobs scheduled
- [ ] Monitoring configured
- [ ] Backups scheduled
- [ ] Performance optimized
- [ ] Security measures active

---

**Deployment Complete! 🎉**

Your Mobile Parts Database is now live and ready for users!
