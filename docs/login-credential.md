# 🔑 Login Credentials

## Admin Users

| Role | Email | Password | Plan | Searches |
|------|-------|----------|------|----------|
| Super Admin | <EMAIL> | admin123 | Premium | 0 |
| Content Manager | <EMAIL> | content123 | Premium | 0 |

## Test Users

| Type | Email | Password | Plan | Searches | Status |
|------|-------|----------|------|----------|--------|
| Premium User | <EMAIL> | password | Premium | 45 | Active subscription |
| Free User | <EMAIL> | password | Free | 15 | Within limit (5 left) |
| Free Near Limit | <EMAIL> | password | Free | 19 | Near limit (1 left) |
| Free Over Limit | <EMAIL> | password | Free | 25 | Exceeded limit |

## 🧪 Testing Scenarios

Use these credentials to test different user scenarios:

- **Super Admin**: Full system access and management capabilities
- **Content Manager**: Content creation and management permissions
- **Premium User**: Full search capabilities with active subscription
- **Free User**: Standard free tier usage within limits
- **Free Near Limit**: Testing near-limit warnings and notifications
- **Free Over Limit**: Testing limit exceeded scenarios and upgrade prompts
