# Paddle Payment Gateway - Configuration Guide

This guide covers the complete configuration of Paddle payment gateway, including environment setup, Paddle dashboard configuration, and Laravel application settings.

## 🏗️ Laravel Configuration

### Create Paddle Configuration File

Create `config/paddle.php`:

```php
<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Paddle Environment
    |--------------------------------------------------------------------------
    |
    | This value determines which Paddle environment your application will
    | use. Supported: "sandbox", "production"
    |
    */
    'environment' => env('PADDLE_ENVIRONMENT', 'sandbox'),

    /*
    |--------------------------------------------------------------------------
    | Paddle API Credentials
    |--------------------------------------------------------------------------
    |
    | Your Paddle API credentials. These can be found in your Paddle
    | dashboard under Developer Tools > Authentication.
    |
    */
    'api_key' => env('PADDLE_API_KEY'),
    'client_token' => env('PADDLE_CLIENT_TOKEN'),
    'webhook_secret' => env('PADDLE_WEBHOOK_SECRET'),
    'vendor_id' => env('PADDLE_VENDOR_ID'),

    /*
    |--------------------------------------------------------------------------
    | Paddle URLs
    |--------------------------------------------------------------------------
    |
    | The base URLs for Paddle API endpoints. These are automatically
    | set based on the environment but can be overridden if needed.
    |
    */
    'urls' => [
        'sandbox' => env('PADDLE_SANDBOX_URL', 'https://sandbox-api.paddle.com'),
        'production' => env('PADDLE_PRODUCTION_URL', 'https://api.paddle.com'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Paddle Logging
    |--------------------------------------------------------------------------
    |
    | Configure logging for Paddle API requests and responses.
    | Useful for debugging and monitoring.
    |
    */
    'logging' => [
        'enabled' => env('PADDLE_LOGGING_ENABLED', true),
        'level' => env('PADDLE_LOG_LEVEL', 'info'),
        'channel' => env('PADDLE_LOG_CHANNEL', 'daily'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Paddle Retry Configuration
    |--------------------------------------------------------------------------
    |
    | Configure retry behavior for failed API requests.
    |
    */
    'retry' => [
        'attempts' => env('PADDLE_RETRY_ATTEMPTS', 3),
        'delay' => env('PADDLE_RETRY_DELAY', 1000), // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Paddle Timeout Configuration
    |--------------------------------------------------------------------------
    |
    | Configure timeout settings for API requests.
    |
    */
    'timeout' => [
        'connect' => env('PADDLE_CONNECT_TIMEOUT', 10), // seconds
        'request' => env('PADDLE_REQUEST_TIMEOUT', 30), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    |
    | Configure webhook handling settings.
    |
    */
    'webhooks' => [
        'verify_signature' => env('PADDLE_VERIFY_WEBHOOK_SIGNATURE', true),
        'tolerance' => env('PADDLE_WEBHOOK_TOLERANCE', 300), // seconds
        'events' => [
            'transaction.completed',
            'transaction.updated',
            'subscription.created',
            'subscription.updated',
            'subscription.cancelled',
            'subscription.paused',
            'subscription.resumed',
            'customer.created',
            'customer.updated',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency Configuration
    |--------------------------------------------------------------------------
    |
    | Default currency settings for Paddle transactions.
    |
    */
    'currency' => [
        'default' => env('PADDLE_DEFAULT_CURRENCY', 'USD'),
        'supported' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Subscription Configuration
    |--------------------------------------------------------------------------
    |
    | Default settings for subscription management.
    |
    */
    'subscriptions' => [
        'trial_days' => env('PADDLE_TRIAL_DAYS', 0),
        'grace_period_days' => env('PADDLE_GRACE_PERIOD_DAYS', 3),
        'proration' => env('PADDLE_PRORATION_ENABLED', true),
    ],
];
```

### Environment Variables

Add to your `.env` file:

```env
# Paddle Core Configuration
PADDLE_ENVIRONMENT=sandbox
PADDLE_API_KEY=your_paddle_api_key
PADDLE_CLIENT_TOKEN=your_paddle_client_token
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret
PADDLE_VENDOR_ID=your_paddle_vendor_id

# Paddle Logging
PADDLE_LOGGING_ENABLED=true
PADDLE_LOG_LEVEL=info
PADDLE_LOG_CHANNEL=daily

# Paddle API Settings
PADDLE_RETRY_ATTEMPTS=3
PADDLE_RETRY_DELAY=1000
PADDLE_CONNECT_TIMEOUT=10
PADDLE_REQUEST_TIMEOUT=30

# Webhook Settings
PADDLE_VERIFY_WEBHOOK_SIGNATURE=true
PADDLE_WEBHOOK_TOLERANCE=300

# Currency & Subscription Settings
PADDLE_DEFAULT_CURRENCY=USD
PADDLE_TRIAL_DAYS=0
PADDLE_GRACE_PERIOD_DAYS=3
PADDLE_PRORATION_ENABLED=true
```

## 🎛️ Paddle Dashboard Configuration

### 1. Create Paddle Account
1. Visit [Paddle.com](https://paddle.com) and create an account
2. Complete business verification process
3. Set up your business details and tax information

### 2. Developer Settings

#### API Keys Setup
1. Navigate to **Developer Tools > Authentication**
2. Generate API Key:
   - Click "Generate API Key"
   - Set appropriate permissions
   - Copy the API key to `PADDLE_API_KEY`

3. Generate Client-side Token:
   - Click "Generate Client-side Token"
   - Set domain restrictions for security
   - Copy the token to `PADDLE_CLIENT_TOKEN`

#### Webhook Configuration
1. Navigate to **Developer Tools > Notifications**
2. Create new webhook endpoint:
   - **URL**: `https://yourdomain.com/webhooks/paddle`
   - **Events**: Select all subscription and transaction events
   - **Secret**: Generate and copy to `PADDLE_WEBHOOK_SECRET`

### 3. Product & Pricing Setup

#### Create Products
```bash
# Example product creation via API
curl -X POST https://sandbox-api.paddle.com/products \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Premium Subscription",
    "description": "Premium mobile parts database access",
    "type": "standard"
  }'
```

#### Create Pricing Plans
```bash
# Example price creation via API
curl -X POST https://sandbox-api.paddle.com/prices \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": "pro_01h1vjes1y163xfj1rh1tkfb65",
    "description": "Monthly Premium Subscription",
    "billing_cycle": {
      "interval": "month",
      "frequency": 1
    },
    "unit_price": {
      "amount": "2999",
      "currency_code": "USD"
    }
  }'
```

## 🗄️ Database Configuration

### Update Pricing Plans Table

Add Paddle IDs to your existing pricing plans:

```sql
-- Update existing pricing plans with Paddle IDs
UPDATE pricing_plans SET 
  paddle_product_id = 'pro_01h1vjes1y163xfj1rh1tkfb65',
  paddle_price_id_monthly = 'pri_01h1vjqt2jym4f0yzrrbx6h8y4',
  paddle_price_id_yearly = 'pri_01h1vjr8x8x3f0yzrrbx6h8z5'
WHERE name = 'premium';
```

### Seed Paddle Configuration

Create a seeder for Paddle configuration:

```php
// database/seeders/PaddleConfigurationSeeder.php
<?php

namespace Database\Seeders;

use App\Models\PricingPlan;
use Illuminate\Database\Seeder;

class PaddleConfigurationSeeder extends Seeder
{
    public function run(): void
    {
        // Update pricing plans with Paddle IDs
        $plans = [
            'premium' => [
                'paddle_product_id' => 'pro_01h1vjes1y163xfj1rh1tkfb65',
                'paddle_price_id_monthly' => 'pri_01h1vjqt2jym4f0yzrrbx6h8y4',
                'paddle_price_id_yearly' => 'pri_01h1vjr8x8x3f0yzrrbx6h8z5',
            ],
            // Add more plans as needed
        ];

        foreach ($plans as $planName => $paddleIds) {
            PricingPlan::where('name', $planName)->update($paddleIds);
        }
    }
}
```

## 🔒 Security Configuration

### CSRF Protection
Ensure webhook routes are excluded from CSRF protection:

```php
// app/Http/Middleware/VerifyCsrfToken.php
protected $except = [
    'webhooks/paddle',
];
```

### Rate Limiting
Configure rate limiting for Paddle endpoints:

```php
// app/Http/Kernel.php
protected $middlewareGroups = [
    'api' => [
        'throttle:paddle',
        // ... other middleware
    ],
];

protected $middlewareAliases = [
    'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
];
```

### Environment-Specific Security

#### Production Security
```env
# Production environment
PADDLE_ENVIRONMENT=production
PADDLE_VERIFY_WEBHOOK_SIGNATURE=true
PADDLE_LOGGING_ENABLED=false
APP_DEBUG=false
```

#### Development Security
```env
# Development environment
PADDLE_ENVIRONMENT=sandbox
PADDLE_VERIFY_WEBHOOK_SIGNATURE=true
PADDLE_LOGGING_ENABLED=true
APP_DEBUG=true
```

## 🧪 Configuration Testing

### Test Configuration Loading
```php
// Test in tinker
php artisan tinker
>>> config('paddle.api_key');
>>> config('paddle.environment');
>>> config('paddle.webhooks.events');
```

### Validate Paddle Connection
```php
// Test Paddle service
>>> $paddle = app(App\Services\PaddleService::class);
>>> $paddle->isConfigured();
>>> // Should return true if properly configured
```

### Test Environment Variables
```bash
# Check environment variables are loaded
php artisan env
php artisan config:show paddle
```

## 📋 Configuration Checklist

### Paddle Dashboard
- [ ] Paddle account created and verified
- [ ] API key generated and configured
- [ ] Client-side token generated and configured
- [ ] Webhook endpoint created and configured
- [ ] Products created in Paddle dashboard
- [ ] Pricing plans created with correct billing cycles
- [ ] Tax settings configured
- [ ] Payment methods enabled

### Laravel Application
- [ ] Configuration file created (`config/paddle.php`)
- [ ] Environment variables set in `.env`
- [ ] Service provider registered
- [ ] Database migrations completed
- [ ] Pricing plans updated with Paddle IDs
- [ ] CSRF exceptions configured
- [ ] Rate limiting configured
- [ ] Logging configured

### Security
- [ ] Webhook signature verification enabled
- [ ] HTTPS enabled for production
- [ ] Environment-specific configurations set
- [ ] API keys secured and not exposed
- [ ] Rate limiting configured
- [ ] Error handling implemented

## 🚨 Common Configuration Issues

### Issue: Invalid API Key
```bash
# Check API key format and permissions
# Regenerate if necessary from Paddle dashboard
```

### Issue: Webhook Signature Verification Fails
```bash
# Ensure webhook secret matches Paddle dashboard
# Check webhook URL is accessible from internet
```

### Issue: Environment Variables Not Loading
```bash
# Clear configuration cache
php artisan config:clear
php artisan config:cache
```

---

**Next:** Continue with [Backend Implementation Guide](./paddle-backend.md) to implement the core Paddle functionality.
