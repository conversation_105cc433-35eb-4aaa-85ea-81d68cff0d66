# Paddle Payment Gateway - API Reference

This document provides a comprehensive API reference for the Paddle payment gateway integration, including all endpoints, request/response formats, and usage examples.

## 🔗 API Endpoints

### Authentication
All API endpoints (except webhooks) require authentication via Laravel Sanctum or session-based authentication.

**Headers Required:**
```
Authorization: Bearer {token}
Content-Type: application/json
X-CSRF-TOKEN: {csrf_token}
```

---

## 📋 Paddle Configuration API

### GET /paddle/config
Get Paddle configuration for frontend initialization.

**Authentication:** Required  
**Method:** GET  
**URL:** `/paddle/config`

#### Response

**Success (200):**
```json
{
  "environment": "sandbox",
  "client_token": "ctok_01h1vjes1y163xfj1rh1tkfb65",
  "vendor_id": "12345"
}
```

**Error (503) - Paddle Not Configured:**
```json
{
  "error": "Paddle is not configured"
}
```

#### Usage Example

```javascript
// Frontend usage
const response = await fetch('/paddle/config', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json',
  }
});

const config = await response.json();

if (response.ok) {
  // Initialize Paddle with config
  const paddle = new Paddle({
    environment: config.environment,
    token: config.client_token,
  });
}
```

---

## 💳 Checkout API

### POST /paddle/checkout
Create a new checkout session for subscription purchase.

**Authentication:** Required  
**Method:** POST  
**URL:** `/paddle/checkout`

#### Request Body

```json
{
  "plan_id": 1,
  "billing_cycle": "month"
}
```

**Parameters:**
- `plan_id` (integer, required): ID of the pricing plan
- `billing_cycle` (string, required): Billing cycle - "month" or "year"

#### Response

**Success (200):**
```json
{
  "success": true,
  "checkout_url": "https://checkout.paddle.com/checkout?checkout=cho_01h1vjes1y163xfj1rh1tkfb65",
  "transaction_id": "txn_01h1vjes1y163xfj1rh1tkfb65"
}
```

**Error (400) - User Already Premium:**
```json
{
  "error": "You already have an active premium subscription."
}
```

**Error (400) - Unsupported Billing Cycle:**
```json
{
  "error": "This plan does not support yearly billing."
}
```

**Error (422) - Validation Error:**
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "plan_id": ["The plan id field is required."],
    "billing_cycle": ["The billing cycle field is required."]
  }
}
```

**Error (500) - Checkout Creation Failed:**
```json
{
  "error": "Failed to create checkout session. Please try again."
}
```

#### Usage Example

```javascript
// Create checkout session
const response = await fetch('/paddle/checkout', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json',
    'X-CSRF-TOKEN': csrfToken,
  },
  body: JSON.stringify({
    plan_id: 1,
    billing_cycle: 'month'
  })
});

const data = await response.json();

if (response.ok) {
  // Redirect to Paddle checkout
  window.location.href = data.checkout_url;
} else {
  console.error('Checkout failed:', data.error);
}
```

---

## 🔔 Webhook API

### POST /webhooks/paddle
Receive and process Paddle webhooks.

**Authentication:** None (uses signature verification)  
**Method:** POST  
**URL:** `/webhooks/paddle`

#### Request Headers

```
Content-Type: application/json
Paddle-Signature: t=1234567890,h1=abc123def456...
```

#### Request Body

The request body varies by event type. Here are common examples:

**Transaction Completed:**
```json
{
  "event_type": "transaction.completed",
  "event_id": "evt_01h1vjes1y163xfj1rh1tkfb65",
  "occurred_at": "2024-01-15T10:30:00.000Z",
  "data": {
    "id": "txn_01h1vjes1y163xfj1rh1tkfb65",
    "status": "completed",
    "customer_id": "ctm_01h1vjes1y163xfj1rh1tkfb65",
    "address_id": "add_01h1vjes1y163xfj1rh1tkfb65",
    "business_id": null,
    "custom_data": null,
    "origin": "web",
    "subscription_id": "sub_01h1vjes1y163xfj1rh1tkfb65",
    "invoice_id": "inv_01h1vjes1y163xfj1rh1tkfb65",
    "invoice_number": "10001-10001",
    "collection_mode": "automatic",
    "discount_id": null,
    "billing_details": {
      "enable_checkout": true,
      "purchase_order_number": "PO-12345",
      "additional_information": null,
      "payment_terms": {
        "interval": "month",
        "frequency": 1
      }
    },
    "billing_period": {
      "starts_at": "2024-01-15T10:30:00.000Z",
      "ends_at": "2024-02-15T10:30:00.000Z"
    },
    "currency_code": "USD",
    "details": {
      "tax_rates_used": [
        {
          "tax_rate": "0.08750",
          "totals": {
            "subtotal": "2999",
            "discount": "0",
            "tax": "262",
            "total": "3261"
          }
        }
      ],
      "totals": {
        "subtotal": "2999",
        "discount": "0",
        "tax": "262",
        "total": "3261",
        "credit": "0",
        "balance": "3261",
        "grand_total": "3261",
        "fee": null,
        "earnings": null,
        "currency_code": "USD"
      },
      "adjusted_totals": {
        "subtotal": "2999",
        "tax": "262",
        "total": "3261",
        "grand_total": "3261",
        "fee": "145",
        "earnings": "3116",
        "currency_code": "USD"
      },
      "payout_totals": null,
      "adjusted_payout_totals": null,
      "line_items": [
        {
          "id": "txnitm_01h1vjes1y163xfj1rh1tkfb65",
          "price_id": "pri_01h1vjes1y163xfj1rh1tkfb65",
          "quantity": 1,
          "proration": null,
          "tax_rate": "0.08750",
          "unit_totals": {
            "subtotal": "2999",
            "discount": "0",
            "tax": "262",
            "total": "3261"
          },
          "totals": {
            "subtotal": "2999",
            "discount": "0",
            "tax": "262",
            "total": "3261"
          },
          "product": {
            "id": "pro_01h1vjes1y163xfj1rh1tkfb65",
            "name": "Premium Subscription",
            "description": "Premium mobile parts database access",
            "type": "standard",
            "tax_category": "standard",
            "image_url": null,
            "custom_data": null,
            "status": "active",
            "created_at": "2024-01-01T00:00:00.000Z",
            "updated_at": "2024-01-01T00:00:00.000Z"
          }
        }
      ]
    },
    "payments": [
      {
        "payment_id": "pay_01h1vjes1y163xfj1rh1tkfb65",
        "payment_method_id": "paymtd_01h1vjes1y163xfj1rh1tkfb65",
        "payment_attempt_id": "payatt_01h1vjes1y163xfj1rh1tkfb65",
        "stored_payment_method_id": "stpmtd_01h1vjes1y163xfj1rh1tkfb65",
        "amount": "3261",
        "currency_code": "USD",
        "status": "captured",
        "created_at": "2024-01-15T10:30:00.000Z",
        "captured_at": "2024-01-15T10:30:00.000Z"
      }
    ],
    "checkout": {
      "url": "https://checkout.paddle.com/checkout?checkout=cho_01h1vjes1y163xfj1rh1tkfb65"
    },
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T10:30:00.000Z",
    "billed_at": "2024-01-15T10:30:00.000Z"
  }
}
```

**Subscription Created:**
```json
{
  "event_type": "subscription.created",
  "event_id": "evt_01h1vjes1y163xfj1rh1tkfb65",
  "occurred_at": "2024-01-15T10:30:00.000Z",
  "data": {
    "id": "sub_01h1vjes1y163xfj1rh1tkfb65",
    "status": "active",
    "customer_id": "ctm_01h1vjes1y163xfj1rh1tkfb65",
    "address_id": "add_01h1vjes1y163xfj1rh1tkfb65",
    "business_id": null,
    "currency_code": "USD",
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T10:30:00.000Z",
    "started_at": "2024-01-15T10:30:00.000Z",
    "first_billed_at": "2024-01-15T10:30:00.000Z",
    "next_billed_at": "2024-02-15T10:30:00.000Z",
    "paused_at": null,
    "canceled_at": null,
    "discount_id": null,
    "collection_mode": "automatic",
    "billing_details": {
      "enable_checkout": true,
      "purchase_order_number": "PO-12345",
      "additional_information": null,
      "payment_terms": {
        "interval": "month",
        "frequency": 1
      }
    },
    "current_billing_period": {
      "starts_at": "2024-01-15T10:30:00.000Z",
      "ends_at": "2024-02-15T10:30:00.000Z"
    },
    "billing_cycle": {
      "interval": "month",
      "frequency": 1
    },
    "recurring_transaction_details": {
      "tax_rates_used": [
        {
          "tax_rate": "0.08750",
          "totals": {
            "subtotal": "2999",
            "discount": "0",
            "tax": "262",
            "total": "3261"
          }
        }
      ],
      "totals": {
        "subtotal": "2999",
        "discount": "0",
        "tax": "262",
        "total": "3261",
        "credit": "0",
        "balance": "3261",
        "grand_total": "3261",
        "fee": null,
        "earnings": null,
        "currency_code": "USD"
      },
      "line_items": [
        {
          "id": "subitem_01h1vjes1y163xfj1rh1tkfb65",
          "price_id": "pri_01h1vjes1y163xfj1rh1tkfb65",
          "quantity": 1,
          "recurring": true,
          "created_at": "2024-01-15T10:30:00.000Z",
          "updated_at": "2024-01-15T10:30:00.000Z",
          "next_billed_at": "2024-02-15T10:30:00.000Z",
          "previously_billed_at": "2024-01-15T10:30:00.000Z",
          "trial_dates": null,
          "product": {
            "id": "pro_01h1vjes1y163xfj1rh1tkfb65",
            "name": "Premium Subscription",
            "description": "Premium mobile parts database access",
            "type": "standard",
            "tax_category": "standard",
            "image_url": null,
            "custom_data": null,
            "status": "active",
            "created_at": "2024-01-01T00:00:00.000Z",
            "updated_at": "2024-01-01T00:00:00.000Z"
          },
          "price": {
            "id": "pri_01h1vjes1y163xfj1rh1tkfb65",
            "product_id": "pro_01h1vjes1y163xfj1rh1tkfb65",
            "description": "Monthly Premium Subscription",
            "type": "standard",
            "billing_cycle": {
              "interval": "month",
              "frequency": 1
            },
            "trial_period": null,
            "tax_mode": "account_setting",
            "unit_price": {
              "amount": "2999",
              "currency_code": "USD"
            },
            "unit_price_overrides": [],
            "quantity": {
              "minimum": 1,
              "maximum": 1
            },
            "status": "active",
            "custom_data": null,
            "created_at": "2024-01-01T00:00:00.000Z",
            "updated_at": "2024-01-01T00:00:00.000Z"
          }
        }
      ]
    },
    "scheduled_change": null,
    "items": [
      {
        "status": "active",
        "quantity": 1,
        "recurring": true,
        "created_at": "2024-01-15T10:30:00.000Z",
        "updated_at": "2024-01-15T10:30:00.000Z",
        "trial_dates": null,
        "price": {
          "id": "pri_01h1vjes1y163xfj1rh1tkfb65",
          "product_id": "pro_01h1vjes1y163xfj1rh1tkfb65",
          "description": "Monthly Premium Subscription",
          "type": "standard",
          "billing_cycle": {
            "interval": "month",
            "frequency": 1
          },
          "trial_period": null,
          "tax_mode": "account_setting",
          "unit_price": {
            "amount": "2999",
            "currency_code": "USD"
          },
          "unit_price_overrides": [],
          "quantity": {
            "minimum": 1,
            "maximum": 1
          },
          "status": "active",
          "custom_data": null,
          "created_at": "2024-01-01T00:00:00.000Z",
          "updated_at": "2024-01-01T00:00:00.000Z"
        },
        "product": {
          "id": "pro_01h1vjes1y163xfj1rh1tkfb65",
          "name": "Premium Subscription",
          "description": "Premium mobile parts database access",
          "type": "standard",
          "tax_category": "standard",
          "image_url": null,
          "custom_data": null,
          "status": "active",
          "created_at": "2024-01-01T00:00:00.000Z",
          "updated_at": "2024-01-01T00:00:00.000Z"
        }
      }
    ],
    "custom_data": null,
    "management_urls": {
      "update_payment_method": "https://checkout.paddle.com/subscription/update_payment_method?subscription=sub_01h1vjes1y163xfj1rh1tkfb65",
      "cancel": "https://checkout.paddle.com/subscription/cancel?subscription=sub_01h1vjes1y163xfj1rh1tkfb65"
    }
  }
}
```

#### Response

**Success (200):**
```json
{
  "success": true
}
```

**Error (400) - Missing Signature:**
```json
{
  "error": "Missing signature"
}
```

**Error (400) - Invalid Webhook:**
```json
{
  "error": "Invalid webhook"
}
```

#### Supported Event Types

- `transaction.completed` - Payment completed successfully
- `transaction.updated` - Transaction details updated
- `subscription.created` - New subscription created
- `subscription.updated` - Subscription details updated
- `subscription.cancelled` - Subscription cancelled
- `subscription.paused` - Subscription paused
- `subscription.resumed` - Subscription resumed
- `customer.created` - New customer created
- `customer.updated` - Customer details updated

---

## 🎯 Frontend Pages

### GET /subscription/paddle/success
Display success page after successful payment.

**Authentication:** Required  
**Method:** GET  
**URL:** `/subscription/paddle/success`

**Query Parameters:**
- `transaction_id` (string, optional): Paddle transaction ID

#### Response
Returns Inertia.js page component `subscription/paddle/Success`

### GET /subscription/paddle/cancelled
Display cancelled page after payment cancellation.

**Authentication:** Required  
**Method:** GET  
**URL:** `/subscription/paddle/cancelled`

#### Response
Returns Inertia.js page component `subscription/paddle/Cancelled`

---

## 🔧 Service Methods

### PaddleService Class

The `PaddleService` class provides the following public methods:

#### `isConfigured(): bool`
Check if Paddle is properly configured.

```php
$paddleService = app(PaddleService::class);
$isConfigured = $paddleService->isConfigured();
```

#### `getOrCreateCustomer(User $user): ?Customer`
Get or create a Paddle customer for the given user.

```php
$customer = $paddleService->getOrCreateCustomer($user);
```

#### `getPrice(string $priceId): ?Price`
Get price information from Paddle.

```php
$price = $paddleService->getPrice('pri_01h1vjes1y163xfj1rh1tkfb65');
```

#### `createCheckoutSession(User $user, PricingPlan $plan, string $billingCycle): ?array`
Create a checkout session for a subscription.

```php
$checkoutData = $paddleService->createCheckoutSession($user, $plan, 'month');
```

#### `processWebhook(array $payload, string $signature): bool`
Process webhook payload.

```php
$success = $paddleService->processWebhook($payload, $signature);
```

---

## 📊 Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 400 | Bad Request | Check request format and required parameters |
| 401 | Unauthorized | Ensure user is authenticated |
| 403 | Forbidden | Check user permissions |
| 404 | Not Found | Verify endpoint URL and resource existence |
| 422 | Validation Error | Fix validation errors in request data |
| 500 | Internal Server Error | Check server logs for detailed error information |
| 503 | Service Unavailable | Paddle service not configured or unavailable |

---

## 🔍 Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Authenticated endpoints**: 60 requests per minute per user
- **Webhook endpoint**: 1000 requests per minute (no user limit)

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

---

**Next:** Continue with [Troubleshooting Guide](./paddle-troubleshooting.md) for common issues and solutions.
