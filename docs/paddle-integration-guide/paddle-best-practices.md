# Paddle Payment Gateway - Best Practices Guide

This guide outlines development best practices, security considerations, and optimization strategies for Paddle payment gateway integration.

## 🔒 Security Best Practices

### 1. API Key Management

**✅ DO:**
```php
// Store API keys in environment variables
'api_key' => env('PADDLE_API_KEY'),

// Use different keys for different environments
// .env.production
PADDLE_API_KEY=live_api_key_here
PADDLE_ENVIRONMENT=production

// .env.local
PADDLE_API_KEY=test_api_key_here
PADDLE_ENVIRONMENT=sandbox
```

**❌ DON'T:**
```php
// Never hardcode API keys
'api_key' => 'sk_live_abc123...',

// Never commit API keys to version control
// Add .env to .gitignore
```

### 2. Webhook Security

**✅ DO:**
```php
// Always verify webhook signatures
protected function verifyWebhookSignature(array $payload, string $signature): bool
{
    if (!config('paddle.webhooks.verify_signature')) {
        return true; // Only for development
    }
    
    $secret = config('paddle.webhook_secret');
    if (!$secret) {
        Log::warning('Paddle webhook secret not configured');
        return false;
    }
    
    // Implement proper signature verification
    return $this->validateSignature($payload, $signature, $secret);
}

// Use HTTPS for webhook endpoints
Route::post('/webhooks/paddle', [PaddleController::class, 'webhook'])
    ->middleware(['throttle:1000,1']); // Rate limiting
```

**❌ DON'T:**
```php
// Never skip signature verification in production
if (app()->environment('production')) {
    return true; // DANGEROUS!
}

// Never expose sensitive data in logs
Log::info('Webhook received', $payload); // May contain PII
```

### 3. Data Protection

**✅ DO:**
```php
// Encrypt sensitive data
protected $casts = [
    'paddle_data' => 'encrypted:array',
    'payment_details' => 'encrypted:json',
];

// Sanitize user input
$validated = $request->validate([
    'plan_id' => 'required|integer|exists:pricing_plans,id',
    'billing_cycle' => 'required|in:month,year',
]);

// Use database transactions for critical operations
DB::transaction(function () use ($user, $plan) {
    $user->update(['subscription_plan' => $plan->name]);
    $user->subscriptions()->create([...]);
});
```

---

## 🚀 Performance Optimization

### 1. Database Optimization

**Indexing Strategy:**
```sql
-- Essential indexes for performance
CREATE INDEX idx_users_paddle_customer_id ON users(paddle_customer_id);
CREATE INDEX idx_paddle_transactions_user_status ON paddle_transactions(user_id, status);
CREATE INDEX idx_paddle_webhooks_status_created ON paddle_webhooks(status, created_at);
CREATE INDEX idx_subscriptions_user_status ON subscriptions(user_id, status);
```

**Query Optimization:**
```php
// Use eager loading to prevent N+1 queries
$transactions = PaddleTransaction::with(['user', 'pricingPlan'])
    ->where('status', 'completed')
    ->get();

// Use database-level filtering
$recentTransactions = PaddleTransaction::completed()
    ->where('created_at', '>', now()->subDays(30))
    ->orderBy('created_at', 'desc')
    ->paginate(20);

// Cache expensive queries
$revenue = Cache::remember('monthly_revenue', 3600, function () {
    return PaddleTransaction::completed()
        ->whereBetween('completed_at', [now()->startOfMonth(), now()->endOfMonth()])
        ->sum('amount');
});
```

### 2. API Request Optimization

**Connection Pooling:**
```php
// Configure HTTP client for better performance
$options = new Options($environment);
$options->timeout = 30;
$options->connectTimeout = 10;

// Implement retry logic with exponential backoff
protected function makeApiRequest(callable $request, int $maxRetries = 3): mixed
{
    $attempt = 0;
    
    while ($attempt < $maxRetries) {
        try {
            return $request();
        } catch (ApiError $e) {
            $attempt++;
            if ($attempt >= $maxRetries) {
                throw $e;
            }
            
            // Exponential backoff
            sleep(pow(2, $attempt));
        }
    }
}
```

**Caching Strategy:**
```php
// Cache Paddle price information
public function getPrice(string $priceId): ?Price
{
    return Cache::remember("paddle_price_{$priceId}", 3600, function () use ($priceId) {
        if (!$this->paddle) {
            return null;
        }
        
        try {
            return $this->paddle->prices->get($priceId);
        } catch (ApiError $e) {
            Log::error('Failed to get Paddle price', [
                'price_id' => $priceId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    });
}
```

### 3. Frontend Performance

**Lazy Loading:**
```javascript
// Load Paddle.js only when needed
const loadPaddle = async () => {
    if (window.Paddle) {
        return window.Paddle;
    }
    
    const { Paddle } = await import('@paddle/paddle-js');
    
    const paddle = new Paddle({
        environment: import.meta.env.VITE_PADDLE_ENVIRONMENT,
        token: import.meta.env.VITE_PADDLE_CLIENT_TOKEN,
    });
    
    window.Paddle = paddle;
    return paddle;
};

// Use React.lazy for component loading
const PaddleCheckout = React.lazy(() => import('./components/PaddleCheckout'));

// Wrap in Suspense
<Suspense fallback={<div>Loading checkout...</div>}>
    <PaddleCheckout plan={plan} />
</Suspense>
```

---

## 🧪 Testing Best Practices

### 1. Test Environment Setup

**Isolated Testing:**
```php
// Use in-memory database for tests
// phpunit.xml
<env name="DB_CONNECTION" value="sqlite"/>
<env name="DB_DATABASE" value=":memory:"/>

// Mock external services
public function test_checkout_creation(): void
{
    $this->mock(PaddleService::class, function ($mock) {
        $mock->shouldReceive('createCheckoutSession')
            ->once()
            ->andReturn([
                'checkout_url' => 'https://test.paddle.com/checkout',
                'transaction_id' => 'txn_test_123',
            ]);
    });
    
    // Test implementation
}
```

**Test Data Management:**
```php
// Use factories for consistent test data
PaddleTransaction::factory()->completed()->create([
    'user_id' => $user->id,
    'amount' => 29.99,
]);

// Clean up after tests
protected function tearDown(): void
{
    Mockery::close();
    parent::tearDown();
}
```

### 2. Integration Testing

**Webhook Testing:**
```php
public function test_webhook_processing(): void
{
    $payload = [
        'event_type' => 'transaction.completed',
        'event_id' => 'evt_test_123',
        'data' => ['id' => 'txn_test_123'],
    ];
    
    // Disable signature verification for testing
    config(['paddle.webhooks.verify_signature' => false]);
    
    $response = $this->postJson('/webhooks/paddle', $payload, [
        'Paddle-Signature' => 'test_signature',
    ]);
    
    $response->assertStatus(200);
    $this->assertDatabaseHas('paddle_webhooks', [
        'event_type' => 'transaction.completed',
        'status' => 'processed',
    ]);
}
```

---

## 📊 Monitoring and Logging

### 1. Comprehensive Logging

**Structured Logging:**
```php
// Use consistent log structure
Log::info('Paddle checkout initiated', [
    'user_id' => $user->id,
    'plan_id' => $plan->id,
    'billing_cycle' => $billingCycle,
    'amount' => $plan->price,
    'currency' => 'USD',
    'timestamp' => now()->toISOString(),
]);

// Log errors with context
Log::error('Paddle checkout failed', [
    'user_id' => $user->id,
    'plan_id' => $plan->id,
    'error' => $e->getMessage(),
    'trace' => $e->getTraceAsString(),
]);
```

**Performance Monitoring:**
```php
// Monitor API response times
$startTime = microtime(true);

try {
    $result = $this->paddle->customers->create($operation);
    
    Log::info('Paddle API call successful', [
        'method' => 'customers.create',
        'duration_ms' => round((microtime(true) - $startTime) * 1000, 2),
    ]);
    
    return $result;
} catch (ApiError $e) {
    Log::error('Paddle API call failed', [
        'method' => 'customers.create',
        'duration_ms' => round((microtime(true) - $startTime) * 1000, 2),
        'error' => $e->getMessage(),
    ]);
    
    throw $e;
}
```

### 2. Health Checks

**Service Health Monitoring:**
```php
// Create health check endpoint
Route::get('/health/paddle', function () {
    $paddleService = app(PaddleService::class);
    
    $health = [
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'checks' => [
            'paddle_configured' => $paddleService->isConfigured(),
            'database_connection' => true,
            'recent_webhooks' => PaddleWebhook::where('created_at', '>', now()->subHour())->count(),
        ],
    ];
    
    $status = collect($health['checks'])->every(fn($check) => $check === true) ? 200 : 503;
    
    return response()->json($health, $status);
});
```

---

## 🔄 Error Handling

### 1. Graceful Degradation

**Fallback Mechanisms:**
```php
public function createCheckoutSession(User $user, PricingPlan $plan, string $billingCycle): ?array
{
    if (!$this->paddle) {
        // Fallback to offline payment
        Log::warning('Paddle not available, redirecting to offline payment');
        return [
            'fallback' => true,
            'redirect_url' => route('payment-requests.create', [
                'plan' => $plan->name,
                'billing_cycle' => $billingCycle,
            ]),
        ];
    }
    
    try {
        return $this->createPaddleCheckout($user, $plan, $billingCycle);
    } catch (ApiError $e) {
        Log::error('Paddle checkout failed, offering alternatives', [
            'error' => $e->getMessage(),
            'user_id' => $user->id,
        ]);
        
        return [
            'error' => true,
            'message' => 'Payment processing temporarily unavailable. Please try offline payment.',
            'fallback_url' => route('payment-requests.create'),
        ];
    }
}
```

### 2. User-Friendly Error Messages

**Frontend Error Handling:**
```javascript
const handleCheckoutError = (error) => {
    const userFriendlyMessages = {
        'network_error': 'Please check your internet connection and try again.',
        'payment_declined': 'Your payment was declined. Please try a different payment method.',
        'service_unavailable': 'Payment processing is temporarily unavailable. Please try again later.',
    };
    
    const message = userFriendlyMessages[error.code] || 'An unexpected error occurred. Please contact support.';
    
    setError(message);
    
    // Track errors for monitoring
    if (window.gtag) {
        window.gtag('event', 'exception', {
            description: error.message,
            fatal: false,
        });
    }
};
```

---

## 📈 Scalability Considerations

### 1. Queue Processing

**Webhook Processing:**
```php
// Process webhooks asynchronously
public function webhook(Request $request): JsonResponse
{
    $payload = $request->all();
    $signature = $request->header('Paddle-Signature');
    
    // Quick validation and queue for processing
    if (!$signature) {
        return response()->json(['error' => 'Missing signature'], 400);
    }
    
    ProcessPaddleWebhook::dispatch($payload, $signature);
    
    return response()->json(['success' => true]);
}

// Job class
class ProcessPaddleWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public function handle(PaddleService $paddleService): void
    {
        $success = $paddleService->processWebhook($this->payload, $this->signature);
        
        if (!$success) {
            $this->fail('Webhook processing failed');
        }
    }
}
```

### 2. Database Partitioning

**Large Dataset Management:**
```sql
-- Partition webhook table by date
CREATE TABLE paddle_webhooks_2024_01 PARTITION OF paddle_webhooks
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Archive old transactions
CREATE TABLE paddle_transactions_archive AS 
SELECT * FROM paddle_transactions 
WHERE created_at < NOW() - INTERVAL '1 year';
```

---

## 📋 Development Checklist

### Pre-Development
- [ ] Environment variables configured
- [ ] Paddle account set up with sandbox
- [ ] Database migrations planned
- [ ] Test data prepared

### During Development
- [ ] Error handling implemented
- [ ] Logging added to critical paths
- [ ] Unit tests written
- [ ] Integration tests created
- [ ] Security measures implemented

### Pre-Production
- [ ] Production credentials configured
- [ ] Webhook endpoints tested
- [ ] Performance testing completed
- [ ] Security audit performed
- [ ] Monitoring set up

### Post-Deployment
- [ ] Health checks passing
- [ ] Webhook delivery confirmed
- [ ] User flows tested
- [ ] Error rates monitored
- [ ] Performance metrics tracked

---

**Next:** Continue with [Deployment Guide](./paddle-deployment.md) for production deployment strategies.
