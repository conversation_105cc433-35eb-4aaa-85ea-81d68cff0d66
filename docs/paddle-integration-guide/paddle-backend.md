# Paddle Payment Gateway - Backend Implementation Guide

This guide covers the complete backend implementation of Paddle payment gateway integration, including service providers, controllers, services, and models.

## 🏗️ Service Provider Implementation

### Create Paddle Service Provider

Create `app/Providers/PaddleServiceProvider.php`:

```php
<?php

namespace App\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Paddle\SDK\Client;
use Paddle\SDK\Environment;
use Paddle\SDK\Options;

class PaddleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(Client::class, function ($app) {
            $config = config('paddle');
            
            // If no API key is configured, return null to indicate Paddle is not available
            if (empty($config['api_key'])) {
                return null;
            }
            
            try {
                // Determine environment
                $environment = $config['environment'] === 'production' 
                    ? Environment::PRODUCTION 
                    : Environment::SANDBOX;

                // Create options with environment
                $options = new Options($environment);
                
                // Configure logging if enabled
                if ($config['logging']['enabled'] ?? false) {
                    // You can add custom logging configuration here if needed
                }

                // Configure retry settings
                if (isset($config['retry'])) {
                    // Configure retry settings if the SDK supports it
                }

                // Configure timeout settings
                if (isset($config['timeout'])) {
                    // Configure timeout settings if the SDK supports it
                }

                return new Client($config['api_key'], $options);
            } catch (\Exception $e) {
                // Log the error but don't fail the application
                Log::warning('Failed to initialize Paddle SDK: ' . $e->getMessage());
                return null;
            }
        });

        // Register the Paddle client as an alias
        $this->app->alias(Client::class, 'paddle');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/paddle.php' => config_path('paddle.php'),
        ], 'paddle-config');

        // Load routes
        $this->loadRoutesFrom(__DIR__.'/../../routes/paddle.php');
    }
}
```

### Register Service Provider

Add to `config/app.php`:

```php
'providers' => [
    // ... other providers
    App\Providers\PaddleServiceProvider::class,
],
```

## 🔧 Core Service Implementation

### Create Paddle Service

Create `app/Services/PaddleService.php`:

```php
<?php

namespace App\Services;

use App\Models\PaddleTransaction;
use App\Models\PaddleWebhook;
use App\Models\PricingPlan;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Paddle\SDK\Client;
use Paddle\SDK\Entities\Customer;
use Paddle\SDK\Entities\Price;
use Paddle\SDK\Entities\Subscription;
use Paddle\SDK\Entities\Transaction;
use Paddle\SDK\Exceptions\ApiError;
use Paddle\SDK\Exceptions\SdkExceptions\MalformedResponse;
use Paddle\SDK\Resources\Customers\Operations\CreateCustomer;
use Paddle\SDK\Resources\Prices\Operations\ListPrices;
use Paddle\SDK\Resources\Transactions\Operations\CreateTransaction;
use Paddle\SDK\Resources\Transactions\Operations\Create\TransactionCreateItem;

class PaddleService
{
    protected ?Client $paddle;

    public function __construct(?Client $paddle)
    {
        $this->paddle = $paddle;
    }

    /**
     * Get or create a Paddle customer for the given user.
     */
    public function getOrCreateCustomer(User $user): ?Customer
    {
        if (!$this->paddle) {
            return null;
        }

        try {
            // If user already has a Paddle customer ID, try to get the customer
            if ($user->paddle_customer_id) {
                try {
                    return $this->paddle->customers->get($user->paddle_customer_id);
                } catch (ApiError $e) {
                    // Customer not found, create a new one
                    Log::warning('Paddle customer not found, creating new one', [
                        'user_id' => $user->id,
                        'paddle_customer_id' => $user->paddle_customer_id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Create new customer
            $operation = new CreateCustomer(
                email: $user->email,
                name: $user->name
            );

            $customer = $this->paddle->customers->create($operation);

            // Update user with Paddle customer ID
            $user->update(['paddle_customer_id' => $customer->id]);

            Log::info('Created new Paddle customer', [
                'user_id' => $user->id,
                'paddle_customer_id' => $customer->id
            ]);

            return $customer;
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get or create Paddle customer', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get price information from Paddle.
     */
    public function getPrice(string $priceId): ?Price
    {
        if (!$this->paddle) {
            return null;
        }

        try {
            return $this->paddle->prices->get($priceId);
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get Paddle price', [
                'price_id' => $priceId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get all prices for a product.
     */
    public function getPricesForProduct(string $productId): array
    {
        if (!$this->paddle) {
            return [];
        }

        try {
            $operation = new ListPrices(productIds: [$productId]);
            $collection = $this->paddle->prices->list($operation);
            
            // Convert collection to array
            $prices = [];
            foreach ($collection as $price) {
                $prices[] = $price;
            }
            return $prices;
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get Paddle prices for product', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Create a checkout session for a subscription.
     */
    public function createCheckoutSession(User $user, PricingPlan $plan, string $billingCycle): ?array
    {
        if (!$this->paddle) {
            return null;
        }

        try {
            // Get or create customer
            $customer = $this->getOrCreateCustomer($user);
            if (!$customer) {
                throw new \Exception('Failed to create or retrieve customer');
            }

            // Get the appropriate price ID
            $priceId = $plan->getPaddlePriceId($billingCycle);
            if (!$priceId) {
                throw new \Exception("No Paddle price ID found for {$billingCycle}ly billing");
            }

            // Create transaction for checkout
            $operation = new CreateTransaction(
                items: [
                    new TransactionCreateItem(
                        priceId: $priceId,
                        quantity: 1
                    )
                ],
                customerId: $customer->id
            );

            $transaction = $this->paddle->transactions->create($operation);

            // Store transaction in database
            PaddleTransaction::create([
                'user_id' => $user->id,
                'paddle_transaction_id' => $transaction->id,
                'pricing_plan_id' => $plan->id,
                'amount' => $transaction->details->totals->total,
                'currency' => $transaction->currencyCode,
                'status' => $transaction->status,
                'billing_cycle' => $billingCycle,
                'paddle_data' => $transaction,
            ]);

            Log::info('Created Paddle checkout session', [
                'user_id' => $user->id,
                'transaction_id' => $transaction->id,
                'plan_id' => $plan->id,
                'billing_cycle' => $billingCycle
            ]);

            return [
                'transaction_id' => $transaction->id,
                'checkout_url' => $transaction->checkout?->url,
                'customer_id' => $customer->id,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create Paddle checkout session', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'billing_cycle' => $billingCycle,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get transaction details.
     */
    public function getTransaction(string $transactionId): ?Transaction
    {
        if (!$this->paddle) {
            return null;
        }

        try {
            return $this->paddle->transactions->get($transactionId);
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get Paddle transaction', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get subscription details.
     */
    public function getSubscription(string $subscriptionId): ?Subscription
    {
        if (!$this->paddle) {
            return null;
        }

        try {
            return $this->paddle->subscriptions->get($subscriptionId);
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get Paddle subscription', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Process webhook payload.
     */
    public function processWebhook(array $payload, string $signature): bool
    {
        try {
            // Verify webhook signature
            if (!$this->verifyWebhookSignature($payload, $signature)) {
                Log::warning('Invalid Paddle webhook signature');
                return false;
            }

            // Store webhook for processing
            $webhook = PaddleWebhook::create([
                'event_type' => $payload['event_type'],
                'paddle_event_id' => $payload['event_id'],
                'payload' => $payload,
                'signature' => $signature,
                'status' => 'pending',
            ]);

            // Process webhook based on event type
            $this->handleWebhookEvent($webhook);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to process Paddle webhook', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            return false;
        }
    }

    /**
     * Verify webhook signature.
     */
    protected function verifyWebhookSignature(array $payload, string $signature): bool
    {
        if (!config('paddle.webhooks.verify_signature')) {
            return true; // Skip verification if disabled
        }

        $secret = config('paddle.webhook_secret');
        if (!$secret) {
            Log::warning('Paddle webhook secret not configured');
            return false;
        }

        // Implement Paddle webhook signature verification
        // This is a simplified version - implement according to Paddle docs
        $expectedSignature = hash_hmac('sha256', json_encode($payload), $secret);
        
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Handle webhook event based on type.
     */
    protected function handleWebhookEvent(PaddleWebhook $webhook): void
    {
        $payload = $webhook->payload;
        
        try {
            switch ($webhook->event_type) {
                case 'transaction.completed':
                    $this->handleTransactionCompleted($payload);
                    break;
                    
                case 'subscription.created':
                    $this->handleSubscriptionCreated($payload);
                    break;
                    
                case 'subscription.updated':
                    $this->handleSubscriptionUpdated($payload);
                    break;
                    
                case 'subscription.cancelled':
                    $this->handleSubscriptionCancelled($payload);
                    break;
                    
                default:
                    Log::info('Unhandled Paddle webhook event', [
                        'event_type' => $webhook->event_type,
                        'event_id' => $webhook->paddle_event_id
                    ]);
            }
            
            $webhook->update(['status' => 'processed']);
        } catch (\Exception $e) {
            Log::error('Failed to handle Paddle webhook event', [
                'webhook_id' => $webhook->id,
                'event_type' => $webhook->event_type,
                'error' => $e->getMessage()
            ]);
            
            $webhook->update([
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle transaction completed event.
     */
    protected function handleTransactionCompleted(array $payload): void
    {
        $transactionData = $payload['data'];
        
        // Find the transaction in our database
        $transaction = PaddleTransaction::where('paddle_transaction_id', $transactionData['id'])->first();
        
        if ($transaction) {
            $transaction->update([
                'status' => 'completed',
                'paddle_data' => $transactionData,
            ]);
            
            // Activate user's subscription
            $user = $transaction->user;
            $plan = $transaction->pricingPlan;
            
            if ($user && $plan) {
                $user->update(['subscription_plan' => $plan->name]);
                
                // Create subscription record
                $user->subscriptions()->create([
                    'pricing_plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                    'status' => 'active',
                    'current_period_start' => now(),
                    'current_period_end' => now()->addMonth(), // Adjust based on billing cycle
                ]);
                
                Log::info('Activated user subscription via Paddle', [
                    'user_id' => $user->id,
                    'plan_name' => $plan->name,
                    'transaction_id' => $transactionData['id']
                ]);
            }
        }
    }

    /**
     * Handle subscription created event.
     */
    protected function handleSubscriptionCreated(array $payload): void
    {
        // Implementation for subscription created
        Log::info('Paddle subscription created', ['payload' => $payload]);
    }

    /**
     * Handle subscription updated event.
     */
    protected function handleSubscriptionUpdated(array $payload): void
    {
        // Implementation for subscription updated
        Log::info('Paddle subscription updated', ['payload' => $payload]);
    }

    /**
     * Handle subscription cancelled event.
     */
    protected function handleSubscriptionCancelled(array $payload): void
    {
        // Implementation for subscription cancelled
        Log::info('Paddle subscription cancelled', ['payload' => $payload]);
    }

    /**
     * Check if Paddle is properly configured.
     */
    public function isConfigured(): bool
    {
        $config = config('paddle');
        return $this->paddle !== null && !empty($config['api_key']) && !empty($config['client_token']);
    }
}
```

## 🎮 Controller Implementation

### Create Paddle Controller

Create `app/Http/Controllers/PaddleController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\PricingPlan;
use App\Services\PaddleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class PaddleController extends Controller
{
    protected PaddleService $paddleService;

    public function __construct(PaddleService $paddleService)
    {
        $this->paddleService = $paddleService;
    }

    /**
     * Get Paddle configuration for frontend.
     */
    public function config(Request $request): JsonResponse
    {
        if (!$this->paddleService->isConfigured()) {
            return response()->json(['error' => 'Paddle is not configured'], 503);
        }

        return response()->json([
            'environment' => config('paddle.environment'),
            'client_token' => config('paddle.client_token'),
            'vendor_id' => config('paddle.vendor_id'),
        ]);
    }

    /**
     * Create a checkout session.
     */
    public function createCheckout(Request $request): JsonResponse
    {
        $user = $request->user();

        if ($user->isPremium()) {
            return response()->json([
                'error' => 'You already have an active premium subscription.'
            ], 400);
        }

        $validated = $request->validate([
            'plan_id' => 'required|exists:pricing_plans,id',
            'billing_cycle' => 'required|in:month,year',
        ]);

        $plan = PricingPlan::findOrFail($validated['plan_id']);

        if (!$plan->supportsBillingCycle($validated['billing_cycle'])) {
            return response()->json([
                'error' => "This plan does not support {$validated['billing_cycle']}ly billing."
            ], 400);
        }

        $checkoutData = $this->paddleService->createCheckoutSession(
            $user,
            $plan,
            $validated['billing_cycle']
        );

        if (!$checkoutData) {
            return response()->json([
                'error' => 'Failed to create checkout session. Please try again.'
            ], 500);
        }

        return response()->json([
            'success' => true,
            'checkout_url' => $checkoutData['checkout_url'],
            'transaction_id' => $checkoutData['transaction_id'],
        ]);
    }

    /**
     * Handle Paddle webhooks.
     */
    public function webhook(Request $request): JsonResponse
    {
        $signature = $request->header('Paddle-Signature');
        
        if (!$signature) {
            Log::warning('Paddle webhook received without signature');
            return response()->json(['error' => 'Missing signature'], 400);
        }

        $payload = $request->all();
        
        if (!$this->paddleService->processWebhook($payload, $signature)) {
            return response()->json(['error' => 'Invalid webhook'], 400);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Show success page after payment.
     */
    public function success(Request $request): Response
    {
        return Inertia::render('subscription/paddle/Success', [
            'transaction_id' => $request->query('transaction_id'),
            'user' => $request->user(),
        ]);
    }

    /**
     * Show cancelled page after payment cancellation.
     */
    public function cancelled(Request $request): Response
    {
        return Inertia::render('subscription/paddle/Cancelled', [
            'user' => $request->user(),
        ]);
    }
}
```

## 🛣️ Route Configuration

### Create Paddle Routes File

Create `routes/paddle.php`:

```php
<?php

use App\Http\Controllers\PaddleController;
use Illuminate\Support\Facades\Route;

// Paddle webhook route (outside auth middleware)
Route::post('/webhooks/paddle', [PaddleController::class, 'webhook'])->name('webhooks.paddle');

// Authenticated Paddle routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Paddle API routes
    Route::prefix('paddle')->name('paddle.')->group(function () {
        Route::get('config', [PaddleController::class, 'config'])->name('config');
        Route::post('checkout', [PaddleController::class, 'createCheckout'])->name('checkout');
    });

    // Paddle success/cancel pages
    Route::prefix('subscription/paddle')->name('subscription.paddle.')->group(function () {
        Route::get('success', [PaddleController::class, 'success'])->name('success');
        Route::get('cancelled', [PaddleController::class, 'cancelled'])->name('cancelled');
    });
});
```

### Update Web Routes

Add to `routes/web.php`:

```php
// Include Paddle routes
require __DIR__.'/paddle.php';
```

## 🗄️ Model Enhancements

### Update User Model

Add to `app/Models/User.php`:

```php
/**
 * Get the user's Paddle transactions.
 */
public function paddleTransactions(): HasMany
{
    return $this->hasMany(PaddleTransaction::class);
}

/**
 * Get the user's latest Paddle transaction.
 */
public function latestPaddleTransaction(): HasOne
{
    return $this->hasOne(PaddleTransaction::class)->latestOfMany();
}
```

### Update PricingPlan Model

Add to `app/Models/PricingPlan.php`:

```php
/**
 * Check if plan has Paddle integration.
 */
public function hasPaddleIntegration(): bool
{
    return !empty($this->paddle_product_id);
}

/**
 * Check if plan supports a billing cycle.
 */
public function supportsBillingCycle(string $cycle): bool
{
    return match ($cycle) {
        'month' => !empty($this->paddle_price_id_monthly),
        'year' => !empty($this->paddle_price_id_yearly),
        default => false,
    };
}

/**
 * Get Paddle price ID for billing cycle.
 */
public function getPaddlePriceId(string $cycle): ?string
{
    return match ($cycle) {
        'month' => $this->paddle_price_id_monthly,
        'year' => $this->paddle_price_id_yearly,
        default => null,
    };
}
```

## 🧪 Service Testing

### Test Paddle Service

Create `tests/Unit/PaddleServiceTest.php`:

```php
<?php

namespace Tests\Unit;

use App\Models\User;
use App\Services\PaddleService;
use Paddle\SDK\Client;
use Tests\TestCase;

class PaddleServiceTest extends TestCase
{
    public function test_service_handles_null_client(): void
    {
        $service = new PaddleService(null);

        $this->assertFalse($service->isConfigured());
        $this->assertNull($service->getPrice('test_price_id'));
    }

    public function test_service_with_valid_client(): void
    {
        $client = $this->createMock(Client::class);
        $service = new PaddleService($client);

        // Test with mocked client
        $this->assertTrue($service->isConfigured());
    }
}
```

## 📋 Backend Implementation Checklist

### Core Components
- [ ] Service provider created and registered
- [ ] Paddle service implemented with null safety
- [ ] Controller created with all endpoints
- [ ] Routes configured and protected
- [ ] Models enhanced with Paddle relationships

### Error Handling
- [ ] Graceful handling when Paddle unavailable
- [ ] Comprehensive logging implemented
- [ ] User-friendly error messages
- [ ] Webhook signature verification
- [ ] Transaction failure handling

### Security
- [ ] Authentication middleware applied
- [ ] CSRF protection configured
- [ ] Webhook signature verification
- [ ] Input validation implemented
- [ ] Rate limiting configured

### Testing
- [ ] Unit tests for service methods
- [ ] Integration tests for controllers
- [ ] Webhook processing tests
- [ ] Error scenario testing
- [ ] Configuration validation tests

---

**Next:** Continue with [Frontend Integration Guide](./paddle-frontend.md) to implement the user interface components.
