# Paddle Payment Gateway - Frontend Integration Guide

This guide covers the complete frontend implementation of Paddle payment gateway integration, including React components, checkout flow, and user interface elements.

## 🎨 Frontend Setup

### Install Paddle.js

```bash
# Install Paddle.js SDK
npm install @paddle/paddle-js

# Install additional dependencies for React integration
npm install react-query axios
```

### Configure Vite Environment

Update `vite.config.js`:

```javascript
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';

export default defineConfig({
    plugins: [
        laravel({
            input: 'resources/js/app.jsx',
            refresh: true,
        }),
        react(),
    ],
    define: {
        'import.meta.env.VITE_PADDLE_ENVIRONMENT': JSON.stringify(process.env.PADDLE_ENVIRONMENT),
        'import.meta.env.VITE_PADDLE_CLIENT_TOKEN': JSON.stringify(process.env.PADDLE_CLIENT_TOKEN),
    },
});
```

### Add Environment Variables

Add to `.env`:

```env
# Frontend Paddle Configuration
VITE_PADDLE_ENVIRONMENT=sandbox
VITE_PADDLE_CLIENT_TOKEN=your_paddle_client_token
```

## 🔧 Core Components

### Paddle Context Provider

Create `resources/js/contexts/PaddleContext.jsx`:

```jsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { Paddle } from '@paddle/paddle-js';

const PaddleContext = createContext();

export const usePaddle = () => {
    const context = useContext(PaddleContext);
    if (!context) {
        throw new Error('usePaddle must be used within a PaddleProvider');
    }
    return context;
};

export const PaddleProvider = ({ children }) => {
    const [paddle, setPaddle] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const initializePaddle = async () => {
            try {
                const environment = import.meta.env.VITE_PADDLE_ENVIRONMENT || 'sandbox';
                const token = import.meta.env.VITE_PADDLE_CLIENT_TOKEN;

                if (!token) {
                    throw new Error('Paddle client token not configured');
                }

                const paddleInstance = new Paddle({
                    environment,
                    token,
                });

                await paddleInstance.Setup({
                    vendor: import.meta.env.VITE_PADDLE_VENDOR_ID,
                });

                setPaddle(paddleInstance);
                setError(null);
            } catch (err) {
                console.error('Failed to initialize Paddle:', err);
                setError(err.message);
            } finally {
                setIsLoading(false);
            }
        };

        initializePaddle();
    }, []);

    const value = {
        paddle,
        isLoading,
        error,
        isConfigured: !!paddle && !error,
    };

    return (
        <PaddleContext.Provider value={value}>
            {children}
        </PaddleContext.Provider>
    );
};
```

### Paddle Checkout Component

Create `resources/js/components/PaddleCheckout.jsx`:

```jsx
import React, { useState } from 'react';
import { usePaddle } from '../contexts/PaddleContext';
import { Button } from './ui/button';
import { Alert, AlertDescription } from './ui/alert';
import { Loader2 } from 'lucide-react';

const PaddleCheckout = ({ plan, billingCycle, onSuccess, onError }) => {
    const { paddle, isConfigured } = usePaddle();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleCheckout = async () => {
        if (!isConfigured) {
            setError('Paddle is not properly configured');
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            // Create checkout session via backend
            const response = await fetch('/paddle/checkout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                },
                body: JSON.stringify({
                    plan_id: plan.id,
                    billing_cycle: billingCycle,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to create checkout session');
            }

            // Open Paddle checkout
            if (data.checkout_url) {
                // Redirect to Paddle checkout
                window.location.href = data.checkout_url;
            } else {
                // Use Paddle.js for inline checkout
                paddle.Checkout.open({
                    transactionId: data.transaction_id,
                    successUrl: route('subscription.paddle.success'),
                    cancelUrl: route('subscription.paddle.cancelled'),
                });
            }

            onSuccess?.(data);
        } catch (err) {
            console.error('Checkout error:', err);
            setError(err.message);
            onError?.(err);
        } finally {
            setIsLoading(false);
        }
    };

    if (!isConfigured) {
        return (
            <Alert variant="destructive">
                <AlertDescription>
                    Payment processing is currently unavailable. Please try again later.
                </AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="space-y-4">
            {error && (
                <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            <Button
                onClick={handleCheckout}
                disabled={isLoading}
                className="w-full"
                size="lg"
            >
                {isLoading ? (
                    <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                    </>
                ) : (
                    `Subscribe to ${plan.display_name} - $${plan.price}/${billingCycle}`
                )}
            </Button>
        </div>
    );
};

export default PaddleCheckout;
```

### Payment Method Selector

Create `resources/js/components/PaymentMethodSelector.jsx`:

```jsx
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { CreditCard, FileText, Zap, Clock } from 'lucide-react';
import PaddleCheckout from './PaddleCheckout';

const PaymentMethodSelector = ({ plan, billingCycle, user }) => {
    const [selectedMethod, setSelectedMethod] = useState(null);

    const paymentMethods = [
        {
            id: 'paddle',
            name: 'Credit Card (Instant)',
            description: 'Pay with credit card and get instant access',
            icon: CreditCard,
            badge: 'Recommended',
            badgeVariant: 'default',
            features: ['Instant activation', 'Secure payment', 'Automatic renewal'],
            component: PaddleCheckout,
        },
        {
            id: 'offline',
            name: 'Offline Payment',
            description: 'Submit payment request for manual processing',
            icon: FileText,
            badge: 'Manual Review',
            badgeVariant: 'secondary',
            features: ['Bank transfer', 'Manual approval', '1-2 business days'],
            component: null, // Will redirect to payment request form
        },
    ];

    const handleMethodSelect = (method) => {
        setSelectedMethod(method);
        
        if (method.id === 'offline') {
            // Redirect to payment request form
            window.location.href = route('payment-requests.create', {
                plan: plan.name,
                billing_cycle: billingCycle,
            });
        }
    };

    const handlePaddleSuccess = (data) => {
        // Handle successful Paddle payment
        console.log('Payment successful:', data);
    };

    const handlePaddleError = (error) => {
        // Handle Paddle payment error
        console.error('Payment error:', error);
        setSelectedMethod(null);
    };

    return (
        <div className="space-y-6">
            <div className="text-center">
                <h2 className="text-2xl font-bold">Choose Payment Method</h2>
                <p className="text-muted-foreground mt-2">
                    Select how you'd like to pay for your {plan.display_name} subscription
                </p>
            </div>

            {!selectedMethod ? (
                <div className="grid gap-4 md:grid-cols-2">
                    {paymentMethods.map((method) => {
                        const IconComponent = method.icon;
                        
                        return (
                            <Card
                                key={method.id}
                                className="cursor-pointer transition-all hover:shadow-md border-2 hover:border-primary/50"
                                onClick={() => handleMethodSelect(method)}
                            >
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <IconComponent className="h-5 w-5" />
                                            <CardTitle className="text-lg">{method.name}</CardTitle>
                                        </div>
                                        <Badge variant={method.badgeVariant}>
                                            {method.badge}
                                        </Badge>
                                    </div>
                                    <CardDescription>{method.description}</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <ul className="space-y-2">
                                        {method.features.map((feature, index) => (
                                            <li key={index} className="flex items-center space-x-2 text-sm">
                                                {method.id === 'paddle' ? (
                                                    <Zap className="h-3 w-3 text-green-500" />
                                                ) : (
                                                    <Clock className="h-3 w-3 text-orange-500" />
                                                )}
                                                <span>{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                    <Button className="w-full mt-4" variant="outline">
                                        Select {method.name}
                                    </Button>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>
            ) : (
                <div className="max-w-md mx-auto">
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle>Complete Payment</CardTitle>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setSelectedMethod(null)}
                                >
                                    Change Method
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            {selectedMethod.id === 'paddle' && (
                                <PaddleCheckout
                                    plan={plan}
                                    billingCycle={billingCycle}
                                    onSuccess={handlePaddleSuccess}
                                    onError={handlePaddleError}
                                />
                            )}
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    );
};

export default PaymentMethodSelector;
```

## 📄 Page Components

### Subscription Checkout Page

Create `resources/js/pages/subscription/checkout.jsx`:

```jsx
import React from 'react';
import { Head } from '@inertiajs/react';
import AuthenticatedLayout from '../../layouts/AuthenticatedLayout';
import PaymentMethodSelector from '../../components/PaymentMethodSelector';
import { PaddleProvider } from '../../contexts/PaddleContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Check } from 'lucide-react';

const SubscriptionCheckout = ({ auth, plan, billingCycle = 'month' }) => {
    const billingCycleDisplay = billingCycle === 'month' ? 'Monthly' : 'Yearly';
    const price = billingCycle === 'month' ? plan.price : plan.yearly_price || plan.price * 12;
    const savings = billingCycle === 'year' ? Math.round(((plan.price * 12) - price) / (plan.price * 12) * 100) : 0;

    return (
        <AuthenticatedLayout user={auth.user}>
            <Head title={`Subscribe to ${plan.display_name}`} />

            <PaddleProvider>
                <div className="py-12">
                    <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                        <div className="grid gap-8 lg:grid-cols-3">
                            {/* Plan Summary */}
                            <div className="lg:col-span-1">
                                <Card>
                                    <CardHeader>
                                        <div className="flex items-center justify-between">
                                            <CardTitle>{plan.display_name}</CardTitle>
                                            {plan.is_popular && (
                                                <Badge>Most Popular</Badge>
                                            )}
                                        </div>
                                        <CardDescription>{plan.description}</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="text-center">
                                            <div className="text-3xl font-bold">
                                                ${price}
                                                <span className="text-lg font-normal text-muted-foreground">
                                                    /{billingCycle}
                                                </span>
                                            </div>
                                            {savings > 0 && (
                                                <div className="text-sm text-green-600 font-medium">
                                                    Save {savings}% with yearly billing
                                                </div>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <h4 className="font-medium">Features included:</h4>
                                            <ul className="space-y-1">
                                                {plan.features.map((feature, index) => (
                                                    <li key={index} className="flex items-center space-x-2 text-sm">
                                                        <Check className="h-4 w-4 text-green-500" />
                                                        <span>{feature}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Payment Method Selection */}
                            <div className="lg:col-span-2">
                                <PaymentMethodSelector
                                    plan={plan}
                                    billingCycle={billingCycle}
                                    user={auth.user}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </PaddleProvider>
        </AuthenticatedLayout>
    );
};

export default SubscriptionCheckout;
```

### Success Page

Create `resources/js/pages/subscription/paddle/Success.jsx`:

```jsx
import React, { useEffect } from 'react';
import { Head, Link } from '@inertiajs/react';
import AuthenticatedLayout from '../../../layouts/AuthenticatedLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { CheckCircle, ArrowRight, Home } from 'lucide-react';

const Success = ({ auth, transaction_id }) => {
    useEffect(() => {
        // Track successful payment
        if (window.gtag) {
            window.gtag('event', 'purchase', {
                transaction_id: transaction_id,
                value: auth.user.subscription_plan === 'premium' ? 29.99 : 0,
                currency: 'USD',
            });
        }
    }, [transaction_id]);

    return (
        <AuthenticatedLayout user={auth.user}>
            <Head title="Payment Successful" />

            <div className="py-12">
                <div className="max-w-2xl mx-auto sm:px-6 lg:px-8">
                    <Card className="text-center">
                        <CardHeader className="pb-4">
                            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                                <CheckCircle className="w-8 h-8 text-green-600" />
                            </div>
                            <CardTitle className="text-2xl">Payment Successful!</CardTitle>
                            <CardDescription className="text-lg">
                                Your subscription has been activated
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h3 className="font-medium text-green-800 mb-2">
                                    Welcome to Premium!
                                </h3>
                                <p className="text-green-700 text-sm">
                                    You now have unlimited access to our mobile parts database.
                                    Start exploring with no search limits!
                                </p>
                            </div>

                            {transaction_id && (
                                <div className="text-sm text-muted-foreground">
                                    Transaction ID: {transaction_id}
                                </div>
                            )}

                            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                                <Button asChild>
                                    <Link href={route('search')}>
                                        Start Searching
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button variant="outline" asChild>
                                    <Link href={route('dashboard')}>
                                        <Home className="mr-2 h-4 w-4" />
                                        Go to Dashboard
                                    </Link>
                                </Button>
                            </div>

                            <div className="text-xs text-muted-foreground">
                                A confirmation email has been sent to your email address.
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
};

export default Success;
```

### Cancelled Page

Create `resources/js/pages/subscription/paddle/Cancelled.jsx`:

```jsx
import React from 'react';
import { Head, Link } from '@inertiajs/react';
import AuthenticatedLayout from '../../../layouts/AuthenticatedLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { XCircle, ArrowLeft, RefreshCw } from 'lucide-react';

const Cancelled = ({ auth }) => {
    return (
        <AuthenticatedLayout user={auth.user}>
            <Head title="Payment Cancelled" />

            <div className="py-12">
                <div className="max-w-2xl mx-auto sm:px-6 lg:px-8">
                    <Card className="text-center">
                        <CardHeader className="pb-4">
                            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                                <XCircle className="w-8 h-8 text-red-600" />
                            </div>
                            <CardTitle className="text-2xl">Payment Cancelled</CardTitle>
                            <CardDescription className="text-lg">
                                Your payment was cancelled and no charges were made
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h3 className="font-medium text-blue-800 mb-2">
                                    No worries!
                                </h3>
                                <p className="text-blue-700 text-sm">
                                    You can try again anytime or explore our free tier features.
                                    Your account remains active with the current plan.
                                </p>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                                <Button asChild>
                                    <Link href={route('subscription.plans')}>
                                        <RefreshCw className="mr-2 h-4 w-4" />
                                        Try Again
                                    </Link>
                                </Button>
                                <Button variant="outline" asChild>
                                    <Link href={route('dashboard')}>
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Back to Dashboard
                                    </Link>
                                </Button>
                            </div>

                            <div className="text-xs text-muted-foreground">
                                Need help? Contact our support team for assistance.
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
};

export default Cancelled;
```

## 🔧 App Integration

### Update Main App Component

Update `resources/js/app.jsx`:

```jsx
import './bootstrap';
import '../css/app.css';

import { createRoot } from 'react-dom/client';
import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./pages/${name}.jsx`, import.meta.glob('./pages/**/*.jsx')),
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(<App {...props} />);
    },
    progress: {
        color: '#4F46E5',
    },
});
```

## 📋 Frontend Implementation Checklist

### Core Setup
- [ ] Paddle.js installed and configured
- [ ] Environment variables set up
- [ ] Vite configuration updated
- [ ] Context provider implemented

### Components
- [ ] Paddle checkout component created
- [ ] Payment method selector implemented
- [ ] Success page component created
- [ ] Cancelled page component created
- [ ] Error handling components added

### Integration
- [ ] Components integrated with backend API
- [ ] CSRF token handling implemented
- [ ] Route helpers configured
- [ ] Analytics tracking added

### User Experience
- [ ] Loading states implemented
- [ ] Error messages displayed
- [ ] Success feedback provided
- [ ] Mobile responsive design
- [ ] Accessibility features added

---

**Next:** Continue with [Database Schema Guide](./paddle-database.md) to understand the data structure.
