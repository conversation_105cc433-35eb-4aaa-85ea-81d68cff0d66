# Paddle Payment Gateway - Troubleshooting Guide

This guide covers common issues, error messages, and solutions for Paddle payment gateway integration.

## 🚨 Common Issues and Solutions

### 1. Paddle SDK Initialization Errors

#### Issue: "Failed to initialize Paddle SDK"
**Symptoms:**
- 500 errors on Paddle endpoints
- "Paddle is not configured" messages
- SDK initialization failures

**Causes:**
- Missing or invalid API credentials
- Incorrect environment configuration
- Network connectivity issues

**Solutions:**

```bash
# Check environment variables
php artisan tinker
>>> config('paddle.api_key');
>>> config('paddle.environment');
>>> config('paddle.client_token');

# Verify credentials in Paddle dashboard
# Regenerate API keys if necessary

# Clear configuration cache
php artisan config:clear
php artisan config:cache
```

**Prevention:**
```php
// Add validation in PaddleServiceProvider
if (empty($config['api_key'])) {
    Log::warning('Paddle API key not configured');
    return null;
}
```

---

### 2. Webhook Signature Verification Failures

#### Issue: "Invalid Paddle webhook signature"
**Symptoms:**
- Webhooks returning 400 errors
- Subscription activations not working
- Missing webhook processing

**Causes:**
- Incorrect webhook secret
- Signature verification algorithm mismatch
- Timestamp tolerance issues

**Solutions:**

```php
// Debug webhook signature
Log::info('Webhook signature debug', [
    'received_signature' => $signature,
    'payload' => $payload,
    'secret' => config('paddle.webhook_secret'),
]);

// Temporarily disable signature verification for testing
config(['paddle.webhooks.verify_signature' => false]);

// Check webhook secret in Paddle dashboard
// Ensure it matches PADDLE_WEBHOOK_SECRET
```

**Fix Implementation:**
```php
// In PaddleService::verifyWebhookSignature()
protected function verifyWebhookSignature(array $payload, string $signature): bool
{
    if (!config('paddle.webhooks.verify_signature')) {
        return true;
    }

    $secret = config('paddle.webhook_secret');
    if (!$secret) {
        Log::warning('Paddle webhook secret not configured');
        return false;
    }

    // Parse signature header
    $signatureParts = [];
    foreach (explode(',', $signature) as $part) {
        [$key, $value] = explode('=', $part, 2);
        $signatureParts[$key] = $value;
    }

    if (!isset($signatureParts['t']) || !isset($signatureParts['h1'])) {
        return false;
    }

    $timestamp = $signatureParts['t'];
    $receivedSignature = $signatureParts['h1'];

    // Check timestamp tolerance (5 minutes)
    if (abs(time() - $timestamp) > 300) {
        Log::warning('Webhook timestamp outside tolerance');
        return false;
    }

    // Verify signature
    $payload = $timestamp . ':' . json_encode($payload);
    $expectedSignature = hash_hmac('sha256', $payload, $secret);

    return hash_equals($expectedSignature, $receivedSignature);
}
```

---

### 3. Frontend Integration Issues

#### Issue: "Paddle.js not loading or initializing"
**Symptoms:**
- Checkout buttons not working
- JavaScript errors in console
- Paddle context not available

**Causes:**
- Missing client token
- Incorrect environment configuration
- Network blocking Paddle scripts

**Solutions:**

```javascript
// Debug Paddle initialization
console.log('Paddle Environment:', import.meta.env.VITE_PADDLE_ENVIRONMENT);
console.log('Paddle Token:', import.meta.env.VITE_PADDLE_CLIENT_TOKEN);

// Check network requests in browser dev tools
// Ensure Paddle scripts are loading from CDN

// Add error handling to PaddleContext
const initializePaddle = async () => {
    try {
        const paddle = new Paddle({
            environment: import.meta.env.VITE_PADDLE_ENVIRONMENT || 'sandbox',
            token: import.meta.env.VITE_PADDLE_CLIENT_TOKEN,
        });

        // Test Paddle connection
        await paddle.Setup({
            vendor: import.meta.env.VITE_PADDLE_VENDOR_ID,
        });

        setPaddle(paddle);
    } catch (error) {
        console.error('Paddle initialization failed:', error);
        setError(error.message);
    }
};
```

**Environment Variable Issues:**
```bash
# Check if variables are properly loaded
npm run dev
# Look for VITE_ prefixed variables in browser console

# Ensure .env variables are prefixed with VITE_
VITE_PADDLE_ENVIRONMENT=sandbox
VITE_PADDLE_CLIENT_TOKEN=your_token
```

---

### 4. Database Migration Issues

#### Issue: "Migration failed" or "Column already exists"
**Symptoms:**
- Migration errors during setup
- Database schema inconsistencies
- Foreign key constraint failures

**Solutions:**

```bash
# Check migration status
php artisan migrate:status

# Rollback specific migration
php artisan migrate:rollback --step=1

# Fresh migration (WARNING: destroys data)
php artisan migrate:fresh

# Check for existing columns before migration
php artisan tinker
>>> Schema::hasColumn('users', 'paddle_customer_id');
>>> Schema::hasTable('paddle_transactions');
```

**Fix Migration Conflicts:**
```php
// In migration file, check if column exists
public function up(): void
{
    Schema::table('users', function (Blueprint $table) {
        if (!Schema::hasColumn('users', 'paddle_customer_id')) {
            $table->string('paddle_customer_id')->nullable();
        }
    });
}
```

---

### 5. Payment Processing Errors

#### Issue: "Failed to create checkout session"
**Symptoms:**
- 500 errors during checkout
- Users unable to subscribe
- Missing transaction records

**Causes:**
- Invalid pricing plan configuration
- Missing Paddle price IDs
- API rate limiting

**Solutions:**

```php
// Debug checkout creation
Log::info('Checkout creation debug', [
    'user_id' => $user->id,
    'plan_id' => $plan->id,
    'billing_cycle' => $billingCycle,
    'paddle_price_id' => $plan->getPaddlePriceId($billingCycle),
]);

// Verify pricing plan configuration
$plan = PricingPlan::find($planId);
if (!$plan->hasPaddleIntegration()) {
    throw new \Exception('Plan does not have Paddle integration');
}

if (!$plan->supportsBillingCycle($billingCycle)) {
    throw new \Exception("Plan does not support {$billingCycle}ly billing");
}
```

**Fix Missing Price IDs:**
```sql
-- Update pricing plans with Paddle IDs
UPDATE pricing_plans SET 
    paddle_product_id = 'pro_01h1vjes1y163xfj1rh1tkfb65',
    paddle_price_id_monthly = 'pri_01h1vjqt2jym4f0yzrrbx6h8y4',
    paddle_price_id_yearly = 'pri_01h1vjr8x8x3f0yzrrbx6h8z5'
WHERE name = 'premium';
```

---

### 6. User Subscription Status Issues

#### Issue: "User subscription not activated after payment"
**Symptoms:**
- Payment completed but user still on free plan
- Webhook processed but subscription not created
- Database inconsistencies

**Causes:**
- Webhook processing failures
- Transaction ID mismatches
- Race conditions

**Solutions:**

```php
// Debug webhook processing
Log::info('Webhook processing debug', [
    'event_type' => $payload['event_type'],
    'transaction_id' => $payload['data']['id'],
    'customer_id' => $payload['data']['customer_id'],
]);

// Check for existing transaction
$transaction = PaddleTransaction::where('paddle_transaction_id', $transactionId)->first();
if (!$transaction) {
    Log::error('Transaction not found for webhook', ['transaction_id' => $transactionId]);
    return;
}

// Manually activate subscription
$user = User::where('paddle_customer_id', $customerId)->first();
if ($user) {
    $user->update(['subscription_plan' => 'premium']);
    
    $user->subscriptions()->create([
        'pricing_plan_id' => $plan->id,
        'plan_name' => 'premium',
        'status' => 'active',
        'current_period_start' => now(),
        'current_period_end' => now()->addMonth(),
    ]);
}
```

---

### 7. CSRF Token Issues

#### Issue: "419 Page Expired" or CSRF token mismatch
**Symptoms:**
- Form submissions failing
- AJAX requests returning 419
- Checkout process interrupted

**Solutions:**

```javascript
// Ensure CSRF token is included in requests
const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

fetch('/paddle/checkout', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': csrfToken,
    },
    body: JSON.stringify(data),
});

// For Inertia.js applications
import { router } from '@inertiajs/react';

router.post('/paddle/checkout', data, {
    onError: (errors) => {
        console.error('Request failed:', errors);
    },
});
```

**Exclude Webhook Routes:**
```php
// In app/Http/Middleware/VerifyCsrfToken.php
protected $except = [
    'webhooks/paddle',
];
```

---

### 8. Environment-Specific Issues

#### Issue: "Works in development but fails in production"
**Symptoms:**
- Different behavior between environments
- Missing environment variables
- SSL/HTTPS issues

**Solutions:**

```bash
# Check production environment variables
php artisan config:show paddle

# Verify SSL certificates for webhooks
curl -I https://yourdomain.com/webhooks/paddle

# Check production logs
tail -f storage/logs/laravel.log

# Ensure production environment is set correctly
PADDLE_ENVIRONMENT=production
APP_ENV=production
APP_DEBUG=false
```

**Production Checklist:**
- [ ] HTTPS enabled for webhook endpoints
- [ ] Production Paddle credentials configured
- [ ] Webhook URLs updated in Paddle dashboard
- [ ] SSL certificates valid
- [ ] Firewall allows Paddle webhook IPs

---

## 🔍 Debugging Tools

### Enable Debug Logging

```php
// In config/paddle.php
'logging' => [
    'enabled' => true,
    'level' => 'debug',
    'channel' => 'daily',
],

// In PaddleService methods
Log::debug('Paddle API request', [
    'method' => 'createCheckoutSession',
    'user_id' => $user->id,
    'plan_id' => $plan->id,
]);
```

### Test Webhook Locally

```bash
# Use ngrok to expose local server
ngrok http 8000

# Update webhook URL in Paddle dashboard to ngrok URL
https://abc123.ngrok.io/webhooks/paddle

# Test webhook with curl
curl -X POST https://abc123.ngrok.io/webhooks/paddle \
  -H "Content-Type: application/json" \
  -H "Paddle-Signature: test_signature" \
  -d '{"event_type": "transaction.completed", "data": {"id": "test"}}'
```

### Database Debugging

```sql
-- Check transaction records
SELECT * FROM paddle_transactions ORDER BY created_at DESC LIMIT 10;

-- Check webhook processing
SELECT * FROM paddle_webhooks WHERE status = 'failed';

-- Check user subscription status
SELECT u.id, u.email, u.subscription_plan, s.status, s.plan_name 
FROM users u 
LEFT JOIN subscriptions s ON u.id = s.user_id 
WHERE u.subscription_plan = 'premium';
```

---

## 📞 Getting Help

### 1. Check Logs First
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# Web server logs
tail -f /var/log/nginx/error.log
tail -f /var/log/apache2/error.log
```

### 2. Paddle Dashboard
- Check webhook delivery status
- Review transaction details
- Verify product and price configurations

### 3. Test in Sandbox
- Use sandbox environment for testing
- Verify webhook endpoints are accessible
- Test with sandbox payment methods

### 4. Community Resources
- [Paddle Developer Documentation](https://developer.paddle.com/)
- [Laravel Documentation](https://laravel.com/docs)
- [GitHub Issues](https://github.com/paddlehq/paddle-php-sdk/issues)

---

**Next:** Continue with [Best Practices Guide](./paddle-best-practices.md) for development guidelines and optimization tips.
