# Paddle Payment Gateway - Installation Guide

This guide walks you through the complete installation and setup of Paddle payment gateway integration in your Laravel application.

## 📋 Prerequisites

### System Requirements
- **Laravel**: 11.x or higher
- **PHP**: 8.1 or higher
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Node.js**: 18.x or higher
- **NPM/Yarn**: Latest stable version

### Paddle Account Requirements
- Active Paddle account (sandbox or production)
- API credentials configured
- Webhook endpoints set up
- Products and prices created in Paddle dashboard

## 🚀 Step 1: Install Dependencies

### Backend Dependencies
```bash
# Install Paddle PHP SDK
composer require paddlehq/paddle-php-sdk

# Install additional HTTP client dependencies (if needed)
composer require guzzlehttp/guzzle
composer require php-http/curl-client
```

### Frontend Dependencies
```bash
# Install Paddle.js for frontend integration
npm install @paddle/paddle-js

# Install additional React dependencies (if needed)
npm install react-query
npm install axios
```

## 🔧 Step 2: Environment Configuration

### Create Paddle Configuration File
```bash
# Create the configuration file
touch config/paddle.php
```

### Add Environment Variables
Add these variables to your `.env` file:

```env
# Paddle Configuration
PADDLE_ENVIRONMENT=sandbox
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_CLIENT_TOKEN=your_paddle_client_token_here
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret_here
PADDLE_VENDOR_ID=your_paddle_vendor_id_here

# Paddle URLs (automatically set based on environment)
PADDLE_SANDBOX_URL=https://sandbox-api.paddle.com
PADDLE_PRODUCTION_URL=https://api.paddle.com

# Paddle Logging
PADDLE_LOGGING_ENABLED=true
PADDLE_LOG_LEVEL=info
```

### Environment-Specific Settings

#### Sandbox Environment (.env.local)
```env
PADDLE_ENVIRONMENT=sandbox
PADDLE_API_KEY=test_api_key_sandbox
PADDLE_CLIENT_TOKEN=test_client_token_sandbox
```

#### Production Environment (.env.production)
```env
PADDLE_ENVIRONMENT=production
PADDLE_API_KEY=live_api_key_production
PADDLE_CLIENT_TOKEN=live_client_token_production
```

## 🗄️ Step 3: Database Setup

### Run Paddle Migrations
```bash
# Run all Paddle-related migrations
php artisan migrate

# Or run specific migrations
php artisan migrate --path=database/migrations/2025_06_23_162133_add_paddle_customer_id_to_users_table.php
php artisan migrate --path=database/migrations/2025_06_23_162159_add_paddle_price_ids_to_pricing_plans_table.php
php artisan migrate --path=database/migrations/2025_06_23_162221_add_paddle_subscription_id_to_subscriptions_table.php
php artisan migrate --path=database/migrations/2025_06_23_162239_create_paddle_transactions_table.php
php artisan migrate --path=database/migrations/2025_06_23_162301_create_paddle_webhooks_table.php
```

### Verify Database Schema
```bash
# Check that all tables were created
php artisan tinker
>>> Schema::hasTable('paddle_transactions');
>>> Schema::hasTable('paddle_webhooks');
>>> Schema::hasColumn('users', 'paddle_customer_id');
```

## ⚙️ Step 4: Service Provider Registration

### Register Paddle Service Provider
Add to `config/app.php`:

```php
'providers' => [
    // ... other providers
    App\Providers\PaddleServiceProvider::class,
],
```

### Register Service Aliases (Optional)
Add to `config/app.php`:

```php
'aliases' => [
    // ... other aliases
    'Paddle' => App\Facades\PaddleFacade::class,
],
```

## 🛣️ Step 5: Route Registration

### Add Paddle Routes
Routes are automatically registered by the service provider. Verify with:

```bash
# Check Paddle routes
php artisan route:list --name=paddle

# Expected output:
# POST   paddle/checkout ........................ paddle.checkout
# GET    paddle/config .......................... paddle.config
# POST   webhooks/paddle ........................ webhooks.paddle
# GET    subscription/paddle/success ............ subscription.paddle.success
# GET    subscription/paddle/cancelled .......... subscription.paddle.cancelled
```

## 🎨 Step 6: Frontend Setup

### Add Paddle.js to Your Layout
```javascript
// resources/js/app.js
import { Paddle } from '@paddle/paddle-js';

// Initialize Paddle
const paddle = new Paddle({
    environment: import.meta.env.VITE_PADDLE_ENVIRONMENT || 'sandbox',
    token: import.meta.env.VITE_PADDLE_CLIENT_TOKEN,
});

// Make Paddle available globally
window.Paddle = paddle;
```

### Add Environment Variables to Vite
```javascript
// vite.config.js
export default defineConfig({
    // ... other config
    define: {
        'import.meta.env.VITE_PADDLE_ENVIRONMENT': JSON.stringify(process.env.PADDLE_ENVIRONMENT),
        'import.meta.env.VITE_PADDLE_CLIENT_TOKEN': JSON.stringify(process.env.PADDLE_CLIENT_TOKEN),
    },
});
```

## 🧪 Step 7: Verification & Testing

### Run Installation Tests
```bash
# Test Paddle service initialization
php artisan tinker
>>> app(App\Services\PaddleService::class)->isConfigured();

# Run Paddle integration tests
php artisan test tests/Feature/PaddleIntegrationTest.php

# Expected output: All tests should pass
```

### Test Frontend Integration
```bash
# Build frontend assets
npm run build

# Start development server
npm run dev

# Visit test pages
# http://localhost:8000/subscription/plans
# http://localhost:8000/subscription/paddle/success
```

### Verify Webhook Endpoint
```bash
# Test webhook endpoint accessibility
curl -X POST http://localhost:8000/webhooks/paddle \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Should return 400 (invalid signature) - this is expected
```

## 🔍 Step 8: Configuration Validation

### Check Paddle Configuration
```bash
php artisan tinker
>>> config('paddle.api_key');
>>> config('paddle.environment');
>>> config('paddle.client_token');
```

### Test Paddle API Connection
```bash
php artisan tinker
>>> $paddle = app(App\Services\PaddleService::class);
>>> $paddle->isConfigured();
```

## 🚨 Common Installation Issues

### Issue: Paddle SDK Not Found
```bash
# Solution: Clear composer cache and reinstall
composer clear-cache
composer install --no-cache
```

### Issue: Migration Errors
```bash
# Solution: Check database connection and permissions
php artisan migrate:status
php artisan migrate:refresh --seed
```

### Issue: Frontend Build Errors
```bash
# Solution: Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install
npm run build
```

### Issue: Route Not Found
```bash
# Solution: Clear route cache
php artisan route:clear
php artisan route:cache
```

## ✅ Installation Checklist

- [ ] Paddle PHP SDK installed via Composer
- [ ] Frontend dependencies installed via NPM
- [ ] Environment variables configured
- [ ] Database migrations executed successfully
- [ ] Service provider registered
- [ ] Routes accessible and working
- [ ] Frontend assets building without errors
- [ ] Paddle service initializing correctly
- [ ] Webhook endpoint responding
- [ ] All tests passing

## 🎯 Next Steps

After successful installation:

1. **Configure Paddle Products** - Set up your products and pricing in Paddle dashboard
2. **Implement Backend Logic** - Follow the [Backend Implementation Guide](./paddle-backend.md)
3. **Create Frontend Components** - Follow the [Frontend Integration Guide](./paddle-frontend.md)
4. **Set Up Webhooks** - Configure webhook handling with the [Webhook Guide](./paddle-webhooks.md)
5. **Test Integration** - Use the [Testing Guide](./paddle-testing.md) for comprehensive testing

---

**Next:** Continue with [Configuration Guide](./paddle-configuration.md) to set up your Paddle dashboard and environment.
