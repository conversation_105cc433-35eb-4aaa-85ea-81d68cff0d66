# Paddle Payment Gateway - Database Schema Guide

This guide covers the complete database schema for Paddle payment gateway integration, including migrations, model relationships, and data structure.

## 🗄️ Database Migrations

### 1. Add Paddle Customer ID to Users Table

Create migration: `2025_06_23_162133_add_paddle_customer_id_to_users_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('paddle_customer_id')->nullable()->after('email_verified_at');
            $table->index('paddle_customer_id');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['paddle_customer_id']);
            $table->dropColumn('paddle_customer_id');
        });
    }
};
```

### 2. Add Paddle Price IDs to Pricing Plans Table

Create migration: `2025_06_23_162159_add_paddle_price_ids_to_pricing_plans_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->string('paddle_price_id_monthly')->nullable()->after('sort_order');
            $table->string('paddle_price_id_yearly')->nullable()->after('paddle_price_id_monthly');
            $table->string('paddle_product_id')->nullable()->after('paddle_price_id_yearly');
            
            $table->index('paddle_price_id_monthly');
            $table->index('paddle_price_id_yearly');
            $table->index('paddle_product_id');
        });
    }

    public function down(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->dropIndex(['paddle_price_id_monthly']);
            $table->dropIndex(['paddle_price_id_yearly']);
            $table->dropIndex(['paddle_product_id']);
            
            $table->dropColumn([
                'paddle_price_id_monthly',
                'paddle_price_id_yearly',
                'paddle_product_id'
            ]);
        });
    }
};
```

### 3. Add Paddle Subscription ID to Subscriptions Table

Create migration: `2025_06_23_162221_add_paddle_subscription_id_to_subscriptions_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->string('paddle_subscription_id')->nullable()->after('id');
            $table->index('paddle_subscription_id');
        });
    }

    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex(['paddle_subscription_id']);
            $table->dropColumn('paddle_subscription_id');
        });
    }
};
```

### 4. Create Paddle Transactions Table

Create migration: `2025_06_23_162239_create_paddle_transactions_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('paddle_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('pricing_plan_id')->constrained()->onDelete('cascade');
            $table->string('paddle_transaction_id')->unique();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3);
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded']);
            $table->enum('billing_cycle', ['month', 'year']);
            $table->json('paddle_data')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['paddle_transaction_id']);
            $table->index(['status', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('paddle_transactions');
    }
};
```

### 5. Create Paddle Webhooks Table

Create migration: `2025_06_23_162301_create_paddle_webhooks_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('paddle_webhooks', function (Blueprint $table) {
            $table->id();
            $table->string('event_type');
            $table->string('paddle_event_id')->unique();
            $table->json('payload');
            $table->text('signature');
            $table->enum('status', ['pending', 'processed', 'failed'])->default('pending');
            $table->text('error_message')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['event_type', 'status']);
            $table->index(['paddle_event_id']);
            $table->index(['status', 'created_at']);
            $table->index(['retry_count', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('paddle_webhooks');
    }
};
```

## 🏗️ Model Definitions

### PaddleTransaction Model

Create `app/Models/PaddleTransaction.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaddleTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'pricing_plan_id',
        'paddle_transaction_id',
        'amount',
        'currency',
        'status',
        'billing_cycle',
        'paddle_data',
        'completed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paddle_data' => 'array',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the pricing plan for the transaction.
     */
    public function pricingPlan(): BelongsTo
    {
        return $this->belongsTo(PricingPlan::class);
    }

    /**
     * Check if transaction is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if transaction is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if transaction failed.
     */
    public function isFailed(): bool
    {
        return in_array($this->status, ['failed', 'cancelled']);
    }

    /**
     * Mark transaction as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Get formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->amount, 2);
    }

    /**
     * Scope for completed transactions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending transactions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for failed transactions.
     */
    public function scopeFailed($query)
    {
        return $query->whereIn('status', ['failed', 'cancelled']);
    }
}
```

### PaddleWebhook Model

Create `app/Models/PaddleWebhook.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaddleWebhook extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_type',
        'paddle_event_id',
        'payload',
        'signature',
        'status',
        'error_message',
        'retry_count',
        'processed_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'processed_at' => 'datetime',
    ];

    /**
     * Check if webhook is processed.
     */
    public function isProcessed(): bool
    {
        return $this->status === 'processed';
    }

    /**
     * Check if webhook is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if webhook failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Mark webhook as processed.
     */
    public function markAsProcessed(): void
    {
        $this->update([
            'status' => 'processed',
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark webhook as failed.
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Increment retry count.
     */
    public function incrementRetryCount(): void
    {
        $this->increment('retry_count');
    }

    /**
     * Check if webhook can be retried.
     */
    public function canRetry(): bool
    {
        return $this->retry_count < 5 && $this->status === 'failed';
    }

    /**
     * Scope for pending webhooks.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for failed webhooks that can be retried.
     */
    public function scopeRetryable($query)
    {
        return $query->where('status', 'failed')
                    ->where('retry_count', '<', 5);
    }

    /**
     * Scope for specific event types.
     */
    public function scopeEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }
}
```

## 🔗 Model Relationships

### Update User Model

Add to `app/Models/User.php`:

```php
/**
 * Get the user's Paddle transactions.
 */
public function paddleTransactions(): HasMany
{
    return $this->hasMany(PaddleTransaction::class);
}

/**
 * Get the user's latest Paddle transaction.
 */
public function latestPaddleTransaction(): HasOne
{
    return $this->hasOne(PaddleTransaction::class)->latestOfMany();
}

/**
 * Get the user's completed Paddle transactions.
 */
public function completedPaddleTransactions(): HasMany
{
    return $this->paddleTransactions()->completed();
}

/**
 * Check if user has any completed Paddle transactions.
 */
public function hasPaddleTransactions(): bool
{
    return $this->completedPaddleTransactions()->exists();
}
```

### Update PricingPlan Model

Add to `app/Models/PricingPlan.php`:

```php
/**
 * Get the Paddle transactions for this plan.
 */
public function paddleTransactions(): HasMany
{
    return $this->hasMany(PaddleTransaction::class);
}

/**
 * Check if plan has Paddle integration.
 */
public function hasPaddleIntegration(): bool
{
    return !empty($this->paddle_product_id);
}

/**
 * Check if plan supports a billing cycle.
 */
public function supportsBillingCycle(string $cycle): bool
{
    return match ($cycle) {
        'month' => !empty($this->paddle_price_id_monthly),
        'year' => !empty($this->paddle_price_id_yearly),
        default => false,
    };
}

/**
 * Get Paddle price ID for billing cycle.
 */
public function getPaddlePriceId(string $cycle): ?string
{
    return match ($cycle) {
        'month' => $this->paddle_price_id_monthly,
        'year' => $this->paddle_price_id_yearly,
        default => null,
    };
}

/**
 * Get all supported billing cycles.
 */
public function getSupportedBillingCycles(): array
{
    $cycles = [];
    
    if ($this->paddle_price_id_monthly) {
        $cycles[] = 'month';
    }
    
    if ($this->paddle_price_id_yearly) {
        $cycles[] = 'year';
    }
    
    return $cycles;
}
```

### Update Subscription Model

Add to `app/Models/Subscription.php`:

```php
/**
 * Get the Paddle transaction that created this subscription.
 */
public function paddleTransaction(): HasOne
{
    return $this->hasOne(PaddleTransaction::class, 'user_id', 'user_id')
                ->where('pricing_plan_id', $this->pricing_plan_id)
                ->completed()
                ->latest();
}

/**
 * Check if subscription was created via Paddle.
 */
public function isPaddleSubscription(): bool
{
    return !empty($this->paddle_subscription_id);
}
```

## 📊 Database Indexes and Performance

### Key Indexes for Performance

```sql
-- Users table indexes
CREATE INDEX idx_users_paddle_customer_id ON users(paddle_customer_id);

-- Pricing plans table indexes
CREATE INDEX idx_pricing_plans_paddle_product_id ON pricing_plans(paddle_product_id);
CREATE INDEX idx_pricing_plans_paddle_price_monthly ON pricing_plans(paddle_price_id_monthly);
CREATE INDEX idx_pricing_plans_paddle_price_yearly ON pricing_plans(paddle_price_id_yearly);

-- Subscriptions table indexes
CREATE INDEX idx_subscriptions_paddle_subscription_id ON subscriptions(paddle_subscription_id);

-- Paddle transactions table indexes
CREATE INDEX idx_paddle_transactions_user_status ON paddle_transactions(user_id, status);
CREATE INDEX idx_paddle_transactions_paddle_id ON paddle_transactions(paddle_transaction_id);
CREATE INDEX idx_paddle_transactions_status_created ON paddle_transactions(status, created_at);

-- Paddle webhooks table indexes
CREATE INDEX idx_paddle_webhooks_event_status ON paddle_webhooks(event_type, status);
CREATE INDEX idx_paddle_webhooks_paddle_event_id ON paddle_webhooks(paddle_event_id);
CREATE INDEX idx_paddle_webhooks_status_created ON paddle_webhooks(status, created_at);
CREATE INDEX idx_paddle_webhooks_retry_status ON paddle_webhooks(retry_count, status);
```

## 🔍 Database Queries and Examples

### Common Query Patterns

```php
// Get user's Paddle transactions
$transactions = User::find(1)->paddleTransactions()
    ->with('pricingPlan')
    ->completed()
    ->orderBy('created_at', 'desc')
    ->get();

// Get pending webhooks for processing
$pendingWebhooks = PaddleWebhook::pending()
    ->orderBy('created_at')
    ->limit(100)
    ->get();

// Get failed webhooks that can be retried
$retryableWebhooks = PaddleWebhook::retryable()
    ->where('created_at', '>', now()->subHours(24))
    ->get();

// Get pricing plans with Paddle integration
$paddlePlans = PricingPlan::whereNotNull('paddle_product_id')
    ->where('is_active', true)
    ->get();

// Get revenue statistics
$revenue = PaddleTransaction::completed()
    ->whereBetween('completed_at', [now()->startOfMonth(), now()->endOfMonth()])
    ->sum('amount');
```

## 📋 Database Schema Checklist

### Migrations
- [ ] Users table updated with paddle_customer_id
- [ ] Pricing plans table updated with Paddle IDs
- [ ] Subscriptions table updated with paddle_subscription_id
- [ ] Paddle transactions table created
- [ ] Paddle webhooks table created
- [ ] All indexes created for performance

### Models
- [ ] PaddleTransaction model created with relationships
- [ ] PaddleWebhook model created with status methods
- [ ] User model updated with Paddle relationships
- [ ] PricingPlan model updated with Paddle methods
- [ ] Subscription model updated with Paddle integration

### Data Integrity
- [ ] Foreign key constraints added
- [ ] Unique constraints on Paddle IDs
- [ ] Proper column types and sizes
- [ ] JSON columns for flexible data storage
- [ ] Timestamp columns for audit trails

---

**Next:** Continue with [Testing Guide](./paddle-testing.md) to implement comprehensive testing strategies.
