# Paddle Payment Gateway - Testing Guide

This guide covers comprehensive testing strategies for Paddle payment gateway integration, including unit tests, integration tests, and end-to-end testing.

## 🧪 Testing Setup

### Test Environment Configuration

Create `.env.testing`:

```env
# Database
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Paddle Test Configuration
PADDLE_ENVIRONMENT=sandbox
PADDLE_API_KEY=test_api_key
PADDLE_CLIENT_TOKEN=test_client_token
PADDLE_WEBHOOK_SECRET=test_webhook_secret
PADDLE_VENDOR_ID=test_vendor_id

# Disable external services in tests
PADDLE_LOGGING_ENABLED=false
PADDLE_VERIFY_WEBHOOK_SIGNATURE=false

# Mail
MAIL_MAILER=array

# Queue
QUEUE_CONNECTION=sync
```

### PHPUnit Configuration

Update `phpunit.xml`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
        <testsuite name="Paddle">
            <directory>tests/Feature/Paddle</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <env name="PADDLE_ENVIRONMENT" value="sandbox"/>
        <env name="PADDLE_VERIFY_WEBHOOK_SIGNATURE" value="false"/>
    </php>
</phpunit>
```

## 🔧 Unit Tests

### PaddleService Unit Tests

Create `tests/Unit/PaddleServiceTest.php`:

```php
<?php

namespace Tests\Unit;

use App\Models\PricingPlan;
use App\Models\User;
use App\Services\PaddleService;
use Paddle\SDK\Client;
use Paddle\SDK\Entities\Customer;
use Paddle\SDK\Resources\Customers\CustomersClient;
use PHPUnit\Framework\TestCase;
use Mockery;

class PaddleServiceTest extends TestCase
{
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_service_handles_null_client(): void
    {
        $service = new PaddleService(null);
        
        $this->assertFalse($service->isConfigured());
        $this->assertNull($service->getPrice('test_price_id'));
        $this->assertEmpty($service->getPricesForProduct('test_product_id'));
    }

    public function test_service_with_valid_client(): void
    {
        $client = Mockery::mock(Client::class);
        $service = new PaddleService($client);
        
        // Mock config
        config(['paddle.api_key' => 'test_key', 'paddle.client_token' => 'test_token']);
        
        $this->assertTrue($service->isConfigured());
    }

    public function test_get_or_create_customer_with_existing_customer(): void
    {
        $client = Mockery::mock(Client::class);
        $customersClient = Mockery::mock(CustomersClient::class);
        $customer = Mockery::mock(Customer::class);
        
        $client->customers = $customersClient;
        
        $user = new User([
            'id' => 1,
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'paddle_customer_id' => 'cus_test_123'
        ]);

        $customersClient->shouldReceive('get')
            ->with('cus_test_123')
            ->once()
            ->andReturn($customer);

        $service = new PaddleService($client);
        $result = $service->getOrCreateCustomer($user);

        $this->assertSame($customer, $result);
    }

    public function test_verify_webhook_signature_disabled(): void
    {
        config(['paddle.webhooks.verify_signature' => false]);
        
        $service = new PaddleService(null);
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('verifyWebhookSignature');
        $method->setAccessible(true);

        $result = $method->invoke($service, ['test' => 'data'], 'invalid_signature');
        
        $this->assertTrue($result);
    }

    public function test_verify_webhook_signature_no_secret(): void
    {
        config(['paddle.webhooks.verify_signature' => true, 'paddle.webhook_secret' => null]);
        
        $service = new PaddleService(null);
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('verifyWebhookSignature');
        $method->setAccessible(true);

        $result = $method->invoke($service, ['test' => 'data'], 'signature');
        
        $this->assertFalse($result);
    }
}
```

### PaddleTransaction Model Tests

Create `tests/Unit/PaddleTransactionTest.php`:

```php
<?php

namespace Tests\Unit;

use App\Models\PaddleTransaction;
use App\Models\PricingPlan;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaddleTransactionTest extends TestCase
{
    use RefreshDatabase;

    public function test_transaction_belongs_to_user(): void
    {
        $user = User::factory()->create();
        $plan = PricingPlan::factory()->create();
        
        $transaction = PaddleTransaction::factory()->create([
            'user_id' => $user->id,
            'pricing_plan_id' => $plan->id,
        ]);

        $this->assertInstanceOf(User::class, $transaction->user);
        $this->assertEquals($user->id, $transaction->user->id);
    }

    public function test_transaction_belongs_to_pricing_plan(): void
    {
        $user = User::factory()->create();
        $plan = PricingPlan::factory()->create();
        
        $transaction = PaddleTransaction::factory()->create([
            'user_id' => $user->id,
            'pricing_plan_id' => $plan->id,
        ]);

        $this->assertInstanceOf(PricingPlan::class, $transaction->pricingPlan);
        $this->assertEquals($plan->id, $transaction->pricingPlan->id);
    }

    public function test_transaction_status_methods(): void
    {
        $transaction = PaddleTransaction::factory()->create(['status' => 'completed']);
        $this->assertTrue($transaction->isCompleted());
        $this->assertFalse($transaction->isPending());
        $this->assertFalse($transaction->isFailed());

        $transaction->status = 'pending';
        $this->assertFalse($transaction->isCompleted());
        $this->assertTrue($transaction->isPending());
        $this->assertFalse($transaction->isFailed());

        $transaction->status = 'failed';
        $this->assertFalse($transaction->isCompleted());
        $this->assertFalse($transaction->isPending());
        $this->assertTrue($transaction->isFailed());
    }

    public function test_mark_as_completed(): void
    {
        $transaction = PaddleTransaction::factory()->create(['status' => 'pending']);
        
        $transaction->markAsCompleted();
        
        $this->assertEquals('completed', $transaction->status);
        $this->assertNotNull($transaction->completed_at);
    }

    public function test_formatted_amount_attribute(): void
    {
        $transaction = PaddleTransaction::factory()->create([
            'amount' => 29.99,
            'currency' => 'USD'
        ]);

        $this->assertEquals('USD 29.99', $transaction->formatted_amount);
    }

    public function test_scopes(): void
    {
        PaddleTransaction::factory()->create(['status' => 'completed']);
        PaddleTransaction::factory()->create(['status' => 'pending']);
        PaddleTransaction::factory()->create(['status' => 'failed']);

        $this->assertEquals(1, PaddleTransaction::completed()->count());
        $this->assertEquals(1, PaddleTransaction::pending()->count());
        $this->assertEquals(1, PaddleTransaction::failed()->count());
    }
}
```

## 🔗 Integration Tests

### Paddle Controller Tests

Create `tests/Feature/Paddle/PaddleControllerTest.php`:

```php
<?php

namespace Tests\Feature\Paddle;

use App\Models\PricingPlan;
use App\Models\User;
use App\Services\PaddleService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class PaddleControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test pricing plan
        PricingPlan::factory()->create([
            'name' => 'premium',
            'paddle_product_id' => 'pro_test_123',
            'paddle_price_id_monthly' => 'pri_test_monthly',
            'paddle_price_id_yearly' => 'pri_test_yearly',
        ]);
    }

    public function test_config_endpoint_when_paddle_not_configured(): void
    {
        $user = User::factory()->create();
        
        // Mock PaddleService to return false for isConfigured
        $this->mock(PaddleService::class, function ($mock) {
            $mock->shouldReceive('isConfigured')->andReturn(false);
        });

        $response = $this->actingAs($user)->get(route('paddle.config'));

        $response->assertStatus(503);
        $response->assertJson(['error' => 'Paddle is not configured']);
    }

    public function test_config_endpoint_when_paddle_configured(): void
    {
        $user = User::factory()->create();
        
        config([
            'paddle.environment' => 'sandbox',
            'paddle.client_token' => 'test_token',
            'paddle.vendor_id' => 'test_vendor',
        ]);

        $this->mock(PaddleService::class, function ($mock) {
            $mock->shouldReceive('isConfigured')->andReturn(true);
        });

        $response = $this->actingAs($user)->get(route('paddle.config'));

        $response->assertStatus(200);
        $response->assertJson([
            'environment' => 'sandbox',
            'client_token' => 'test_token',
            'vendor_id' => 'test_vendor',
        ]);
    }

    public function test_checkout_requires_authentication(): void
    {
        $plan = PricingPlan::where('name', 'premium')->first();

        $response = $this->postJson(route('paddle.checkout'), [
            'plan_id' => $plan->id,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(401);
    }

    public function test_checkout_validates_input(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->postJson(route('paddle.checkout'), [
            // Missing required fields
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['plan_id', 'billing_cycle']);
    }

    public function test_checkout_rejects_premium_users(): void
    {
        $user = User::factory()->create(['subscription_plan' => 'premium']);
        $plan = PricingPlan::where('name', 'premium')->first();

        // Create active subscription
        $user->subscriptions()->create([
            'pricing_plan_id' => $plan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
        ]);

        $response = $this->actingAs($user)->postJson(route('paddle.checkout'), [
            'plan_id' => $plan->id,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(400);
        $response->assertJson(['error' => 'You already have an active premium subscription.']);
    }

    public function test_checkout_creates_session_successfully(): void
    {
        $user = User::factory()->create();
        $plan = PricingPlan::where('name', 'premium')->first();

        $this->mock(PaddleService::class, function ($mock) {
            $mock->shouldReceive('createCheckoutSession')
                ->once()
                ->andReturn([
                    'checkout_url' => 'https://checkout.paddle.com/test',
                    'transaction_id' => 'txn_test_123',
                ]);
        });

        $response = $this->actingAs($user)->postJson(route('paddle.checkout'), [
            'plan_id' => $plan->id,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'checkout_url' => 'https://checkout.paddle.com/test',
            'transaction_id' => 'txn_test_123',
        ]);
    }

    public function test_webhook_requires_signature(): void
    {
        $response = $this->postJson(route('webhooks.paddle'), [
            'event_type' => 'transaction.completed',
            'data' => ['id' => 'txn_test_123'],
        ]);

        $response->assertStatus(400);
        $response->assertJson(['error' => 'Missing signature']);
    }

    public function test_webhook_processes_valid_payload(): void
    {
        $this->mock(PaddleService::class, function ($mock) {
            $mock->shouldReceive('processWebhook')
                ->once()
                ->andReturn(true);
        });

        $response = $this->postJson(route('webhooks.paddle'), [
            'event_type' => 'transaction.completed',
            'data' => ['id' => 'txn_test_123'],
        ], [
            'Paddle-Signature' => 'valid_signature',
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }

    public function test_success_page_loads(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('subscription.paddle.success'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('subscription/paddle/Success')
        );
    }

    public function test_cancelled_page_loads(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('subscription.paddle.cancelled'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('subscription/paddle/Cancelled')
        );
    }
}
```

### Webhook Processing Tests

Create `tests/Feature/Paddle/WebhookProcessingTest.php`:

```php
<?php

namespace Tests\Feature\Paddle;

use App\Models\PaddleTransaction;
use App\Models\PaddleWebhook;
use App\Models\PricingPlan;
use App\Models\User;
use App\Services\PaddleService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WebhookProcessingTest extends TestCase
{
    use RefreshDatabase;

    public function test_webhook_creates_record(): void
    {
        $payload = [
            'event_type' => 'transaction.completed',
            'event_id' => 'evt_test_123',
            'data' => ['id' => 'txn_test_123'],
        ];

        $service = app(PaddleService::class);
        $result = $service->processWebhook($payload, 'test_signature');

        $this->assertTrue($result);
        $this->assertDatabaseHas('paddle_webhooks', [
            'event_type' => 'transaction.completed',
            'paddle_event_id' => 'evt_test_123',
        ]);
    }

    public function test_transaction_completed_webhook_activates_subscription(): void
    {
        $user = User::factory()->create();
        $plan = PricingPlan::factory()->create(['name' => 'premium']);
        
        $transaction = PaddleTransaction::factory()->create([
            'user_id' => $user->id,
            'pricing_plan_id' => $plan->id,
            'paddle_transaction_id' => 'txn_test_123',
            'status' => 'pending',
        ]);

        $payload = [
            'event_type' => 'transaction.completed',
            'event_id' => 'evt_test_123',
            'data' => [
                'id' => 'txn_test_123',
                'status' => 'completed',
            ],
        ];

        $webhook = PaddleWebhook::create([
            'event_type' => 'transaction.completed',
            'paddle_event_id' => 'evt_test_123',
            'payload' => $payload,
            'signature' => 'test_signature',
            'status' => 'pending',
        ]);

        $service = app(PaddleService::class);
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('handleWebhookEvent');
        $method->setAccessible(true);
        $method->invoke($service, $webhook);

        $transaction->refresh();
        $user->refresh();

        $this->assertEquals('completed', $transaction->status);
        $this->assertEquals('premium', $user->subscription_plan);
        $this->assertTrue($user->subscriptions()->where('plan_name', 'premium')->exists());
    }

    public function test_webhook_retry_mechanism(): void
    {
        $webhook = PaddleWebhook::factory()->create([
            'status' => 'failed',
            'retry_count' => 2,
        ]);

        $this->assertTrue($webhook->canRetry());

        $webhook->update(['retry_count' => 5]);
        $this->assertFalse($webhook->canRetry());
    }
}
```

## 🎭 Test Factories

### PaddleTransaction Factory

Create `database/factories/PaddleTransactionFactory.php`:

```php
<?php

namespace Database\Factories;

use App\Models\PaddleTransaction;
use App\Models\PricingPlan;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaddleTransactionFactory extends Factory
{
    protected $model = PaddleTransaction::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'pricing_plan_id' => PricingPlan::factory(),
            'paddle_transaction_id' => 'txn_' . $this->faker->uuid(),
            'amount' => $this->faker->randomFloat(2, 10, 100),
            'currency' => 'USD',
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed']),
            'billing_cycle' => $this->faker->randomElement(['month', 'year']),
            'paddle_data' => [
                'id' => 'txn_' . $this->faker->uuid(),
                'status' => 'completed',
            ],
        ];
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'completed_at' => null,
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'completed_at' => null,
        ]);
    }
}
```

### PaddleWebhook Factory

Create `database/factories/PaddleWebhookFactory.php`:

```php
<?php

namespace Database\Factories;

use App\Models\PaddleWebhook;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaddleWebhookFactory extends Factory
{
    protected $model = PaddleWebhook::class;

    public function definition(): array
    {
        return [
            'event_type' => $this->faker->randomElement([
                'transaction.completed',
                'subscription.created',
                'subscription.updated',
                'subscription.cancelled',
            ]),
            'paddle_event_id' => 'evt_' . $this->faker->uuid(),
            'payload' => [
                'event_type' => 'transaction.completed',
                'event_id' => 'evt_' . $this->faker->uuid(),
                'data' => ['id' => 'txn_' . $this->faker->uuid()],
            ],
            'signature' => $this->faker->sha256(),
            'status' => 'pending',
            'retry_count' => 0,
        ];
    }

    public function processed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'processed',
            'processed_at' => now(),
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'error_message' => 'Test error message',
        ]);
    }
}
```

## 🚀 Running Tests

### Test Commands

```bash
# Run all tests
php artisan test

# Run only Paddle tests
php artisan test tests/Feature/Paddle/

# Run with coverage
php artisan test --coverage

# Run specific test class
php artisan test tests/Feature/Paddle/PaddleControllerTest.php

# Run specific test method
php artisan test --filter test_checkout_creates_session_successfully

# Run tests in parallel
php artisan test --parallel

# Run tests with verbose output
php artisan test --verbose
```

### Continuous Integration

Create `.github/workflows/tests.yml`:

```yaml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: yes
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
        coverage: xdebug

    - name: Install dependencies
      run: |
        composer install --no-progress --prefer-dist --optimize-autoloader
        npm ci

    - name: Copy environment file
      run: cp .env.testing .env

    - name: Generate key
      run: php artisan key:generate

    - name: Run migrations
      run: php artisan migrate

    - name: Build frontend
      run: npm run build

    - name: Run tests
      run: php artisan test --coverage --min=80
```

## 📋 Testing Checklist

### Unit Tests
- [ ] PaddleService methods tested with mocks
- [ ] Model relationships and methods tested
- [ ] Webhook signature verification tested
- [ ] Error handling scenarios covered

### Integration Tests
- [ ] Controller endpoints tested
- [ ] Authentication and authorization tested
- [ ] Input validation tested
- [ ] Database interactions tested

### Feature Tests
- [ ] Complete checkout flow tested
- [ ] Webhook processing tested
- [ ] Success and error scenarios covered
- [ ] User experience flows tested

### Performance Tests
- [ ] Database query optimization tested
- [ ] API response times measured
- [ ] Memory usage monitored
- [ ] Concurrent request handling tested

---

**Next:** Continue with [Webhook Handling Guide](./paddle-webhooks.md) to implement secure webhook processing.
