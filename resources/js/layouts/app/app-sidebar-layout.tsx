import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppSidebarHeader } from '@/components/app-sidebar-header';
import { GlobalSearchCommand } from '@/components/global-search-command';
import { MobileQuickActions } from '@/components/mobile-quick-actions';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';

export default function AppSidebarLayout({ children, breadcrumbs = [] }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    const page = usePage<SharedData>();
    const { auth } = page.props;
    const isAdmin = auth.user?.email === '<EMAIL>' || auth.user?.email === '<EMAIL>';

    return (
        <>
            <ImpersonationBanner />
            <AppShell variant="sidebar">
                <AppSidebar />
                <AppContent variant="sidebar" className="overflow-x-hidden">
                    <AppSidebarHeader breadcrumbs={breadcrumbs} />
                    {children}
                </AppContent>
            </AppShell>
            <GlobalSearchCommand isAdmin={isAdmin} />
            <MobileQuickActions />
        </>
    );
}
