import { AppContent } from '@/components/app-content';
import { AppHeader } from '@/components/app-header';
import { AppShell } from '@/components/app-shell';
import { GlobalSearchCommand } from '@/components/global-search-command';
import { MobileQuickActions } from '@/components/mobile-quick-actions';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import type { PropsWithChildren } from 'react';

export default function AppHeaderLayout({ children, breadcrumbs }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    const page = usePage<SharedData>();
    const { auth } = page.props;
    const isAdmin = auth.user?.email === '<EMAIL>' || auth.user?.email === '<EMAIL>';

    return (
        <>
            <ImpersonationBanner />
            <AppShell>
                <AppHeader breadcrumbs={breadcrumbs} />
                <AppContent>{children}</AppContent>
            </AppShell>
            <GlobalSearchCommand isAdmin={isAdmin} />
            <MobileQuickActions />
        </>
    );
}
