import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Bug, 
    RefreshCw, 
    CheckCircle, 
    XCircle, 
    AlertTriangle,
    Copy,
    Eye,
    EyeOff
} from 'lucide-react';
import { getCsrfToken, validateCsrfToken, refreshCsrfToken, fixCsrfIssues, testCsrfToken } from '@/utils/checkout-helpers';
import { toast } from 'sonner';

interface CheckoutDebugPanelProps {
    plan: {
        id: number;
        name: string;
        supports_online_payment: boolean;
        has_paddle_integration: boolean;
        has_shurjopay_integration: boolean;
    };
}

export function CheckoutDebugPanel({ plan }: CheckoutDebugPanelProps) {
    const [isVisible, setIsVisible] = useState(false);
    const [csrfToken, setCsrfToken] = useState('');
    const [isValidToken, setIsValidToken] = useState(false);
    const [debugInfo, setDebugInfo] = useState<any>({});
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Only show in development
    if (process.env.NODE_ENV !== 'development') {
        return null;
    }

    useEffect(() => {
        updateDebugInfo();
    }, []);

    const updateDebugInfo = () => {
        const token = getCsrfToken();
        const isValid = validateCsrfToken(token);
        
        setCsrfToken(token);
        setIsValidToken(isValid);
        
        setDebugInfo({
            csrf_token_length: token.length,
            csrf_token_preview: token.substring(0, 20) + '...',
            csrf_token_valid: isValid,
            user_agent: navigator.userAgent,
            current_url: window.location.href,
            session_storage_keys: Object.keys(sessionStorage),
            local_storage_keys: Object.keys(localStorage),
            plan_info: {
                id: plan.id,
                name: plan.name,
                supports_online_payment: plan.supports_online_payment,
                has_paddle_integration: plan.has_paddle_integration,
                has_shurjopay_integration: plan.has_shurjopay_integration,
            },
            meta_tags: Array.from(document.querySelectorAll('meta')).map(meta => ({
                name: meta.getAttribute('name'),
                content: meta.getAttribute('content')?.substring(0, 50) + '...'
            })).filter(meta => meta.name),
            timestamp: new Date().toISOString(),
        });
    };

    const handleRefreshToken = async () => {
        setIsRefreshing(true);
        try {
            const newToken = await refreshCsrfToken();
            toast.success('CSRF token refreshed successfully');
            updateDebugInfo();
        } catch (error) {
            toast.error('Failed to refresh CSRF token');
            console.error('Token refresh error:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleFixCsrf = async () => {
        setIsRefreshing(true);
        try {
            const newToken = await fixCsrfIssues();
            toast.success('CSRF issues fixed successfully');
            updateDebugInfo();
        } catch (error) {
            toast.error('Failed to fix CSRF issues');
            console.error('CSRF fix error:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        toast.success('Copied to clipboard');
    };

    const handleTestCsrf = async () => {
        setIsRefreshing(true);
        try {
            const isValid = await testCsrfToken();
            if (isValid) {
                toast.success('CSRF token test passed');
            } else {
                toast.error('CSRF token test failed');
            }
            updateDebugInfo();
        } catch (error) {
            toast.error('CSRF token test error');
            console.error('CSRF test error:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const testCheckoutEndpoint = async (endpoint: string) => {
        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify({
                    plan_id: plan.id,
                    billing_cycle: 'month',
                }),
            });

            const result = {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
                url: response.url,
                ok: response.ok
            };

            console.log(`Test ${endpoint} result:`, result);
            toast.success(`Test completed. Check console for details.`);
        } catch (error) {
            console.error(`Test ${endpoint} error:`, error);
            toast.error(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    };

    if (!isVisible) {
        return (
            <div className="fixed bottom-4 right-4 z-50">
                <Button
                    onClick={() => setIsVisible(true)}
                    variant="outline"
                    size="sm"
                    className="bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100"
                >
                    <Bug className="h-4 w-4 mr-2" />
                    Debug
                </Button>
            </div>
        );
    }

    return (
        <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-y-auto">
            <Card className="bg-yellow-50 border-yellow-200">
                <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Bug className="h-4 w-4 text-yellow-600" />
                            <CardTitle className="text-sm text-yellow-800">Checkout Debug</CardTitle>
                        </div>
                        <Button
                            onClick={() => setIsVisible(false)}
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                        >
                            <EyeOff className="h-4 w-4" />
                        </Button>
                    </div>
                    <CardDescription className="text-xs text-yellow-700">
                        Development debugging panel
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3 text-xs">
                    {/* CSRF Token Status */}
                    <div className="space-y-2">
                        <div className="flex items-center justify-between">
                            <span className="font-medium">CSRF Token</span>
                            <Badge variant={isValidToken ? "default" : "destructive"} className="text-xs">
                                {isValidToken ? (
                                    <><CheckCircle className="h-3 w-3 mr-1" /> Valid</>
                                ) : (
                                    <><XCircle className="h-3 w-3 mr-1" /> Invalid</>
                                )}
                            </Badge>
                        </div>
                        <div className="flex gap-1">
                            <Button
                                onClick={() => copyToClipboard(csrfToken)}
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs"
                            >
                                <Copy className="h-3 w-3 mr-1" />
                                Copy
                            </Button>
                            <Button
                                onClick={handleRefreshToken}
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs"
                                disabled={isRefreshing}
                            >
                                <RefreshCw className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                                Refresh
                            </Button>
                            <Button
                                onClick={handleFixCsrf}
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs"
                                disabled={isRefreshing}
                            >
                                <CheckCircle className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                                Fix
                            </Button>
                            <Button
                                onClick={handleTestCsrf}
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs"
                                disabled={isRefreshing}
                            >
                                <AlertTriangle className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                                Test
                            </Button>
                            <Button
                                onClick={updateDebugInfo}
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs"
                            >
                                <Eye className="h-3 w-3 mr-1" />
                                Update
                            </Button>
                        </div>
                    </div>

                    {/* Test Endpoints */}
                    <div className="space-y-2">
                        <span className="font-medium">Test Endpoints</span>
                        <div className="flex gap-1">
                            <Button
                                onClick={() => testCheckoutEndpoint('/paddle/checkout')}
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs"
                            >
                                Test Paddle
                            </Button>
                            <Button
                                onClick={() => testCheckoutEndpoint('/shurjopay/checkout')}
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs"
                            >
                                Test ShurjoPay
                            </Button>
                        </div>
                    </div>

                    {/* Debug Info */}
                    <div className="space-y-1">
                        <span className="font-medium">Debug Info</span>
                        <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
                            {JSON.stringify(debugInfo, null, 2)}
                        </pre>
                    </div>

                    {/* Warnings */}
                    {!isValidToken && (
                        <Alert className="border-red-200 bg-red-50">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                            <AlertDescription className="text-xs text-red-700">
                                Invalid CSRF token detected. Try refreshing the token.
                            </AlertDescription>
                        </Alert>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
