import { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
    Shield, 
    Mail, 
    Clock, 
    AlertTriangle,
    CheckCircle,
    RefreshCw
} from 'lucide-react';

interface OtpVerificationModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
    action: string;
    actionData: Record<string, unknown>;
    endpoint: string;
    method?: 'post' | 'put' | 'patch' | 'delete';
}

interface ValidationErrors {
    message?: string;
    otp_code?: string;
    error?: string;
    requires_otp?: boolean;
    action?: string;
    remaining_attempts?: number;
    lockout_remaining_minutes?: number;
    [key: string]: string | string[] | boolean | number | undefined;
}

interface OtpError {
    error: string;
    message: string;
    requires_otp: boolean;
    action: string;
    remaining_attempts?: number;
    lockout_remaining_minutes?: number;
}

export default function OtpVerificationModal({
    isOpen,
    onClose,
    onSuccess,
    action,
    actionData,
    endpoint,
    method = 'post'
}: OtpVerificationModalProps) {
    const [otpCode, setOtpCode] = useState('');
    const [isVerifying, setIsVerifying] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [remainingAttempts, setRemainingAttempts] = useState<number | null>(null);
    const [lockoutMinutes, setLockoutMinutes] = useState<number | null>(null);
    const [isResendingOtp, setIsResendingOtp] = useState(false);

    // Reset state when modal opens/closes
    useEffect(() => {
        if (isOpen) {
            setOtpCode('');
            setError(null);
            setRemainingAttempts(null);
            setLockoutMinutes(null);
        }
    }, [isOpen]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (otpCode.length !== 6) {
            setError('Please enter a 6-digit verification code');
            return;
        }

        setIsVerifying(true);
        setError(null);

        // Add OTP code to the action data
        const dataWithOtp = {
            ...actionData,
            otp_code: otpCode
        };

        router[method](endpoint, dataWithOtp, {
            onSuccess: () => {
                setIsVerifying(false);
                onSuccess();
                onClose();
            },
            onError: (errors: ValidationErrors) => {
                setIsVerifying(false);

                // Handle OTP-specific errors
                if (errors.error && errors.requires_otp) {
                    const otpError = errors as OtpError;
                    setError(otpError.message);
                    setRemainingAttempts(otpError.remaining_attempts || null);
                    setLockoutMinutes(otpError.lockout_remaining_minutes || null);
                } else {
                    // Handle other validation errors
                    const errorMessage = typeof errors === 'string' 
                        ? errors 
                        : errors.otp_code || errors.message || 'An error occurred';
                    setError(errorMessage);
                }
            }
        });
    };

    const handleResendOtp = () => {
        setIsResendingOtp(true);
        setError(null);

        router.post('/admin/two-factor/send-test', { action }, {
            onSuccess: () => {
                setIsResendingOtp(false);
                setError(null);
            },
            onError: (errors: ValidationErrors) => {
                setIsResendingOtp(false);
                setError(errors.message || 'Failed to resend verification code');
            }
        });
    };

    const formatActionName = (action: string) => {
        return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    };

    const handleOtpInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/\D/g, '').slice(0, 6);
        setOtpCode(value);
        setError(null);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Shield className="h-5 w-5 text-blue-600" />
                        Two-Factor Authentication Required
                    </DialogTitle>
                    <DialogDescription>
                        Please enter the verification code sent to your email to proceed with this action.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    {/* Action Info */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="flex items-center gap-2 text-blue-800">
                            <Shield className="h-4 w-4" />
                            <span className="font-medium">Action:</span>
                            <Badge variant="outline" className="border-blue-300 text-blue-700">
                                {formatActionName(action)}
                            </Badge>
                        </div>
                    </div>

                    {/* Lockout Warning */}
                    {lockoutMinutes && lockoutMinutes > 0 && (
                        <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                Account temporarily locked. Try again in {lockoutMinutes} minutes.
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Error Message */}
                    {error && !lockoutMinutes && (
                        <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                {error}
                                {remainingAttempts !== null && (
                                    <div className="mt-1 text-sm">
                                        {remainingAttempts} attempts remaining
                                    </div>
                                )}
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* OTP Form */}
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="otp_code" className="flex items-center gap-2">
                                <Mail className="h-4 w-4" />
                                Verification Code
                            </Label>
                            <Input
                                id="otp_code"
                                type="text"
                                value={otpCode}
                                onChange={handleOtpInputChange}
                                placeholder="000000"
                                maxLength={6}
                                className="text-center text-lg tracking-widest font-mono"
                                disabled={isVerifying || Boolean(lockoutMinutes && lockoutMinutes > 0)}
                                autoComplete="one-time-code"
                                autoFocus
                            />
                            <p className="text-xs text-muted-foreground text-center">
                                Enter the 6-digit code sent to your email
                            </p>
                        </div>

                        <div className="flex gap-2">
                            <Button
                                type="submit"
                                disabled={otpCode.length !== 6 || isVerifying || Boolean(lockoutMinutes && lockoutMinutes > 0)}
                                className="flex-1"
                            >
                                {isVerifying ? (
                                    <>
                                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                        Verifying...
                                    </>
                                ) : (
                                    <>
                                        <CheckCircle className="h-4 w-4 mr-2" />
                                        Verify & Proceed
                                    </>
                                )}
                            </Button>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onClose}
                                disabled={isVerifying}
                            >
                                Cancel
                            </Button>
                        </div>
                    </form>

                    {/* Resend OTP */}
                    <div className="text-center">
                        <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={handleResendOtp}
                            disabled={isResendingOtp || Boolean(lockoutMinutes && lockoutMinutes > 0)}
                            className="text-muted-foreground hover:text-foreground"
                        >
                            {isResendingOtp ? (
                                <>
                                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                                    Sending...
                                </>
                            ) : (
                                <>
                                    <Mail className="h-3 w-3 mr-1" />
                                    Didn't receive code? Resend
                                </>
                            )}
                        </Button>
                    </div>

                    {/* Help Text */}
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                        <div className="flex items-start gap-2 text-sm text-gray-600">
                            <Clock className="h-4 w-4 mt-0.5 flex-shrink-0" />
                            <div>
                                <p className="font-medium">Security Notice:</p>
                                <ul className="mt-1 space-y-1 text-xs">
                                    <li>• Verification codes expire in 10 minutes</li>
                                    <li>• You have 3 attempts before temporary lockout</li>
                                    <li>• Check your spam folder if you don't see the email</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
