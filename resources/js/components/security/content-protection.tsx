import { useEffect, useRef } from 'react';

interface ContentProtectionProps {
    children: React.ReactNode;
    enableScreenshotPrevention?: boolean;
    enableRightClickDisable?: boolean;
    enableTextSelection?: boolean;
    enableDevToolsDetection?: boolean;
}

export default function ContentProtection({
    children,
    enableScreenshotPrevention = true,
    enableRightClickDisable = true,
    enableTextSelection = false,
    enableDevToolsDetection = true,
}: ContentProtectionProps) {
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        // Screenshot prevention
        if (enableScreenshotPrevention) {
            // Add CSS to prevent screenshots on mobile
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (container.style as any).webkitUserSelect = 'none';
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (container.style as any).webkitTouchCallout = 'none';
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (container.style as any).webkitTapHighlightColor = 'transparent';
            
            // Prevent print screen
            const handleKeyDown = (e: KeyboardEvent) => {
                // Prevent Print Screen
                if (e.key === 'PrintScreen') {
                    e.preventDefault();
                    console.warn('Screenshot attempt detected');
                    return false;
                }
                
                // Prevent Ctrl+P (Print)
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    console.warn('Print attempt detected');
                    return false;
                }
                
                // Prevent F12 (Dev Tools)
                if (enableDevToolsDetection && e.key === 'F12') {
                    e.preventDefault();
                    console.warn('Developer tools access attempt detected');
                    return false;
                }
                
                // Prevent Ctrl+Shift+I (Dev Tools)
                if (enableDevToolsDetection && e.ctrlKey && e.shiftKey && e.key === 'I') {
                    e.preventDefault();
                    console.warn('Developer tools access attempt detected');
                    return false;
                }
                
                // Prevent Ctrl+U (View Source)
                if (e.ctrlKey && e.key === 'u') {
                    e.preventDefault();
                    console.warn('View source attempt detected');
                    return false;
                }
            };

            document.addEventListener('keydown', handleKeyDown);

            // Cleanup
            return () => {
                document.removeEventListener('keydown', handleKeyDown);
            };
        }
    }, [enableScreenshotPrevention, enableDevToolsDetection]);

    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        // Right-click prevention
        if (enableRightClickDisable) {
            const handleContextMenu = (e: MouseEvent) => {
                e.preventDefault();
                console.warn('Right-click attempt detected');
                return false;
            };

            container.addEventListener('contextmenu', handleContextMenu);

            return () => {
                container.removeEventListener('contextmenu', handleContextMenu);
            };
        }
    }, [enableRightClickDisable]);

    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        // Text selection prevention
        if (!enableTextSelection) {
            container.style.userSelect = 'none';
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (container.style as any).webkitUserSelect = 'none';
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (container.style as any).mozUserSelect = 'none';
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (container.style as any).msUserSelect = 'none';

            const handleSelectStart = (e: Event) => {
                e.preventDefault();
                return false;
            };

            container.addEventListener('selectstart', handleSelectStart);

            return () => {
                container.removeEventListener('selectstart', handleSelectStart);
            };
        }
    }, [enableTextSelection]);

    useEffect(() => {
        // Developer tools detection
        if (enableDevToolsDetection) {
            const devtools = { open: false, orientation: null };
            
            const threshold = 160;
            
            setInterval(() => {
                if (
                    window.outerHeight - window.innerHeight > threshold ||
                    window.outerWidth - window.innerWidth > threshold
                ) {
                    if (!devtools.open) {
                        devtools.open = true;
                        console.warn('Developer tools opened');
                        // You could dispatch an event here to notify the backend
                        // or take other security measures
                    }
                } else {
                    devtools.open = false;
                }
            }, 500);

            // Detect console usage
            let consoleWarned = false;
            const originalConsole = console.log;
            console.log = function(...args) {
                if (!consoleWarned) {
                    console.warn('Console usage detected');
                    consoleWarned = true;
                }
                originalConsole.apply(console, args);
            };
        }
    }, [enableDevToolsDetection]);

    // Add watermark overlay for premium content
    const addWatermark = () => {
        return (
            <div 
                className="absolute inset-0 pointer-events-none z-10 opacity-5"
                style={{
                    // cSpell:ignore Csvg Ctext
                    backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200'%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='16' fill='%23000' text-anchor='middle' dominant-baseline='middle' transform='rotate(-45 100 100)'%3EMobile Parts DB%3C/text%3E%3C/svg%3E")`,
                    backgroundRepeat: 'repeat',
                    backgroundSize: '200px 200px',
                }}
            />
        );
    };

    return (
        <div 
            ref={containerRef}
            className="relative"
            style={{
                // Additional CSS protection
                WebkitUserSelect: enableTextSelection ? 'text' : 'none',
                userSelect: enableTextSelection ? 'text' : 'none',
                WebkitTouchCallout: 'none',
                WebkitTapHighlightColor: 'transparent',
            } as React.CSSProperties}
        >
            {children}
            {enableScreenshotPrevention && addWatermark()}
        </div>
    );
}

// Hook for content protection
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function useContentProtection(_options: Partial<ContentProtectionProps> = {}) {
    useEffect(() => {
        // Disable drag and drop
        const handleDragStart = (e: DragEvent) => {
            e.preventDefault();
            return false;
        };

        // Disable image saving
        const handleDragOver = (e: DragEvent) => {
            e.preventDefault();
            return false;
        };

        document.addEventListener('dragstart', handleDragStart);
        document.addEventListener('dragover', handleDragOver);

        // Blur content when window loses focus (screenshot prevention)
        const handleVisibilityChange = () => {
            if (document.hidden) {
                document.body.style.filter = 'blur(5px)';
            } else {
                document.body.style.filter = 'none';
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);

        return () => {
            document.removeEventListener('dragstart', handleDragStart);
            document.removeEventListener('dragover', handleDragOver);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            document.body.style.filter = 'none';
        };
    }, []);
}

// Component for protecting specific content areas
export function ProtectedContent({ 
    children, 
    className = '',
    ...props 
}: ContentProtectionProps & { className?: string }) {
    useContentProtection(props);
    
    return (
        <ContentProtection {...props}>
            <div className={`protected-content ${className}`}>
                {children}
            </div>
        </ContentProtection>
    );
}
