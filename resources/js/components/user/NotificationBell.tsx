import { useState, useEffect } from 'react';
import { Link, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
    Bell, 
    BellOff,
    Info, 
    AlertTriangle, 
    CheckCircle2, 
    XCircle,
    Megaphone,
    Eye
} from 'lucide-react';

interface UserNotification {
    id: number;
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success' | 'error' | 'announcement';
    read_at: string | null;
    created_at: string;
    sentBy?: {
        id: number;
        name: string;
    };
}

interface NotificationBellProps {
    className?: string;
}

const getNotificationIcon = (type: string, size = 'w-4 h-4') => {
    switch (type) {
        case 'info':
            return <Info className={`${size} text-blue-500`} />;
        case 'warning':
            return <AlertTriangle className={`${size} text-yellow-500`} />;
        case 'success':
            return <CheckCircle2 className={`${size} text-green-500`} />;
        case 'error':
            return <XCircle className={`${size} text-red-500`} />;
        case 'announcement':
            return <Megaphone className={`${size} text-purple-500`} />;
        default:
            return <Bell className={`${size} text-gray-500`} />;
    }
};

export default function NotificationBell({ className = '' }: NotificationBellProps) {
    const [unreadCount, setUnreadCount] = useState(0);
    const [recentNotifications, setRecentNotifications] = useState<UserNotification[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        fetchNotificationData();
        
        // Set up polling for real-time updates
        const interval = setInterval(fetchNotificationData, 30000); // Poll every 30 seconds
        
        return () => clearInterval(interval);
    }, []);

    const fetchNotificationData = async () => {
        try {
            // Fetch unread count
            const countResponse = await fetch(route('notifications.unread-count'));

            // Check if response is ok and not a redirect
            if (!countResponse.ok) {
                if (countResponse.status === 401 || countResponse.status === 403) {
                    // User is not authenticated, silently fail
                    setUnreadCount(0);
                    setRecentNotifications([]);
                    return;
                }
                throw new Error(`HTTP error! status: ${countResponse.status}`);
            }

            const countData = await countResponse.json();
            setUnreadCount(countData.count || 0);

            // Fetch recent notifications
            const recentResponse = await fetch(route('notifications.recent'));
            if (recentResponse.ok) {
                const recentData = await recentResponse.json();
                setRecentNotifications(recentData.notifications || []);
            }
        } catch (error) {
            console.error('Failed to fetch notification data:', error);
            // Set default values on error
            setUnreadCount(0);
            setRecentNotifications([]);
        } finally {
            setIsLoading(false);
        }
    };

    const markAsRead = async (notificationId: number) => {
        try {
            await router.post(route('notifications.mark-read', notificationId), {}, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    // Update local state
                    setUnreadCount(prev => Math.max(0, prev - 1));
                    setRecentNotifications(prev => 
                        prev.map(notification => 
                            notification.id === notificationId 
                                ? { ...notification, read_at: new Date().toISOString() }
                                : notification
                        )
                    );
                }
            });
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
        }
    };

    const formatTimeAgo = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes}m ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours}h ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days}d ago`;
        }
    };

    const truncateMessage = (message: string, maxLength = 60) => {
        return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className={`relative ${className}`}>
                    {unreadCount > 0 ? (
                        <Bell className="w-5 h-5" />
                    ) : (
                        <BellOff className="w-5 h-5" />
                    )}
                    {unreadCount > 0 && (
                        <Badge 
                            variant="destructive" 
                            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                        >
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </Badge>
                    )}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel className="flex items-center justify-between">
                    <span>Notifications</span>
                    {unreadCount > 0 && (
                        <Badge variant="secondary" className="text-xs">
                            {unreadCount} unread
                        </Badge>
                    )}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                {isLoading ? (
                    <div className="p-4 text-center text-sm text-gray-500">
                        Loading notifications...
                    </div>
                ) : recentNotifications.length > 0 ? (
                    <>
                        {recentNotifications.map((notification) => (
                            <DropdownMenuItem 
                                key={notification.id} 
                                className={`p-3 cursor-pointer ${!notification.read_at ? 'bg-blue-50' : ''}`}
                                onClick={() => router.get(route('notifications.show', notification.id))}
                            >
                                <div className="flex items-start space-x-3 w-full">
                                    {getNotificationIcon(notification.type)}
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between mb-1">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {notification.title}
                                            </p>
                                            {!notification.read_at && (
                                                <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 flex-shrink-0"></div>
                                            )}
                                        </div>
                                        <p className="text-xs text-gray-600 mb-1">
                                            {truncateMessage(notification.message)}
                                        </p>
                                        <div className="flex items-center justify-between">
                                            <span className="text-xs text-gray-500">
                                                {formatTimeAgo(notification.created_at)}
                                            </span>
                                            {!notification.read_at && (
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-6 w-6 p-0"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        markAsRead(notification.id);
                                                    }}
                                                >
                                                    <Eye className="w-3 h-3" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </DropdownMenuItem>
                        ))}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                            <Link href={route('notifications.index')} className="w-full text-center">
                                <Button variant="ghost" className="w-full">
                                    View All Notifications
                                </Button>
                            </Link>
                        </DropdownMenuItem>
                    </>
                ) : (
                    <div className="p-4 text-center">
                        <BellOff className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-500 mb-2">No notifications</p>
                        <Link href={route('notifications.index')}>
                            <Button variant="ghost" size="sm">
                                View All
                            </Button>
                        </Link>
                    </div>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
