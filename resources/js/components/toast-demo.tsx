import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';
import { Trash2, CheckCircle, XCircle, Info, AlertTriangle } from 'lucide-react';

export default function ToastDemo() {
    const { showDeleteConfirmation } = useDeleteConfirmation();

    const showSuccessToast = () => {
        toast.success('Operation completed successfully!', {
            description: 'Your changes have been saved.',
        });
    };

    const showErrorToast = () => {
        toast.error('Something went wrong!', {
            description: 'Please try again or contact support.',
        });
    };

    const showInfoToast = () => {
        toast.info('Information', {
            description: 'This is an informational message.',
        });
    };

    const showWarningToast = () => {
        toast.warning('Warning', {
            description: 'Please review your input before proceeding.',
        });
    };

    const showDeleteConfirmationDemo = () => {
        showDeleteConfirmation({
            title: 'Delete Demo Item?',
            description: 'This is a demo of the centered delete confirmation dialog. No actual deletion will occur. The modal should appear perfectly centered on your screen.',
            onConfirm: () => {
                toast.success('Demo item deleted successfully!', {
                    description: 'The delete confirmation worked perfectly!'
                });
            },
            onCancel: () => {
                toast.info('Delete cancelled', {
                    description: 'No changes were made.'
                });
            }
        });
    };

    return (
        <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Toast Notifications Demo
                </CardTitle>
                <CardDescription>
                    Test the different types of toast notifications used throughout the application
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <Button 
                        onClick={showSuccessToast}
                        className="flex items-center gap-2"
                        variant="default"
                    >
                        <CheckCircle className="h-4 w-4" />
                        Success Toast
                    </Button>

                    <Button 
                        onClick={showErrorToast}
                        className="flex items-center gap-2"
                        variant="destructive"
                    >
                        <XCircle className="h-4 w-4" />
                        Error Toast
                    </Button>

                    <Button 
                        onClick={showInfoToast}
                        className="flex items-center gap-2"
                        variant="outline"
                    >
                        <Info className="h-4 w-4" />
                        Info Toast
                    </Button>

                    <Button 
                        onClick={showWarningToast}
                        className="flex items-center gap-2"
                        variant="secondary"
                    >
                        <AlertTriangle className="h-4 w-4" />
                        Warning Toast
                    </Button>
                </div>

                <div className="pt-4 border-t">
                    <Button 
                        onClick={showDeleteConfirmationDemo}
                        className="w-full flex items-center gap-2"
                        variant="destructive"
                    >
                        <Trash2 className="h-4 w-4" />
                        Delete Confirmation (Center Modal)
                    </Button>
                    <p className="text-sm text-muted-foreground mt-2 text-center">
                        This will show the centered delete confirmation modal
                    </p>
                </div>
            </CardContent>
        </Card>
    );
}
