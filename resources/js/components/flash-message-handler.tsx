import { useEffect } from 'react';
import { usePage } from '@inertiajs/react';
import { toast } from 'sonner';

interface FlashMessages {
    success?: string;
    error?: string;
    warning?: string;
    info?: string;
    message?: string;
}

interface PageProps {
    flash?: FlashMessages;
    [key: string]: unknown;
}

export default function FlashMessageHandler() {
    const { flash } = usePage<PageProps>().props;

    useEffect(() => {
        if (!flash) return;

        // Handle different types of flash messages
        if (flash.success) {
            toast.success(flash.success);
        }

        if (flash.error) {
            toast.error(flash.error);
        }

        if (flash.warning) {
            toast.warning(flash.warning);
        }

        if (flash.info) {
            toast.info(flash.info);
        }

        if (flash.message) {
            toast.info(flash.message);
        }
    }, [flash]);

    return null; // This component doesn't render anything
}
