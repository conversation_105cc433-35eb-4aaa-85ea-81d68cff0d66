import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import MediaPicker from '../MediaPicker'

// Mock fetch
global.fetch = vi.fn()

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

// Mock CSRF token
Object.defineProperty(document, 'querySelector', {
  value: vi.fn().mockReturnValue({
    getAttribute: vi.fn().mockReturnValue('mock-csrf-token'),
  }),
})

const mockMedia = [
  {
    id: 1,
    filename: 'test-image.jpg',
    original_filename: 'test-image.jpg',
    mime_type: 'image/jpeg',
    size: 1024000,
    path: '/storage/media/test-image.jpg',
    alt_text: 'Test image',
    title: 'Test Image',
    description: 'A test image',
    width: 800,
    height: 600,
    url: 'http://localhost:8000/storage/media/test-image.jpg',
    formatted_size: '1.0 MB',
    created_at: '2024-01-01T00:00:00.000000Z',
  },
  {
    id: 2,
    filename: 'document.pdf',
    original_filename: 'document.pdf',
    mime_type: 'application/pdf',
    size: 512000,
    path: '/storage/media/document.pdf',
    alt_text: null,
    title: 'Document',
    description: null,
    width: null,
    height: null,
    url: 'http://localhost:8000/storage/media/document.pdf',
    formatted_size: '512 KB',
    created_at: '2024-01-01T00:00:00.000000Z',
  },
]

const mockApiResponse = {
  data: mockMedia,
  current_page: 1,
  last_page: 1,
  per_page: 24,
  total: 2,
}

describe('MediaPicker', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    onSelect: vi.fn(),
    multiple: false,
    selectedIds: [],
    title: 'Choose Media',
    acceptedTypes: ['image/*'],
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock successful API response
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockApiResponse),
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it('renders dialog when open', async () => {
    render(<MediaPicker {...defaultProps} />)
    
    expect(screen.getByText('Choose Media')).toBeInTheDocument()
    expect(screen.getByText('Select a file from your media library or upload new ones')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<MediaPicker {...defaultProps} isOpen={false} />)
    
    expect(screen.queryByText('Choose Media')).not.toBeInTheDocument()
  })

  it('loads media when opened', async () => {
    render(<MediaPicker {...defaultProps} />)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/admin/media/select')
      )
    })
  })

  it('displays media items in grid', async () => {
    render(<MediaPicker {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByAltText('Test image')).toBeInTheDocument()
    })
  })

  it('handles media selection', async () => {
    render(<MediaPicker {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByAltText('Test image')).toBeInTheDocument()
    })
    
    fireEvent.click(screen.getByAltText('Test image'))
    
    expect(defaultProps.onSelect).toHaveBeenCalledWith([mockMedia[0]])
  })

  it('handles multiple selection', async () => {
    render(<MediaPicker {...defaultProps} multiple={true} />)
    
    await waitFor(() => {
      expect(screen.getByAltText('Test image')).toBeInTheDocument()
    })
    
    // Select first item
    fireEvent.click(screen.getByAltText('Test image'))
    
    // Select second item (PDF)
    const pdfElement = screen.getByText('document.pdf')
    fireEvent.click(pdfElement.closest('[data-testid="media-item"]') || pdfElement)
    
    expect(defaultProps.onSelect).toHaveBeenCalledWith(
      expect.arrayContaining([mockMedia[0], mockMedia[1]])
    )
  })

  it('handles search functionality', async () => {
    render(<MediaPicker {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search media...')
    fireEvent.change(searchInput, { target: { value: 'test' } })
    
    const searchButton = screen.getByText('Search')
    fireEvent.click(searchButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('search=test')
      )
    })
  })

  it('switches between tabs', () => {
    render(<MediaPicker {...defaultProps} />)
    
    const uploadTab = screen.getByText('Upload Files')
    fireEvent.click(uploadTab)
    
    expect(screen.getByText('Select files from your computer to upload')).toBeInTheDocument()
  })

  it('handles file upload', async () => {
    // Mock successful upload response
    ;(global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ message: 'Files uploaded successfully' }),
    })
    
    render(<MediaPicker {...defaultProps} />)
    
    // Switch to upload tab
    const uploadTab = screen.getByText('Upload Files')
    fireEvent.click(uploadTab)
    
    // Mock file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
    
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    })
    
    fireEvent.change(fileInput)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/admin/media',
        expect.objectContaining({
          method: 'POST',
          body: expect.any(FormData),
        })
      )
    })
  })

  it('displays loading state', () => {
    render(<MediaPicker {...defaultProps} />)
    
    // Should show loading spinner initially
    expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument()
  })

  it('displays empty state when no media found', async () => {
    // Mock empty response
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        data: [],
        current_page: 1,
        last_page: 1,
        per_page: 24,
        total: 0,
      }),
    })
    
    render(<MediaPicker {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('No media files found')).toBeInTheDocument()
    })
  })

  it('handles API errors gracefully', async () => {
    // Mock API error
    ;(global.fetch as any).mockRejectedValue(new Error('API Error'))
    
    render(<MediaPicker {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('No media files found')).toBeInTheDocument()
    })
  })

  it('calls onClose when close button is clicked', () => {
    render(<MediaPicker {...defaultProps} />)
    
    const closeButton = screen.getByRole('button', { name: /close/i })
    fireEvent.click(closeButton)
    
    expect(defaultProps.onClose).toHaveBeenCalled()
  })

  it('shows selected count for multiple selection', async () => {
    render(<MediaPicker {...defaultProps} multiple={true} selectedIds={[1]} />)
    
    await waitFor(() => {
      expect(screen.getByText('1 selected')).toBeInTheDocument()
    })
  })

  it('pre-selects media based on selectedIds prop', async () => {
    render(<MediaPicker {...defaultProps} selectedIds={[1]} />)
    
    await waitFor(() => {
      const selectedItem = screen.getByAltText('Test image').closest('[class*="border-blue-500"]')
      expect(selectedItem).toBeInTheDocument()
    })
  })
})
