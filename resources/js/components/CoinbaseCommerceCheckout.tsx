import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
    Bitcoin,
    Check,
    Loader2,
    AlertTriangle,
    Shield,
    Zap,
    Globe,
    ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';
import { createCheckoutRequestWithRetry, logCheckoutError } from '@/utils/checkout-helpers';
import { PricingPlan } from '@/types';

interface CoinbaseCommerceCheckoutProps {
    plan: PricingPlan;
    billingCycle: 'month' | 'year';
    onSuccess?: (transactionId: string) => void;
    onError?: (error: string) => void;
    disabled?: boolean;
}

export function CoinbaseCommerceCheckout({ 
    plan, 
    billingCycle,  
    onError, 
    disabled = false 
}: CoinbaseCommerceCheckoutProps) {
    const [isLoading, setIsLoading] = useState(false);

    // Development logging utility
    const logDebug = (message: string, data?: any) => {
        if (process.env.NODE_ENV === 'development') {
            const timestamp = new Date().toISOString();
            const logMessage = `[${timestamp}] Coinbase Commerce Checkout: ${message}`;
            console.log(logMessage, data || '');
        }
    };

    const handleCheckout = async () => {
        if (!plan.crypto_payment_enabled) {
            const errorMsg = 'Crypto payments are not available for this plan';
            logDebug('Checkout failed: Crypto payments not enabled', { plan_id: plan.id });
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        if (!plan.has_coinbase_commerce_integration) {
            const errorMsg = 'Coinbase Commerce integration not configured for this plan';
            logDebug('Checkout failed: No Coinbase Commerce integration', { plan_id: plan.id });
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        if (!plan.supports_monthly && billingCycle === 'month') {
            const errorMsg = 'This plan does not support monthly billing';
            logDebug('Checkout failed: Monthly billing not supported', { plan_id: plan.id });
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        if (!plan.supports_yearly && billingCycle === 'year') {
            const errorMsg = 'This plan does not support yearly billing';
            logDebug('Checkout failed: Yearly billing not supported', { plan_id: plan.id });
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        logDebug('Starting Coinbase Commerce checkout', {
            plan_id: plan.id,
            billing_cycle: billingCycle,
            plan_name: plan.name
        });

        setIsLoading(true);

        try {
            // Use the utility function for checkout request with retry logic
            const data = await createCheckoutRequestWithRetry(
                route('coinbase-commerce.charge'),
                plan.id,
                billingCycle,
                'Coinbase Commerce'
            );

            logDebug('Checkout request successful', {
                charge_id: data.charge_id,
                transaction_id: data.transaction_id
            });

            // Redirect to Coinbase Commerce hosted checkout
            if (data.hosted_url) {
                logDebug('Redirecting to Coinbase Commerce', { hosted_url: data.hosted_url });
                window.location.href = data.hosted_url;
            } else {
                throw new Error('No hosted checkout URL received from Coinbase Commerce');
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
            logCheckoutError('Coinbase Commerce', error);
            logDebug('Checkout failed', { error: errorMessage });
            toast.error(errorMessage);
            onError?.(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    const getSupportedCryptocurrencies = () => {
        return [
            { name: 'Bitcoin', symbol: 'BTC', icon: '₿' },
            { name: 'Ethereum', symbol: 'ETH', icon: 'Ξ' },
            { name: 'USD Coin', symbol: 'USDC', icon: '$' },
            { name: 'Dai', symbol: 'DAI', icon: '◈' },
            { name: 'Litecoin', symbol: 'LTC', icon: 'Ł' },
            { name: 'Bitcoin Cash', symbol: 'BCH', icon: '₿' },
        ];
    };

    const getOnchainFeatures = () => {
        return [
            {
                icon: <Shield className="h-4 w-4 text-orange-600" />,
                title: 'Auto USDC Settlement',
                description: 'Avoid volatility with guaranteed settlement'
            },
            {
                icon: <Globe className="h-4 w-4 text-blue-600" />,
                title: 'Hundreds of Currencies',
                description: 'Accept payments in any supported crypto'
            },
            {
                icon: <Zap className="h-4 w-4 text-green-600" />,
                title: 'Instant Confirmation',
                description: 'Low-cost transactions on Base & Polygon'
            }
        ];
    };

    return (
        <Card className="w-full max-w-md mx-auto border-2 border-orange-200 dark:border-orange-800 bg-gradient-to-br from-orange-50/30 to-amber-50/20 dark:from-orange-950/20 dark:to-amber-950/10 shadow-lg hover:shadow-xl transition-all duration-200">
            <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-center mb-3">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center shadow-lg ring-2 ring-orange-200 dark:ring-orange-800">
                        <Bitcoin className="w-6 h-6 text-white" />
                    </div>
                </div>
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent dark:from-orange-400 dark:to-amber-400">
                    Coinbase Commerce
                </CardTitle>
                <CardDescription className="text-sm text-muted-foreground">
                    Pay with cryptocurrency using Onchain Payment Protocol
                </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
                {/* Plan Details */}
                <div className="bg-white/50 dark:bg-gray-900/30 rounded-lg p-3 border border-orange-100 dark:border-orange-900">
                    <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Plan:</span>
                        <span className="text-sm font-semibold text-orange-700 dark:text-orange-300">{plan.display_name}</span>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Billing:</span>
                        <span className="text-sm font-semibold text-orange-700 dark:text-orange-300">
                            {billingCycle === 'year' ? 'Yearly' : 'Monthly'}
                        </span>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Amount:</span>
                        <span className="text-lg font-bold text-orange-700 dark:text-orange-300">{plan.formatted_price}</span>
                    </div>
                </div>

                {/* Supported Cryptocurrencies */}
                <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center">
                        <Globe className="h-4 w-4 mr-2 text-orange-600" />
                        Supported Cryptocurrencies
                    </h4>
                    <div className="grid grid-cols-3 gap-2">
                        {getSupportedCryptocurrencies().map((crypto) => (
                            <div key={crypto.symbol} className="flex items-center space-x-1 text-xs">
                                <span className="text-orange-600 font-mono">{crypto.icon}</span>
                                <span className="text-gray-600 dark:text-gray-400">{crypto.symbol}</span>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Onchain Protocol Features */}
                <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center">
                        <Zap className="h-4 w-4 mr-2 text-orange-600" />
                        Onchain Protocol Features
                    </h4>
                    <div className="space-y-2">
                        {getOnchainFeatures().map((feature, index) => (
                            <div key={index} className="flex items-start space-x-2">
                                {feature.icon}
                                <div className="flex-1">
                                    <p className="text-xs font-medium text-gray-700 dark:text-gray-300">{feature.title}</p>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">{feature.description}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Checkout Button */}
                <div className="pt-4 border-t border-orange-100 dark:border-orange-900">
                    <Button
                        onClick={handleCheckout}
                        disabled={disabled || isLoading || !plan.crypto_payment_enabled}
                        className="w-full bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white shadow-md"
                        size="lg"
                    >
                        {isLoading ? (
                            <>
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Creating Charge...
                            </>
                        ) : (
                            <>
                                <Bitcoin className="h-4 w-4 mr-2" />
                                Pay with Crypto
                            </>
                        )}
                    </Button>

                    <div className="mt-3 text-center">
                        <p className="text-xs text-gray-500 flex items-center justify-center">
                            <Shield className="h-3 w-3 mr-1" />
                            Secure crypto payments powered by Coinbase Commerce
                        </p>
                        <p className="text-xs text-gray-500 mt-1 flex items-center justify-center">
                            <ExternalLink className="h-3 w-3 mr-1" />
                            Onchain Payment Protocol with auto USDC settlement
                        </p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
