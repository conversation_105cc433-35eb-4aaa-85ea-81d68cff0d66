import React from 'react';
import { ImageIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImagePlaceholderProps {
    className?: string;
    width?: number;
    height?: number;
    text?: string;
    showIcon?: boolean;
}

export function ImagePlaceholder({ 
    className, 
    width = 400, 
    height = 300, 
    text = "No Image Available",
    showIcon = true 
}: ImagePlaceholderProps) {
    return (
        <div 
            className={cn(
                "flex flex-col items-center justify-center bg-gray-50 border-2 border-dashed border-gray-200 rounded-lg",
                className
            )}
            style={{ width, height }}
        >
            {showIcon && (
                <ImageIcon className="w-12 h-12 text-gray-400 mb-2" />
            )}
            <span className="text-sm text-gray-500 font-medium">{text}</span>
        </div>
    );
}

interface ImageWithFallbackProps extends React.ImgHTMLAttributes<HTMLImageElement> {
    fallbackClassName?: string;
    fallbackText?: string;
    showFallbackIcon?: boolean;
}

export function ImageWithFallback({ 
    src, 
    alt, 
    className, 
    fallbackClassName,
    fallbackText = "Image not available",
    showFallbackIcon = true,
    onError,
    ...props 
}: ImageWithFallbackProps) {
    const [hasError, setHasError] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);

    const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
        setHasError(true);
        setIsLoading(false);
        if (onError) {
            onError(e);
        }
    };

    const handleLoad = () => {
        setIsLoading(false);
        setHasError(false);
    };

    if (hasError || !src) {
        return (
            <ImagePlaceholder 
                className={cn(className, fallbackClassName)}
                text={fallbackText}
                showIcon={showFallbackIcon}
            />
        );
    }

    return (
        <>
            {isLoading && (
                <ImagePlaceholder 
                    className={cn(className, fallbackClassName)}
                    text="Loading..."
                    showIcon={false}
                />
            )}
            <img
                src={src}
                alt={alt}
                className={cn(className, isLoading ? 'hidden' : 'block')}
                onError={handleError}
                onLoad={handleLoad}
                {...props}
            />
        </>
    );
}
