import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
    CreditCard,
    Check,
    Loader2,
    AlertTriangle,
    Smartphone,
    Building,
    Globe
} from 'lucide-react';
import { toast } from 'sonner';
import { createCheckoutRequestWithRetry, logCheckoutError } from '@/utils/checkout-helpers';
import { PricingPlan } from '@/types';

interface ShurjoPayCheckoutProps {
    plan: PricingPlan;
    billingCycle: 'month' | 'year';
    onSuccess?: (transactionId: string) => void;
    onError?: (error: string) => void;
    disabled?: boolean;
}

export function ShurjoPayCheckout({ 
    plan, 
    billingCycle,  
    onError, 
    disabled = false 
}: ShurjoPayCheckoutProps) {
    const [isLoading, setIsLoading] = useState(false);

    const handleCheckout = async () => {
        if (!plan.supports_online_payment) {
            const errorMsg = 'Online payments are not available for this plan';
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        if (!plan.supports_monthly && billingCycle === 'month') {
            const errorMsg = 'This plan does not support monthly billing';
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        if (!plan.supports_yearly && billingCycle === 'year') {
            const errorMsg = 'This plan does not support yearly billing';
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        setIsLoading(true);

        try {
            // Use the utility function for checkout request with retry logic
            const data = await createCheckoutRequestWithRetry(
                '/shurjopay/checkout',
                plan.id,
                billingCycle,
                'ShurjoPay'
            );

            // Redirect to ShurjoPay checkout
            window.location.href = data.checkout_url;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
            logCheckoutError('ShurjoPay', error);
            toast.error(errorMessage);
            onError?.(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    const getPriceDisplay = () => {
        const basePrice = plan.price;
        const totalPrice = billingCycle === 'year' ? basePrice * 12 : basePrice;
        const currency = plan.currency === 'USD' ? 'BDT' : plan.currency; // Convert to BDT for ShurjoPay
        
        if (billingCycle === 'year') {
            return (
                <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">
                        ৳{(totalPrice * 85).toLocaleString()} {/* Approximate USD to BDT conversion */}
                    </div>
                    <div className="text-sm text-gray-500">
                        ৳{(basePrice * 85).toLocaleString()}/month, billed annually
                    </div>
                </div>
            );
        }

        return (
            <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">
                    ৳{(basePrice * 85).toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">per month</div>
            </div>
        );
    };

    const getSavingsDisplay = () => {
        if (billingCycle === 'year') {
            const monthlyTotal = plan.price * 12;
            const yearlyPrice = plan.price * 12; // Assuming same price for demo
            const savings = monthlyTotal - yearlyPrice;
            
            if (savings > 0) {
                return (
                    <Badge className="bg-green-100 text-green-800 border-green-200 mt-2">
                        Save ৳{(savings * 85).toLocaleString()} per year
                    </Badge>
                );
            }
        }
        return null;
    };

    const getPaymentMethods = () => {
        return (
            <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Supported Payment Methods:</h4>
                <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Smartphone className="h-4 w-4 text-green-600" />
                        Mobile Banking
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Building className="h-4 w-4 text-blue-600" />
                        Internet Banking
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                        <CreditCard className="h-4 w-4 text-purple-600" />
                        Card Payments
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Globe className="h-4 w-4 text-orange-600" />
                        Local Banks
                    </div>
                </div>
            </div>
        );
    };

    if (!plan.supports_online_payment) {
        return (
            <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                    Online payments are not available for this plan. Please contact support for assistance.
                </AlertDescription>
            </Alert>
        );
    }

    return (
        <Card className="w-full max-w-md border-green-200">
            <CardHeader className="text-center bg-gradient-to-br from-green-50 to-green-100">
                <div className="flex items-center justify-center gap-2 mb-2">
                    <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                        <span className="text-white font-semibold text-sm">SP</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                        ShurjoPay
                    </Badge>
                </div>
                <CardTitle className="text-green-900">
                    {plan.display_name}
                </CardTitle>
                <CardDescription className="text-green-700">{plan.description}</CardDescription>
                <div className="mt-4">
                    {getPriceDisplay()}
                    {getSavingsDisplay()}
                </div>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
                <div className="space-y-2">
                    <h4 className="font-medium text-gray-900 mb-3">Features Included:</h4>
                    {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                            <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                    ))}
                </div>

                {getPaymentMethods()}

                <div className="pt-4 border-t border-gray-100">
                    <Button
                        onClick={handleCheckout}
                        disabled={disabled || isLoading || !plan.supports_online_payment}
                        className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-md"
                        size="lg"
                    >
                        {isLoading ? (
                            <>
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Redirecting to ShurjoPay...
                            </>
                        ) : (
                            <>
                                <CreditCard className="h-4 w-4 mr-2" />
                                Pay with ShurjoPay
                            </>
                        )}
                    </Button>

                    <div className="mt-3 text-center">
                        <p className="text-xs text-gray-500">
                            🔒 Secure payment powered by ShurjoPay
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                            Leading payment gateway in Bangladesh
                        </p>
                    </div>
                </div>

                {!plan.supports_online_payment && (
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                            Online payments not available for this plan
                        </AlertDescription>
                    </Alert>
                )}
            </CardContent>
        </Card>
    );
}
