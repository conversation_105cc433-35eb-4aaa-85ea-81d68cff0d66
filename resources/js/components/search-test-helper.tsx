import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    CheckCircle, 
    XCircle, 
    Clock, 
    Search, 
    TestTube,
    AlertTriangle,
    Info
} from 'lucide-react';

interface TestResult {
    name: string;
    status: 'pending' | 'running' | 'passed' | 'failed';
    message?: string;
    duration?: number;
}

interface TestCase {
    id: string;
    name: string;
    description: string;
    steps: string[];
    expectedResult: string;
    category: 'critical' | 'important' | 'nice-to-have';
}

export default function SearchTestHelper() {
    const [testResults, setTestResults] = useState<Record<string, TestResult>>({});
    const [currentTest, setCurrentTest] = useState<string | null>(null);

    const testCases: TestCase[] = [
        {
            id: 'category-basic',
            name: 'Category Search - Basic Functionality',
            description: 'Verify category search page loads and basic search works',
            category: 'important',
            steps: [
                'Navigate to Display category search',
                'Enter "iPhone 15" in search box',
                'Click Search button',
                'Verify results appear'
            ],
            expectedResult: 'Search returns relevant iPhone 15 results'
        },
        {
            id: 'category-consecutive',
            name: 'Category Search - Consecutive Searches (CRITICAL)',
            description: 'Verify the main bug fix - subsequent searches work correctly',
            category: 'critical',
            steps: [
                'Search for "Apple iPhone 15 Pro Max Display"',
                'Verify results appear',
                'Clear and search for "Apple iPhone 13 Pro Max Display"',
                'Verify second search shows results (NOT "No display parts found")',
                'Clear and search for "Samsung Galaxy"',
                'Verify third search shows results'
            ],
            expectedResult: 'All three searches return appropriate results without getting stuck'
        },
        {
            id: 'brand-basic',
            name: 'Brand Search - Basic Functionality', 
            description: 'Verify brand search page loads and basic search works',
            category: 'important',
            steps: [
                'Navigate to Apple brand search',
                'Enter "iPhone" in search box',
                'Click Search button',
                'Verify results appear'
            ],
            expectedResult: 'Search returns Apple iPhone results only'
        },
        {
            id: 'brand-consecutive',
            name: 'Brand Search - Consecutive Searches (CRITICAL)',
            description: 'Verify the main bug fix for brand search',
            category: 'critical',
            steps: [
                'Search for "iPhone 15"',
                'Verify results appear',
                'Clear and search for "iPhone 13"',
                'Verify second search shows results (NOT "No Apple parts found")',
                'Clear and search for "Display"',
                'Verify third search shows results'
            ],
            expectedResult: 'All three searches return appropriate results'
        },
        {
            id: 'suggestions',
            name: 'Search Suggestions',
            description: 'Verify autocomplete suggestions work correctly',
            category: 'important',
            steps: [
                'Start typing "iPhone" in search box',
                'Verify suggestions dropdown appears',
                'Click on a suggestion',
                'Verify search is performed'
            ],
            expectedResult: 'Suggestions appear and clicking performs search'
        },
        {
            id: 'clear-search',
            name: 'Clear Search Functionality',
            description: 'Verify the improved Clear Search button works correctly',
            category: 'important',
            steps: [
                'Perform a search that returns no results',
                'Click "Clear Search" button',
                'Verify all state is reset',
                'Perform a new search'
            ],
            expectedResult: 'Clear Search resets all state and subsequent searches work'
        },
        {
            id: 'search-types',
            name: 'Search Type Filtering',
            description: 'Verify different search types work correctly',
            category: 'nice-to-have',
            steps: [
                'Enter "Display" in search box',
                'Test "All" search type',
                'Test "Parts" search type', 
                'Test "Models" search type'
            ],
            expectedResult: 'Different search types return appropriate filtered results'
        },
        {
            id: 'filters',
            name: 'Advanced Filters',
            description: 'Verify additional filters work with search',
            category: 'nice-to-have',
            steps: [
                'Perform a search with multiple results',
                'Open filters panel',
                'Apply brand filter',
                'Apply manufacturer filter',
                'Verify results update'
            ],
            expectedResult: 'Filters reduce result set appropriately'
        }
    ];

    const updateTestResult = (testId: string, result: Partial<TestResult>) => {
        setTestResults(prev => ({
            ...prev,
            [testId]: { ...prev[testId], ...result }
        }));
    };

    const startTest = (testId: string) => {
        setCurrentTest(testId);
        updateTestResult(testId, {
            name: testCases.find(t => t.id === testId)?.name || testId,
            status: 'running'
        });
    };

    const markTestPassed = (testId: string, message?: string) => {
        updateTestResult(testId, {
            status: 'passed',
            message: message || 'Test completed successfully'
        });
        setCurrentTest(null);
    };

    const markTestFailed = (testId: string, message?: string) => {
        updateTestResult(testId, {
            status: 'failed',
            message: message || 'Test failed - see details'
        });
        setCurrentTest(null);
    };

    const resetTests = () => {
        setTestResults({});
        setCurrentTest(null);
    };

    const getStatusIcon = (status: TestResult['status']) => {
        switch (status) {
            case 'passed':
                return <CheckCircle className="w-5 h-5 text-green-600" />;
            case 'failed':
                return <XCircle className="w-5 h-5 text-red-600" />;
            case 'running':
                return <Clock className="w-5 h-5 text-blue-600 animate-spin" />;
            default:
                return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />;
        }
    };

    const getCategoryBadge = (category: TestCase['category']) => {
        const variants = {
            'critical': 'destructive',
            'important': 'default', 
            'nice-to-have': 'secondary'
        } as const;

        return (
            <Badge variant={variants[category]} className="text-xs">
                {category.toUpperCase()}
            </Badge>
        );
    };

    const criticalTests = testCases.filter(t => t.category === 'critical');
    const importantTests = testCases.filter(t => t.category === 'important');
    const niceToHaveTests = testCases.filter(t => t.category === 'nice-to-have');

    const passedTests = Object.values(testResults).filter(r => r.status === 'passed').length;
    const failedTests = Object.values(testResults).filter(r => r.status === 'failed').length;
    const totalTests = testCases.length;

    return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <TestTube className="w-6 h-6" />
                        Category & Brand Search Test Helper
                    </CardTitle>
                    <CardDescription>
                        Systematic testing tool for the search functionality fixes. 
                        Use this to verify that consecutive searches work correctly.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-4">
                            <div className="text-sm">
                                <span className="font-medium">Progress:</span> {passedTests}/{totalTests} tests passed
                            </div>
                            {failedTests > 0 && (
                                <div className="text-sm text-red-600">
                                    {failedTests} tests failed
                                </div>
                            )}
                        </div>
                        <Button onClick={resetTests} variant="outline" size="sm">
                            Reset All Tests
                        </Button>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                        <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${(passedTests / totalTests) * 100}%` }}
                        />
                    </div>
                </CardContent>
            </Card>

            {/* Critical Tests */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                        Critical Tests
                        <Badge variant="destructive">Must Pass</Badge>
                    </CardTitle>
                    <CardDescription>
                        These tests verify the main bug fixes for consecutive searches
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {criticalTests.map((test) => (
                        <Card key={test.id} className="border-l-4 border-l-red-500">
                            <CardContent className="p-4">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                        {getStatusIcon(testResults[test.id]?.status)}
                                        <div>
                                            <h4 className="font-medium">{test.name}</h4>
                                            <p className="text-sm text-gray-600">{test.description}</p>
                                        </div>
                                    </div>
                                    {getCategoryBadge(test.category)}
                                </div>

                                <div className="mb-3">
                                    <p className="text-sm font-medium mb-2">Steps:</p>
                                    <ol className="text-sm text-gray-600 space-y-1">
                                        {test.steps.map((step, index) => (
                                            <li key={index} className="flex gap-2">
                                                <span className="font-mono text-xs bg-gray-100 px-1 rounded">
                                                    {index + 1}
                                                </span>
                                                {step}
                                            </li>
                                        ))}
                                    </ol>
                                </div>

                                <div className="mb-3">
                                    <p className="text-sm font-medium">Expected Result:</p>
                                    <p className="text-sm text-gray-600">{test.expectedResult}</p>
                                </div>

                                {testResults[test.id]?.message && (
                                    <div className="mb-3 p-2 bg-gray-50 rounded text-sm">
                                        {testResults[test.id].message}
                                    </div>
                                )}

                                <div className="flex gap-2">
                                    <Button 
                                        onClick={() => startTest(test.id)}
                                        disabled={currentTest === test.id}
                                        size="sm"
                                    >
                                        {currentTest === test.id ? 'Testing...' : 'Start Test'}
                                    </Button>
                                    {currentTest === test.id && (
                                        <>
                                            <Button 
                                                onClick={() => markTestPassed(test.id)}
                                                variant="outline"
                                                size="sm"
                                                className="text-green-600"
                                            >
                                                Mark Passed
                                            </Button>
                                            <Button 
                                                onClick={() => markTestFailed(test.id)}
                                                variant="outline" 
                                                size="sm"
                                                className="text-red-600"
                                            >
                                                Mark Failed
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </CardContent>
            </Card>

            {/* Important Tests */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Info className="w-5 h-5 text-blue-600" />
                        Important Tests
                        <Badge variant="default">Should Pass</Badge>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {importantTests.map((test) => (
                        <Card key={test.id} className="border-l-4 border-l-blue-500">
                            <CardContent className="p-4">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                        {getStatusIcon(testResults[test.id]?.status)}
                                        <div>
                                            <h4 className="font-medium">{test.name}</h4>
                                            <p className="text-sm text-gray-600">{test.description}</p>
                                        </div>
                                    </div>
                                    {getCategoryBadge(test.category)}
                                </div>

                                <details className="mb-3">
                                    <summary className="text-sm font-medium cursor-pointer">View Test Details</summary>
                                    <div className="mt-2 space-y-2">
                                        <div>
                                            <p className="text-sm font-medium">Steps:</p>
                                            <ol className="text-sm text-gray-600 space-y-1">
                                                {test.steps.map((step, index) => (
                                                    <li key={index} className="flex gap-2">
                                                        <span className="font-mono text-xs bg-gray-100 px-1 rounded">
                                                            {index + 1}
                                                        </span>
                                                        {step}
                                                    </li>
                                                ))}
                                            </ol>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium">Expected Result:</p>
                                            <p className="text-sm text-gray-600">{test.expectedResult}</p>
                                        </div>
                                    </div>
                                </details>

                                {testResults[test.id]?.message && (
                                    <div className="mb-3 p-2 bg-gray-50 rounded text-sm">
                                        {testResults[test.id].message}
                                    </div>
                                )}

                                <div className="flex gap-2">
                                    <Button 
                                        onClick={() => startTest(test.id)}
                                        disabled={currentTest === test.id}
                                        size="sm"
                                        variant="outline"
                                    >
                                        {currentTest === test.id ? 'Testing...' : 'Start Test'}
                                    </Button>
                                    {currentTest === test.id && (
                                        <>
                                            <Button 
                                                onClick={() => markTestPassed(test.id)}
                                                variant="outline"
                                                size="sm"
                                                className="text-green-600"
                                            >
                                                Mark Passed
                                            </Button>
                                            <Button 
                                                onClick={() => markTestFailed(test.id)}
                                                variant="outline"
                                                size="sm" 
                                                className="text-red-600"
                                            >
                                                Mark Failed
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </CardContent>
            </Card>

            {/* Nice to Have Tests */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Search className="w-5 h-5 text-gray-600" />
                        Additional Tests
                        <Badge variant="secondary">Nice to Have</Badge>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {niceToHaveTests.map((test) => (
                        <Card key={test.id} className="border-l-4 border-l-gray-400">
                            <CardContent className="p-4">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                        {getStatusIcon(testResults[test.id]?.status)}
                                        <div>
                                            <h4 className="font-medium">{test.name}</h4>
                                            <p className="text-sm text-gray-600">{test.description}</p>
                                        </div>
                                    </div>
                                    {getCategoryBadge(test.category)}
                                </div>

                                <details className="mb-3">
                                    <summary className="text-sm font-medium cursor-pointer">View Test Details</summary>
                                    <div className="mt-2 space-y-2">
                                        <div>
                                            <p className="text-sm font-medium">Steps:</p>
                                            <ol className="text-sm text-gray-600 space-y-1">
                                                {test.steps.map((step, index) => (
                                                    <li key={index} className="flex gap-2">
                                                        <span className="font-mono text-xs bg-gray-100 px-1 rounded">
                                                            {index + 1}
                                                        </span>
                                                        {step}
                                                    </li>
                                                ))}
                                            </ol>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium">Expected Result:</p>
                                            <p className="text-sm text-gray-600">{test.expectedResult}</p>
                                        </div>
                                    </div>
                                </details>

                                <div className="flex gap-2">
                                    <Button 
                                        onClick={() => startTest(test.id)}
                                        disabled={currentTest === test.id}
                                        size="sm"
                                        variant="ghost"
                                    >
                                        {currentTest === test.id ? 'Testing...' : 'Start Test'}
                                    </Button>
                                    {currentTest === test.id && (
                                        <>
                                            <Button 
                                                onClick={() => markTestPassed(test.id)}
                                                variant="outline"
                                                size="sm"
                                                className="text-green-600"
                                            >
                                                Mark Passed
                                            </Button>
                                            <Button 
                                                onClick={() => markTestFailed(test.id)}
                                                variant="outline"
                                                size="sm"
                                                className="text-red-600"
                                            >
                                                Mark Failed
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </CardContent>
            </Card>

            {/* Test Summary */}
            {Object.keys(testResults).length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle>Test Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-3 gap-4 text-center">
                            <div className="p-4 bg-green-50 rounded-lg">
                                <div className="text-2xl font-bold text-green-600">{passedTests}</div>
                                <div className="text-sm text-green-600">Passed</div>
                            </div>
                            <div className="p-4 bg-red-50 rounded-lg">
                                <div className="text-2xl font-bold text-red-600">{failedTests}</div>
                                <div className="text-sm text-red-600">Failed</div>
                            </div>
                            <div className="p-4 bg-gray-50 rounded-lg">
                                <div className="text-2xl font-bold text-gray-600">{totalTests - passedTests - failedTests}</div>
                                <div className="text-sm text-gray-600">Pending</div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
