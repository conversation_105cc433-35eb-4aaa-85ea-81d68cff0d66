import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { CreditCard, FileText, Clock, Zap, Check, AlertCircle, Smartphone, Bitcoin } from 'lucide-react';
import { PaddleCheckout } from './PaddleCheckout';
import { ShurjoPayCheckout } from './ShurjoPayCheckout';
import { CoinbaseCommerceCheckout } from './CoinbaseCommerceCheckout';
import { Link } from '@inertiajs/react';
import { toast } from 'sonner';
import { PricingPlan } from '@/types';

interface PaymentMethodSelectorProps {
    plan: PricingPlan;
    billingCycle: 'month' | 'year';
    onBillingCycleChange: (cycle: 'month' | 'year') => void;
}

export function PaymentMethodSelector({
    plan,
    billingCycle,
    onBillingCycleChange
}: PaymentMethodSelectorProps) {
    // Default to online payment if available, otherwise offline
    const getDefaultMethod = () => {
        if (plan.has_online_payment_enabled) {
            // Prefer ShurjoPay for BDT, Coinbase Commerce for crypto, Paddle for others
            if (plan.has_shurjopay_integration) return 'shurjopay';
            if (plan.has_coinbase_commerce_integration && plan.crypto_payment_enabled) return 'coinbase';
            return 'paddle';
        }
        return 'offline';
    };
    const [selectedMethod, setSelectedMethod] = useState<'paddle' | 'shurjopay' | 'coinbase' | 'offline'>(getDefaultMethod());

    const handlePaddleSuccess = (transactionId: string) => {
        console.log('Payment successful:', transactionId);
        // Handle success - maybe redirect or show success message
    };

    const handlePaddleError = (error: string) => {
        console.error('Paddle payment error:', error);

        // Show more user-friendly error for development mode
        if (error.includes('Development Mode:')) {
            toast.error('Development Mode: Paddle is using placeholder credentials. Please configure real Paddle sandbox credentials for testing.', {
                duration: 8000,
            });
        } else {
            toast.error(error);
        }

        // Handle error - maybe show error message or fallback to offline
        setSelectedMethod('offline');
    };

    const handleShurjoPaySuccess = (transactionId: string) => {
        console.log('ShurjoPay payment successful:', transactionId);
        // Handle success - maybe redirect or show success message
    };

    const handleShurjoPayError = (error: string) => {
        console.error('ShurjoPay payment error:', error);
        // Handle error - maybe show error message or fallback to offline
        setSelectedMethod('offline');
    };

    const handleCoinbaseCommerceSuccess = (transactionId: string) => {
        console.log('Coinbase Commerce payment successful:', transactionId);
        // Handle success - maybe redirect or show success message
    };

    const handleCoinbaseCommerceError = (error: string) => {
        console.error('Coinbase Commerce payment error:', error);
        // Handle error - maybe show error message or fallback to offline
        setSelectedMethod('offline');
    };

    // Get available payment methods for rendering
    const getAvailablePaymentMethods = () => {
        const methods = [];

        if (plan.has_online_payment_enabled && plan.has_paddle_integration) {
            methods.push({
                id: 'paddle',
                name: 'Pay with Card',
                icon: CreditCard,
                badge: { text: 'Instant', color: 'blue' },
                description: 'Credit & Debit Cards'
            });
        }

        if (plan.has_online_payment_enabled && plan.has_shurjopay_integration) {
            methods.push({
                id: 'shurjopay',
                name: 'ShurjoPay',
                icon: Smartphone,
                badge: { text: 'Local', color: 'green' },
                description: 'Mobile & Internet Banking'
            });
        }

        if (plan.crypto_payment_enabled && plan.has_coinbase_commerce_integration) {
            methods.push({
                id: 'coinbase',
                name: 'Pay with Crypto',
                icon: Bitcoin,
                badge: { text: 'Onchain', color: 'orange' },
                description: 'Bitcoin, Ethereum & more'
            });
        }

        if (plan.has_offline_payment_enabled) {
            methods.push({
                id: 'offline',
                name: 'Offline Payment',
                icon: FileText,
                badge: { text: 'Manual', color: 'gray' },
                description: 'Bank Transfer & Manual Review'
            });
        }

        return methods;
    };

    return (
        <div className="space-y-8">
            {/* Enhanced Billing Cycle Toggle */}
            {(plan.supports_monthly && plan.supports_yearly) && (
                <div className="flex justify-center">
                    <div className="inline-flex items-center bg-muted/50 rounded-xl p-1.5 border border-border/50">
                        <Button
                            variant={billingCycle === 'month' ? 'default' : 'ghost'}
                            size="sm"
                            onClick={() => onBillingCycleChange('month')}
                            className="rounded-lg px-6 py-2 font-medium transition-all duration-200"
                        >
                            Monthly
                        </Button>
                        <Button
                            variant={billingCycle === 'year' ? 'default' : 'ghost'}
                            size="sm"
                            onClick={() => onBillingCycleChange('year')}
                            className="rounded-lg px-6 py-2 font-medium transition-all duration-200"
                        >
                            Yearly
                            <Badge variant="secondary" className="ml-2 bg-green-500/10 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800">
                                Save 20%
                            </Badge>
                        </Button>
                    </div>
                </div>
            )}

            {/* Enhanced Payment Method Selection with Sidebar Layout */}
            {plan.has_any_payment_method ? (
                <div className="grid lg:grid-cols-4 gap-6 lg:gap-8">
                    {/* Payment Method Sidebar */}
                    <div className="lg:col-span-1">
                        <div className="lg:sticky lg:top-6">
                            <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider mb-4 lg:block hidden">
                                Payment Options
                            </h4>
                            <div className="space-y-3 lg:space-y-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-3 lg:gap-0">
                            {getAvailablePaymentMethods().map((method) => {
                                const Icon = method.icon;
                                const isSelected = selectedMethod === method.id;

                                return (
                                    <button
                                        key={method.id}
                                        onClick={() => setSelectedMethod(method.id as 'paddle' | 'shurjopay' | 'coinbase' | 'offline')}
                                        className={`w-full p-4 lg:p-4 sm:p-3 rounded-xl border-2 transition-all duration-200 text-left ${
                                            isSelected
                                                ? 'border-primary bg-primary/5 shadow-lg ring-1 ring-primary/20'
                                                : 'border-border/50 hover:border-primary/50 hover:bg-muted/30 hover:shadow-md'
                                        }`}
                                    >
                                        <div className="flex items-start gap-3">
                                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                                                isSelected ? 'bg-primary/10' : 'bg-muted/50'
                                            }`}>
                                                <Icon className={`w-5 h-5 ${
                                                    isSelected ? 'text-primary' : 'text-muted-foreground'
                                                }`} />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-2 mb-1">
                                                    <h3 className={`font-semibold text-sm ${
                                                        isSelected ? 'text-foreground' : 'text-foreground/80'
                                                    }`}>
                                                        {method.name}
                                                    </h3>
                                                    <Badge
                                                        variant={method.badge.color === 'gray' ? 'outline' : 'secondary'}
                                                        className={`text-xs ${
                                                            method.badge.color === 'blue' ? 'bg-blue-500/10 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800' :
                                                            method.badge.color === 'green' ? 'bg-green-500/10 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800' :
                                                            method.badge.color === 'orange' ? 'bg-orange-500/10 text-orange-700 dark:text-orange-400 border-orange-200 dark:border-orange-800' :
                                                            'border-muted-foreground/30'
                                                        }`}
                                                    >
                                                        {method.badge.text}
                                                    </Badge>
                                                </div>
                                                <p className="text-xs text-muted-foreground">
                                                    {method.description}
                                                </p>
                                            </div>
                                        </div>
                                    </button>
                                );
                            })}
                            </div>
                        </div>
                    </div>

                    {/* Payment Method Content */}
                    <div className="lg:col-span-3">
                        <div className="bg-gradient-to-br from-muted/20 to-muted/5 rounded-xl p-6 lg:p-8 min-h-[400px]">
                        {/* Paddle Payment Method */}
                        {selectedMethod === 'paddle' && plan.has_online_payment_enabled && plan.has_paddle_integration && (
                            <div className="space-y-6">
                                <div className="text-center space-y-2 mb-6">
                                    <h3 className="text-xl font-bold text-foreground">Pay with Credit Card</h3>
                                    <p className="text-muted-foreground">Secure payment powered by Paddle</p>
                                </div>

                                <div className="flex justify-center mb-6">
                                    <PaddleCheckout
                                        plan={plan}
                                        billingCycle={billingCycle}
                                        onSuccess={handlePaddleSuccess}
                                        onError={handlePaddleError}
                                    />
                                </div>

                                <div className="bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-950/20 dark:to-green-900/10 border border-green-200 dark:border-green-800 rounded-xl p-4 sm:p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-10 h-10 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                            <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-green-900 dark:text-green-100 mb-1">Instant Activation</h4>
                                            <p className="text-sm text-green-700 dark:text-green-300">
                                                Your subscription will be activated immediately after payment
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* ShurjoPay Payment Method */}
                        {selectedMethod === 'shurjopay' && plan.has_online_payment_enabled && plan.has_shurjopay_integration && (
                            <div className="space-y-6">
                                <div className="text-center space-y-2 mb-6">
                                    <h3 className="text-xl font-bold text-foreground">Pay with ShurjoPay</h3>
                                    <p className="text-muted-foreground">Secure payment with local banking support</p>
                                </div>

                                <div className="flex justify-center mb-6">
                                    <ShurjoPayCheckout
                                        plan={plan}
                                        billingCycle={billingCycle}
                                        onSuccess={handleShurjoPaySuccess}
                                        onError={handleShurjoPayError}
                                    />
                                </div>

                                <div className="bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-950/20 dark:to-green-900/10 border border-green-200 dark:border-green-800 rounded-xl p-4 sm:p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-10 h-10 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                            <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-green-900 dark:text-green-100 mb-1">Local Payment Methods</h4>
                                            <p className="text-sm text-green-700 dark:text-green-300">
                                                Pay using mobile banking, internet banking, or local cards
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Coinbase Commerce Payment Method */}
                        {selectedMethod === 'coinbase' && plan.crypto_payment_enabled && plan.has_coinbase_commerce_integration && (
                            <div className="space-y-6">
                                <div className="text-center space-y-2 mb-6">
                                    <h3 className="text-xl font-bold text-foreground">Pay with Cryptocurrency</h3>
                                    <p className="text-muted-foreground">Secure crypto payments with Onchain Payment Protocol</p>
                                </div>

                                <div className="flex justify-center mb-6">
                                    <CoinbaseCommerceCheckout
                                        plan={plan}
                                        billingCycle={billingCycle}
                                        onSuccess={handleCoinbaseCommerceSuccess}
                                        onError={handleCoinbaseCommerceError}
                                    />
                                </div>

                                <div className="bg-gradient-to-r from-orange-50 to-orange-100/50 dark:from-orange-950/20 dark:to-orange-900/10 border border-orange-200 dark:border-orange-800 rounded-xl p-4 sm:p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-10 h-10 bg-orange-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                            <Check className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-1">Onchain Payment Protocol</h4>
                                            <p className="text-sm text-orange-700 dark:text-orange-300">
                                                Auto USDC settlement, hundreds of currencies, instant confirmation
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Offline Payment Method */}
                        {selectedMethod === 'offline' && plan.has_offline_payment_enabled && (
                            <div className="space-y-6">
                                <div className="text-center space-y-2 mb-6">
                                    <h3 className="text-xl font-bold text-foreground">Offline Payment</h3>
                                    <p className="text-muted-foreground">Submit payment proof for manual approval</p>
                                </div>

                                <Card className="border-0 shadow-lg bg-gradient-to-br from-card to-card/50">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="flex items-center gap-3 text-lg">
                                            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                                                <FileText className="h-5 w-5 text-primary" />
                                            </div>
                                            Payment Request
                                        </CardTitle>
                                        <CardDescription className="text-base">
                                            Submit your payment details and proof for manual verification
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="bg-muted/30 rounded-xl p-4 space-y-3">
                                            <div className="flex justify-between items-center">
                                                <span className="text-muted-foreground">Plan:</span>
                                                <span className="font-semibold text-foreground">{plan.display_name}</span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-muted-foreground">Billing:</span>
                                                <span className="font-semibold text-foreground">
                                                    {billingCycle === 'year' ? 'Yearly' : 'Monthly'}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center pt-2 border-t border-border/50">
                                                <span className="text-muted-foreground">Amount:</span>
                                                <span className="font-bold text-xl text-primary">
                                                    ${plan.price} {plan.currency}
                                                </span>
                                            </div>
                                        </div>

                                        <Link href={route('payment-requests.create')}>
                                            <Button className="w-full h-12 text-base font-semibold" size="lg">
                                                <FileText className="h-5 w-5 mr-2" />
                                                Submit Payment Request
                                            </Button>
                                        </Link>
                                    </CardContent>
                                </Card>

                                <div className="bg-gradient-to-r from-blue-50 to-blue-100/50 dark:from-blue-950/20 dark:to-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-xl p-4 sm:p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                            <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">Manual Review</h4>
                                            <p className="text-sm text-blue-700 dark:text-blue-300">
                                                Your payment will be reviewed and activated within 24-48 hours
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                        </div>
                    </div>
                </div>
            ) : (
                <div className="text-center py-12">
                    <Card className="border-0 shadow-lg bg-gradient-to-br from-yellow-50 to-yellow-100/50 dark:from-yellow-950/20 dark:to-yellow-900/10 max-w-md mx-auto">
                        <CardContent className="p-8">
                            <div className="flex items-center justify-center gap-3 mb-6">
                                <div className="w-12 h-12 bg-yellow-500/10 rounded-full flex items-center justify-center">
                                    <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                                </div>
                                <h3 className="text-xl font-bold text-yellow-900 dark:text-yellow-100">No Payment Methods Available</h3>
                            </div>
                            <p className="text-yellow-700 dark:text-yellow-300 mb-6 leading-relaxed">
                                This plan currently has no payment methods enabled. Please contact support for assistance.
                            </p>
                            <Button variant="outline" className="border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-700 dark:text-yellow-300 dark:hover:bg-yellow-900/20" asChild>
                                <a href="mailto:<EMAIL>">Contact Support</a>
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    );
}
