import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    DropdownMenu, 
    DropdownMenuContent, 
    DropdownMenuItem, 
    DropdownMenuTrigger,
    DropdownMenuSeparator,
    DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { type SharedData } from '@/types';
import { usePage, Link } from '@inertiajs/react';
import { 
    Plus, 
    Search, 
    LayoutGrid, 
    Settings, 
    Zap,
    Package,
    Smartphone,
    Tags,
    Database,
    Upload,
    Heart,
    History,
    BarChart3,
    Bell,
    HelpCircle
} from 'lucide-react';

interface QuickAction {
    title: string;
    href: string;
    icon: any;
    description: string;
    adminOnly?: boolean;
    shortcut?: string;
    category: string;
}

const quickActions: QuickAction[] = [
    // Core Actions
    { 
        title: 'Search Parts', 
        href: '/search', 
        icon: Search, 
        description: 'Find mobile parts quickly',
        shortcut: '⌘K',
        category: 'Core'
    },
    { 
        title: 'Dashboard', 
        href: '/dashboard', 
        icon: LayoutGrid, 
        description: 'Go to main dashboard',
        category: 'Core'
    },
    { 
        title: 'Favorites', 
        href: '/favorites', 
        icon: Heart, 
        description: 'Your saved parts',
        category: 'Core'
    },
    
    // Admin Actions
    { 
        title: 'Add New Part', 
        href: '/admin/parts/create', 
        icon: Plus, 
        description: 'Create a new part entry',
        adminOnly: true,
        category: 'Admin'
    },
    { 
        title: 'Admin Dashboard', 
        href: '/admin/dashboard', 
        icon: BarChart3, 
        description: 'Administrative overview',
        adminOnly: true,
        category: 'Admin'
    },
    { 
        title: 'Manage Parts', 
        href: '/admin/parts', 
        icon: Package, 
        description: 'Manage parts database',
        adminOnly: true,
        category: 'Admin'
    },
    { 
        title: 'Manage Brands', 
        href: '/admin/brands', 
        icon: Smartphone, 
        description: 'Manage brands',
        adminOnly: true,
        category: 'Admin'
    },
    { 
        title: 'Bulk Import', 
        href: '/admin/bulk-import', 
        icon: Upload, 
        description: 'Import data in bulk',
        adminOnly: true,
        category: 'Admin'
    },
    
    // Settings & Support
    { 
        title: 'Settings', 
        href: '/settings/profile', 
        icon: Settings, 
        description: 'Manage your account',
        category: 'Settings'
    },
    { 
        title: 'Notifications', 
        href: '/notifications', 
        icon: Bell, 
        description: 'View your alerts',
        category: 'Settings'
    },
    { 
        title: 'Help & Support', 
        href: '/help', 
        icon: HelpCircle, 
        description: 'Get assistance',
        category: 'Settings'
    },
];

export function MobileQuickActions() {
    const page = usePage<SharedData>();
    const { auth } = page.props;
    const isAdmin = auth.user?.email === '<EMAIL>' || auth.user?.email === '<EMAIL>';
    const [isOpen, setIsOpen] = useState(false);

    // Filter actions based on admin status
    const filteredActions = quickActions.filter(action => !action.adminOnly || isAdmin);

    // Group actions by category
    const groupedActions = filteredActions.reduce((acc, action) => {
        if (!acc[action.category]) {
            acc[action.category] = [];
        }
        acc[action.category].push(action);
        return acc;
    }, {} as Record<string, QuickAction[]>);

    return (
        <div className="fixed bottom-6 right-6 z-50 md:hidden">
            <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
                <DropdownMenuTrigger asChild>
                    <Button
                        size="lg"
                        className={`h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 ${
                            isOpen ? 'rotate-45' : 'rotate-0'
                        } bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80`}
                    >
                        <Plus className={`h-6 w-6 transition-transform duration-300 ${
                            isOpen ? 'rotate-45' : 'rotate-0'
                        }`} />
                        <span className="sr-only">Quick Actions</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent 
                    align="end" 
                    side="top"
                    className="w-72 mb-4 max-h-96 overflow-y-auto"
                >
                    <DropdownMenuLabel className="flex items-center gap-2 px-3 py-2">
                        <Zap className="h-4 w-4 text-primary" />
                        <span className="font-semibold">Quick Actions</span>
                        {isAdmin && (
                            <Badge variant="outline" className="ml-auto text-xs bg-gradient-to-r from-orange-50 to-red-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-red-950 dark:text-orange-300 dark:border-orange-800">
                                Admin
                            </Badge>
                        )}
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    
                    {Object.entries(groupedActions).map(([category, actions]) => (
                        <div key={category}>
                            <DropdownMenuLabel className="px-3 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wide">
                                {category}
                            </DropdownMenuLabel>
                            {actions.map((action) => (
                                <DropdownMenuItem key={action.href} asChild>
                                    <Link 
                                        href={action.href} 
                                        className="flex items-center gap-3 p-3 cursor-pointer"
                                        onClick={() => setIsOpen(false)}
                                    >
                                        <div className={`p-2 rounded-lg ${
                                            action.category === 'Admin' 
                                                ? 'bg-orange-100 dark:bg-orange-900/20' 
                                                : action.category === 'Settings'
                                                ? 'bg-purple-100 dark:bg-purple-900/20'
                                                : 'bg-blue-100 dark:bg-blue-900/20'
                                        }`}>
                                            <action.icon className={`h-4 w-4 ${
                                                action.category === 'Admin' 
                                                    ? 'text-orange-600 dark:text-orange-400' 
                                                    : action.category === 'Settings'
                                                    ? 'text-purple-600 dark:text-purple-400'
                                                    : 'text-blue-600 dark:text-blue-400'
                                            }`} />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="font-medium truncate">{action.title}</div>
                                            <div className="text-xs text-muted-foreground truncate">{action.description}</div>
                                        </div>
                                        {action.shortcut && (
                                            <Badge variant="outline" className="text-xs font-mono">
                                                {action.shortcut}
                                            </Badge>
                                        )}
                                    </Link>
                                </DropdownMenuItem>
                            ))}
                            <DropdownMenuSeparator />
                        </div>
                    ))}
                    
                    <DropdownMenuLabel className="px-3 py-2 text-xs text-muted-foreground">
                        💡 Tip: Press ⌘K to search from anywhere
                    </DropdownMenuLabel>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
}
