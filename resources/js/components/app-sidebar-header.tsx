import { Breadcrumbs } from '@/components/breadcrumbs';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
    DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { type BreadcrumbItem as BreadcrumbItemType, type SharedData } from '@/types';
import { usePage, Link } from '@inertiajs/react';
import {
    Search,
    Plus,
    Settings,
    HelpCircle,
    Zap,
    Moon,
    Sun,
    Monitor,
    Shield,
    LayoutGrid,
    Globe
} from 'lucide-react';
import NotificationBell from '@/components/user/NotificationBell';
import { useAppearance } from '@/hooks/use-appearance';

export function AppSidebarHeader({ breadcrumbs = [] }: { breadcrumbs?: BreadcrumbItemType[] }) {
    const page = usePage<SharedData>();
    const { auth } = page.props;
    const { appearance, setAppearance } = useAppearance();
    const isAdmin = auth.user?.email === '<EMAIL>' || auth.user?.email === '<EMAIL>';

    const quickActions = [
        {
            title: 'Search Parts',
            href: '/search',
            icon: Search,
            description: 'Find mobile parts quickly',
            shortcut: '⌘K'
        },
        {
            title: 'Add New Part',
            href: '/admin/parts/create',
            icon: Plus,
            description: 'Create a new part entry',
            adminOnly: true
        },
        {
            title: 'Dashboard',
            href: isAdmin ? '/admin/dashboard' : '/dashboard',
            icon: LayoutGrid,
            description: 'Go to main dashboard'
        },
        {
            title: 'Settings',
            href: '/settings/profile',
            icon: Settings,
            description: 'Manage your account'
        },
    ];

    const themeOptions = [
        { value: 'light', label: 'Light', icon: Sun },
        { value: 'dark', label: 'Dark', icon: Moon },
        { value: 'system', label: 'System', icon: Monitor },
    ];

    return (
        <header className="flex h-16 shrink-0 items-center gap-3 border-b border-sidebar-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4">
            {/* Left Section - Hamburger Menu and Breadcrumbs */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="h-6" />
                <div className="flex-1 min-w-0">
                    <Breadcrumbs breadcrumbs={breadcrumbs} />
                </div>
            </div>

            {/* Right Section - Action Buttons */}
            <div className="flex items-center gap-2">
                {/* Quick Search Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="hidden md:flex items-center gap-2 h-8 px-3 text-muted-foreground hover:text-foreground transition-colors"
                    asChild
                >
                    <Link href="/search">
                        <Search className="h-4 w-4" />
                        <span className="text-sm">Search</span>
                        <Badge variant="outline" className="ml-1 px-1.5 py-0.5 text-xs font-mono">
                            ⌘K
                        </Badge>
                    </Link>
                </Button>

                {/* Visit Site Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center gap-2 h-8 px-3 text-muted-foreground hover:text-foreground transition-colors hover:bg-teal-50 hover:text-teal-700 dark:hover:bg-teal-950 dark:hover:text-teal-300"
                    asChild
                >
                    <a href="/" target="_blank" rel="noopener noreferrer">
                        <Globe className="h-4 w-4" />
                        <span className="text-sm hidden sm:inline">Visit Site</span>
                    </a>
                </Button>

                {/* Quick Actions Dropdown */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                        >
                            <Plus className="h-4 w-4" />
                            <span className="sr-only">Quick Actions</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-64">
                        <DropdownMenuLabel className="flex items-center gap-2">
                            <Zap className="h-4 w-4" />
                            Quick Actions
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {quickActions.map((action) => {
                            if (action.adminOnly && !isAdmin) return null;
                            return (
                                <DropdownMenuItem key={action.href} asChild>
                                    <Link href={action.href} className="flex items-center gap-3 p-2">
                                        <action.icon className="h-4 w-4 text-muted-foreground" />
                                        <div className="flex-1">
                                            <div className="font-medium">{action.title}</div>
                                            <div className="text-xs text-muted-foreground">{action.description}</div>
                                        </div>
                                        {action.shortcut && (
                                            <Badge variant="outline" className="text-xs font-mono">
                                                {action.shortcut}
                                            </Badge>
                                        )}
                                    </Link>
                                </DropdownMenuItem>
                            );
                        })}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Theme Switcher */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                        >
                            {appearance === 'light' ? (
                                <Sun className="h-4 w-4" />
                            ) : appearance === 'dark' ? (
                                <Moon className="h-4 w-4" />
                            ) : (
                                <Monitor className="h-4 w-4" />
                            )}
                            <span className="sr-only">Toggle theme</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Theme</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {themeOptions.map((theme) => (
                            <DropdownMenuItem
                                key={theme.value}
                                onSelect={() => {
                                    console.log('Theme selected:', theme.value); // Debug log
                                    setAppearance(theme.value as any);
                                }}
                                className="flex items-center gap-2"
                            >
                                <theme.icon className="h-4 w-4" />
                                {theme.label}
                                {appearance === theme.value && (
                                    <Badge variant="outline" className="ml-auto text-xs">
                                        Active
                                    </Badge>
                                )}
                            </DropdownMenuItem>
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Help Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                    title="Help & Support"
                >
                    <HelpCircle className="h-4 w-4" />
                    <span className="sr-only">Help</span>
                </Button>

                <Separator orientation="vertical" className="h-6" />

                {/* Notifications */}
                <NotificationBell />

                {/* Admin Badge */}
                {isAdmin && (
                    <Badge variant="outline" className="hidden sm:flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-amber-950 dark:text-orange-300 dark:border-orange-800">
                        <Shield className="h-3 w-3" />
                        <span className="text-xs font-medium">Admin</span>
                    </Badge>
                )}
            </div>
        </header>
    );
}
