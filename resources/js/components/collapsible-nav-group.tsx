import { SidebarGroup, SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { useNavGroupState } from '@/hooks/use-nav-group-state';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { ChevronRight, type LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import React from 'react';

interface CollapsibleNavGroupProps {
    title: string;
    items: NavItem[];
    groupId: string;
    icon?: LucideIcon;
    className?: string;
}

export const CollapsibleNavGroup = React.memo(function CollapsibleNavGroup({
    title,
    items,
    groupId,
    icon: Icon,
    className
}: CollapsibleNavGroupProps) {
    const page = usePage();
    const { isExpanded, toggleExpanded } = useNavGroupState(groupId);
    const { state } = useSidebar();
    const isCollapsed = state === "collapsed";

    // When sidebar is collapsed, show icon with dropdown menu
    if (isCollapsed && Icon) {
        return (
            <SidebarGroup className={cn("px-1 py-1", className)}>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <SidebarMenuButton
                                    isActive={items.some(item => page.url.startsWith(item.href))}
                                    tooltip={{ children: title }}
                                    className={cn(
                                        "sidebar-smooth-transition group/item relative",
                                        "hover:bg-sidebar-accent/80 hover:text-sidebar-accent-foreground",
                                        "hover:scale-105 active:scale-95",
                                        "data-[active=true]:bg-sidebar-primary data-[active=true]:text-sidebar-primary-foreground",
                                        "data-[active=true]:shadow-md data-[active=true]:scale-105",
                                        "focus:ring-2 focus:ring-sidebar-ring focus:ring-offset-2 focus:ring-offset-sidebar",
                                        "rounded-lg border border-transparent hover:border-sidebar-border/20",
                                        "w-8 h-8 p-0 justify-center",
                                        "shadow-sm hover:shadow-md"
                                    )}
                                >
                                    <Icon className={cn(
                                        "h-4 w-4 sidebar-smooth-transition",
                                        "group-hover/item:scale-110"
                                    )} />
                                </SidebarMenuButton>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                                side="right"
                                align="start"
                                className="w-56"
                                sideOffset={8}
                            >
                                <DropdownMenuLabel className="flex items-center gap-2">
                                    <Icon className="h-4 w-4" />
                                    {title}
                                </DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                {items.map((item) => (
                                    <DropdownMenuItem key={item.title} asChild>
                                        <Link
                                            href={item.href}
                                            prefetch
                                            className={cn(
                                                "flex items-center gap-2 w-full",
                                                page.url.startsWith(item.href) && "bg-accent text-accent-foreground"
                                            )}
                                        >
                                            {item.icon && <item.icon className="h-4 w-4" />}
                                            {item.title}
                                        </Link>
                                    </DropdownMenuItem>
                                ))}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarGroup>
        );
    }

    return (
        <SidebarGroup className={cn("px-2 py-1 sidebar-group-container", className)}>
            <Collapsible open={isExpanded} onOpenChange={toggleExpanded}>
                <CollapsibleTrigger asChild>
                    <button
                        className={cn(
                            "flex w-full items-center justify-between py-3 px-3 text-sm font-semibold",
                            "text-sidebar-foreground/80 hover:text-sidebar-foreground",
                            "hover:bg-sidebar-accent/60 rounded-lg sidebar-smooth-transition",
                            "group focus:outline-none focus:ring-2 focus:ring-sidebar-ring focus:ring-offset-2",
                            "focus:ring-offset-sidebar border border-transparent hover:border-sidebar-border/30",
                            "shadow-sm hover:shadow-md",
                            "relative overflow-hidden",
                            // Enhanced styling for expanded state
                            isExpanded && "bg-sidebar-accent/40 text-sidebar-foreground border-sidebar-border/50 shadow-md"
                        )}
                        aria-expanded={isExpanded}
                        aria-controls={`nav-group-${groupId}`}
                    >
                        {/* Subtle gradient overlay on hover */}
                        <div className="absolute inset-0 bg-gradient-to-r from-sidebar-primary/5 to-transparent opacity-0 group-hover:opacity-100 sidebar-smooth-transition" />

                        <div className="flex items-center gap-2 relative z-10">
                            {Icon && (
                                <Icon className={cn(
                                    "h-4 w-4 sidebar-smooth-transition",
                                    "group-hover:scale-105",
                                    isExpanded && "text-sidebar-primary"
                                )} />
                            )}
                            <span className="text-left tracking-wide">{title}</span>
                        </div>
                        <ChevronRight
                            className={cn(
                                "h-4 w-4 sidebar-smooth-transition relative z-10",
                                "group-hover:scale-105",
                                isExpanded && "rotate-90 text-sidebar-primary"
                            )}
                        />
                    </button>
                </CollapsibleTrigger>
                
                <CollapsibleContent
                    id={`nav-group-${groupId}`}
                    className="overflow-hidden data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down"
                >
                    <SidebarMenu className="mt-2 space-y-1 px-1">
                        {items.map((item) => (
                            <SidebarMenuItem key={item.title}>
                                <SidebarMenuButton
                                    asChild
                                    isActive={page.url.startsWith(item.href)}
                                    tooltip={{ children: item.title }}
                                    className={cn(
                                        "sidebar-smooth-transition group/item relative",
                                        "hover:bg-sidebar-accent/80 hover:text-sidebar-accent-foreground",
                                        "hover:translate-x-0.5 hover:shadow-sm",
                                        "data-[active=true]:bg-sidebar-primary data-[active=true]:text-sidebar-primary-foreground",
                                        "data-[active=true]:shadow-md data-[active=true]:border-sidebar-primary/20",
                                        "focus:ring-2 focus:ring-sidebar-ring focus:ring-offset-2 focus:ring-offset-sidebar",
                                        "rounded-lg border border-transparent hover:border-sidebar-border/20"
                                    )}
                                >
                                    <Link href={item.href} prefetch className="flex items-center gap-3 w-full">
                                        {item.icon && (
                                            <item.icon className={cn(
                                                "h-4 w-4 sidebar-smooth-transition",
                                                "group-hover/item:scale-105 group-hover/item:text-sidebar-primary"
                                            )} />
                                        )}
                                        <span className="sidebar-smooth-transition font-medium">
                                            {item.title}
                                        </span>

                                        {/* Active indicator */}
                                        <div className={cn(
                                            "ml-auto w-1.5 h-1.5 rounded-full sidebar-smooth-transition",
                                            "bg-sidebar-primary opacity-0 scale-0",
                                            page.url.startsWith(item.href) && "opacity-100 scale-100"
                                        )} />
                                    </Link>
                                </SidebarMenuButton>
                            </SidebarMenuItem>
                        ))}
                    </SidebarMenu>
                </CollapsibleContent>
            </Collapsible>
        </SidebarGroup>
    );
});
