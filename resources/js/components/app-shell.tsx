import { SidebarProvider } from '@/components/ui/sidebar';
import { Toaster } from '@/components/ui/sonner';
import { AccordionProvider } from '@/contexts/accordion-context';
import FlashMessageHandler from '@/components/flash-message-handler';
import { SharedData } from '@/types';
import { usePage } from '@inertiajs/react';

interface AppShellProps {
    children: React.ReactNode;
    variant?: 'header' | 'sidebar';
}

export function AppShell({ children, variant = 'header' }: AppShellProps) {
    const { sidebarOpen, auth } = usePage<SharedData>().props;

    // Check if user is admin and determine default expanded group
    const isAdmin = auth.user?.email === '<EMAIL>' || auth.user?.email === '<EMAIL>';
    const defaultExpandedGroupId = isAdmin ? "admin-core" : "user-platform";

    if (variant === 'header') {
        return (
            <div className="flex min-h-screen w-full flex-col">
                {children}
                <FlashMessageHandler />
                <Toaster />
            </div>
        );
    }

    return (
        <SidebarProvider defaultOpen={sidebarOpen}>
            <AccordionProvider defaultExpandedGroupId={defaultExpandedGroupId}>
                {children}
                <FlashMessageHandler />
                <Toaster />
            </AccordionProvider>
        </SidebarProvider>
    );
}
