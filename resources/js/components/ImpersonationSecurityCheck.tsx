import { useState } from 'react';
import { router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle, Eye, Clock, User } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    status: string;
    approval_status: string;
    subscription_plan: string;
    last_login_at?: string | null;
    login_count?: number;
}

interface ImpersonationSecurityCheckProps {
    user: User;
    isOpen: boolean;
    onClose: () => void;
    onConfirm: (reason: string, duration: number) => void;
}

export default function ImpersonationSecurityCheck({ 
    user, 
    isOpen, 
    onClose, 
    onConfirm 
}: ImpersonationSecurityCheckProps) {
    const [reason, setReason] = useState('');
    const [duration, setDuration] = useState(30); // Default 30 minutes
    const [isConfirming, setIsConfirming] = useState(false);
    const [securityChecks, setSecurityChecks] = useState({
        reasonProvided: false,
        durationSet: false,
        userStatusVerified: false,
        adminConfirmed: false,
    });

    // Security validation checks
    const canImpersonate = () => {
        const checks = [];
        
        // Check if user is active
        if (user.status !== 'active') {
            checks.push(`User status is "${user.status}" - only active users can be impersonated`);
        }
        
        // Check if user is approved
        if (user.approval_status !== 'approved') {
            checks.push(`User approval status is "${user.approval_status}" - only approved users can be impersonated`);
        }
        
        // Check if user has logged in recently (within last 30 days)
        if (user.last_login_at) {
            const lastLogin = new Date(user.last_login_at);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            
            if (lastLogin < thirtyDaysAgo) {
                checks.push('User has not logged in within the last 30 days');
            }
        }
        
        return checks;
    };

    const securityIssues = canImpersonate();
    const hasSecurityIssues = securityIssues.length > 0;

    const handleReasonChange = (value: string) => {
        setReason(value);
        setSecurityChecks(prev => ({
            ...prev,
            reasonProvided: value.trim().length >= 10
        }));
    };

    const handleDurationChange = (value: number) => {
        setDuration(value);
        setSecurityChecks(prev => ({
            ...prev,
            durationSet: value > 0 && value <= 480 // Max 8 hours
        }));
    };

    const handleUserStatusCheck = (checked: boolean) => {
        setSecurityChecks(prev => ({
            ...prev,
            userStatusVerified: checked
        }));
    };

    const handleAdminConfirmation = (checked: boolean) => {
        setSecurityChecks(prev => ({
            ...prev,
            adminConfirmed: checked
        }));
    };

    const canProceed = () => {
        return !hasSecurityIssues && 
               securityChecks.reasonProvided && 
               securityChecks.durationSet && 
               securityChecks.userStatusVerified && 
               securityChecks.adminConfirmed;
    };

    const handleConfirm = async () => {
        if (!canProceed()) return;
        
        setIsConfirming(true);
        
        try {
            await onConfirm(reason, duration);
        } finally {
            setIsConfirming(false);
        }
    };

    const handleClose = () => {
        setReason('');
        setDuration(30);
        setSecurityChecks({
            reasonProvided: false,
            durationSet: false,
            userStatusVerified: false,
            adminConfirmed: false,
        });
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-2xl">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Shield className="h-5 w-5 text-orange-500" />
                        Security Check: User Impersonation
                    </DialogTitle>
                    <DialogDescription>
                        You are about to impersonate <strong>{user.name}</strong> ({user.email}). 
                        Please complete the security verification below.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                    {/* Security Issues Alert */}
                    {hasSecurityIssues && (
                        <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                <div className="font-medium mb-2">Security Issues Detected:</div>
                                <ul className="list-disc list-inside space-y-1">
                                    {securityIssues.map((issue, index) => (
                                        <li key={index} className="text-sm">{issue}</li>
                                    ))}
                                </ul>
                                <div className="mt-2 text-sm">
                                    Impersonation is not recommended for this user.
                                </div>
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* User Information */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-3 flex items-center gap-2">
                            <User className="h-4 w-4" />
                            User Information
                        </h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span className="font-medium">Status:</span> 
                                <span className={`ml-2 px-2 py-1 rounded text-xs ${
                                    user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                }`}>
                                    {user.status}
                                </span>
                            </div>
                            <div>
                                <span className="font-medium">Approval:</span> 
                                <span className={`ml-2 px-2 py-1 rounded text-xs ${
                                    user.approval_status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                    {user.approval_status}
                                </span>
                            </div>
                            <div>
                                <span className="font-medium">Plan:</span> 
                                <span className="ml-2">{user.subscription_plan}</span>
                            </div>
                            <div>
                                <span className="font-medium">Last Login:</span> 
                                <span className="ml-2">
                                    {user.last_login_at ? new Date(user.last_login_at).toLocaleDateString() : 'Never'}
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Reason Input */}
                    <div className="space-y-2">
                        <Label htmlFor="reason">
                            Reason for Impersonation <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                            id="reason"
                            placeholder="Please provide a detailed reason for impersonating this user (minimum 10 characters)"
                            value={reason}
                            onChange={(e) => handleReasonChange(e.target.value)}
                            className={securityChecks.reasonProvided ? 'border-green-500' : ''}
                        />
                        <div className="text-sm text-gray-500">
                            {reason.length}/10 characters minimum
                        </div>
                    </div>

                    {/* Duration Input */}
                    <div className="space-y-2">
                        <Label htmlFor="duration" className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Session Duration (minutes) <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="duration"
                            type="number"
                            min="1"
                            max="480"
                            value={duration}
                            onChange={(e) => handleDurationChange(parseInt(e.target.value) || 0)}
                            className={securityChecks.durationSet ? 'border-green-500' : ''}
                        />
                        <div className="text-sm text-gray-500">
                            Maximum 8 hours (480 minutes)
                        </div>
                    </div>

                    {/* Security Confirmations */}
                    <div className="space-y-3">
                        <h4 className="font-medium">Security Confirmations</h4>
                        
                        <label className="flex items-start gap-3 cursor-pointer">
                            <input
                                type="checkbox"
                                checked={securityChecks.userStatusVerified}
                                onChange={(e) => handleUserStatusCheck(e.target.checked)}
                                className="mt-1"
                            />
                            <span className="text-sm">
                                I have verified that this user's status and approval are appropriate for impersonation
                            </span>
                        </label>

                        <label className="flex items-start gap-3 cursor-pointer">
                            <input
                                type="checkbox"
                                checked={securityChecks.adminConfirmed}
                                onChange={(e) => handleAdminConfirmation(e.target.checked)}
                                className="mt-1"
                            />
                            <span className="text-sm">
                                I understand that this action will be logged and audited, and I take full responsibility for this impersonation session
                            </span>
                        </label>
                    </div>
                </div>

                <DialogFooter>
                    <Button variant="outline" onClick={handleClose}>
                        Cancel
                    </Button>
                    <Button 
                        onClick={handleConfirm}
                        disabled={!canProceed() || isConfirming}
                        className="bg-orange-600 hover:bg-orange-700"
                    >
                        {isConfirming ? (
                            <>Processing...</>
                        ) : (
                            <>
                                <Eye className="h-4 w-4 mr-2" />
                                Start Impersonation
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
