import { useEffect, useState, useRef, useCallback } from 'react';
import { router } from '@inertiajs/react';
import {
    CommandDialog,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
    CommandSeparator,
} from '@/components/ui/command';
import { Badge } from '@/components/ui/badge';
import {
    Search,
    Package,
    Smartphone,
    Tags,
    Database,
    LayoutGrid,
    Settings,
    User,
    CreditCard,
    Shield,
    Activity,
    Heart,
    History,
    BarChart3,
    Users,
    FileText,
    Bell,
    Mail,
    Key,
    DollarSign,
    Wallet,
    Lock,
    Cog,
    FolderOpen,
    Upload,
    Image
} from 'lucide-react';

interface SearchItem {
    id: string;
    title: string;
    description?: string;
    href: string;
    icon: any;
    category: string;
    keywords?: string[];
    adminOnly?: boolean;
}

const searchItems: SearchItem[] = [
    // Dashboard & Core
    { id: 'dashboard', title: 'Dashboard', href: '/dashboard', icon: LayoutGrid, category: 'Core', description: 'Main dashboard overview' },
    { id: 'admin-dashboard', title: 'Admin Dashboard', href: '/admin/dashboard', icon: Shield, category: 'Admin', description: 'Administrative dashboard', adminOnly: true },
    
    // Search & Browse
    { id: 'search', title: 'Search Parts', href: '/search', icon: Search, category: 'Search', description: 'Find mobile parts', keywords: ['find', 'lookup', 'parts'] },
    { id: 'parts-index', title: 'Parts Index', href: '/parts', icon: Package, category: 'Search', description: 'Browse all parts' },
    { id: 'brands', title: 'Brands', href: '/brands', icon: Smartphone, category: 'Search', description: 'Browse by brand' },
    { id: 'categories', title: 'Categories', href: '/categories', icon: Tags, category: 'Search', description: 'Browse by category' },
    
    // User Activity
    { id: 'favorites', title: 'Favorites', href: '/favorites', icon: Heart, category: 'Activity', description: 'Your saved parts' },
    { id: 'search-history', title: 'Search History', href: '/search-history', icon: History, category: 'Activity', description: 'Recent searches' },
    { id: 'analytics', title: 'Analytics', href: '/analytics', icon: BarChart3, category: 'Activity', description: 'Usage analytics' },
    
    // Admin - Core
    { id: 'admin-users', title: 'User Management', href: '/admin/users', icon: Users, category: 'Admin', description: 'Manage users', adminOnly: true },
    { id: 'admin-analytics', title: 'Advanced Analytics', href: route('admin.analytics.index'), icon: Activity, category: 'Admin', description: 'System analytics', adminOnly: true },
    { id: 'admin-search-config', title: 'Search Configuration', href: '/admin/search-config', icon: Search, category: 'Admin', description: 'Configure search limits and settings', adminOnly: true },
    
    // Admin - Financial
    { id: 'admin-subscriptions', title: 'Subscriptions', href: '/admin/subscriptions', icon: CreditCard, category: 'Admin', description: 'Manage subscriptions', adminOnly: true },
    { id: 'admin-payments', title: 'Payment Gateways', href: '/admin/payment-gateways', icon: Wallet, category: 'Admin', description: 'Payment configuration', adminOnly: true },
    
    // Admin - Content
    { id: 'admin-parts', title: 'Manage Parts', href: '/admin/parts', icon: Package, category: 'Admin', description: 'Manage parts database', adminOnly: true },
    { id: 'admin-categories', title: 'Manage Categories', href: '/admin/categories', icon: Tags, category: 'Admin', description: 'Manage categories', adminOnly: true },
    { id: 'admin-media', title: 'Media Library', href: '/admin/media', icon: Image, category: 'Admin', description: 'Manage media files', adminOnly: true },
    { id: 'admin-brands', title: 'Manage Brands', href: '/admin/brands', icon: Smartphone, category: 'Admin', description: 'Manage brands', adminOnly: true },
    { id: 'admin-models', title: 'Manage Models', href: '/admin/models', icon: Database, category: 'Admin', description: 'Manage device models', adminOnly: true },
    { id: 'admin-bulk-import', title: 'Bulk Import', href: '/admin/bulk-import', icon: Upload, category: 'Admin', description: 'Import data in bulk', adminOnly: true },
    
    // Settings
    { id: 'profile', title: 'Profile Settings', href: '/settings/profile', icon: User, category: 'Settings', description: 'Manage your profile' },
    { id: 'password', title: 'Password Settings', href: '/settings/password', icon: Key, category: 'Settings', description: 'Change password' },
    { id: 'two-factor', title: 'Two-Factor Auth', href: '/settings/two-factor', icon: Shield, category: 'Settings', description: 'Security settings' },
    { id: 'appearance', title: 'Appearance', href: '/settings/appearance', icon: Settings, category: 'Settings', description: 'Theme and display' },
];

interface GlobalSearchCommandProps {
    isAdmin?: boolean;
}

// Singleton pattern to prevent multiple instances
let globalSearchInstance: GlobalSearchManager | null = null;

class GlobalSearchManager {
    private static instance: GlobalSearchManager | null = null;
    private isOpen = false;
    public listeners: Set<(open: boolean) => void> = new Set();
    private keydownHandler: ((e: KeyboardEvent) => void) | null = null;

    static getInstance(): GlobalSearchManager {
        if (!GlobalSearchManager.instance) {
            GlobalSearchManager.instance = new GlobalSearchManager();
        }
        return GlobalSearchManager.instance;
    }

    subscribe(listener: (open: boolean) => void): () => void {
        this.listeners.add(listener);
        return () => this.listeners.delete(listener);
    }

    toggle(): void {
        this.isOpen = !this.isOpen;
        this.notifyListeners();
    }

    close(): void {
        this.isOpen = false;
        this.notifyListeners();
    }

    private notifyListeners(): void {
        this.listeners.forEach(listener => listener(this.isOpen));
    }

    initKeyboardListener(): void {
        if (this.keydownHandler) return; // Already initialized

        this.keydownHandler = (e: KeyboardEvent) => {
            if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
                e.stopPropagation();
                this.toggle();
            }
        };

        document.addEventListener('keydown', this.keydownHandler, { capture: true });
    }

    removeKeyboardListener(): void {
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler, { capture: true });
            this.keydownHandler = null;
        }
    }
}

export function GlobalSearchCommandComponent({ isAdmin = false }: GlobalSearchCommandProps) {
    const [open, setOpen] = useState(false);
    const [isNavigating, setIsNavigating] = useState(false);
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Filter items based on admin status
    const filteredItems = searchItems.filter(item => !item.adminOnly || isAdmin);

    // Group items by category
    const groupedItems = filteredItems.reduce((acc, item) => {
        if (!acc[item.category]) {
            acc[item.category] = [];
        }
        acc[item.category].push(item);
        return acc;
    }, {} as Record<string, SearchItem[]>);

    // Initialize singleton and subscribe to state changes
    useEffect(() => {
        const searchInstance = GlobalSearchManager.getInstance();

        // Subscribe to state changes
        const unsubscribe = searchInstance.subscribe(setOpen);

        // Initialize keyboard listener (only once globally)
        searchInstance.initKeyboardListener();

        return () => {
            unsubscribe();
            // Don't remove keyboard listener here as other instances might still need it
        };
    }, []);

    // Cleanup keyboard listener when all components are unmounted
    useEffect(() => {
        return () => {
            // This will only run when the last component unmounts
            if (globalSearchInstance && globalSearchInstance.listeners.size === 0) {
                globalSearchInstance.removeKeyboardListener();
            }
        };
    }, []);

    const handleSelect = useCallback((href: string) => {
        // Clear any existing debounce timeout
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }

        // Debounce rapid selections (300ms)
        debounceTimeoutRef.current = setTimeout(async () => {
            if (isNavigating) return; // Prevent multiple simultaneous navigations

            try {
                setIsNavigating(true);
                const searchInstance = GlobalSearchManager.getInstance();
                searchInstance.close();

                await router.visit(href, {
                    onError: (errors) => {
                        console.error('Navigation error:', errors);
                        // Could add toast notification here
                    },
                    onFinish: () => {
                        setIsNavigating(false);
                    },
                    onCancel: () => {
                        setIsNavigating(false);
                    }
                });
            } catch (error) {
                console.error('Search navigation error:', error);
                setIsNavigating(false);
                // Could add toast notification here
            }
        }, 300);
    }, [isNavigating]);

    // Cleanup debounce timeout on unmount
    useEffect(() => {
        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
            }
        };
    }, []);

    const handleOpenChange = (newOpen: boolean) => {
        const searchInstance = GlobalSearchManager.getInstance();
        if (newOpen) {
            searchInstance.toggle();
        } else {
            searchInstance.close();
        }
    };

    return (
        <CommandDialog open={open} onOpenChange={handleOpenChange}>
            <CommandInput placeholder="Search for pages, features, and more..." />
            <CommandList>
                <CommandEmpty>No results found.</CommandEmpty>
                
                {Object.entries(groupedItems).map(([category, items]) => (
                    <div key={category}>
                        <CommandGroup heading={category}>
                            {items.map((item) => (
                                <CommandItem
                                    key={item.id}
                                    value={`${item.title} ${item.description} ${item.keywords?.join(' ') || ''}`}
                                    onSelect={() => handleSelect(item.href)}
                                    className="flex items-center gap-3 p-3"
                                >
                                    <item.icon className="h-4 w-4 text-muted-foreground" />
                                    <div className="flex-1">
                                        <div className="font-medium">{item.title}</div>
                                        {item.description && (
                                            <div className="text-sm text-muted-foreground">{item.description}</div>
                                        )}
                                    </div>
                                    {item.adminOnly && (
                                        <Badge variant="outline" className="text-xs">
                                            Admin
                                        </Badge>
                                    )}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                        <CommandSeparator />
                    </div>
                ))}
                
                <CommandGroup heading="Tips">
                    <CommandItem disabled className="text-sm text-muted-foreground">
                        <Search className="h-4 w-4 mr-2" />
                        Press <Badge variant="outline" className="mx-1 text-xs">⌘K</Badge> to open search anytime
                    </CommandItem>
                </CommandGroup>
            </CommandList>
        </CommandDialog>
    );
}

// Export the component with the original name for backward compatibility
export const GlobalSearchCommand = GlobalSearchCommandComponent;
