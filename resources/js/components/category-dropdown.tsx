import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { type SharedData, type Category } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { Menu, ChevronRight } from 'lucide-react';

export function CategoryDropdown() {
    const { categories } = usePage<SharedData>().props;

    // Create a mapping of category names to icons (same as in search page)
    const categoryIcons: Record<string, string> = {
        'Display': '📱',
        'Battery': '🔋',
        'Camera': '📷',
        'Charging IC': '⚡',
        'Speaker': '🔊',
        'Microphone': '🎤',
        'Screen': '📱',
        'LCD': '📱',
        'Touch': '👆',
        'Charger': '⚡',
        'Power': '🔌',
        'Audio': '🔊',
        'Sound': '🔊',
        'Mic': '🎤',
        'Lens': '📷',
        'Sensor': '📡',
        'Connector': '🔌',
        'Cable': '🔌',
        'Button': '🔘',
        'Switch': '🔘',
        'Memory': '💾',
        'Storage': '💾',
        'Processor': '🧠',
        'CPU': '🧠',
        'Antenna': '📡',
        'WiFi': '📶',
        'Bluetooth': '📶',
        'GPS': '🗺️',
        'Vibrator': '📳',
        'Motor': '⚙️',
        'Frame': '🔲',
        'Housing': '🔲',
        'Cover': '🔲',
        'Glass': '🔍',
        'Flex': '🔗',
        'Board': '🔧',
        'IC': '🔧',
        'Chip': '🔧',
        'Sensors': '📡',
        'Connectors': '🔌',
    };

    // Function to get icon for a category
    const getCategoryIcon = (categoryName: string): string => {
        // Check for exact match first
        if (categoryIcons[categoryName]) {
            return categoryIcons[categoryName];
        }

        // Check for partial matches
        const lowerName = categoryName.toLowerCase();
        for (const [key, icon] of Object.entries(categoryIcons)) {
            if (lowerName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerName)) {
                return icon;
            }
        }

        // Default icon for categories without specific icons
        return '📦';
    };

    if (!categories || categories.length === 0) {
        return null;
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                    <Menu className="h-4 w-4" />
                    Categories
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-80 p-0" align="start">
                {/* Orange header */}
                <div className="bg-orange-500 text-white px-4 py-3 flex items-center gap-2">
                    <Menu className="h-5 w-5" />
                    <span className="font-semibold">Categories</span>
                </div>
                
                {/* Category list */}
                <div className="max-h-96 overflow-y-auto">
                    {categories.map((category: Category) => (
                        <DropdownMenuItem key={category.id} asChild className="p-0">
                            <Link
                                href={route('categories.show', category.slug || category.id)}
                                className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer w-full"
                            >
                                <div className="flex items-center gap-3 flex-1">
                                    <span className="text-lg">{getCategoryIcon(category.name)}</span>
                                    <span className="text-sm font-medium text-gray-900">{category.name}</span>
                                </div>
                                <ChevronRight className="h-4 w-4 text-gray-400" />
                            </Link>
                        </DropdownMenuItem>
                    ))}
                </div>
                
                {/* All Categories link */}
                <div className="border-t">
                    <DropdownMenuItem asChild className="p-0">
                        <Link
                            href={route('search.index')}
                            className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer w-full"
                        >
                            <div className="flex items-center gap-3 flex-1">
                                <span className="text-lg">🔍</span>
                                <span className="text-sm font-medium text-gray-900">All Categories</span>
                            </div>
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                        </Link>
                    </DropdownMenuItem>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
