import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Menu, ChevronRight, ChevronDown } from 'lucide-react';

interface Category {
    id: number;
    name: string;
}

interface CategorySelectionDropdownProps {
    categories: Category[];
    selectedCategoryId: string;
    onCategorySelect: (categoryId: string) => void;
    getCategoryIcon: (categoryName: string) => string;
}

export function CategorySelectionDropdown({ 
    categories, 
    selectedCategoryId, 
    onCategorySelect, 
    getCategoryIcon 
}: CategorySelectionDropdownProps) {
    const selectedCategory = categories.find(cat => cat.id.toString() === selectedCategoryId);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                    <div className="flex items-center gap-2">
                        <Menu className="h-4 w-4" />
                        <span>
                            {selectedCategory ? selectedCategory.name : 'All Categories'}
                        </span>
                    </div>
                    <ChevronDown className="h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-80 p-0" align="start">
                {/* Orange header */}
                <div className="bg-orange-500 text-white px-4 py-3 flex items-center gap-2">
                    <Menu className="h-5 w-5" />
                    <span className="font-semibold">Categories</span>
                </div>
                
                {/* All Categories option */}
                <DropdownMenuItem 
                    className="flex items-center gap-3 px-4 py-3 cursor-pointer"
                    onClick={() => onCategorySelect('all')}
                >
                    <div className="flex items-center gap-3 flex-1">
                        <span className="text-lg">🔍</span>
                        <span className="text-sm font-medium text-gray-900">All Categories</span>
                    </div>
                    {selectedCategoryId === 'all' && (
                        <div className="w-2 h-2 bg-orange-500 rounded-full" />
                    )}
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                </DropdownMenuItem>
                
                {/* Category list */}
                <div className="max-h-96 overflow-y-auto">
                    {categories.map((category) => (
                        <DropdownMenuItem 
                            key={category.id}
                            className="flex items-center gap-3 px-4 py-3 cursor-pointer"
                            onClick={() => onCategorySelect(category.id.toString())}
                        >
                            <div className="flex items-center gap-3 flex-1">
                                <span className="text-lg">{getCategoryIcon(category.name)}</span>
                                <span className="text-sm font-medium text-gray-900">{category.name}</span>
                            </div>
                            {selectedCategoryId === category.id.toString() && (
                                <div className="w-2 h-2 bg-orange-500 rounded-full" />
                            )}
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                        </DropdownMenuItem>
                    ))}
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
