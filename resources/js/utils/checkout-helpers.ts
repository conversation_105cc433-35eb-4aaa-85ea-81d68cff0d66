/**
 * Checkout utility functions for payment processing
 */

/**
 * Get CSRF token from various sources with fallback methods
 */
export function getCsrfToken(): string {
    // Try meta tag first (most common)
    const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (metaToken) {
        if (process.env.NODE_ENV === 'development') {
            console.log('CSRF token found in meta tag:', {
                length: metaToken.length,
                preview: metaToken.substring(0, 10) + '...'
            });
        }
        return metaToken;
    }

    // Try hidden input field
    const inputToken = document.querySelector('input[name="_token"]')?.getAttribute('value');
    if (inputToken) {
        if (process.env.NODE_ENV === 'development') {
            console.log('CSRF token found in input field:', {
                length: inputToken.length,
                preview: inputToken.substring(0, 10) + '...'
            });
        }
        return inputToken;
    }

    // Try from window object (if set by <PERSON><PERSON>)
    const windowToken = (window as any).Laravel?.csrfToken;
    if (windowToken) {
        if (process.env.NODE_ENV === 'development') {
            console.log('CSRF token found in window object:', {
                length: windowToken.length,
                preview: windowToken.substring(0, 10) + '...'
            });
        }
        return windowToken;
    }

    if (process.env.NODE_ENV === 'development') {
        console.error('CSRF token not found in any location!', {
            metaTag: !!document.querySelector('meta[name="csrf-token"]'),
            inputField: !!document.querySelector('input[name="_token"]'),
            windowObject: !!(window as any).Laravel?.csrfToken
        });
    }

    return '';
}

/**
 * Validate CSRF token presence and format
 */
export function validateCsrfToken(token: string): boolean {
    return token.length >= 40; // Laravel CSRF tokens are typically 40+ characters
}

/**
 * Create standardized headers for API requests
 */
export function createApiHeaders(csrfToken: string): Record<string, string> {
    return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-CSRF-TOKEN': csrfToken,
        'X-Requested-With': 'XMLHttpRequest',
    };
}

/**
 * Handle API response errors with proper error messages
 */
export async function handleApiResponse(response: Response): Promise<any> {
    // Debug response for development
    if (process.env.NODE_ENV === 'development') {
        console.log('API Response:', {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            url: response.url
        });
    }

    // Handle non-JSON responses (like HTML error pages)
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();

        if (process.env.NODE_ENV === 'development') {
            console.error('Non-JSON response received:', {
                status: response.status,
                contentType,
                responsePreview: textResponse.substring(0, 200) + '...'
            });
        }

        // Handle specific error cases
        if (response.status === 419) {
            throw new Error('CSRF token mismatch');
        } else if (response.status === 401) {
            throw new Error('Authentication required. Please log in and try again.');
        } else if (response.status === 403) {
            throw new Error('Access denied. Please check your permissions.');
        } else if (response.status === 422) {
            throw new Error('Invalid data provided. Please check your input and try again.');
        } else if (response.status === 503) {
            throw new Error('Service temporarily unavailable. Please try again later or use another payment method.');
        } else if (response.status >= 500) {
            throw new Error('Server error. Please try again later.');
        } else {
            throw new Error(`Request failed (${response.status}). Please try again.`);
        }
    }

    const data = await response.json();

    if (!response.ok) {
        // Handle development mode errors more gracefully
        if (data.development_mode) {
            console.warn('Development Mode Error:', {
                error: data.error,
                help: data.help,
                documentation: data.documentation,
                current_price_id: data.current_price_id,
                plan_name: data.plan_name
            });

            // Provide specific error messages for different development issues
            if (data.current_price_id && data.plan_name) {
                throw new Error(`Development Mode: Placeholder price IDs detected for "${data.plan_name}" plan.\n\nHelp: ${data.help || 'Configure real Paddle price IDs in your dashboard.'}`);
            } else {
                throw new Error(`Development Mode: ${data.error}\n\nHelp: ${data.help || 'Configure real Paddle credentials for testing.'}`);
            }
        }

        // Handle specific Coinbase Commerce configuration errors
        if (data.error && data.error.includes('Coinbase Commerce is not configured')) {
            throw new Error('Cryptocurrency payments are not configured. Please contact support or use another payment method.');
        }

        // Handle CSRF-related errors from JSON responses
        if (response.status === 419 || (data.error && data.error.toLowerCase().includes('csrf'))) {
            throw new Error('CSRF token mismatch');
        }

        throw new Error(data.error || data.message || `Request failed with status ${response.status}`);
    }

    return data;
}

/**
 * Create a checkout request with proper error handling
 */
export async function createCheckoutRequest(
    url: string,
    planId: number,
    billingCycle: 'month' | 'year',
    paymentMethod: string
): Promise<{ checkout_url?: string; hosted_url?: string; charge_id?: string; transaction_id: string }> {
    // Get and validate CSRF token
    const csrfToken = getCsrfToken();
    if (!csrfToken) {
        if (process.env.NODE_ENV === 'development') {
            console.error('CSRF token not found during checkout request');
        }
        throw new Error('CSRF token not found. Please refresh the page and try again.');
    }

    if (!validateCsrfToken(csrfToken)) {
        if (process.env.NODE_ENV === 'development') {
            console.error('Invalid CSRF token during checkout request:', {
                length: csrfToken.length,
                preview: csrfToken.substring(0, 10) + '...'
            });
        }
        throw new Error('Invalid CSRF token. Please refresh the page and try again.');
    }

    // Debug logging for development
    if (process.env.NODE_ENV === 'development') {
        console.log(`${paymentMethod} Checkout Debug:`, {
            plan_id: planId,
            billing_cycle: billingCycle,
            csrf_token_length: csrfToken.length,
            csrf_token_preview: csrfToken.substring(0, 10) + '...',
            url,
            headers: createApiHeaders(csrfToken)
        });
    }

    // Make the request
    const response = await fetch(url, {
        method: 'POST',
        headers: createApiHeaders(csrfToken),
        body: JSON.stringify({
            plan_id: planId,
            pricing_plan_id: planId, // For Coinbase Commerce compatibility
            billing_cycle: billingCycle,
        }),
    });

    const data = await handleApiResponse(response);

    // Validate response data - support both checkout_url (Paddle/ShurjoPay) and hosted_url (Coinbase Commerce)
    if (!data.checkout_url && !data.hosted_url) {
        throw new Error('No checkout URL received from server');
    }

    if (process.env.NODE_ENV === 'development') {
        console.log(`Redirecting to ${paymentMethod} checkout:`, data.checkout_url || data.hosted_url);
    }

    return data;
}

/**
 * Refresh CSRF token by making a request to get a new one
 */
export async function refreshCsrfToken(): Promise<string> {
    try {
        const response = await fetch('/csrf-token', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });

        if (response.ok) {
            const data = await response.json();
            if (data.csrf_token) {
                // Update the meta tag
                const metaTag = document.querySelector('meta[name="csrf-token"]');
                if (metaTag) {
                    metaTag.setAttribute('content', data.csrf_token);
                }
                return data.csrf_token;
            }
        }
    } catch (error) {
        console.error('Failed to refresh CSRF token:', error);
    }

    throw new Error('Failed to refresh CSRF token');
}

/**
 * Test CSRF token validity by making a simple request
 */
export async function testCsrfToken(): Promise<boolean> {
    try {
        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            if (process.env.NODE_ENV === 'development') {
                console.error('No CSRF token found for testing');
            }
            return false;
        }

        const response = await fetch('/csrf-token', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrfToken,
            },
        });

        if (process.env.NODE_ENV === 'development') {
            console.log('CSRF token test result:', {
                status: response.status,
                ok: response.ok,
                token_used: csrfToken.substring(0, 10) + '...'
            });
        }

        return response.ok;
    } catch (error) {
        if (process.env.NODE_ENV === 'development') {
            console.error('CSRF token test failed:', error);
        }
        return false;
    }
}

/**
 * Attempt to fix CSRF issues using the backend fix endpoint
 */
export async function fixCsrfIssues(): Promise<string> {
    try {
        const currentToken = getCsrfToken();

        if (process.env.NODE_ENV === 'development') {
            console.log('Attempting to fix CSRF issues with current token:', {
                token_present: !!currentToken,
                token_length: currentToken?.length || 0,
                token_preview: currentToken ? currentToken.substring(0, 10) + '...' : 'none'
            });
        }

        const response = await fetch('/csrf-fix', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': currentToken,
            },
        });

        if (process.env.NODE_ENV === 'development') {
            console.log('CSRF fix response:', {
                status: response.status,
                ok: response.ok,
                contentType: response.headers.get('Content-Type')
            });
        }

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.new_token) {
                // Update the meta tag
                const metaTag = document.querySelector('meta[name="csrf-token"]');
                if (metaTag) {
                    metaTag.setAttribute('content', data.new_token);
                }

                if (process.env.NODE_ENV === 'development') {
                    console.log('CSRF issues fixed:', {
                        fixes_applied: data.fixes_applied,
                        new_token_length: data.new_token.length,
                        new_token_preview: data.new_token.substring(0, 10) + '...'
                    });
                }

                return data.new_token;
            }
        }
    } catch (error) {
        console.error('Failed to fix CSRF issues:', error);
    }

    throw new Error('Failed to fix CSRF issues');
}

/**
 * Create a checkout request with CSRF token retry logic
 */
export async function createCheckoutRequestWithRetry(
    url: string,
    planId: number,
    billingCycle: 'month' | 'year',
    paymentMethod: string,
    maxRetries: number = 1
): Promise<{ checkout_url?: string; hosted_url?: string; charge_id?: string; transaction_id: string }> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await createCheckoutRequest(url, planId, billingCycle, paymentMethod);
        } catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');

            // If it's a CSRF error and we haven't exhausted retries, try refreshing the token
            const isCsrfError = lastError.message.includes('CSRF token mismatch') ||
                               lastError.message.includes('Session expired') ||
                               lastError.message.includes('419');

            if (attempt < maxRetries && isCsrfError) {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`Attempt ${attempt + 1} failed with CSRF error: "${lastError.message}", refreshing token...`);
                }

                try {
                    // First test if the current CSRF token is working
                    const tokenIsValid = await testCsrfToken();
                    if (process.env.NODE_ENV === 'development') {
                        console.log('CSRF token validity test:', tokenIsValid);
                    }

                    if (!tokenIsValid) {
                        // Try the comprehensive fix first, then fallback to simple refresh
                        try {
                            await fixCsrfIssues();
                            if (process.env.NODE_ENV === 'development') {
                                console.log('CSRF issues fixed, retrying request...');
                            }
                        } catch (fixError) {
                            console.warn('CSRF fix failed, trying simple refresh:', fixError);
                            await refreshCsrfToken();
                            if (process.env.NODE_ENV === 'development') {
                                console.log('CSRF token refreshed, retrying request...');
                            }
                        }
                    } else {
                        // Token seems valid, but request still failed - might be a different issue
                        if (process.env.NODE_ENV === 'development') {
                            console.warn('CSRF token appears valid but request failed. This might indicate a different issue.');
                        }
                        // Still try to refresh the token as a last resort
                        await refreshCsrfToken();
                    }

                    continue; // Retry with new token
                } catch (refreshError) {
                    console.error('Failed to refresh CSRF token:', refreshError);
                    break; // Don't retry if token refresh fails
                }
            }

            // For other errors or if we've exhausted retries, break
            break;
        }
    }

    throw lastError || new Error('Checkout request failed');
}

/**
 * Validate CSRF setup on page load
 */
export function validateCsrfSetup(): void {
    if (process.env.NODE_ENV !== 'development') {
        return;
    }

    const csrfToken = getCsrfToken();
    const metaTag = document.querySelector('meta[name="csrf-token"]');

    console.log('CSRF Setup Validation:', {
        meta_tag_exists: !!metaTag,
        meta_tag_content_length: metaTag?.getAttribute('content')?.length || 0,
        csrf_token_found: !!csrfToken,
        csrf_token_length: csrfToken?.length || 0,
        csrf_token_valid: validateCsrfToken(csrfToken),
        page_url: window.location.href,
        user_agent: navigator.userAgent.substring(0, 50) + '...'
    });

    if (!csrfToken) {
        console.error('❌ CSRF token not found! This will cause checkout failures.');
    } else if (!validateCsrfToken(csrfToken)) {
        console.error('❌ CSRF token is invalid! This will cause checkout failures.');
    } else {
        console.log('✅ CSRF token setup appears correct.');
    }
}

/**
 * Log checkout errors for debugging
 */
export function logCheckoutError(paymentMethod: string, error: unknown): void {
    console.error(`${paymentMethod} payment error:`, error);

    if (process.env.NODE_ENV === 'development') {
        const csrfToken = getCsrfToken();
        console.error('Checkout Error Details:', {
            paymentMethod,
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : error,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            csrf_debug: {
                token_present: !!csrfToken,
                token_length: csrfToken?.length || 0,
                token_preview: csrfToken ? csrfToken.substring(0, 10) + '...' : 'none',
                meta_tag_exists: !!document.querySelector('meta[name="csrf-token"]'),
                meta_tag_content: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')?.substring(0, 10) + '...' || 'none'
            }
        });
    }
}
