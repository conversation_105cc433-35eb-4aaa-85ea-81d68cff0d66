import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { XCircle, ArrowLeft, FileText, CreditCard } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

export default function Cancelled() {
    return (
        <AppLayout>
            <Head title="Payment Cancelled" />

            <div className="py-12">
                <div className="max-w-2xl mx-auto sm:px-6 lg:px-8">
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                            <XCircle className="w-8 h-8 text-orange-600" />
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Payment Cancelled
                        </h1>
                        <p className="text-gray-600">
                            Your payment was cancelled and no charges were made
                        </p>
                    </div>

                    <Card className="mb-8">
                        <CardHeader className="text-center">
                            <CardTitle>What happened?</CardTitle>
                            <CardDescription>
                                You cancelled the payment process before completion
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h3 className="font-semibold text-blue-900 mb-2">Don't worry!</h3>
                                <p className="text-sm text-blue-700">
                                    No charges were made to your payment method. You can try again anytime 
                                    or choose a different payment option.
                                </p>
                            </div>

                            <div className="space-y-3">
                                <h3 className="font-semibold">Alternative options:</h3>
                                <div className="grid gap-3">
                                    <div className="flex items-center gap-3 p-3 border rounded-lg">
                                        <CreditCard className="w-5 h-5 text-blue-600" />
                                        <div>
                                            <div className="font-medium">Try payment again</div>
                                            <div className="text-sm text-gray-600">
                                                Return to checkout and complete your subscription
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center gap-3 p-3 border rounded-lg">
                                        <FileText className="w-5 h-5 text-green-600" />
                                        <div>
                                            <div className="font-medium">Offline payment</div>
                                            <div className="text-sm text-gray-600">
                                                Submit a payment request for manual processing
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="pt-4 space-y-3">
                                <Link href={route('subscription.plans')}>
                                    <Button className="w-full" size="lg">
                                        <CreditCard className="w-4 h-4 mr-2" />
                                        Try Payment Again
                                    </Button>
                                </Link>
                                
                                <Link href={route('payment-requests.create')}>
                                    <Button variant="outline" className="w-full">
                                        <FileText className="w-4 h-4 mr-2" />
                                        Submit Offline Payment
                                    </Button>
                                </Link>

                                <Link href={route('dashboard')}>
                                    <Button variant="ghost" className="w-full">
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Return to Dashboard
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="text-center">
                        <p className="text-sm text-gray-600 mb-4">
                            Having trouble with payment? We're here to help!
                        </p>
                        <p className="text-sm text-gray-500">
                            Contact us: <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
                        </p>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
