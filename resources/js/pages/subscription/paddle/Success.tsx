import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, ArrowRight, CreditCard } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Props {
    transaction_id?: string;
}

export default function Success({ transaction_id }: Props) {
    return (
        <AppLayout>
            <Head title="Payment Successful" />

            <div className="py-12">
                <div className="max-w-2xl mx-auto sm:px-6 lg:px-8">
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                            <CheckCircle className="w-8 h-8 text-green-600" />
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Payment Successful!
                        </h1>
                        <p className="text-gray-600">
                            Your subscription has been activated successfully
                        </p>
                    </div>

                    <Card className="mb-8">
                        <CardHeader className="text-center">
                            <CardTitle className="flex items-center justify-center gap-2">
                                <CreditCard className="w-5 h-5" />
                                Subscription Activated
                            </CardTitle>
                            <CardDescription>
                                You now have access to all premium features
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {transaction_id && (
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <div className="text-sm">
                                        <span className="font-medium">Transaction ID:</span>
                                        <span className="ml-2 font-mono text-gray-600">{transaction_id}</span>
                                    </div>
                                </div>
                            )}

                            <div className="space-y-3">
                                <h3 className="font-semibold">What's included in your subscription:</h3>
                                <ul className="space-y-2">
                                    <li className="flex items-center gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                        <span>Unlimited searches</span>
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                        <span>High-resolution images</span>
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                        <span>Detailed specifications</span>
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                        <span>Priority support</span>
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                        <span>Advanced filters</span>
                                    </li>
                                </ul>
                            </div>

                            <div className="pt-4 space-y-3">
                                <Link href={route('dashboard')}>
                                    <Button className="w-full" size="lg">
                                        Start Exploring
                                        <ArrowRight className="w-4 h-4 ml-2" />
                                    </Button>
                                </Link>
                                
                                <Link href={route('subscription.dashboard')}>
                                    <Button variant="outline" className="w-full">
                                        View Subscription Details
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="text-center">
                        <p className="text-sm text-gray-600 mb-4">
                            You will receive a confirmation email shortly with your receipt and subscription details.
                        </p>
                        <p className="text-sm text-gray-500">
                            Need help? <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">Contact Support</a>
                        </p>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
