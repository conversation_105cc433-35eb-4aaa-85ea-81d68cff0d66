import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
    Crown, 
    Search, 
    Calendar, 
    TrendingUp, 
    Star,
    ArrowRight,
    CheckCircle,
    Clock
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Subscription {
    id: number;
    plan_name: string;
    status: string;
    current_period_start: string;
    current_period_end: string;
}

interface Plan {
    id: number;
    name: string;
    price: number;
    billing_cycle: string;
    features: string[];
    is_popular: boolean;
}

interface Props {
    subscription: Subscription | null;
    currentPlan: string;
    remainingSearches: number;
    plans: Plan[];
}

export default function SubscriptionIndex({ subscription, currentPlan, remainingSearches, plans }: Props) {
    const isPremium = currentPlan === 'premium';
    const isUnlimited = remainingSearches === -1;

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-100 text-green-800">Active</Badge>;
            case 'cancelled':
                return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
            case 'expired':
                return <Badge className="bg-gray-100 text-gray-800">Expired</Badge>;
            default:
                return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <AppLayout>
            <Head title="Subscription Management" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Subscription Management</h1>
                            <p className="text-muted-foreground mt-2">
                                Manage your subscription and view usage statistics
                            </p>
                        </div>
                        {!isPremium && (
                            <Link href="/subscription/checkout">
                                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                                    <Crown className="h-4 w-4 mr-2" />
                                    Upgrade to Premium
                                </Button>
                            </Link>
                        )}
                    </div>

                    {/* Current Status */}
                    <div className="grid gap-4 md:grid-cols-3">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Current Plan</CardTitle>
                                <Crown className={`h-4 w-4 ${isPremium ? 'text-yellow-600' : 'text-gray-400'}`} />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold capitalize">{currentPlan}</div>
                                {subscription && (
                                    <div className="mt-2">
                                        {getStatusBadge(subscription.status)}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Daily Searches</CardTitle>
                                <Search className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {isUnlimited ? 'Unlimited' : remainingSearches}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    {isUnlimited ? 'Premium benefits' : 'Remaining today'}
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Next Billing</CardTitle>
                                <Calendar className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {subscription ? formatDate(subscription.current_period_end) : 'N/A'}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    {subscription ? 'Auto-renewal' : 'No active subscription'}
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Subscription Details */}
                    {subscription && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                    Active Subscription
                                </CardTitle>
                                <CardDescription>
                                    Your current subscription details and billing information
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div>
                                        <h4 className="font-medium text-sm text-muted-foreground">Plan Details</h4>
                                        <div className="mt-2 space-y-1">
                                            <p><strong>Plan:</strong> {subscription.plan_name}</p>
                                            <p><strong>Status:</strong> {getStatusBadge(subscription.status)}</p>
                                            <p><strong>Started:</strong> {formatDate(subscription.current_period_start)}</p>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 className="font-medium text-sm text-muted-foreground">Billing Information</h4>
                                        <div className="mt-2 space-y-1">
                                            <p><strong>Next Billing:</strong> {formatDate(subscription.current_period_end)}</p>
                                            <p><strong>Auto-Renewal:</strong> Enabled</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Available Plans */}
                    {!isPremium && (
                        <div className="space-y-4">
                            <h2 className="text-2xl font-bold">Available Plans</h2>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {plans.map((plan) => (
                                    <Card key={plan.id} className={`relative ${plan.is_popular ? 'border-purple-200 shadow-lg' : ''}`}>
                                        {plan.is_popular && (
                                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                                <Badge className="bg-purple-600 text-white">
                                                    <Star className="h-3 w-3 mr-1" />
                                                    Most Popular
                                                </Badge>
                                            </div>
                                        )}
                                        <CardHeader>
                                            <CardTitle className="flex items-center justify-between">
                                                <span className="capitalize">{plan.name}</span>
                                                <Crown className={`h-5 w-5 ${plan.name === 'premium' ? 'text-yellow-600' : 'text-gray-400'}`} />
                                            </CardTitle>
                                            <CardDescription>
                                                <span className="text-3xl font-bold">${plan.price}</span>
                                                <span className="text-muted-foreground">/{plan.billing_cycle}</span>
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <ul className="space-y-2 mb-4">
                                                {plan.features.map((feature, index) => (
                                                    <li key={index} className="flex items-center gap-2">
                                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                                        <span className="text-sm">{feature}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                            <Link href={`/subscription/checkout?plan=${plan.name}`}>
                                                <Button className="w-full" variant={plan.is_popular ? 'default' : 'outline'}>
                                                    Choose {plan.name}
                                                    <ArrowRight className="h-4 w-4 ml-2" />
                                                </Button>
                                            </Link>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Quick Actions */}
                    <div className="grid gap-4 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Usage Statistics
                                </CardTitle>
                                <CardDescription>
                                    View detailed analytics of your search activity
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Link href="/subscription/search-stats">
                                    <Button variant="outline" className="w-full">
                                        View Statistics
                                        <ArrowRight className="h-4 w-4 ml-2" />
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="h-5 w-5" />
                                    Subscription Dashboard
                                </CardTitle>
                                <CardDescription>
                                    Manage your subscription settings and billing
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Link href="/subscription/dashboard">
                                    <Button variant="outline" className="w-full">
                                        Manage Subscription
                                        <ArrowRight className="h-4 w-4 ml-2" />
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
