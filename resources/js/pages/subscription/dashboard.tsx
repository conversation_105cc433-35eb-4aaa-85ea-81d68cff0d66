import { Head, Link, router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import {
    Search,
    TrendingUp,
    Crown,
    AlertCircle,
    Activity,
    Zap,
    BarChart3,
    Clock,
    CheckCircle,
    XCircle,
    ArrowUpRight,
    Settings,
    Star
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Subscription {
    id: number;
    plan_name: string;
    status: string;
    current_period_start: string;
    current_period_end: string;
}

interface SearchHistory {
    id: number;
    search_query: string;
    search_type: string;
    results_count: number;
    created_at: string;
}

interface Props {
    subscription: Subscription | null;
    currentPlan: string;
    remainingSearches: number;
    searchHistory: SearchHistory[];
}

export default function Dashboard({ subscription, currentPlan, remainingSearches, searchHistory }: Props) {
    const handleCancelSubscription = () => {
        if (confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
            router.post(route('subscription.cancel'));
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <AppLayout>
            <Head title="Subscription Dashboard" />

            <div className="bg-gradient-to-br from-background via-muted/20 to-background min-h-screen">
                <div className="container mx-auto px-4 py-6 max-w-7xl">
                    {/* Modern Header Section */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-4">
                            <div>
                                <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center gap-3">
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                        <Crown className="w-6 h-6 text-primary" />
                                    </div>
                                    Subscription Dashboard
                                </h1>
                                <p className="text-muted-foreground text-lg">
                                    Manage your subscription and monitor your usage statistics
                                </p>
                            </div>
                            <div className="flex gap-3">
                                <Link href={route('subscription.plans')}>
                                    <Button variant="outline" className="gap-2">
                                        <Settings className="w-4 h-4" />
                                        Manage Plans
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* Stats Overview Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        {/* Current Plan Status */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Current Plan</p>
                                        <div className="flex items-center gap-2 mt-1">
                                            {currentPlan === 'premium' && <Crown className="w-4 h-4 text-primary" />}
                                            <p className="text-2xl font-bold text-foreground">
                                                {currentPlan === 'premium' ? 'Premium' : 'Free'}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="p-3 bg-primary/10 rounded-full">
                                        <Star className="w-6 h-6 text-primary" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Subscription Status */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Status</p>
                                        <div className="flex items-center gap-2 mt-1">
                                            {subscription?.status === 'active' ? (
                                                <CheckCircle className="w-4 h-4 text-green-500" />
                                            ) : (
                                                <XCircle className="w-4 h-4 text-red-500" />
                                            )}
                                            <p className="text-2xl font-bold text-foreground capitalize">
                                                {subscription?.status || 'Free'}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="p-3 bg-green-500/10 rounded-full">
                                        <Activity className="w-6 h-6 text-green-500" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Daily Searches */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Daily Searches</p>
                                        <p className="text-2xl font-bold text-foreground mt-1">
                                            {remainingSearches === -1 ? '∞' : remainingSearches}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            {remainingSearches === -1 ? 'Unlimited' : 'Remaining today'}
                                        </p>
                                    </div>
                                    <div className="p-3 bg-blue-500/10 rounded-full">
                                        <Zap className="w-6 h-6 text-blue-500" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Total Searches */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Total Searches</p>
                                        <p className="text-2xl font-bold text-foreground mt-1">
                                            {searchHistory.length}
                                        </p>
                                        <p className="text-xs text-muted-foreground">All time</p>
                                    </div>
                                    <div className="p-3 bg-purple-500/10 rounded-full">
                                        <BarChart3 className="w-6 h-6 text-purple-500" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Main Content Grid */}
                    <div className="grid lg:grid-cols-3 gap-6">
                        {/* Subscription Details Card */}
                        <Card className="lg:col-span-2 shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="pb-4">
                                <CardTitle className="flex items-center gap-3 text-xl">
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                        <Crown className="w-5 h-5 text-primary" />
                                    </div>
                                    Subscription Details
                                </CardTitle>
                                <CardDescription className="text-base">
                                    Manage your current subscription and billing information
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Plan Information */}
                                <div className="bg-muted/30 rounded-lg p-4 border border-border">
                                    <div className="grid md:grid-cols-2 gap-4">
                                        <div className="space-y-3">
                                            <div className="flex items-center justify-between">
                                                <span className="text-sm font-medium text-muted-foreground">Plan Type</span>
                                                <Badge
                                                    variant={currentPlan === 'premium' ? 'default' : 'secondary'}
                                                    className="text-sm px-3 py-1"
                                                >
                                                    {currentPlan === 'premium' ? 'Premium' : 'Free Plan'}
                                                </Badge>
                                            </div>

                                            {subscription && (
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm font-medium text-muted-foreground">Status</span>
                                                    <Badge
                                                        variant={subscription.status === 'active' ? 'default' : 'destructive'}
                                                        className="text-sm px-3 py-1"
                                                    >
                                                        {subscription.status}
                                                    </Badge>
                                                </div>
                                            )}
                                        </div>

                                        {subscription && (
                                            <div className="space-y-3">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm font-medium text-muted-foreground">Billing Period</span>
                                                    <div className="text-right">
                                                        <p className="text-sm font-medium text-foreground">
                                                            {formatDate(subscription.current_period_start)}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            to {formatDate(subscription.current_period_end)}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Search Quota */}
                                <div className="bg-muted/30 rounded-lg p-4 border border-border">
                                    <div className="flex items-center justify-between mb-3">
                                        <h4 className="font-semibold text-foreground">Search Quota</h4>
                                        <div className="flex items-center gap-2">
                                            <Zap className="w-4 h-4 text-primary" />
                                            <span className="text-sm font-medium text-foreground">
                                                {remainingSearches === -1 ? 'Unlimited' : `${remainingSearches} left`}
                                            </span>
                                        </div>
                                    </div>

                                    {remainingSearches !== -1 && (
                                        <div className="space-y-2">
                                            <div className="w-full bg-muted rounded-full h-2">
                                                <div
                                                    className="bg-primary h-2 rounded-full transition-all duration-300"
                                                    style={{
                                                        width: `${remainingSearches > 0 ? (remainingSearches / 20) * 100 : 0}%`
                                                    }}
                                                />
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                                Daily limit resets at midnight
                                            </p>
                                        </div>
                                    )}
                                </div>

                                {/* Action Buttons */}
                                <div className="flex flex-wrap gap-3 pt-4">
                                    {currentPlan === 'free' ? (
                                        <Link href={route('subscription.plans')}>
                                            <Button className="gap-2 bg-primary hover:bg-primary/90">
                                                <ArrowUpRight className="w-4 h-4" />
                                                Upgrade to Premium
                                            </Button>
                                        </Link>
                                    ) : (
                                        <Button
                                            variant="destructive"
                                            onClick={handleCancelSubscription}
                                            className="gap-2"
                                        >
                                            <XCircle className="w-4 h-4" />
                                            Cancel Subscription
                                        </Button>
                                    )}

                                    <Link href={route('subscription.plans')}>
                                        <Button variant="outline" className="gap-2">
                                            <Settings className="w-4 h-4" />
                                            View All Plans
                                        </Button>
                                    </Link>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Quick Actions & Alerts */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="pb-4">
                                <CardTitle className="flex items-center gap-3 text-xl">
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                        <TrendingUp className="w-5 h-5 text-primary" />
                                    </div>
                                    Quick Actions
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* Search Limit Alert */}
                                {remainingSearches === 0 && (
                                    <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
                                        <div className="flex items-start gap-3">
                                            <AlertCircle className="w-5 h-5 text-destructive mt-0.5" />
                                            <div>
                                                <h4 className="font-semibold text-destructive mb-1">Daily Limit Reached</h4>
                                                <p className="text-sm text-destructive/80 mb-3">
                                                    You've used all your daily searches. Upgrade to Premium for unlimited access.
                                                </p>
                                                <Link href={route('subscription.plans')}>
                                                    <Button size="sm" className="bg-primary hover:bg-primary/90">
                                                        Upgrade Now
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Low Search Warning */}
                                {remainingSearches > 0 && remainingSearches <= 5 && remainingSearches !== -1 && (
                                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                                        <div className="flex items-start gap-3">
                                            <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                                            <div>
                                                <h4 className="font-semibold text-yellow-700 mb-1">Running Low</h4>
                                                <p className="text-sm text-yellow-600 mb-3">
                                                    Only {remainingSearches} searches remaining today. Consider upgrading for unlimited access.
                                                </p>
                                                <Link href={route('subscription.plans')}>
                                                    <Button size="sm" variant="outline" className="border-yellow-500 text-yellow-700 hover:bg-yellow-50">
                                                        View Plans
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Usage Summary */}
                                <div className="bg-muted/30 rounded-lg p-4 border border-border">
                                    <h4 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                                        <BarChart3 className="w-4 h-4 text-primary" />
                                        Usage Summary
                                    </h4>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="text-center">
                                            <div className="text-lg font-bold text-primary">
                                                {searchHistory.length}
                                            </div>
                                            <div className="text-xs text-muted-foreground">Total Searches</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-lg font-bold text-green-600">
                                                {remainingSearches === -1 ? '∞' : remainingSearches}
                                            </div>
                                            <div className="text-xs text-muted-foreground">
                                                {remainingSearches === -1 ? 'Unlimited' : 'Remaining'}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Quick Actions */}
                                <div className="space-y-2">
                                    <Link href="/search" className="block">
                                        <Button variant="outline" className="w-full justify-start gap-2">
                                            <Search className="w-4 h-4" />
                                            Start New Search
                                        </Button>
                                    </Link>
                                    <Link href={route('subscription.plans')} className="block">
                                        <Button variant="outline" className="w-full justify-start gap-2">
                                            <Crown className="w-4 h-4" />
                                            Compare Plans
                                        </Button>
                                    </Link>
                                </div>
                            </CardContent>
                        </Card>

                    </div>

                    {/* Recent Searches Section */}
                    <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm mt-6">
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="flex items-center gap-3 text-xl">
                                        <div className="p-2 bg-primary/10 rounded-lg">
                                            <Search className="w-5 h-5 text-primary" />
                                        </div>
                                        Recent Search Activity
                                    </CardTitle>
                                    <CardDescription className="text-base mt-1">
                                        Track your latest searches and their results
                                    </CardDescription>
                                </div>
                                <Badge variant="secondary" className="text-sm px-3 py-1">
                                    {searchHistory.length} searches
                                </Badge>
                            </div>
                        </CardHeader>
                        <CardContent>
                            {searchHistory.length > 0 ? (
                                <div className="space-y-1">
                                    {searchHistory.map((search, index) => (
                                        <div
                                            key={search.id}
                                            className="group flex items-center justify-between p-4 rounded-lg border border-border/50 hover:border-primary/30 hover:bg-muted/30 transition-all duration-200"
                                        >
                                            <div className="flex items-center gap-4 flex-1 min-w-0">
                                                <div className="flex-shrink-0">
                                                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                                                        <Search className="w-4 h-4 text-primary" />
                                                    </div>
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center gap-2 mb-1">
                                                        <h4 className="font-semibold text-foreground truncate">
                                                            {search.search_query}
                                                        </h4>
                                                        <Badge variant="outline" className="text-xs px-2 py-0.5 flex-shrink-0">
                                                            {search.search_type}
                                                        </Badge>
                                                    </div>
                                                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                                        <span className="flex items-center gap-1">
                                                            <BarChart3 className="w-3 h-3" />
                                                            {search.results_count} results
                                                        </span>
                                                        <span className="flex items-center gap-1">
                                                            <Clock className="w-3 h-3" />
                                                            {formatDate(search.created_at)}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex-shrink-0 ml-4">
                                                <div className="text-right">
                                                    <div className="text-sm font-medium text-foreground">
                                                        #{index + 1}
                                                    </div>
                                                    <div className="text-xs text-muted-foreground">
                                                        Search
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <div className="w-20 h-20 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <Search className="w-8 h-8 text-muted-foreground" />
                                    </div>
                                    <h3 className="text-lg font-semibold text-foreground mb-2">No searches yet</h3>
                                    <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                                        Start exploring our mobile parts database to see your search history here
                                    </p>
                                    <Link href="/search">
                                        <Button className="gap-2">
                                            <Search className="w-4 h-4" />
                                            Start Your First Search
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
