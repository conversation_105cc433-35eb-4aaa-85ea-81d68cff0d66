import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Shield, Clock, CheckCircle, Star } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { PaymentMethodSelector } from '@/components/PaymentMethodSelector';
import { CheckoutDebugPanel } from '@/components/CheckoutDebugPanel';
import { PaddleProvider } from '@/contexts/PaddleContext';
import { useState, useEffect } from 'react';
import { PricingPlan } from '@/types';
import { validateCsrfSetup } from '@/utils/checkout-helpers';

interface Props {
    plan: PricingPlan;
    currentPlan: string;
    remainingSearches: number;
}

export default function Checkout({ plan }: Props) {
    const [billingCycle, setBillingCycle] = useState<'month' | 'year'>('month');

    // Validate CSRF setup on page load for debugging
    useEffect(() => {
        validateCsrfSetup();
    }, []);

    return (
        <AppLayout>
            <Head title={`Checkout - ${plan.display_name}`} />

            <PaddleProvider>
                {/* Modern gradient background */}
                <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
                    <div className="py-8 sm:py-12">
                        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                            {/* Header with enhanced styling */}
                            <div className="mb-8 sm:mb-12">
                                <Link href={route('subscription.plans')}>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="mb-6 hover:bg-primary/5 transition-colors duration-200 border-border/50"
                                    >
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Back to Plans
                                    </Button>
                                </Link>

                                <div className="text-center space-y-4">
                                    <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
                                        <Star className="w-4 h-4" />
                                        Premium Subscription
                                    </div>
                                    <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mb-3">
                                        Subscribe to {plan.display_name}
                                    </h1>
                                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                                        Choose your preferred payment method and get instant access to premium features
                                    </p>
                                </div>
                            </div>

                            {/* Enhanced Plan Summary */}
                            <div className="grid lg:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12">
                                {/* Main Plan Card */}
                                <div className="lg:col-span-2">
                                    <Card className="border-0 shadow-xl bg-gradient-to-br from-card to-card/50 backdrop-blur-sm">
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center justify-between">
                                                <div>
                                                    <CardTitle className="text-xl sm:text-2xl font-bold text-foreground">
                                                        {plan.display_name}
                                                    </CardTitle>
                                                    <CardDescription className="text-base text-muted-foreground mt-2">
                                                        {plan.description}
                                                    </CardDescription>
                                                </div>
                                                {plan.is_popular && (
                                                    <div className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-semibold">
                                                        Most Popular
                                                    </div>
                                                )}
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-6">
                                            {/* Pricing Display */}
                                            <div className="bg-muted/30 rounded-xl p-4 sm:p-6">
                                                <div className="flex items-baseline gap-2 mb-2">
                                                    <span className="text-3xl sm:text-4xl font-bold text-foreground">
                                                        ${plan.price}
                                                    </span>
                                                    <span className="text-muted-foreground">
                                                        /{plan.interval}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-muted-foreground">
                                                    Billed {plan.interval}ly • Cancel anytime
                                                </p>
                                            </div>

                                            {/* Key Features Grid */}
                                            <div className="grid sm:grid-cols-2 gap-4">
                                                <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                                                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                                        <CheckCircle className="w-4 h-4 text-primary" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium text-sm">Search Limit</div>
                                                        <div className="text-xs text-muted-foreground">
                                                            {plan.search_limit === -1 ? 'Unlimited' : `${plan.search_limit} per day`}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                                                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                                        <Shield className="w-4 h-4 text-primary" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium text-sm">Support</div>
                                                        <div className="text-xs text-muted-foreground">Priority Email</div>
                                                    </div>
                                                </div>

                                                <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                                                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                                        <CheckCircle className="w-4 h-4 text-primary" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium text-sm">High-res Images</div>
                                                        <div className="text-xs text-muted-foreground">Included</div>
                                                    </div>
                                                </div>

                                                <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                                                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                                        <Clock className="w-4 h-4 text-primary" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium text-sm">Activation</div>
                                                        <div className="text-xs text-muted-foreground">Instant</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>

                                {/* Features Sidebar */}
                                <div className="lg:col-span-1">
                                    <Card className="border-0 shadow-lg bg-gradient-to-br from-primary/5 to-primary/10 h-full">
                                        <CardHeader>
                                            <CardTitle className="text-lg font-semibold flex items-center gap-2">
                                                <Star className="w-5 h-5 text-primary" />
                                                Features Included
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <ul className="space-y-3">
                                                {plan.features.map((feature, index) => (
                                                    <li key={index} className="flex items-start gap-3">
                                                        <div className="w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                                                            <CheckCircle className="w-3 h-3 text-primary" />
                                                        </div>
                                                        <span className="text-sm text-foreground leading-relaxed">{feature}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>

                            {/* Enhanced Payment Method Selection */}
                            <div className="max-w-6xl mx-auto">
                                <Card className="border-0 shadow-xl bg-gradient-to-br from-card to-card/50 backdrop-blur-sm">
                                    <CardHeader className="text-center pb-6">
                                        <CardTitle className="text-2xl font-bold text-foreground">
                                            Choose Payment Method
                                        </CardTitle>
                                        <CardDescription className="text-base text-muted-foreground">
                                            Select your preferred payment option to complete your subscription
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <PaymentMethodSelector
                                            plan={plan}
                                            billingCycle={billingCycle}
                                            onBillingCycleChange={setBillingCycle}
                                        />
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Trust Indicators */}
                            <div className="mt-8 sm:mt-12 max-w-6xl mx-auto">
                                <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                                    {/* Security Notice */}
                                    <Card className="border-0 bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-950/20 dark:to-green-900/10">
                                        <CardContent className="p-4 sm:p-6">
                                            <div className="flex items-start gap-3">
                                                <div className="w-10 h-10 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                                    <Shield className="w-5 h-5 text-green-600 dark:text-green-400" />
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-green-900 dark:text-green-100 mb-1">
                                                        Secure & Encrypted
                                                    </h3>
                                                    <p className="text-sm text-green-700 dark:text-green-300">
                                                        Your payment information is protected with industry-standard security measures
                                                    </p>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Instant Activation */}
                                    <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-950/20 dark:to-blue-900/10">
                                        <CardContent className="p-4 sm:p-6">
                                            <div className="flex items-start gap-3">
                                                <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                                    <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
                                                        Instant Activation
                                                    </h3>
                                                    <p className="text-sm text-blue-700 dark:text-blue-300">
                                                        Your subscription will be activated immediately after payment
                                                    </p>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Support */}
                                    <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-950/20 dark:to-purple-900/10 sm:col-span-2 lg:col-span-1">
                                        <CardContent className="p-4 sm:p-6">
                                            <div className="flex items-start gap-3">
                                                <div className="w-10 h-10 bg-purple-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                                    <Clock className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-1">
                                                        24/7 Support
                                                    </h3>
                                                    <p className="text-sm text-purple-700 dark:text-purple-300 mb-2">
                                                        Need help? We're here for you
                                                    </p>
                                                    <a
                                                        href="mailto:<EMAIL>"
                                                        className="text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 transition-colors"
                                                    >
                                                        Contact Support →
                                                    </a>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Debug Panel for Development */}
                <CheckoutDebugPanel plan={plan} />
            </PaddleProvider>
        </AppLayout>
    );
}
