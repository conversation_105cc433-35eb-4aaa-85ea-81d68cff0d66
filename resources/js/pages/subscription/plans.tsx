import { Head, Link, router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Zap } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Plan {
    id?: number;
    name: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_popular?: boolean;
    formatted_price?: string;
    metadata?: Record<string, unknown>;
}

interface Props {
    plans: Record<string, Plan>;
    currentPlan: string;
    remainingSearches: number;
}

export default function Plans({ plans, currentPlan, remainingSearches }: Props) {
    const handleUpgrade = (planKey: string) => {
        // Navigate to checkout page with plan selection
        router.get(route('subscription.checkout', { plan: planKey }));
    };

    return (
        <AppLayout>
            <Head title="Subscription Plans" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Choose Your Plan
                        </h1>
                        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                            Get unlimited access to our comprehensive mobile parts database
                        </p>
                        {currentPlan === 'free' && (
                            <div className="mt-4">
                                <Badge variant="outline" className="text-sm">
                                    {remainingSearches === -1 
                                        ? 'Unlimited searches remaining' 
                                        : `${remainingSearches} searches remaining today`
                                    }
                                </Badge>
                            </div>
                        )}
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                        {Object.entries(plans).map(([key, plan]) => (
                            <Card
                                key={key}
                                className={`relative ${
                                    plan.is_popular
                                        ? 'border-blue-500 shadow-lg scale-105'
                                        : plan.metadata?.color === 'purple'
                                        ? 'border-purple-300 shadow-md'
                                        : 'border-gray-200'
                                } ${currentPlan === key ? 'ring-2 ring-green-500' : ''}`}
                            >
                                {plan.is_popular && (
                                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                        <Badge className="bg-blue-500 text-white px-4 py-1">
                                            <Crown className="w-4 h-4 mr-1" />
                                            Most Popular
                                        </Badge>
                                    </div>
                                )}

                                {plan.metadata?.color === 'purple' && !plan.is_popular && (
                                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                        <Badge className="bg-purple-600 text-white px-4 py-1">
                                            Enterprise
                                        </Badge>
                                    </div>
                                )}
                                
                                {currentPlan === key && (
                                    <div className="absolute -top-4 right-4">
                                        <Badge className="bg-green-500 text-white px-3 py-1">
                                            Current Plan
                                        </Badge>
                                    </div>
                                )}

                                <CardHeader className="text-center pb-8">
                                    <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2">
                                        {plan.is_popular && <Zap className="w-6 h-6 text-blue-500" />}
                                        {plan.name}
                                    </CardTitle>
                                    <div className="mt-4">
                                        <span className="text-4xl font-bold">
                                            {plan.formatted_price || `$${plan.price}`}
                                        </span>
                                        {!plan.formatted_price && (
                                            <span className="text-gray-600">/{plan.interval}</span>
                                        )}
                                    </div>
                                    <CardDescription className="mt-2">
                                        {plan.metadata?.color === 'purple'
                                            ? 'Custom solutions for large organizations'
                                            : plan.search_limit === -1
                                            ? 'Unlimited access for professionals'
                                            : `Perfect for occasional searches (${plan.search_limit} per day)`
                                        }
                                    </CardDescription>
                                </CardHeader>

                                <CardContent>
                                    <ul className="space-y-3">
                                        {plan.features.map((feature, index) => (
                                            <li key={index} className="flex items-center gap-3">
                                                <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                                                <span className="text-gray-700">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </CardContent>

                                <CardFooter>
                                    {currentPlan === key ? (
                                        <Button
                                            className="w-full"
                                            variant="outline"
                                            disabled
                                        >
                                            Current Plan
                                        </Button>
                                    ) : plan.metadata?.contact_sales ? (
                                        <Button
                                            className="w-full bg-purple-600 hover:bg-purple-700"
                                            onClick={() => window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank')}
                                        >
                                            Contact Sales
                                        </Button>
                                    ) : plan.price > 0 ? (
                                        <Button
                                            className="w-full bg-blue-600 hover:bg-blue-700"
                                            onClick={() => handleUpgrade(key)}
                                        >
                                            Upgrade to {plan.name}
                                        </Button>
                                    ) : (
                                        <Button
                                            className="w-full"
                                            variant="outline"
                                            disabled
                                        >
                                            Free Plan
                                        </Button>
                                    )}
                                </CardFooter>
                            </Card>
                        ))}
                    </div>

                    <div className="text-center mt-12">
                        <p className="text-gray-600 mb-4">
                            Need help choosing? <Link href="#" className="text-blue-600 hover:underline">Contact our support team</Link>
                        </p>
                        <Link 
                            href={route('subscription.dashboard')} 
                            className="text-blue-600 hover:underline"
                        >
                            Manage your subscription →
                        </Link>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
