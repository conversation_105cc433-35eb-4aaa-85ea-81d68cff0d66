import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    Calendar, 
    Search, 
    TrendingUp, 
    Crown, 
    AlertCircle, 
    BarChart3,
    PieChart,
    Download,
    Filter,
    Target,
    Activity,
    Clock,
    CheckCircle,
    XCircle
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface SearchStats {
    remaining_searches: number;
    is_premium: boolean;
    current_plan: string;
    daily_searches: number;
    weekly_searches: number;
    monthly_searches: number;
    total_searches: number;
    search_success_rate: number;
    most_searched_terms: Array<{
        term: string;
        count: number;
        success_rate: number;
    }>;
    search_types_breakdown: Array<{
        type: string;
        count: number;
        percentage: number;
    }>;
    daily_usage_chart: Array<{
        date: string;
        searches: number;
        successful: number;
    }>;
    monthly_trends: Array<{
        month: string;
        searches: number;
        plan: string;
    }>;
    popular_categories: Array<{
        category: string;
        searches: number;
        percentage: number;
    }>;
    usage_recommendations: Array<{
        type: string;
        message: string;
        action?: string;
    }>;
}

interface Props {
    stats: SearchStats;
    plan_limits: {
        free_daily_limit: number;
        premium_daily_limit: number;
    };
}

export default function SearchStatsPage({ stats, plan_limits }: Props) {
    const [dateRange, setDateRange] = useState('30d');

    const getUsagePercentage = () => {
        if (stats.is_premium) return 0; // Unlimited
        return (stats.daily_searches / plan_limits.free_daily_limit) * 100;
    };

    const getUsageStatus = () => {
        const percentage = getUsagePercentage();
        if (stats.is_premium) return { color: 'text-green-600', status: 'Unlimited' };
        if (percentage >= 90) return { color: 'text-red-600', status: 'Critical' };
        if (percentage >= 70) return { color: 'text-yellow-600', status: 'Warning' };
        return { color: 'text-green-600', status: 'Good' };
    };

    const exportData = () => {
        // Implementation for data export
        console.log('Exporting search statistics...');
    };

    return (
        <AppLayout>
            <Head title="Search Statistics" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-8">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Search Analytics</h1>
                            <p className="text-gray-600 mt-2">
                                Comprehensive insights into your search patterns and usage
                            </p>
                        </div>
                        <div className="flex items-center gap-3">
                            <select 
                                value={dateRange} 
                                onChange={(e) => setDateRange(e.target.value)}
                                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                            >
                                <option value="7d">Last 7 days</option>
                                <option value="30d">Last 30 days</option>
                                <option value="90d">Last 90 days</option>
                                <option value="1y">Last year</option>
                            </select>
                            <Button variant="outline" size="sm" onClick={exportData}>
                                <Download className="w-4 h-4 mr-2" />
                                Export
                            </Button>
                        </div>
                    </div>

                    {/* Key Metrics Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        {/* Current Plan Status */}
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Current Plan</p>
                                        <div className="flex items-center gap-2 mt-1">
                                            <p className="text-2xl font-bold text-gray-900 capitalize">
                                                {stats.current_plan}
                                            </p>
                                            {stats.is_premium && <Crown className="w-5 h-5 text-yellow-500" />}
                                        </div>
                                    </div>
                                    <div className={`text-right ${getUsageStatus().color}`}>
                                        <p className="text-sm font-medium">{getUsageStatus().status}</p>
                                        {!stats.is_premium && (
                                            <p className="text-xs">
                                                {stats.remaining_searches} left today
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Daily Searches */}
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Today's Searches</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.daily_searches}</p>
                                    </div>
                                    <Search className="w-8 h-8 text-blue-500" />
                                </div>
                                {!stats.is_premium && (
                                    <div className="mt-3">
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div 
                                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                                style={{ width: `${Math.min(getUsagePercentage(), 100)}%` }}
                                            />
                                        </div>
                                        <p className="text-xs text-gray-500 mt-1">
                                            {Math.round(getUsagePercentage())}% of daily limit
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Success Rate */}
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Success Rate</p>
                                        <p className="text-2xl font-bold text-gray-900">
                                            {Math.round(stats.search_success_rate)}%
                                        </p>
                                    </div>
                                    <Target className="w-8 h-8 text-green-500" />
                                </div>
                                <p className="text-xs text-gray-500 mt-2">
                                    Searches with results
                                </p>
                            </CardContent>
                        </Card>

                        {/* Total Searches */}
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Total Searches</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total_searches}</p>
                                    </div>
                                    <Activity className="w-8 h-8 text-purple-500" />
                                </div>
                                <p className="text-xs text-gray-500 mt-2">
                                    All time activity
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Usage Recommendations */}
                    {stats.usage_recommendations.length > 0 && (
                        <Card className="mb-8">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertCircle className="w-5 h-5" />
                                    Usage Insights & Recommendations
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {stats.usage_recommendations.map((rec, index) => (
                                        <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                                            <div className="flex-1">
                                                <p className="text-sm font-medium text-blue-900">{rec.message}</p>
                                                {rec.action && (
                                                    <p className="text-xs text-blue-700 mt-1">{rec.action}</p>
                                                )}
                                            </div>
                                            {rec.type === 'upgrade' && (
                                                <Link href={route('subscription.plans')}>
                                                    <Button size="sm" variant="outline">
                                                        Upgrade
                                                    </Button>
                                                </Link>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Charts Row */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        {/* Daily Usage Chart */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="w-5 h-5" />
                                    Daily Usage Trend
                                </CardTitle>
                                <CardDescription>
                                    Search activity over the last {dateRange === '7d' ? '7 days' : '30 days'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="h-64 flex items-end justify-between gap-2">
                                    {stats.daily_usage_chart.slice(-7).map((day, index) => (
                                        <div key={index} className="flex-1 flex flex-col items-center">
                                            <div className="w-full bg-gray-200 rounded-t-md relative" style={{ height: '200px' }}>
                                                <div 
                                                    className="w-full bg-blue-500 rounded-t-md absolute bottom-0"
                                                    style={{ 
                                                        height: `${Math.max((day.searches / Math.max(...stats.daily_usage_chart.map(d => d.searches))) * 100, 5)}%` 
                                                    }}
                                                />
                                                <div 
                                                    className="w-full bg-green-500 rounded-t-md absolute bottom-0 opacity-70"
                                                    style={{ 
                                                        height: `${Math.max((day.successful / Math.max(...stats.daily_usage_chart.map(d => d.searches))) * 100, 2)}%` 
                                                    }}
                                                />
                                            </div>
                                            <p className="text-xs text-gray-600 mt-2">
                                                {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
                                            </p>
                                            <p className="text-xs font-medium">{day.searches}</p>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex items-center gap-4 mt-4 text-xs">
                                    <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-blue-500 rounded" />
                                        <span>Total Searches</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-green-500 rounded" />
                                        <span>Successful</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Search Types Breakdown */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <PieChart className="w-5 h-5" />
                                    Search Types Distribution
                                </CardTitle>
                                <CardDescription>
                                    How you search for mobile parts
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {stats.search_types_breakdown.map((type, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div 
                                                    className="w-4 h-4 rounded"
                                                    style={{ 
                                                        backgroundColor: [
                                                            '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
                                                        ][index % 5] 
                                                    }}
                                                />
                                                <span className="text-sm font-medium capitalize">
                                                    {type.type.replace('_', ' ')}
                                                </span>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-bold">{type.count}</p>
                                                <p className="text-xs text-gray-500">{type.percentage}%</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Detailed Statistics */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                        {/* Most Searched Terms */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="w-5 h-5" />
                                    Top Search Terms
                                </CardTitle>
                                <CardDescription>
                                    Your most frequently searched parts
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {stats.most_searched_terms.slice(0, 8).map((term, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <p className="text-sm font-medium text-gray-900 truncate">
                                                    {term.term}
                                                </p>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <div className="w-full bg-gray-200 rounded-full h-1.5 max-w-[100px]">
                                                        <div
                                                            className="bg-blue-500 h-1.5 rounded-full"
                                                            style={{
                                                                width: `${(term.count / Math.max(...stats.most_searched_terms.map(t => t.count))) * 100}%`
                                                            }}
                                                        />
                                                    </div>
                                                    <span className="text-xs text-gray-500">{term.count}</span>
                                                </div>
                                            </div>
                                            <div className="ml-3 text-right">
                                                <Badge
                                                    variant={term.success_rate > 80 ? "default" : term.success_rate > 50 ? "secondary" : "destructive"}
                                                    className="text-xs"
                                                >
                                                    {Math.round(term.success_rate)}%
                                                </Badge>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Popular Categories */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Filter className="w-5 h-5" />
                                    Popular Categories
                                </CardTitle>
                                <CardDescription>
                                    Most searched part categories
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {stats.popular_categories.map((category, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <p className="text-sm font-medium text-gray-900">
                                                    {category.category}
                                                </p>
                                                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                                                    <div
                                                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                                                        style={{ width: `${category.percentage}%` }}
                                                    />
                                                </div>
                                            </div>
                                            <div className="ml-3 text-right">
                                                <p className="text-sm font-bold">{category.searches}</p>
                                                <p className="text-xs text-gray-500">{category.percentage}%</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Usage Summary */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="w-5 h-5" />
                                    Usage Summary
                                </CardTitle>
                                <CardDescription>
                                    Your search activity breakdown
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <Calendar className="w-5 h-5 text-blue-500" />
                                            <span className="text-sm font-medium">This Week</span>
                                        </div>
                                        <span className="text-lg font-bold">{stats.weekly_searches}</span>
                                    </div>

                                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <Calendar className="w-5 h-5 text-green-500" />
                                            <span className="text-sm font-medium">This Month</span>
                                        </div>
                                        <span className="text-lg font-bold">{stats.monthly_searches}</span>
                                    </div>

                                    <Separator />

                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <CheckCircle className="w-4 h-4 text-green-500" />
                                                <span className="text-sm">Successful</span>
                                            </div>
                                            <span className="text-sm font-medium">
                                                {Math.round((stats.search_success_rate / 100) * stats.total_searches)}
                                            </span>
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <XCircle className="w-4 h-4 text-red-500" />
                                                <span className="text-sm">No Results</span>
                                            </div>
                                            <span className="text-sm font-medium">
                                                {stats.total_searches - Math.round((stats.search_success_rate / 100) * stats.total_searches)}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Monthly Trends */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="w-5 h-5" />
                                Monthly Usage Trends
                            </CardTitle>
                            <CardDescription>
                                Your search activity and plan changes over time
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <div className="flex items-end gap-4 min-w-[600px] h-48">
                                    {stats.monthly_trends.map((month, index) => (
                                        <div key={index} className="flex-1 flex flex-col items-center">
                                            <div className="w-full bg-gray-200 rounded-t-md relative" style={{ height: '160px' }}>
                                                <div
                                                    className={`w-full rounded-t-md absolute bottom-0 ${
                                                        month.plan === 'premium' ? 'bg-yellow-500' : 'bg-blue-500'
                                                    }`}
                                                    style={{
                                                        height: `${Math.max((month.searches / Math.max(...stats.monthly_trends.map(m => m.searches))) * 100, 5)}%`
                                                    }}
                                                />
                                            </div>
                                            <div className="text-center mt-2">
                                                <p className="text-xs text-gray-600">{month.month}</p>
                                                <p className="text-xs font-medium">{month.searches}</p>
                                                {month.plan === 'premium' && (
                                                    <Crown className="w-3 h-3 text-yellow-500 mx-auto mt-1" />
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex items-center gap-4 mt-4 text-xs">
                                    <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-blue-500 rounded" />
                                        <span>Free Plan</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-yellow-500 rounded" />
                                        <span>Premium Plan</span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Action Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Optimize Your Searches</CardTitle>
                                <CardDescription>
                                    Tips to get better results from your searches
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-start gap-3">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                                        <p className="text-sm text-gray-700">
                                            Use specific part numbers when available for exact matches
                                        </p>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                                        <p className="text-sm text-gray-700">
                                            Try searching by mobile model for compatible parts
                                        </p>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                                        <p className="text-sm text-gray-700">
                                            Use category filters to narrow down results
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Need More Searches?</CardTitle>
                                <CardDescription>
                                    Upgrade to Premium for unlimited access
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="flex items-center gap-3">
                                        <CheckCircle className="w-5 h-5 text-green-500" />
                                        <span className="text-sm">Unlimited daily searches</span>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <CheckCircle className="w-5 h-5 text-green-500" />
                                        <span className="text-sm">High-resolution part images</span>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <CheckCircle className="w-5 h-5 text-green-500" />
                                        <span className="text-sm">Priority customer support</span>
                                    </div>
                                    <Link href={route('subscription.plans')}>
                                        <Button className="w-full mt-4">
                                            <Crown className="w-4 h-4 mr-2" />
                                            Upgrade to Premium
                                        </Button>
                                    </Link>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
