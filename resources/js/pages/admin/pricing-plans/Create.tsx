import { Head, Link, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save, Plus, X } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface PricingPlanFormData {
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_active: boolean;
    is_default: boolean;
    is_popular: boolean;
    sort_order: number;
    metadata: Record<string, string | number | boolean>;
    paddle_price_id_monthly: string;
    paddle_price_id_yearly: string;
    paddle_product_id: string;
    online_payment_enabled: boolean;
    offline_payment_enabled: boolean;
    [key: string]: string | number | boolean | string[] | Record<string, string | number | boolean>;
}

export default function Create() {
    const { data, setData, post, processing, errors } = useForm<PricingPlanFormData>({
        name: '',
        display_name: '',
        description: '',
        price: 0,
        currency: 'USD',
        interval: 'month',
        features: [''],
        search_limit: 20,
        is_active: true,
        is_default: false,
        is_popular: false,
        sort_order: 1,
        metadata: {},
        paddle_price_id_monthly: '',
        paddle_price_id_yearly: '',
        paddle_product_id: '',
        online_payment_enabled: true,
        offline_payment_enabled: true,
    });

    const [features, setFeatures] = useState(['']);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.pricing-plans.store'), {
            onSuccess: () => {
                // Success handled by redirect
            },
        });
    };

    const addFeature = () => {
        const newFeatures = [...features, ''];
        setFeatures(newFeatures);
        setData('features', newFeatures);
    };

    const removeFeature = (index: number) => {
        const newFeatures = features.filter((_, i) => i !== index);
        setFeatures(newFeatures);
        setData('features', newFeatures);
    };

    const updateFeature = (index: number, value: string) => {
        const newFeatures = [...features];
        newFeatures[index] = value;
        setFeatures(newFeatures);
        setData('features', newFeatures);
    };

    return (
        <AppLayout>
            <Head title="Create Pricing Plan" />
            
            <div className="flex h-full flex-1 flex-col p-4 space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href={route('admin.pricing-plans.index')}>
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Plans
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Create Pricing Plan</h1>
                        <p className="text-muted-foreground mt-2">
                            Create a new subscription pricing plan
                        </p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid gap-6 lg:grid-cols-2">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Basic Information</CardTitle>
                                <CardDescription>
                                    Configure the basic details of the pricing plan
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Plan Name (Internal)</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., premium, enterprise"
                                        required
                                    />
                                    {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="display_name">Display Name</Label>
                                    <Input
                                        id="display_name"
                                        value={data.display_name}
                                        onChange={(e) => setData('display_name', e.target.value)}
                                        placeholder="e.g., Premium Plan"
                                        required
                                    />
                                    {errors.display_name && <p className="text-sm text-destructive">{errors.display_name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Brief description of the plan"
                                        rows={3}
                                    />
                                    {errors.description && <p className="text-sm text-destructive">{errors.description}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Pricing */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Pricing</CardTitle>
                                <CardDescription>
                                    Set the price and billing interval
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="price">Price</Label>
                                        <Input
                                            id="price"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            value={data.price}
                                            onChange={(e) => setData('price', parseFloat(e.target.value) || 0)}
                                            required
                                        />
                                        {errors.price && <p className="text-sm text-destructive">{errors.price}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="currency">Currency</Label>
                                        <Select value={data.currency} onValueChange={(value) => setData('currency', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="USD">USD</SelectItem>
                                                <SelectItem value="EUR">EUR</SelectItem>
                                                <SelectItem value="GBP">GBP</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.currency && <p className="text-sm text-destructive">{errors.currency}</p>}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="interval">Billing Interval</Label>
                                    <Select value={data.interval} onValueChange={(value) => setData('interval', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="month">Monthly</SelectItem>
                                            <SelectItem value="year">Yearly</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.interval && <p className="text-sm text-destructive">{errors.interval}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="search_limit">Search Limit (per day)</Label>
                                    <Input
                                        id="search_limit"
                                        type="number"
                                        min="-1"
                                        value={data.search_limit}
                                        onChange={(e) => setData('search_limit', parseInt(e.target.value) || 0)}
                                        placeholder="-1 for unlimited"
                                        required
                                    />
                                    <p className="text-xs text-muted-foreground">Use -1 for unlimited searches</p>
                                    {errors.search_limit && <p className="text-sm text-destructive">{errors.search_limit}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="sort_order">Sort Order</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        min="0"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        required
                                    />
                                    {errors.sort_order && <p className="text-sm text-destructive">{errors.sort_order}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Features */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Features</CardTitle>
                                <CardDescription>
                                    List the features included in this plan
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {features.map((feature, index) => (
                                    <div key={index} className="flex gap-2">
                                        <Input
                                            value={feature}
                                            onChange={(e) => updateFeature(index, e.target.value)}
                                            placeholder="Enter feature description"
                                        />
                                        {features.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeFeature(index)}
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addFeature}
                                >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Feature
                                </Button>
                                {errors.features && <p className="text-sm text-destructive">{errors.features}</p>}
                            </CardContent>
                        </Card>

                        {/* Settings */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Settings</CardTitle>
                                <CardDescription>
                                    Configure plan visibility and behavior
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Active</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Make this plan available for subscription
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.is_active}
                                        onCheckedChange={(checked) => setData('is_active', checked)}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Default Plan</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Assign to new users by default
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.is_default}
                                        onCheckedChange={(checked) => setData('is_default', checked)}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Popular Plan</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Highlight this plan as popular
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.is_popular}
                                        onCheckedChange={(checked) => setData('is_popular', checked)}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        {/* Payment Methods */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Payment Methods</CardTitle>
                                <CardDescription>
                                    Configure available payment options for this plan
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Online Payments</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Allow credit card payments via Paddle
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.online_payment_enabled}
                                        onCheckedChange={(checked) => setData('online_payment_enabled', checked)}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Offline Payments</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Allow manual payment requests and verification
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.offline_payment_enabled}
                                        onCheckedChange={(checked) => setData('offline_payment_enabled', checked)}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        {/* Paddle Integration */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Paddle Integration</CardTitle>
                                <CardDescription>
                                    Configure Paddle payment gateway settings
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="paddle_product_id">Paddle Product ID</Label>
                                    <Input
                                        id="paddle_product_id"
                                        value={data.paddle_product_id}
                                        onChange={(e) => setData('paddle_product_id', e.target.value)}
                                        placeholder="pro_01h1abc123def456ghi789jkl"
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Product ID from your Paddle dashboard
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="paddle_price_id_monthly">Monthly Price ID</Label>
                                    <Input
                                        id="paddle_price_id_monthly"
                                        value={data.paddle_price_id_monthly}
                                        onChange={(e) => setData('paddle_price_id_monthly', e.target.value)}
                                        placeholder="pri_01h1abc123def456ghi789jkl"
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Monthly billing price ID from Paddle
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="paddle_price_id_yearly">Yearly Price ID</Label>
                                    <Input
                                        id="paddle_price_id_yearly"
                                        value={data.paddle_price_id_yearly}
                                        onChange={(e) => setData('paddle_price_id_yearly', e.target.value)}
                                        placeholder="pri_01h1def456ghi789jkl123abc"
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Yearly billing price ID from Paddle
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-4">
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Creating...' : 'Create Plan'}
                        </Button>
                        <Link href={route('admin.pricing-plans.index')}>
                            <Button variant="outline">Cancel</Button>
                        </Link>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
