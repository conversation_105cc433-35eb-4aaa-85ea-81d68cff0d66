import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Activity, 
    ArrowLeft, 
    User,
    Calendar,
    Shield,
    Search,
    Eye,
    FileText,
    Globe
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface ActivityMetadata {
    [key: string]: string | number | boolean | null | undefined;
}

interface UserActivityLog {
    id: number;
    user_id: number;
    activity_type: string;
    description: string;
    ip_address: string | null;
    performed_by: number | null;
    metadata: ActivityMetadata | null;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
    };
    performedBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Props {
    activity: UserActivityLog;
}

const ActivityTypeBadge = ({ type }: { type: string }) => {
    const getTypeConfig = (activityType: string) => {
        if (activityType.includes('login') || activityType.includes('logout')) {
            return { className: 'bg-blue-100 text-blue-800', icon: Shield };
        }
        if (activityType.includes('search')) {
            return { className: 'bg-green-100 text-green-800', icon: Search };
        }
        if (activityType.includes('favorite')) {
            return { className: 'bg-purple-100 text-purple-800', icon: Eye };
        }
        if (activityType.includes('subscription')) {
            return { className: 'bg-orange-100 text-orange-800', icon: FileText };
        }
        if (activityType.includes('admin') || activityType.includes('approval')) {
            return { className: 'bg-red-100 text-red-800', icon: Shield };
        }
        return { className: 'bg-gray-100 text-gray-800', icon: Activity };
    };

    const { className, icon: Icon } = getTypeConfig(type);

    return (
        <Badge className={`flex items-center gap-1 ${className}`}>
            <Icon className="h-3 w-3" />
            {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </Badge>
    );
};

export default function ShowActivity({ activity }: Props) {
    return (
        <AppLayout>
            <Head title={`Activity: ${activity.activity_type}`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href="/admin/activities">
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Activities
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground">Activity Details</h1>
                                <p className="text-muted-foreground mt-2">
                                    View activity log information
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Activity Details */}
                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Activity className="h-5 w-5" />
                                    Activity Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Activity Type</label>
                                    <div className="mt-1">
                                        <ActivityTypeBadge type={activity.activity_type} />
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                                    <p className="text-sm">{activity.description}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Timestamp</label>
                                    <div className="mt-1 flex items-center gap-2">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <span>{new Date(activity.created_at).toLocaleString()}</span>
                                    </div>
                                </div>

                                {activity.ip_address && (
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">IP Address</label>
                                        <div className="mt-1 flex items-center gap-2">
                                            <Globe className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-mono text-sm">{activity.ip_address}</span>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    User & Performer
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">User</label>
                                    <div className="mt-1">
                                        <p className="font-medium">{activity.user?.name || 'Unknown User'}</p>
                                        <p className="text-sm text-muted-foreground">{activity.user?.email || 'No email'}</p>
                                    </div>
                                </div>

                                {activity.performedBy && (
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Performed By</label>
                                        <div className="mt-1">
                                            <p className="font-medium">{activity.performedBy.name}</p>
                                            <p className="text-sm text-muted-foreground">{activity.performedBy.email}</p>
                                        </div>
                                    </div>
                                )}

                                {!activity.performedBy && (
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Performed By</label>
                                        <div className="mt-1">
                                            <p className="text-sm text-muted-foreground">System / User Action</p>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Metadata */}
                    {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    Additional Data
                                </CardTitle>
                                <CardDescription>
                                    Metadata associated with this activity
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                                    {JSON.stringify(activity.metadata, null, 2)}
                                </pre>
                            </CardContent>
                        </Card>
                    )}

                    {/* Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Actions</CardTitle>
                            <CardDescription>
                                Related actions for this activity
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-4">
                                {activity.user?.id && (
                                    <>
                                        <Link href={`/admin/users/${activity.user.id}`}>
                                            <Button variant="outline">
                                                <User className="h-4 w-4 mr-2" />
                                                View User Profile
                                            </Button>
                                        </Link>

                                        <Link href={`/admin/activities?user_id=${activity.user.id}`}>
                                            <Button variant="outline">
                                                <Activity className="h-4 w-4 mr-2" />
                                                User's Activities
                                            </Button>
                                        </Link>
                                    </>
                                )}

                                <Link href="/admin/activities">
                                    <Button variant="outline">
                                        <FileText className="h-4 w-4 mr-2" />
                                        All Activities
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
