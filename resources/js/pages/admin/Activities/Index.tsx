import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Activity, 
    Search, 
    Filter, 
    Calendar,
    Users,
    Download,
    Eye,
    Clock,
    User,
    Shield,
    FileText
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface ActivityMetadata {
    [key: string]: string | number | boolean | null | undefined;
}

interface UserActivityLog {
    id: number;
    user_id: number;
    activity_type: string;
    description: string;
    ip_address: string | null;
    performed_by: number | null;
    metadata: ActivityMetadata | null;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
    };
    performedBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Stats {
    total_activities: number;
    activities_today: number;
    activities_this_week: number;
    unique_users_today: number;
}

interface User {
    id: number;
    name: string;
    email: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface Props {
    activities: {
        data: UserActivityLog[];
        links: PaginationLink[];
        meta: PaginationMeta;
    };
    stats: Stats;
    activityTypes: string[];
    users: User[];
    filters: {
        user_id?: string;
        activity_type?: string;
        performed_by?: string;
        date_from?: string;
        date_to?: string;
        search?: string;
        sort_by?: string;
        sort_order?: string;
    };
}

const ActivityTypeBadge = ({ type }: { type: string }) => {
    const getTypeConfig = (activityType: string) => {
        if (activityType.includes('login') || activityType.includes('logout')) {
            return { className: 'bg-blue-100 text-blue-800', icon: Shield };
        }
        if (activityType.includes('search')) {
            return { className: 'bg-green-100 text-green-800', icon: Search };
        }
        if (activityType.includes('favorite')) {
            return { className: 'bg-purple-100 text-purple-800', icon: Eye };
        }
        if (activityType.includes('subscription')) {
            return { className: 'bg-orange-100 text-orange-800', icon: FileText };
        }
        if (activityType.includes('admin') || activityType.includes('approval')) {
            return { className: 'bg-red-100 text-red-800', icon: Shield };
        }
        return { className: 'bg-gray-100 text-gray-800', icon: Activity };
    };

    const { className, icon: Icon } = getTypeConfig(type);

    return (
        <Badge className={`flex items-center gap-1 ${className}`}>
            <Icon className="h-3 w-3" />
            {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </Badge>
    );
};

export default function ActivitiesIndex({ activities, stats, activityTypes, users, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedUser, setSelectedUser] = useState(filters.user_id || 'all');
    const [selectedActivityType, setSelectedActivityType] = useState(filters.activity_type || 'all');
    const [selectedPerformedBy, setSelectedPerformedBy] = useState(filters.performed_by || 'all');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');

    const handleSearch = () => {
        router.get('/admin/activities', {
            ...filters,
            search: searchTerm,
            user_id: selectedUser === 'all' ? '' : selectedUser,
            activity_type: selectedActivityType === 'all' ? '' : selectedActivityType,
            performed_by: selectedPerformedBy === 'all' ? '' : selectedPerformedBy,
            date_from: dateFrom,
            date_to: dateTo,
        });
    };

    const handleExport = () => {
        const params = new URLSearchParams({
            ...filters,
            user_id: selectedUser === 'all' ? '' : selectedUser,
            activity_type: selectedActivityType === 'all' ? '' : selectedActivityType,
            performed_by: selectedPerformedBy === 'all' ? '' : selectedPerformedBy,
            date_from: dateFrom,
            date_to: dateTo,
        });
        
        window.open(`/admin/activities/export?${params.toString()}`, '_blank');
    };

    return (
        <AppLayout>
            <Head title="User Activity Logs" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">User Activity Logs</h1>
                            <p className="text-muted-foreground mt-2">
                                Monitor and audit user activities across the platform
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button onClick={handleExport} variant="outline">
                                <Download className="h-4 w-4 mr-2" />
                                Export CSV
                            </Button>
                        </div>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
                                <Activity className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.total_activities || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Today</CardTitle>
                                <Calendar className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.activities_today || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">This Week</CardTitle>
                                <Clock className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.activities_this_week || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Active Users Today</CardTitle>
                                <Users className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.unique_users_today || 0}</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filters */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filters
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-7">
                                <div>
                                    <label className="text-sm font-medium mb-2 block">Search</label>
                                    <Input
                                        placeholder="Search activities..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">User</label>
                                    <Select value={selectedUser} onValueChange={setSelectedUser}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Users" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Users</SelectItem>
                                            {users.map((user) => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Activity Type</label>
                                    <Select value={selectedActivityType} onValueChange={setSelectedActivityType}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Types" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Types</SelectItem>
                                            {activityTypes.map((type) => (
                                                <SelectItem key={type} value={type}>
                                                    {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Performed By</label>
                                    <Select value={selectedPerformedBy} onValueChange={setSelectedPerformedBy}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All</SelectItem>
                                            {users.map((user) => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Date From</label>
                                    <Input
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) => setDateFrom(e.target.value)}
                                    />
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Date To</label>
                                    <Input
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) => setDateTo(e.target.value)}
                                    />
                                </div>

                                <div className="flex items-end">
                                    <Button onClick={handleSearch} className="w-full">
                                        <Search className="h-4 w-4 mr-2" />
                                        Apply Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Activities Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Activity Logs</CardTitle>
                            <CardDescription>
                                {activities.meta?.total || activities.data?.length || 0} activities found
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {(activities.data || []).map((activity) => (
                                    <div key={activity.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
                                        <div className="flex items-center gap-4">
                                            <div className="space-y-1">
                                                <div className="flex items-center gap-2">
                                                    <ActivityTypeBadge type={activity.activity_type} />
                                                </div>
                                                <div className="space-y-1">
                                                    <p className="text-sm font-medium text-foreground">
                                                        {activity.description}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <div className="flex items-center gap-1">
                                                        <User className="h-3 w-3" />
                                                        <span>User: {activity.user?.name || 'Unknown User'}</span>
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="h-3 w-3" />
                                                        <span>Time: {new Date(activity.created_at).toLocaleString()}</span>
                                                    </div>
                                                    {activity.performedBy && (
                                                        <div className="flex items-center gap-1">
                                                            <Shield className="h-3 w-3" />
                                                            <span>By: {activity.performedBy.name}</span>
                                                        </div>
                                                    )}
                                                    {activity.ip_address && (
                                                        <span>IP: {activity.ip_address}</span>
                                                    )}
                                                </div>
                                                <div className="text-xs text-muted-foreground">
                                                    <span>Email: {activity.user?.email || 'No email'}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Link href={`/admin/activities/${activity.id}`}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-4 w-4 mr-1" />
                                                    View
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}

                                {(!activities.data || activities.data.length === 0) && (
                                    <div className="text-center py-12">
                                        <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-foreground mb-2">No activities found</h3>
                                        <p className="text-muted-foreground">
                                            No activities match your current filters.
                                        </p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
