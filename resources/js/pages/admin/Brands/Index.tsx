import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
    Plus,
    Edit,
    Trash2,
    Eye,
    Smartphone,
    ExternalLink,
    ChevronLeft,
    ChevronRight,
    Search,
    Filter,
    X,
    ArrowUpDown,
    ArrowUp,
    ArrowDown,
    Grid,
    List,
    Table,
    Download,
    Upload,
    FileText
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';
import { useState, useRef } from 'react';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country: string | null;
    website: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    models_count?: number;
}

interface PaginatedBrands {
    data: Brand[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface FilterOptions {
    countries: string[];
}

interface QueryParams {
    search?: string;
    country?: string;
    status?: string;
    sort_by?: string;
    sort_order?: string;
    view?: string;
}

interface Props {
    brands: PaginatedBrands;
    filters: FilterOptions;
    queryParams: QueryParams;
}

export default function Index({ brands, filters, queryParams }: Props) {
    const { showDeleteConfirmation } = useDeleteConfirmation();

    // Search and filter state
    const [searchTerm, setSearchTerm] = useState(queryParams.search || '');
    const [selectedCountry, setSelectedCountry] = useState(queryParams.country || 'all');
    const [selectedStatus, setSelectedStatus] = useState(queryParams.status || 'all');
    const [sortBy, setSortBy] = useState(queryParams.sort_by || 'name');
    const [sortOrder, setSortOrder] = useState(queryParams.sort_order || 'asc');
    const [showFilters, setShowFilters] = useState(false);

    // View mode state - default to table as requested
    const [viewMode, setViewMode] = useState<'list' | 'grid' | 'table'>(
        (queryParams.view as 'list' | 'grid' | 'table') || 'table'
    );

    // Export/Import state
    const [selectedBrands, setSelectedBrands] = useState<number[]>([]);
    const [isImporting, setIsImporting] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Check if any filters are active
    const hasActiveFilters = searchTerm || selectedCountry !== 'all' || selectedStatus !== 'all';

    const handlePageChange = (page: number) => {
        const params = {
            page,
            ...(searchTerm && { search: searchTerm }),
            ...(selectedCountry !== 'all' && { country: selectedCountry }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/brands', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSearch = () => {
        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedCountry !== 'all' && { country: selectedCountry }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/brands', params, {
            preserveState: true,
            preserveScroll: false,
        });
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setSelectedCountry('all');
        setSelectedStatus('all');
        setSortBy('name');
        setSortOrder('asc');

        router.get('/admin/brands', { view: viewMode !== 'table' ? viewMode : undefined }, {
            preserveState: true,
            preserveScroll: false,
        });
    };

    const handleSort = (field: string) => {
        const newSortOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortOrder(newSortOrder);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedCountry !== 'all' && { country: selectedCountry }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            sort_by: field,
            sort_order: newSortOrder,
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/brands', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleViewModeChange = (newViewMode: 'list' | 'grid' | 'table') => {
        setViewMode(newViewMode);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedCountry !== 'all' && { country: selectedCountry }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(newViewMode !== 'table' && { view: newViewMode }),
        };

        router.get('/admin/brands', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDelete = (brand: Brand) => {
        showDeleteConfirmation({
            title: `Delete "${brand.name}"?`,
            description: "This action cannot be undone. All associated models will also be affected.",
            onConfirm: () => {
                router.delete(`/admin/brands/${brand.id}`, {
                    onSuccess: () => {
                        toast.success(`Brand "${brand.name}" has been deleted successfully.`);
                    },
                    onError: (errors) => {
                        const errorMessage = errors.message || 'Failed to delete brand. It may have associated models.';
                        toast.error(errorMessage);
                    }
                });
            },
            onCancel: () => {
                toast.info('Delete cancelled');
            }
        });
    };

    // Export/Import handlers
    const handleExportAll = () => {
        if (process.env.NODE_ENV === 'development') {
            console.log('📥 EXPORT ALL BUTTON CLICKED');
        }
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (selectedCountry !== 'all') params.append('country', selectedCountry);
        if (selectedStatus !== 'all') params.append('status', selectedStatus);
        if (sortBy !== 'name') params.append('sort_by', sortBy);
        if (sortOrder !== 'asc') params.append('sort_order', sortOrder);

        window.location.href = `/admin/brands/export?${params.toString()}`;
        toast.success('Export started. Your download will begin shortly.');
    };

    const handleExportSelected = () => {
        if (process.env.NODE_ENV === 'development') {
            console.log('📥 EXPORT SELECTED BUTTON CLICKED');
        }
        if (selectedBrands.length === 0) {
            toast.error('Please select brands to export');
            return;
        }

        const params = new URLSearchParams();
        selectedBrands.forEach(id => params.append('ids[]', id.toString()));

        window.location.href = `/admin/brands/export?${params.toString()}`;
        toast.success('Export started. Your download will begin shortly.');
    };

    const handleDownloadTemplate = () => {
        if (process.env.NODE_ENV === 'development') {
            console.log('📄 TEMPLATE DOWNLOAD BUTTON CLICKED');
        }
        window.location.href = '/admin/brands/template/download';
        toast.success('Template download started.');
    };

    const handleImport = () => {
        if (process.env.NODE_ENV === 'development') {
            console.log('📤 IMPORT BUTTON CLICKED');
        }
        fileInputRef.current?.click();
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];

        if (file) {
            // Development debugging
            if (process.env.NODE_ENV === 'development') {
                console.log('📁 BRANDS IMPORT - File selected:', {
                    name: file.name,
                    size: file.size,
                    type: file.type
                });
            }

            setIsImporting(true);

            const formData = new FormData();
            formData.append('file', file);
            formData.append('duplicate_action', 'skip'); // Default to skip duplicates

            router.post('/admin/bulk-import/brands', formData, {
                onSuccess: (page: any) => {
                    if (process.env.NODE_ENV === 'development') {
                        console.log('✅ BRANDS IMPORT - Success response:', page);
                    }

                    setIsImporting(false);

                    // Show more detailed success message
                    const successMessage = page.props?.flash?.success || 'Brands imported successfully!';
                    toast.success(successMessage);

                    // Show import errors if any
                    const importErrors = page.props?.flash?.import_errors;
                    if (importErrors && Array.isArray(importErrors) && importErrors.length > 0) {
                        console.warn('Import completed with some issues:', importErrors);
                        toast.warning(`Import completed but ${importErrors.length} rows had issues. Check console for details.`);
                    }

                    // Reset file input
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }

                    // Refresh the page to show new data
                    router.reload({ only: ['brands'] });
                },
                onError: (errors) => {
                    if (process.env.NODE_ENV === 'development') {
                        console.error('❌ BRANDS IMPORT - Error response:', errors);
                    }

                    setIsImporting(false);

                    // Handle different types of errors
                    let errorMessage = 'Import failed. Please check your file format.';

                    if (errors.file) {
                        errorMessage = Array.isArray(errors.file) ? errors.file[0] : errors.file;
                    } else if (errors.message) {
                        errorMessage = errors.message;
                    }

                    toast.error(errorMessage);

                    // Reset file input
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                }
            });
        }
    };

    // Selection handlers
    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            const allBrandIds = brands.data.map(brand => brand.id);
            setSelectedBrands(allBrandIds);
        } else {
            setSelectedBrands([]);
        }
    };

    const handleSelectBrand = (brandId: number, checked: boolean) => {
        if (checked) {
            setSelectedBrands(prev => [...prev, brandId]);
        } else {
            setSelectedBrands(prev => prev.filter(id => id !== brandId));
        }
    };

    // Check if all brands are selected
    const isAllSelected = brands.data.length > 0 && selectedBrands.length === brands.data.length;
    const isIndeterminate = selectedBrands.length > 0 && selectedBrands.length < brands.data.length;

    // Table View Component
    const BrandTableView = () => (
        <div className="overflow-x-auto">
            <table className="w-full">
                <thead>
                    <tr className="border-b">
                        <th className="text-left p-3 w-12">
                            <Checkbox
                                checked={isAllSelected}
                                onCheckedChange={handleSelectAll}
                                aria-label="Select all brands"
                                className={isIndeterminate ? "data-[state=checked]:bg-primary data-[state=checked]:border-primary" : ""}
                            />
                        </th>
                        <th className="text-left p-3">
                            <Button
                                variant="ghost"
                                onClick={() => handleSort('name')}
                                className="h-auto p-0 font-semibold hover:bg-transparent"
                            >
                                Brand
                                {sortBy === 'name' && (
                                    sortOrder === 'asc' ? (
                                        <ArrowUp className="ml-1 h-4 w-4" />
                                    ) : (
                                        <ArrowDown className="ml-1 h-4 w-4" />
                                    )
                                )}
                                {sortBy !== 'name' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                            </Button>
                        </th>
                        <th className="text-left p-3">
                            <Button
                                variant="ghost"
                                onClick={() => handleSort('country')}
                                className="h-auto p-0 font-semibold hover:bg-transparent"
                            >
                                Country
                                {sortBy === 'country' && (
                                    sortOrder === 'asc' ? (
                                        <ArrowUp className="ml-1 h-4 w-4" />
                                    ) : (
                                        <ArrowDown className="ml-1 h-4 w-4" />
                                    )
                                )}
                                {sortBy !== 'country' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                            </Button>
                        </th>
                        <th className="text-left p-3">Website</th>
                        <th className="text-left p-3">Status</th>
                        <th className="text-left p-3">Models</th>
                        <th className="text-right p-3">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {brands.data.map((brand) => (
                        <tr key={brand.id} className="border-b hover:bg-muted/50">
                            <td className="p-3">
                                <Checkbox
                                    checked={selectedBrands.includes(brand.id)}
                                    onCheckedChange={(checked) => handleSelectBrand(brand.id, checked as boolean)}
                                    aria-label={`Select ${brand.name}`}
                                />
                            </td>
                            <td className="p-3">
                                <div className="flex items-center gap-3">
                                    {brand.logo_url && (
                                        <img
                                            src={brand.logo_url}
                                            alt={brand.name}
                                            className="w-6 h-6 object-contain"
                                        />
                                    )}
                                    <div>
                                        <div className="font-medium">
                                            {brand.name}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td className="p-3">
                                <span className="text-sm">
                                    {brand.country || '-'}
                                </span>
                            </td>
                            <td className="p-3">
                                {brand.website ? (
                                    <a
                                        href={brand.website}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm"
                                    >
                                        Website <ExternalLink className="h-3 w-3" />
                                    </a>
                                ) : (
                                    <span className="text-sm text-muted-foreground">-</span>
                                )}
                            </td>
                            <td className="p-3">
                                <Badge variant={brand.is_active ? "default" : "secondary"}>
                                    {brand.is_active ? "Active" : "Inactive"}
                                </Badge>
                            </td>
                            <td className="p-3">
                                <Badge variant="outline">
                                    {brand.models_count || 0} models
                                </Badge>
                            </td>
                            <td className="p-3">
                                <div className="flex items-center gap-1 justify-end">
                                    <Link href={route('brands.show', brand.slug || brand.id)}>
                                        <Button variant="outline" size="sm" title="View Public Page">
                                            <ExternalLink className="h-3 w-3" />
                                        </Button>
                                    </Link>
                                    <Link href={`/admin/brands/${brand.id}`}>
                                        <Button variant="outline" size="sm" title="Admin View">
                                            <Eye className="h-3 w-3" />
                                        </Button>
                                    </Link>
                                    <Link href={`/admin/brands/${brand.id}/edit`}>
                                        <Button variant="outline" size="sm" title="Edit">
                                            <Edit className="h-3 w-3" />
                                        </Button>
                                    </Link>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-destructive hover:text-destructive"
                                        onClick={() => handleDelete(brand)}
                                        title="Delete Brand"
                                    >
                                        <Trash2 className="h-3 w-3" />
                                    </Button>
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );

    // Grid View Component
    const BrandGridCard = ({ brand }: { brand: Brand }) => (
        <Card className="h-full">
            <CardContent className="p-4">
                <div className="space-y-3">
                    <div className="flex items-start justify-between">
                        <div className="space-y-1 flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                                {brand.logo_url && (
                                    <img
                                        src={brand.logo_url}
                                        alt={brand.name}
                                        className="w-6 h-6 object-contain flex-shrink-0"
                                    />
                                )}
                                <h3 className="font-medium truncate">{brand.name}</h3>
                            </div>
                            <div className="flex items-center gap-2">
                                <Badge variant={brand.is_active ? "default" : "secondary"}>
                                    {brand.is_active ? "Active" : "Inactive"}
                                </Badge>
                                <Badge variant="outline">
                                    {brand.models_count || 0} models
                                </Badge>
                            </div>
                        </div>
                        <Checkbox
                            checked={selectedBrands.includes(brand.id)}
                            onCheckedChange={(checked) => handleSelectBrand(brand.id, checked as boolean)}
                            aria-label={`Select ${brand.name}`}
                            className="mt-1"
                        />
                    </div>

                    {(brand.country || brand.website) && (
                        <div className="space-y-1 text-sm text-muted-foreground">
                            {brand.country && (
                                <p>Country: {brand.country}</p>
                            )}
                            {brand.website && (
                                <a
                                    href={brand.website}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                                >
                                    Website <ExternalLink className="h-3 w-3" />
                                </a>
                            )}
                        </div>
                    )}

                    <div className="flex items-center gap-1 pt-2">
                        <Link href={route('brands.show', brand.slug || brand.id)}>
                            <Button variant="outline" size="sm" title="View Public Page">
                                <ExternalLink className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/brands/${brand.id}`}>
                            <Button variant="outline" size="sm" title="Admin View">
                                <Eye className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/brands/${brand.id}/edit`}>
                            <Button variant="outline" size="sm" title="Edit">
                                <Edit className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Button
                            variant="outline"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDelete(brand)}
                            title="Delete Brand"
                        >
                            <Trash2 className="h-3 w-3" />
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout>
            <Head title="Brands - Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Brands</h1>
                        <p className="text-muted-foreground">
                            Manage mobile device brands
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="flex items-center gap-3">
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors"
                                onClick={handleDownloadTemplate}
                                title="Download CSV Template"
                            >
                                <FileText className="h-4 w-4 mr-2" />
                                Template
                            </Button>
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors disabled:opacity-50"
                                onClick={handleImport}
                                disabled={isImporting}
                                title="Import Brands from CSV"
                            >
                                <Upload className="h-4 w-4 mr-2" />
                                Import
                            </Button>
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors"
                                onClick={handleExportAll}
                                title="Export All Brands to CSV"
                            >
                                <Download className="h-4 w-4 mr-2" />
                                Export All
                            </Button>
                        </div>

                        <Link href="/admin/brands/create">
                            <Button>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Brand
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Hidden file input for import */}
                <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    className="hidden"
                />

                {/* Search and Filters */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="space-y-4">
                            {/* Search Bar */}
                            <div className="flex items-center gap-2">
                                <div className="relative flex-1">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search brands, countries, or websites..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                                handleSearch();
                                            }
                                        }}
                                    />
                                </div>
                                <Button onClick={handleSearch}>
                                    <Search className="h-4 w-4 mr-2" />
                                    Search
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => setShowFilters(!showFilters)}
                                    className={showFilters ? 'bg-muted' : ''}
                                >
                                    <Filter className="h-4 w-4 mr-2" />
                                    Filters
                                    {hasActiveFilters && (
                                        <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
                                            !
                                        </Badge>
                                    )}
                                </Button>
                                {hasActiveFilters && (
                                    <Button variant="ghost" onClick={handleClearFilters}>
                                        <X className="h-4 w-4 mr-2" />
                                        Clear
                                    </Button>
                                )}
                            </div>

                            {/* Filter Controls */}
                            {showFilters && (
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                                    <div className="space-y-2">
                                        <Label htmlFor="country-filter">Country</Label>
                                        <Select value={selectedCountry} onValueChange={setSelectedCountry}>
                                            <SelectTrigger id="country-filter">
                                                <SelectValue placeholder="All countries" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All countries</SelectItem>
                                                {filters.countries.map((country) => (
                                                    <SelectItem key={country} value={country}>
                                                        {country}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="status-filter">Status</Label>
                                        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                            <SelectTrigger id="status-filter">
                                                <SelectValue placeholder="All statuses" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All statuses</SelectItem>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="sort-filter">Sort By</Label>
                                        <div className="flex gap-2">
                                            <Select value={sortBy} onValueChange={setSortBy}>
                                                <SelectTrigger id="sort-filter" className="flex-1">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="name">Name</SelectItem>
                                                    <SelectItem value="country">Country</SelectItem>
                                                    <SelectItem value="created_at">Created Date</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleSort(sortBy)}
                                                className="px-3"
                                            >
                                                {sortOrder === 'asc' ? (
                                                    <ArrowUp className="h-4 w-4" />
                                                ) : (
                                                    <ArrowDown className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Bulk Actions Bar */}
                {selectedBrands.length > 0 && (
                    <Card className="border-blue-200 bg-blue-50">
                        <CardContent className="py-3">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-medium">
                                        {selectedBrands.length} brand{selectedBrands.length !== 1 ? 's' : ''} selected
                                    </span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <Button
                                        variant="outline"
                                        className="rounded-full border-primary bg-transparent text-primary hover:bg-primary hover:text-primary-foreground px-4 py-2 h-auto transition-colors"
                                        onClick={handleExportSelected}
                                    >
                                        <Download className="h-4 w-4 mr-2" />
                                        Export Selected
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        className="rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground px-4 py-2 h-auto transition-colors"
                                        onClick={() => setSelectedBrands([])}
                                    >
                                        <X className="h-4 w-4 mr-2" />
                                        Clear Selection
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Brands List */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    <Smartphone className="h-5 w-5" />
                                    All Brands
                                </CardTitle>
                                <CardDescription>
                                    {brands.total} brands total
                                    {brands.data.length > 0 && (
                                        <span className="ml-2">
                                            (showing {brands.from}-{brands.to})
                                        </span>
                                    )}
                                </CardDescription>
                            </div>

                            {/* View Mode Toggle */}
                            <div className="flex items-center gap-1 border rounded-lg p-1">
                                <Button
                                    variant={viewMode === 'table' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => handleViewModeChange('table')}
                                    className="h-8 px-3"
                                >
                                    <Table className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => handleViewModeChange('list')}
                                    className="h-8 px-3"
                                >
                                    <List className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => handleViewModeChange('grid')}
                                    className="h-8 px-3"
                                >
                                    <Grid className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {brands.data.length > 0 ? (
                            <>
                                {/* Conditional rendering based on view mode */}
                                {viewMode === 'table' ? (
                                    <BrandTableView />
                                ) : viewMode === 'grid' ? (
                                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                        {brands.data.map((brand) => (
                                            <BrandGridCard key={brand.id} brand={brand} />
                                        ))}
                                    </div>
                                ) : (
                                    <div className="space-y-4">
                                        {brands.data.map((brand) => (
                                            <div key={brand.id} className="border rounded-lg p-4 space-y-3">
                                                <div className="flex items-start justify-between">
                                                    <div className="space-y-1">
                                                        <div className="flex items-center gap-2">
                                                            {brand.logo_url && (
                                                                <img
                                                                    src={brand.logo_url}
                                                                    alt={brand.name}
                                                                    className="w-6 h-6 object-contain"
                                                                />
                                                            )}
                                                            <h3 className="font-medium">{brand.name}</h3>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            <Badge variant={brand.is_active ? "default" : "secondary"}>
                                                                {brand.is_active ? "Active" : "Inactive"}
                                                            </Badge>
                                                            <Badge variant="outline">
                                                                {brand.models_count || 0} models
                                                            </Badge>
                                                        </div>
                                                    </div>
                                                    <Checkbox
                                                        checked={selectedBrands.includes(brand.id)}
                                                        onCheckedChange={(checked) => handleSelectBrand(brand.id, checked as boolean)}
                                                        aria-label={`Select ${brand.name}`}
                                                        className="mt-1"
                                                    />
                                                </div>

                                                {(brand.country || brand.website) && (
                                                    <div className="space-y-1 text-sm text-muted-foreground">
                                                        {brand.country && (
                                                            <p>Country: {brand.country}</p>
                                                        )}
                                                        {brand.website && (
                                                            <a
                                                                href={brand.website}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                                                            >
                                                                Website <ExternalLink className="h-3 w-3" />
                                                            </a>
                                                        )}
                                                    </div>
                                                )}

                                                <div className="flex items-center gap-2 pt-2">
                                                    <Link href={route('brands.show', brand.slug || brand.id)}>
                                                        <Button variant="outline" size="sm" title="View Public Page">
                                                            <ExternalLink className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/brands/${brand.id}`}>
                                                        <Button variant="outline" size="sm" title="Admin View">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/brands/${brand.id}/edit`}>
                                                        <Button variant="outline" size="sm" title="Edit">
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="text-destructive hover:text-destructive"
                                                        onClick={() => handleDelete(brand)}
                                                        title="Delete"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="text-center py-8">
                                <Smartphone className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <h3 className="text-lg font-medium mb-2">No brands found</h3>
                                <p className="text-muted-foreground mb-4">
                                    {hasActiveFilters
                                        ? "No brands match your current filters. Try adjusting your search criteria."
                                        : "Get started by adding your first brand."
                                    }
                                </p>
                                {hasActiveFilters ? (
                                    <Button variant="outline" onClick={handleClearFilters}>
                                        <X className="h-4 w-4 mr-2" />
                                        Clear Filters
                                    </Button>
                                ) : (
                                    <Link href="/admin/brands/create">
                                        <Button>
                                            <Plus className="h-4 w-4 mr-2" />
                                            Add Brand
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        )}

                        {/* Pagination */}
                        {brands.data.length > 0 && brands.last_page > 1 && (
                            <div className="flex items-center justify-between pt-4 border-t">
                                <div className="text-sm text-muted-foreground">
                                    Showing {brands.from} to {brands.to} of {brands.total} brands
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(brands.current_page - 1)}
                                        disabled={brands.current_page <= 1}
                                    >
                                        <ChevronLeft className="h-4 w-4 mr-1" />
                                        Previous
                                    </Button>

                                    <div className="flex items-center gap-1">
                                        {/* Show page numbers */}
                                        {Array.from({ length: Math.min(5, brands.last_page) }, (_, i) => {
                                            let pageNum;
                                            if (brands.last_page <= 5) {
                                                pageNum = i + 1;
                                            } else {
                                                const start = Math.max(1, brands.current_page - 2);
                                                const end = Math.min(brands.last_page, start + 4);
                                                pageNum = start + i;
                                                if (pageNum > end) return null;
                                            }

                                            if (pageNum > brands.last_page || pageNum < 1) return null;

                                            return (
                                                <Button
                                                    key={pageNum}
                                                    variant={brands.current_page === pageNum ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => handlePageChange(pageNum)}
                                                    className="w-8 h-8 p-0"
                                                >
                                                    {pageNum}
                                                </Button>
                                            );
                                        })}
                                    </div>

                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(brands.current_page + 1)}
                                        disabled={brands.current_page >= brands.last_page}
                                    >
                                        Next
                                        <ChevronRight className="h-4 w-4 ml-1" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
                </div>
            </div>
        </AppLayout>
    );
}
