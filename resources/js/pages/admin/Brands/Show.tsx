import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    ArrowLeft,
    Edit,
    Building,
    Globe,
    MapPin,
    Calendar,
    Smartphone,
    ExternalLink
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number: string | null;
    release_year: number | null;
    is_active: boolean;
    created_at: string;
}

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country: string | null;
    website: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    models?: MobileModel[];
}

interface Props {
    brand: Brand;
}

export default function Show({ brand }: Props) {
    return (
        <AppLayout>
            <Head title={`${brand.name} - Brands - Admin`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href="/admin/brands">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Brands
                            </Button>
                        </Link>
                        <div className="flex items-center gap-3">
                            {brand.logo_url && (
                                <img 
                                    src={brand.logo_url} 
                                    alt={brand.name}
                                    className="w-8 h-8 object-contain"
                                />
                            )}
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight">{brand.name}</h1>
                                <p className="text-muted-foreground">
                                    Brand details and associated models
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link href={route('brands.show', brand.slug || brand.id)}>
                            <Button variant="outline">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View Public Page
                            </Button>
                        </Link>
                        <Link href={`/admin/brands/${brand.id}/edit`}>
                            <Button>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Brand
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Brand Details */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Building className="h-5 w-5" />
                                Brand Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Brand Name</label>
                                <p className="text-lg font-medium">{brand.name}</p>
                            </div>
                            
                            {brand.logo_url && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Logo</label>
                                    <div className="mt-2">
                                        <img 
                                            src={brand.logo_url} 
                                            alt={brand.name}
                                            className="w-16 h-16 object-contain border rounded-lg p-2"
                                        />
                                    </div>
                                </div>
                            )}
                            
                            {brand.country && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                        <MapPin className="h-3 w-3" />
                                        Country
                                    </label>
                                    <p className="text-sm">{brand.country}</p>
                                </div>
                            )}
                            
                            {brand.website && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                        <Globe className="h-3 w-3" />
                                        Website
                                    </label>
                                    <a 
                                        href={brand.website} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="text-sm text-blue-600 hover:underline flex items-center gap-1"
                                    >
                                        {brand.website} <ExternalLink className="h-3 w-3" />
                                    </a>
                                </div>
                            )}
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Status</label>
                                <div className="mt-1">
                                    <Badge variant={brand.is_active ? "default" : "secondary"}>
                                        {brand.is_active ? "Active" : "Inactive"}
                                    </Badge>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Statistics</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <Smartphone className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm font-medium">Models</p>
                                    <p className="text-2xl font-bold">{brand.models?.length || 0}</p>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-3">
                                <Calendar className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm font-medium">Created</p>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(brand.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-3">
                                <Calendar className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm font-medium">Last Updated</p>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(brand.updated_at).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Associated Models */}
                {brand.models && brand.models.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Smartphone className="h-5 w-5" />
                                Associated Models
                            </CardTitle>
                            <CardDescription>
                                Mobile models from this brand ({brand.models.length} models)
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {brand.models.map((model) => (
                                    <div key={model.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="space-y-1">
                                            <div className="flex items-center gap-2">
                                                <h3 className="font-medium">{model.name}</h3>
                                                {model.release_year && (
                                                    <Badge variant="outline" className="text-xs">
                                                        {model.release_year}
                                                    </Badge>
                                                )}
                                            </div>
                                            {model.model_number && (
                                                <p className="text-sm text-muted-foreground">
                                                    Model #: {model.model_number}
                                                </p>
                                            )}
                                            <p className="text-xs text-muted-foreground">
                                                Created: {new Date(model.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge variant={model.is_active ? "default" : "secondary"}>
                                                {model.is_active ? "Active" : "Inactive"}
                                            </Badge>
                                            <Link href={`/admin/models/${model.id}`}>
                                                <Button variant="outline" size="sm">
                                                    View Model
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* No Models Message */}
                {(!brand.models || brand.models.length === 0) && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Smartphone className="h-5 w-5" />
                                Associated Models
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-8">
                                <Smartphone className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <h3 className="text-lg font-medium mb-2">No models found</h3>
                                <p className="text-muted-foreground mb-4">
                                    This brand doesn't have any models yet.
                                </p>
                                <Link href="/admin/models/create">
                                    <Button variant="outline">
                                        Add Model
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                )}
                </div>
            </div>
        </AppLayout>
    );
}
