"use client"

import type React from "react"

import { Head, router, useForm } from "@inertiajs/react"
import { useState, useRef } from "react"
import { toast } from "sonner"
import { Upload, Search, Filter, Grid3X3, List, Trash2, Edit3, Eye, Plus } from "lucide-react"

import AppLayout from "@/layouts/app-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Media {
  id: number
  filename: string
  original_filename: string
  mime_type: string
  size: number
  path: string
  disk: string
  alt_text: string | null
  title: string | null
  description: string | null
  width: number | null
  height: number | null
  url: string
  thumbnail_url: string | null
  formatted_size: string
  created_at: string
  uploader: {
    id: number
    name: string
  }
}

interface Props {
  media: {
    data: Media[]
    current_page: number
    last_page: number
    per_page: number
    total: number
    links: Array<{
      url: string | null
      label: string
      active: boolean
    }>
  }
  filters: {
    search?: string
    type?: string
  }
}

export default function Index({ media, filters }: Props) {
  // Debug: Log the media data being passed to the component
  console.log("MediaIndex component received media data:", media)
  console.log("First media item:", media.data[0])

  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [selectedMedia, setSelectedMedia] = useState<Media | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const {
    data: searchData,
    setData: setSearchData,
    get,
  } = useForm({
    search: filters.search || "",
    type: filters.type || "all",
  })

  const {
    data: editData,
    setData: setEditData,
    put,
    processing: editProcessing,
  } = useForm({
    title: "",
    alt_text: "",
    description: "",
  })

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    get("/admin/media", {
      preserveState: true,
      preserveScroll: true,
    })
  }

  const handleUpload = () => {
    fileInputRef.current?.click()
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setIsUploading(true)
    const formData = new FormData()

    Array.from(files).forEach((file) => {
      formData.append("files[]", file)
    })

    try {
      const response = await fetch("/admin/media", {
        method: "POST",
        body: formData,
        headers: {
          "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]')?.getAttribute("content") || "",
        },
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(result.message)
        router.reload()
      } else {
        toast.error(result.message || "Upload failed")
      }
    } catch (error) {
      toast.error("Upload failed")
    } finally {
      setIsUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const handleEdit = (media: Media) => {
    setSelectedMedia(media)
    setEditData({
      title: media.title || "",
      alt_text: media.alt_text || "",
      description: media.description || "",
    })
    setIsEditDialogOpen(true)
  }

  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedMedia) return

    put(`/admin/media/${selectedMedia.id}`, {
      onSuccess: () => {
        toast.success("Media updated successfully")
        setIsEditDialogOpen(false)
        setSelectedMedia(null)
      },
      onError: () => {
        toast.error("Failed to update media")
      },
    })
  }

  const handleDelete = async (media: Media) => {
    if (!confirm("Are you sure you want to delete this media file?")) return

    try {
      const response = await fetch(`/admin/media/${media.id}`, {
        method: "DELETE",
        headers: {
          "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]')?.getAttribute("content") || "",
        },
      })

      if (response.ok) {
        toast.success("Media deleted successfully")
        router.reload()
      } else {
        toast.error("Failed to delete media")
      }
    } catch (error) {
      toast.error("Failed to delete media")
    }
  }

  const isImage = (mimeType: string) => mimeType.startsWith("image/")

  return (
    <AppLayout>
      <Head title="Media Library - Admin" />

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Media Library</h1>
            <p className="text-sm text-gray-600">Manage your uploaded files and images</p>
          </div>
          <Button onClick={handleUpload} disabled={isUploading}>
            <Upload className="w-4 h-4 mr-2" />
            {isUploading ? "Uploading..." : "Upload Files"}
          </Button>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-4">
            <form onSubmit={handleSearch} className="flex items-center gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search media files..."
                    value={searchData.search}
                    onChange={(e) => setSearchData("search", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={searchData.type} onValueChange={(value) => setSearchData("type", value)}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="All file types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All file types</SelectItem>
                  <SelectItem value="images">Images</SelectItem>
                  <SelectItem value="application">Documents</SelectItem>
                </SelectContent>
              </Select>
              <Button type="submit">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  type="button"
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Media Grid/List */}
        {media.data.length > 0 ? (
          <div className={viewMode === "grid" ? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6" : "space-y-4"}>
            {media.data.map((item) => (
              <MediaItem key={item.id} media={item} viewMode={viewMode} onEdit={handleEdit} onDelete={handleDelete} />
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No media files found</h3>
              <p className="text-gray-600 mb-4">Upload your first files to get started</p>
              <Button onClick={handleUpload}>
                <Plus className="w-4 h-4 mr-2" />
                Upload Files
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {media.last_page > 1 && (
          <div className="flex items-center justify-center gap-2">
            {media.links.map((link, index) => (
              <Button
                key={index}
                variant={link.active ? "default" : "outline"}
                size="sm"
                disabled={!link.url}
                onClick={() => link.url && router.get(link.url)}
                dangerouslySetInnerHTML={{ __html: link.label }}
              />
            ))}
          </div>
        )}
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,.pdf,.txt"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Media Details</DialogTitle>
            <DialogDescription>Update the title, alt text, and description for this media file.</DialogDescription>
          </DialogHeader>
          {selectedMedia && (
            <form onSubmit={handleEditSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={editData.title}
                  onChange={(e) => setEditData("title", e.target.value)}
                  placeholder="Enter a title for this media"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="alt_text">Alt Text</Label>
                <Input
                  id="alt_text"
                  value={editData.alt_text}
                  onChange={(e) => setEditData("alt_text", e.target.value)}
                  placeholder="Describe this image for accessibility"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={editData.description}
                  onChange={(e) => setEditData("description", e.target.value)}
                  placeholder="Add a description for this media"
                  rows={3}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={editProcessing}>
                  {editProcessing ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </AppLayout>
  )
}

interface MediaItemProps {
  media: Media
  viewMode: "grid" | "list"
  onEdit: (media: Media) => void
  onDelete: (media: Media) => void
}

function MediaItem({ media, viewMode, onEdit, onDelete }: MediaItemProps) {
  const isImage = (mimeType: string) => mimeType.startsWith("image/")

  if (viewMode === "grid") {
    return (
      <div className="relative group">
        <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border border-gray-200">
          {isImage(media.mime_type) ? (
            <img
              src={media.url || "/placeholder.svg"}
              alt={media.alt_text || media.original_filename}
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error("Failed to load image:", media.url, "Error:", e)
                console.error("Image element:", e.currentTarget)
                e.currentTarget.style.display = "none"
              }}
              onLoad={() => {
                console.log("Image loaded successfully:", media.url)
              }}
              onLoadStart={() => {
                console.log("Image load started:", media.url)
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl mb-2">📄</div>
                <div className="text-xs text-gray-500 uppercase">{media.mime_type.split("/")[1]}</div>
              </div>
            </div>
          )}
        </div>

        {/* Action buttons overlay */}
        <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button size="sm" variant="secondary" className="h-6 w-6 p-0" onClick={() => onEdit(media)}>
            <Edit3 className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="h-6 w-6 p-0"
            onClick={() => window.open(media.url, "_blank")}
          >
            <Eye className="w-3 h-3" />
          </Button>
          <Button size="sm" variant="secondary" className="h-6 w-6 p-0" onClick={() => onDelete(media)}>
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>

        {/* File info overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <p className="text-xs font-medium truncate" title={media.original_filename}>
            {media.original_filename}
          </p>
          <div className="flex justify-between items-center text-xs text-gray-300">
            <span>{media.formatted_size}</span>
            {media.width && media.height && (
              <span>
                {media.width} × {media.height}
              </span>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
            {isImage(media.mime_type) ? (
              <img
                src={media.url || "/placeholder.svg"}
                alt={media.alt_text || media.original_filename}
                className="w-full h-full object-cover"
                onError={(e) => {
                  console.error("Failed to load image:", media.url, "Error:", e)
                  console.error("Image element:", e.currentTarget)
                  e.currentTarget.style.display = "none"
                }}
                onLoad={() => {
                  console.log("Image loaded successfully:", media.url)
                }}
                onLoadStart={() => {
                  console.log("Image load started:", media.url)
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg">📄</div>
                  <div className="text-xs text-gray-500 uppercase">{media.mime_type.split("/")[1]}</div>
                </div>
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium truncate">{media.original_filename}</h3>
            <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
              <span>{media.formatted_size}</span>
              {media.width && media.height && (
                <span>
                  {media.width} × {media.height}
                </span>
              )}
              <span>Uploaded by {media.uploader.name}</span>
            </div>
            {media.title && <p className="text-sm text-gray-600 mt-1">{media.title}</p>}
          </div>
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline" onClick={() => onEdit(media)}>
              <Edit3 className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={() => window.open(media.url, "_blank")}>
              <Eye className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={() => onDelete(media)}>
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
