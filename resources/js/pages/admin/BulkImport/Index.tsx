import { Head, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
    Upload,
    FileText,
    Smartphone,
    Package,
    Download,
    Tag
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';

// Debug utility for development environment
const debugLog = (message: string, data?: any) => {
    if (import.meta.env.DEV) {
        console.log(`[BulkImport Debug] ${message}`, data || '');
    }
};

export default function Index() {
    // Debug component lifecycle
    useEffect(() => {
        debugLog('BulkImport component mounted');
        return () => debugLog('BulkImport component unmounted');
    }, []);

    // Categories import state
    const [showCategoriesImportDialog, setShowCategoriesImportDialog] = useState(false);
    const [selectedCategoriesFile, setSelectedCategoriesFile] = useState<File | null>(null);
    const [isImportingCategories, setIsImportingCategories] = useState(false);
    const categoriesFileInputRef = useRef<HTMLInputElement>(null);

    // Brands import state
    const [showBrandsImportDialog, setShowBrandsImportDialog] = useState(false);
    const [selectedBrandsFile, setSelectedBrandsFile] = useState<File | null>(null);
    const [isImportingBrands, setIsImportingBrands] = useState(false);
    const brandsFileInputRef = useRef<HTMLInputElement>(null);

    // Models import state
    const [showModelsImportDialog, setShowModelsImportDialog] = useState(false);
    const [selectedModelsFile, setSelectedModelsFile] = useState<File | null>(null);
    const [isImportingModels, setIsImportingModels] = useState(false);
    const modelsFileInputRef = useRef<HTMLInputElement>(null);

    // Parts import state
    const [showPartsImportDialog, setShowPartsImportDialog] = useState(false);
    const [selectedPartsFile, setSelectedPartsFile] = useState<File | null>(null);
    const [isImportingParts, setIsImportingParts] = useState(false);
    const partsFileInputRef = useRef<HTMLInputElement>(null);

    // Categories import handlers
    const handleCategoriesTemplateDownload = () => {
        debugLog('Categories template download initiated');
        window.location.href = '/admin/categories/template/download';
        toast.success('Categories template download started.');
    };

    const handleCategoriesImport = () => {
        debugLog('Categories import file selection triggered');
        categoriesFileInputRef.current?.click();
    };

    const handleCategoriesFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        debugLog('Categories file selected', { fileName: file?.name, fileSize: file?.size });

        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.csv')) {
            debugLog('Categories file validation failed: not CSV');
            toast.error('Please select a CSV file');
            return;
        }

        setSelectedCategoriesFile(file);
        setShowCategoriesImportDialog(true);
        debugLog('Categories import dialog opened');
    };

    const handleCategoriesImportConfirm = () => {
        if (!selectedCategoriesFile) {
            debugLog('Categories import confirmation failed: no file selected');
            return;
        }

        debugLog('Categories import started', { fileName: selectedCategoriesFile.name });
        setIsImportingCategories(true);
        const formData = new FormData();
        formData.append('file', selectedCategoriesFile);

        // Add CSRF token manually when using forceFormData
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        router.post('/admin/bulk-import/categories', formData, {
            forceFormData: true,
            onSuccess: () => {
                debugLog('Categories import successful');
                toast.success('Categories imported successfully!');
                setShowCategoriesImportDialog(false);
                setSelectedCategoriesFile(null);
                setIsImportingCategories(false);
                if (categoriesFileInputRef.current) {
                    categoriesFileInputRef.current.value = '';
                }
                // Refresh the page to show updated data
                router.reload();
            },
            onError: (errors) => {
                debugLog('Categories import failed', errors);
                console.error('Categories import errors:', errors);
                if (errors.file) {
                    toast.error(`File error: ${errors.file}`);
                } else if (errors.message) {
                    toast.error(errors.message);
                } else {
                    toast.error('Import failed. Please check your CSV format and try again.');
                }
                setIsImportingCategories(false);
            }
        });
    };

    // Brands import handlers
    const handleBrandsTemplateDownload = () => {
        debugLog('Brands template download initiated');
        window.location.href = '/admin/brands/template/download';
        toast.success('Brands template download started.');
    };

    const handleBrandsImport = () => {
        debugLog('Brands import file selection triggered');
        brandsFileInputRef.current?.click();
    };

    const handleBrandsFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        debugLog('Brands file selected', { fileName: file?.name, fileSize: file?.size });

        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.csv')) {
            debugLog('Brands file validation failed: not CSV');
            toast.error('Please select a CSV file');
            return;
        }

        setSelectedBrandsFile(file);
        setShowBrandsImportDialog(true);
        debugLog('Brands import dialog opened');
    };

    const handleBrandsImportConfirm = () => {
        if (!selectedBrandsFile) {
            debugLog('Brands import confirmation failed: no file selected');
            return;
        }

        debugLog('Brands import started', { fileName: selectedBrandsFile.name });
        setIsImportingBrands(true);
        const formData = new FormData();
        formData.append('file', selectedBrandsFile);

        // Add CSRF token manually when using forceFormData
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        router.post('/admin/bulk-import/brands', formData, {
            forceFormData: true,
            onSuccess: () => {
                debugLog('Brands import successful');
                toast.success('Brands imported successfully!');
                setShowBrandsImportDialog(false);
                setSelectedBrandsFile(null);
                setIsImportingBrands(false);
                if (brandsFileInputRef.current) {
                    brandsFileInputRef.current.value = '';
                }
                // Refresh the page to show updated data
                router.reload();
            },
            onError: (errors) => {
                debugLog('Brands import failed', errors);
                console.error('Brands import errors:', errors);
                if (errors.file) {
                    toast.error(`File error: ${errors.file}`);
                } else if (errors.message) {
                    toast.error(errors.message);
                } else {
                    toast.error('Import failed. Please check your CSV format and try again.');
                }
                setIsImportingBrands(false);
            }
        });
    };

    // Models import handlers
    const handleModelsTemplateDownload = () => {
        debugLog('Models template download initiated');
        window.location.href = '/admin/models/template/download';
        toast.success('Models template download started.');
    };

    const handleModelsImport = () => {
        debugLog('Models import file selection triggered');
        modelsFileInputRef.current?.click();
    };

    const handleModelsFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        debugLog('Models file selected', { fileName: file?.name, fileSize: file?.size });

        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.csv')) {
            debugLog('Models file validation failed: not CSV');
            toast.error('Please select a CSV file');
            return;
        }

        setSelectedModelsFile(file);
        setShowModelsImportDialog(true);
        debugLog('Models import dialog opened');
    };

    const handleModelsImportConfirm = () => {
        if (!selectedModelsFile) {
            debugLog('Models import confirmation failed: no file selected');
            return;
        }

        debugLog('Models import started', { fileName: selectedModelsFile.name });
        setIsImportingModels(true);
        const formData = new FormData();
        formData.append('file', selectedModelsFile);

        // Add CSRF token manually when using forceFormData
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        router.post('/admin/bulk-import/models', formData, {
            forceFormData: true,
            onSuccess: () => {
                debugLog('Models import successful');
                toast.success('Models imported successfully!');
                setShowModelsImportDialog(false);
                setSelectedModelsFile(null);
                setIsImportingModels(false);
                if (modelsFileInputRef.current) {
                    modelsFileInputRef.current.value = '';
                }
                // Refresh the page to show updated data
                router.reload();
            },
            onError: (errors) => {
                debugLog('Models import failed', errors);
                console.error('Models import errors:', errors);
                if (errors.file) {
                    toast.error(`File error: ${errors.file}`);
                } else if (errors.message) {
                    toast.error(errors.message);
                } else {
                    toast.error('Import failed. Please check your CSV format and try again.');
                }
                setIsImportingModels(false);
            }
        });
    };

    // Parts import handlers
    const handlePartsTemplateDownload = () => {
        debugLog('Parts template download initiated');
        window.location.href = '/admin/parts/template/download';
        toast.success('Parts template download started.');
    };

    const handlePartsImport = () => {
        debugLog('Parts import file selection triggered');
        partsFileInputRef.current?.click();
    };

    const handlePartsFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        debugLog('Parts file selected', { fileName: file?.name, fileSize: file?.size });

        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.csv')) {
            debugLog('Parts file validation failed: not CSV');
            toast.error('Please select a CSV file');
            return;
        }

        setSelectedPartsFile(file);
        setShowPartsImportDialog(true);
        debugLog('Parts import dialog opened');
    };

    const handlePartsImportConfirm = () => {
        if (!selectedPartsFile) {
            debugLog('Parts import confirmation failed: no file selected');
            return;
        }

        debugLog('Parts import started', { fileName: selectedPartsFile.name });
        setIsImportingParts(true);
        const formData = new FormData();
        formData.append('file', selectedPartsFile);

        // Add CSRF token manually when using forceFormData
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        router.post('/admin/bulk-import/parts', formData, {
            forceFormData: true,
            onSuccess: () => {
                debugLog('Parts import successful');
                toast.success('Parts imported successfully!');
                setShowPartsImportDialog(false);
                setSelectedPartsFile(null);
                setIsImportingParts(false);
                if (partsFileInputRef.current) {
                    partsFileInputRef.current.value = '';
                }
                // Refresh the page to show updated data
                router.reload();
            },
            onError: (errors) => {
                debugLog('Parts import failed', errors);
                console.error('Parts import errors:', errors);
                if (errors.file) {
                    toast.error(`File error: ${errors.file}`);
                } else if (errors.message) {
                    toast.error(errors.message);
                } else {
                    toast.error('Import failed. Please check your CSV format and try again.');
                }
                setIsImportingParts(false);
            }
        });
    };
    return (
        <AppLayout>
            <Head title="Bulk Import - Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Bulk Import</h1>
                    <p className="text-muted-foreground">
                        Import categories, brands, models, and parts in bulk using CSV files
                    </p>
                </div>

                {/* Import Options */}
                <div className="grid gap-6 md:grid-cols-4">
                    {/* Categories Import */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Tag className="h-5 w-5" />
                                Import Categories
                            </CardTitle>
                            <CardDescription>
                                Upload a CSV file to import multiple categories at once
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <p className="text-sm text-muted-foreground">
                                    Required columns: name, description, parent_category, sort_order, is_active
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-full"
                                    onClick={handleCategoriesTemplateDownload}
                                >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download Template
                                </Button>
                            </div>
                            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                                <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                <p className="text-sm text-muted-foreground mb-2">
                                    Drop your CSV file here or click to browse
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleCategoriesImport}
                                    disabled={isImportingCategories}
                                >
                                    {isImportingCategories ? 'Importing...' : 'Choose File'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Brands Import */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Smartphone className="h-5 w-5" />
                                Import Brands
                            </CardTitle>
                            <CardDescription>
                                Upload a CSV file to import multiple brands at once
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <p className="text-sm text-muted-foreground">
                                    Required columns: name, country, website, logo_url
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-full"
                                    onClick={handleBrandsTemplateDownload}
                                >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download Template
                                </Button>
                            </div>
                            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                                <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                <p className="text-sm text-muted-foreground mb-2">
                                    Drop your CSV file here or click to browse
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleBrandsImport}
                                    disabled={isImportingBrands}
                                >
                                    {isImportingBrands ? 'Importing...' : 'Choose File'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Models Import */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Import Models
                            </CardTitle>
                            <CardDescription>
                                Upload a CSV file to import multiple mobile models
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <p className="text-sm text-muted-foreground">
                                    Required columns: Brand Name, Model Name, Model Number, Release Year, Status
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-full"
                                    onClick={handleModelsTemplateDownload}
                                >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download Template
                                </Button>
                            </div>
                            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                                <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                <p className="text-sm text-muted-foreground mb-2">
                                    Drop your CSV file here or click to browse
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleModelsImport}
                                    disabled={isImportingModels}
                                >
                                    {isImportingModels ? 'Importing...' : 'Choose File'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Parts Import */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Import Parts
                            </CardTitle>
                            <CardDescription>
                                Upload a CSV file to import multiple parts
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <p className="text-sm text-muted-foreground">
                                    Required columns: Brand, Models, Part Name, Part Number, Category, Status
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-full"
                                    onClick={handlePartsTemplateDownload}
                                >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download Template
                                </Button>
                            </div>
                            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                                <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                <p className="text-sm text-muted-foreground mb-2">
                                    Drop your CSV file here or click to browse
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handlePartsImport}
                                    disabled={isImportingParts}
                                >
                                    {isImportingParts ? 'Importing...' : 'Choose File'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Instructions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Import Instructions</CardTitle>
                        <CardDescription>
                            Follow these guidelines for successful imports
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div>
                                <h4 className="font-medium mb-2">File Format</h4>
                                <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                                    <li>Files must be in CSV format with UTF-8 encoding</li>
                                    <li>First row should contain column headers</li>
                                    <li>Maximum file size: 10MB</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium mb-2">Data Requirements</h4>
                                <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                                    <li>All required columns must be present</li>
                                    <li>Boolean fields should use 'Active'/'Inactive' or 'true'/'false' values</li>
                                    <li>For models import, brands must exist in the system</li>
                                    <li>For parts import, categories must exist in the system</li>
                                    <li>For categories import, parent categories must be imported before child categories</li>
                                </ul>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                </div>
            </div>

            {/* Hidden file inputs for all import types */}
            <input
                type="file"
                ref={categoriesFileInputRef}
                onChange={handleCategoriesFileChange}
                accept=".csv"
                style={{ display: 'none' }}
            />
            <input
                type="file"
                ref={brandsFileInputRef}
                onChange={handleBrandsFileChange}
                accept=".csv"
                style={{ display: 'none' }}
            />
            <input
                type="file"
                ref={modelsFileInputRef}
                onChange={handleModelsFileChange}
                accept=".csv"
                style={{ display: 'none' }}
            />
            <input
                type="file"
                ref={partsFileInputRef}
                onChange={handlePartsFileChange}
                accept=".csv"
                style={{ display: 'none' }}
            />

            {/* Categories Import Dialog */}
            <Dialog open={showCategoriesImportDialog} onOpenChange={setShowCategoriesImportDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Import Categories</DialogTitle>
                        <DialogDescription>
                            Upload a CSV file to import categories. Make sure your file follows the template format.
                        </DialogDescription>
                    </DialogHeader>

                    {selectedCategoriesFile && (
                        <div className="space-y-4">
                            <div className="p-4 bg-muted rounded-lg">
                                <div className="flex items-center gap-2">
                                    <FileText className="h-4 w-4" />
                                    <span className="font-medium">{selectedCategoriesFile.name}</span>
                                    <span className="text-sm text-muted-foreground">
                                        ({(selectedCategoriesFile.size / 1024).toFixed(1)} KB)
                                    </span>
                                </div>
                            </div>

                            <div className="text-sm text-muted-foreground">
                                <p>• Make sure your CSV file has the correct headers</p>
                                <p>• Parent categories must be imported before child categories</p>
                                <p>• Use the template for the correct format</p>
                                <p>• Status should be 'Active' or 'Inactive'</p>
                            </div>
                        </div>
                    )}

                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                debugLog('Categories import dialog cancelled');
                                setShowCategoriesImportDialog(false);
                                setSelectedCategoriesFile(null);
                                if (categoriesFileInputRef.current) {
                                    categoriesFileInputRef.current.value = '';
                                }
                            }}
                            disabled={isImportingCategories}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleCategoriesImportConfirm}
                            disabled={!selectedCategoriesFile || isImportingCategories}
                        >
                            {isImportingCategories ? 'Importing...' : 'Import Categories'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Brands Import Dialog */}
            <Dialog open={showBrandsImportDialog} onOpenChange={setShowBrandsImportDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Import Brands</DialogTitle>
                        <DialogDescription>
                            Upload a CSV file to import brands. Make sure your file follows the template format.
                        </DialogDescription>
                    </DialogHeader>

                    {selectedBrandsFile && (
                        <div className="space-y-4">
                            <div className="p-4 bg-muted rounded-lg">
                                <div className="flex items-center gap-2">
                                    <FileText className="h-4 w-4" />
                                    <span className="font-medium">{selectedBrandsFile.name}</span>
                                    <span className="text-sm text-muted-foreground">
                                        ({(selectedBrandsFile.size / 1024).toFixed(1)} KB)
                                    </span>
                                </div>
                            </div>

                            <div className="text-sm text-muted-foreground">
                                <p>• Make sure your CSV file has the correct headers</p>
                                <p>• Brand names must be unique</p>
                                <p>• Use the template for the correct format</p>
                                <p>• Website and logo URLs must be valid if provided</p>
                            </div>
                        </div>
                    )}

                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                debugLog('Brands import dialog cancelled');
                                setShowBrandsImportDialog(false);
                                setSelectedBrandsFile(null);
                                if (brandsFileInputRef.current) {
                                    brandsFileInputRef.current.value = '';
                                }
                            }}
                            disabled={isImportingBrands}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleBrandsImportConfirm}
                            disabled={!selectedBrandsFile || isImportingBrands}
                        >
                            {isImportingBrands ? 'Importing...' : 'Import Brands'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Models Import Dialog */}
            <Dialog open={showModelsImportDialog} onOpenChange={setShowModelsImportDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Import Models</DialogTitle>
                        <DialogDescription>
                            Upload a CSV file to import mobile models. Make sure your file follows the template format.
                        </DialogDescription>
                    </DialogHeader>

                    {selectedModelsFile && (
                        <div className="space-y-4">
                            <div className="p-4 bg-muted rounded-lg">
                                <div className="flex items-center gap-2">
                                    <FileText className="h-4 w-4" />
                                    <span className="font-medium">{selectedModelsFile.name}</span>
                                    <span className="text-sm text-muted-foreground">
                                        ({(selectedModelsFile.size / 1024).toFixed(1)} KB)
                                    </span>
                                </div>
                            </div>

                            <div className="text-sm text-muted-foreground">
                                <p>• Make sure your CSV file has the correct headers</p>
                                <p>• Brand names must match existing brands exactly</p>
                                <p>• Use the template for the correct format</p>
                                <p>• Status should be 'Active' or 'Inactive'</p>
                            </div>
                        </div>
                    )}

                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                debugLog('Models import dialog cancelled');
                                setShowModelsImportDialog(false);
                                setSelectedModelsFile(null);
                                if (modelsFileInputRef.current) {
                                    modelsFileInputRef.current.value = '';
                                }
                            }}
                            disabled={isImportingModels}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleModelsImportConfirm}
                            disabled={!selectedModelsFile || isImportingModels}
                        >
                            {isImportingModels ? 'Importing...' : 'Import Models'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Parts Import Dialog */}
            <Dialog open={showPartsImportDialog} onOpenChange={setShowPartsImportDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Import Parts</DialogTitle>
                        <DialogDescription>
                            Upload a CSV file to import parts. Make sure your file follows the template format.
                        </DialogDescription>
                    </DialogHeader>

                    {selectedPartsFile && (
                        <div className="space-y-4">
                            <div className="p-4 bg-muted rounded-lg">
                                <div className="flex items-center gap-2">
                                    <FileText className="h-4 w-4" />
                                    <span className="font-medium">{selectedPartsFile.name}</span>
                                    <span className="text-sm text-muted-foreground">
                                        ({(selectedPartsFile.size / 1024).toFixed(1)} KB)
                                    </span>
                                </div>
                            </div>

                            <div className="text-sm text-muted-foreground">
                                <p>• Make sure your CSV file has the correct headers</p>
                                <p>• Brand and category names must match existing records exactly</p>
                                <p>• Use the template for the correct format</p>
                                <p>• Status should be 'Active' or 'Inactive'</p>
                            </div>
                        </div>
                    )}

                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                debugLog('Parts import dialog cancelled');
                                setShowPartsImportDialog(false);
                                setSelectedPartsFile(null);
                                if (partsFileInputRef.current) {
                                    partsFileInputRef.current.value = '';
                                }
                            }}
                            disabled={isImportingParts}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handlePartsImportConfirm}
                            disabled={!selectedPartsFile || isImportingParts}
                        >
                            {isImportingParts ? 'Importing...' : 'Import Parts'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
