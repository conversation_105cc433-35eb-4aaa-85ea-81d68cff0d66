import { Head, router, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Shield, 
    Settings, 
    AlertTriangle,
    CheckCircle,
    XCircle,
    Clock,
    Users,
    Activity,
    RefreshCw
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface RateLimitConfig {
    max_attempts: number;
    decay_seconds: number;
    description: string;
}

type FormDataType = Record<string, {
    max_attempts: number;
    decay_seconds: number;
    description: string;
}>;

interface UserStatus {
    action: string;
    max_attempts: number;
    remaining_attempts: number;
    window_seconds: number;
    retry_after_seconds: number;
    is_limited: boolean;
    enabled: boolean;
}

interface Statistics {
    total_violations_7_days: number;
    violations_by_action: Record<string, number>;
    top_violating_users: Array<{
        user: { id: number; name: string; email: string };
        count: number;
        last_violation: string;
    }>;
    recent_violations: Array<{
        id: number;
        user: { id: number; name: string; email: string };
        action: string;
        ip_address: string;
        created_at: string;
    }>;
}

interface Props {
    is_enabled: boolean;
    configs: Record<string, RateLimitConfig>;
    user_status: Record<string, UserStatus>;
    statistics: Statistics;
}

export default function RateLimitIndex({ is_enabled, configs, user_status, statistics }: Props) {
    const [isEnabled, setIsEnabled] = useState(is_enabled);
    const [isToggling, setIsToggling] = useState(false);

    const { data, setData, post, processing, reset } = useForm<FormDataType>(
        Object.fromEntries(
            Object.entries(configs).map(([key, config]) => [
                key,
                {
                    max_attempts: config.max_attempts,
                    decay_seconds: config.decay_seconds,
                    description: config.description
                }
            ])
        )
    );

    const handleToggle = () => {
        setIsToggling(true);
        const newState = !isEnabled;
        
        router.post('/admin/rate-limit/toggle', { enabled: newState }, {
            onSuccess: () => {
                setIsEnabled(newState);
            },
            onFinish: () => setIsToggling(false)
        });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/admin/rate-limit/configs');
    };

    const handleReset = () => {
        router.post('/admin/rate-limit/reset', {}, {
            onSuccess: () => {
                reset();
            }
        });
    };

    const formatDuration = (seconds: number) => {
        if (seconds < 60) return `${seconds}s`;
        if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
        return `${Math.floor(seconds / 3600)}h`;
    };

    const getStatusBadge = (status: UserStatus) => {
        if (!status.enabled) {
            return <Badge variant="secondary">Disabled</Badge>;
        }
        if (status.is_limited) {
            return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Limited</Badge>;
        }
        if (status.remaining_attempts <= 5) {
            return <Badge variant="outline" className="border-yellow-500 text-yellow-700"><AlertTriangle className="h-3 w-3 mr-1" />Warning</Badge>;
        }
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />OK</Badge>;
    };

    return (
        <AppLayout>
            <Head title="Rate Limiting" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Rate Limiting</h1>
                            <p className="text-muted-foreground mt-2">
                                Manage rate limiting settings to prevent abuse of admin actions
                            </p>
                        </div>
                        <div className="flex items-center gap-4">
                            <div className="flex items-center space-x-2">
                                <Label htmlFor="rate-limit-toggle">Rate Limiting</Label>
                                <Switch
                                    id="rate-limit-toggle"
                                    checked={isEnabled}
                                    onCheckedChange={handleToggle}
                                    disabled={isToggling}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Status Alert */}
                    {!isEnabled && (
                        <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                Rate limiting is currently disabled. Admin actions are not being rate limited.
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Overview Stats */}
                    <div className="grid gap-4 md:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Status</CardTitle>
                                <Shield className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {isEnabled ? 'Enabled' : 'Disabled'}
                                </div>
                                <div className="mt-2">
                                    {isEnabled ? (
                                        <Badge variant="default" className="bg-green-100 text-green-800">
                                            <CheckCircle className="h-3 w-3 mr-1" />Active
                                        </Badge>
                                    ) : (
                                        <Badge variant="destructive">
                                            <XCircle className="h-3 w-3 mr-1" />Inactive
                                        </Badge>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Violations (7 days)</CardTitle>
                                <AlertTriangle className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{statistics.total_violations_7_days}</div>
                                <p className="text-xs text-muted-foreground">
                                    Rate limit exceeded events
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Protected Actions</CardTitle>
                                <Activity className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{Object.keys(configs).length}</div>
                                <p className="text-xs text-muted-foreground">
                                    Different action types
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Top Violators</CardTitle>
                                <Users className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{statistics.top_violating_users.length}</div>
                                <p className="text-xs text-muted-foreground">
                                    Users with violations
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Main Content Tabs */}
                    <Tabs defaultValue="settings" className="space-y-4">
                        <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="settings">Settings</TabsTrigger>
                            <TabsTrigger value="status">Current Status</TabsTrigger>
                            <TabsTrigger value="statistics">Statistics</TabsTrigger>
                        </TabsList>

                        <TabsContent value="settings" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Settings className="h-5 w-5" />
                                        Rate Limit Configuration
                                    </CardTitle>
                                    <CardDescription>
                                        Configure rate limits for different types of admin actions
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        <div className="grid gap-6">
                                            {Object.entries(data).map(([action, config]) => (
                                                <div key={action} className="border rounded-lg p-4">
                                                    <h3 className="font-medium mb-3 capitalize">
                                                        {action.replace('_', ' ')}
                                                    </h3>
                                                    <p className="text-sm text-muted-foreground mb-4">
                                                        {config.description}
                                                    </p>
                                                    
                                                    <div className="grid gap-4 md:grid-cols-2">
                                                        <div className="space-y-2">
                                                            <Label htmlFor={`${action}_max_attempts`}>
                                                                Max Attempts
                                                            </Label>
                                                            <Input
                                                                id={`${action}_max_attempts`}
                                                                type="number"
                                                                min="1"
                                                                max="1000"
                                                                value={config.max_attempts}
                                                                onChange={(e) => setData(action, {
                                                                    ...config,
                                                                    max_attempts: parseInt(e.target.value)
                                                                })}
                                                            />
                                                        </div>

                                                        <div className="space-y-2">
                                                            <Label htmlFor={`${action}_decay_seconds`}>
                                                                Window (seconds)
                                                            </Label>
                                                            <Input
                                                                id={`${action}_decay_seconds`}
                                                                type="number"
                                                                min="60"
                                                                max="3600"
                                                                value={config.decay_seconds}
                                                                onChange={(e) => setData(action, {
                                                                    ...config,
                                                                    decay_seconds: parseInt(e.target.value)
                                                                })}
                                                            />
                                                            <p className="text-xs text-muted-foreground">
                                                                {formatDuration(config.decay_seconds)} window
                                                            </p>
                                                        </div>

                                                        <div className="space-y-2 md:col-span-2">
                                                            <Label htmlFor={`${action}_description`}>
                                                                Description
                                                            </Label>
                                                            <Input
                                                                id={`${action}_description`}
                                                                value={config.description}
                                                                onChange={(e) => setData(action, {
                                                                    ...config,
                                                                    description: e.target.value
                                                                })}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>

                                        <div className="flex justify-between">
                                            <Button type="button" variant="outline" onClick={handleReset}>
                                                <RefreshCw className="h-4 w-4 mr-2" />
                                                Reset to Defaults
                                            </Button>
                                            <Button type="submit" disabled={processing}>
                                                {processing ? 'Saving...' : 'Save Configuration'}
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="status" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Clock className="h-5 w-5" />
                                        Your Current Rate Limit Status
                                    </CardTitle>
                                    <CardDescription>
                                        Current rate limit status for your admin account
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {Object.entries(user_status).map(([action, status]) => (
                                            <div key={action} className="flex items-center justify-between p-3 border rounded-lg">
                                                <div>
                                                    <h4 className="font-medium capitalize">
                                                        {action.replace('_', ' ')}
                                                    </h4>
                                                    <p className="text-sm text-muted-foreground">
                                                        {status.remaining_attempts} of {status.max_attempts} attempts remaining
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    {getStatusBadge(status)}
                                                    {status.is_limited && (
                                                        <p className="text-xs text-red-600 mt-1">
                                                            Retry in {formatDuration(status.retry_after_seconds)}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="statistics" className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Violations by Action</CardTitle>
                                        <CardDescription>Last 7 days</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {Object.entries(statistics.violations_by_action).map(([action, count]) => (
                                                <div key={action} className="flex justify-between">
                                                    <span className="text-sm capitalize">
                                                        {action.replace('_', ' ')}
                                                    </span>
                                                    <span className="font-bold">{count}</span>
                                                </div>
                                            ))}
                                            {Object.keys(statistics.violations_by_action).length === 0 && (
                                                <p className="text-sm text-muted-foreground">No violations recorded</p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle>Top Violating Users</CardTitle>
                                        <CardDescription>Users with most violations</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {statistics.top_violating_users.map((violator) => (
                                                <div key={violator.user.id} className="flex justify-between">
                                                    <div>
                                                        <span className="text-sm font-medium">
                                                            {violator.user.name}
                                                        </span>
                                                        <p className="text-xs text-muted-foreground">
                                                            {violator.user.email}
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <span className="font-bold">{violator.count}</span>
                                                        <p className="text-xs text-muted-foreground">
                                                            violations
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                            {statistics.top_violating_users.length === 0 && (
                                                <p className="text-sm text-muted-foreground">No violations recorded</p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Recent Violations</CardTitle>
                                    <CardDescription>Latest rate limit violations</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {statistics.recent_violations.map((violation) => (
                                            <div key={violation.id} className="flex justify-between items-center p-3 border rounded-lg">
                                                <div>
                                                    <span className="font-medium">{violation.user.name}</span>
                                                    <p className="text-sm text-muted-foreground">
                                                        {violation.action.replace('_', ' ')} • {violation.ip_address}
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="text-sm">
                                                        {new Date(violation.created_at).toLocaleDateString()}
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {new Date(violation.created_at).toLocaleTimeString()}
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                        {statistics.recent_violations.length === 0 && (
                                            <p className="text-sm text-muted-foreground">No recent violations</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
