import { Head, router, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Shield, 
    Settings, 
    Mail, 
    CheckCircle,
    XCircle,
    Clock,
    AlertTriangle,
    Key,
    Activity,
    Trash2,
    Send
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface OtpStatus {
    has_otp_pending: boolean;
    otp_expires_at: string | null;
    remaining_attempts: number;
    is_locked_out: boolean;
    lockout_remaining_minutes: number;
    two_factor_enabled: boolean;
}

interface GlobalSettings {
    enabled: boolean;
    otp_expiry_minutes: number;
    max_attempts: number;
    lockout_duration_minutes: number;
}

interface Verification {
    id: number;
    type: string;
    action: string;
    success: boolean;
    ip_address: string;
    created_at: string;
}

interface Session {
    action: string;
    verified_at: string;
    expires_at: string;
}

interface Props {
    user_2fa_status: OtpStatus;
    global_settings: GlobalSettings;
    recent_verifications: Verification[];
    active_sessions: Session[];
}

export default function TwoFactorIndex({ 
    user_2fa_status, 
    global_settings, 
    recent_verifications, 
    active_sessions 
}: Props) {
    const [isTestingOtp, setIsTestingOtp] = useState(false);
    const [testOtpSent, setTestOtpSent] = useState(false);
    const [isTogglingGlobal, setIsTogglingGlobal] = useState(false);

    const { data: disableData, setData: setDisableData, post: postDisable, processing: disableProcessing } = useForm({
        confirmation: '',
    });

    const { data: testData, setData: setTestData, post: postTest, processing: testProcessing, reset: resetTest } = useForm({
        otp_code: '',
    });

    const handleEnable2FA = () => {
        router.post('/admin/two-factor/enable');
    };

    const handleDisable2FA = (e: React.FormEvent) => {
        e.preventDefault();
        postDisable('/admin/two-factor/disable', {
            onSuccess: () => {
                setDisableData('confirmation', '');
            }
        });
    };

    const handleSendTestOtp = () => {
        setIsTestingOtp(true);
        router.post('/admin/two-factor/send-test', {}, {
            onSuccess: () => {
                setTestOtpSent(true);
            },
            onFinish: () => setIsTestingOtp(false)
        });
    };

    const handleVerifyTestOtp = (e: React.FormEvent) => {
        e.preventDefault();
        postTest('/admin/two-factor/verify-test', {
            onSuccess: () => {
                setTestOtpSent(false);
                resetTest();
            }
        });
    };

    const handleToggleGlobal = () => {
        setIsTogglingGlobal(true);
        router.post('/admin/two-factor/toggle-global', { 
            enabled: !global_settings.enabled 
        }, {
            onFinish: () => setIsTogglingGlobal(false)
        });
    };

    const handleClearSessions = () => {
        if (confirm('Are you sure you want to clear all verification sessions? You will need to re-verify for protected actions.')) {
            router.post('/admin/two-factor/clear-sessions');
        }
    };

    const formatActionName = (action: string) => {
        return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    };

    const getStatusBadge = (status: OtpStatus) => {
        if (!status.two_factor_enabled) {
            return <Badge variant="secondary">Disabled</Badge>;
        }
        if (status.is_locked_out) {
            return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Locked Out</Badge>;
        }
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>;
    };

    return (
        <AppLayout>
            <Head title="Two-Factor Authentication" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Two-Factor Authentication</h1>
                            <p className="text-muted-foreground mt-2">
                                Secure your admin actions with email-based verification codes
                            </p>
                        </div>
                    </div>

                    {/* Status Overview */}
                    <div className="grid gap-4 md:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Your 2FA Status</CardTitle>
                                <Shield className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {user_2fa_status.two_factor_enabled ? 'Enabled' : 'Disabled'}
                                </div>
                                <div className="mt-2">
                                    {getStatusBadge(user_2fa_status)}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Global Setting</CardTitle>
                                <Settings className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {global_settings.enabled ? 'Required' : 'Optional'}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    For all admin actions
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
                                <Key className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{active_sessions.length}</div>
                                <p className="text-xs text-muted-foreground">
                                    Verified actions
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Recent Attempts</CardTitle>
                                <Activity className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{recent_verifications.length}</div>
                                <p className="text-xs text-muted-foreground">
                                    Last 10 verifications
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Lockout Warning */}
                    {user_2fa_status.is_locked_out && (
                        <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                Your account is temporarily locked due to too many failed verification attempts. 
                                You can try again in {user_2fa_status.lockout_remaining_minutes} minutes.
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Main Content Tabs */}
                    <Tabs defaultValue="settings" className="space-y-4">
                        <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="settings">Settings</TabsTrigger>
                            <TabsTrigger value="test">Test</TabsTrigger>
                            <TabsTrigger value="sessions">Sessions</TabsTrigger>
                            <TabsTrigger value="activity">Activity</TabsTrigger>
                        </TabsList>

                        <TabsContent value="settings" className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                {/* Personal 2FA Settings */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Shield className="h-5 w-5" />
                                            Your 2FA Settings
                                        </CardTitle>
                                        <CardDescription>
                                            Enable or disable two-factor authentication for your account
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        {!user_2fa_status.two_factor_enabled ? (
                                            <div className="space-y-4">
                                                <Alert>
                                                    <Mail className="h-4 w-4" />
                                                    <AlertDescription>
                                                        When enabled, you'll receive verification codes via email for sensitive admin actions.
                                                    </AlertDescription>
                                                </Alert>
                                                <Button onClick={handleEnable2FA} className="w-full">
                                                    <Shield className="h-4 w-4 mr-2" />
                                                    Enable Two-Factor Authentication
                                                </Button>
                                            </div>
                                        ) : (
                                            <div className="space-y-4">
                                                <Alert className="border-green-200 bg-green-50">
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                    <AlertDescription className="text-green-800">
                                                        Two-factor authentication is enabled for your account.
                                                    </AlertDescription>
                                                </Alert>
                                                
                                                <form onSubmit={handleDisable2FA} className="space-y-4">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="confirmation">
                                                            Type "DISABLE_2FA" to confirm disabling
                                                        </Label>
                                                        <Input
                                                            id="confirmation"
                                                            value={disableData.confirmation}
                                                            onChange={(e) => setDisableData('confirmation', e.target.value)}
                                                            placeholder="DISABLE_2FA"
                                                        />
                                                    </div>
                                                    <Button 
                                                        type="submit" 
                                                        variant="destructive" 
                                                        disabled={disableData.confirmation !== 'DISABLE_2FA' || disableProcessing}
                                                        className="w-full"
                                                    >
                                                        {disableProcessing ? 'Disabling...' : 'Disable Two-Factor Authentication'}
                                                    </Button>
                                                </form>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                {/* Global Settings */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Settings className="h-5 w-5" />
                                            Global Settings
                                        </CardTitle>
                                        <CardDescription>
                                            System-wide two-factor authentication settings
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <Label htmlFor="global-2fa">Require 2FA for Admin Actions</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    When enabled, all admins must use 2FA for sensitive actions
                                                </p>
                                            </div>
                                            <Switch
                                                id="global-2fa"
                                                checked={global_settings.enabled}
                                                onCheckedChange={handleToggleGlobal}
                                                disabled={isTogglingGlobal}
                                            />
                                        </div>

                                        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                                            <div>
                                                <Label className="text-sm font-medium">Code Expiry</Label>
                                                <p className="text-sm text-muted-foreground">{global_settings.otp_expiry_minutes} minutes</p>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium">Max Attempts</Label>
                                                <p className="text-sm text-muted-foreground">{global_settings.max_attempts} attempts</p>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium">Lockout Duration</Label>
                                                <p className="text-sm text-muted-foreground">{global_settings.lockout_duration_minutes} minutes</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="test" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Mail className="h-5 w-5" />
                                        Test Email Verification
                                    </CardTitle>
                                    <CardDescription>
                                        Send a test verification code to ensure your email setup is working
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {!user_2fa_status.two_factor_enabled ? (
                                        <Alert>
                                            <AlertTriangle className="h-4 w-4" />
                                            <AlertDescription>
                                                You must enable two-factor authentication before you can test it.
                                            </AlertDescription>
                                        </Alert>
                                    ) : (
                                        <div className="space-y-4">
                                            {!testOtpSent ? (
                                                <Button 
                                                    onClick={handleSendTestOtp} 
                                                    disabled={isTestingOtp || user_2fa_status.is_locked_out}
                                                    className="w-full"
                                                >
                                                    <Send className="h-4 w-4 mr-2" />
                                                    {isTestingOtp ? 'Sending...' : 'Send Test Verification Code'}
                                                </Button>
                                            ) : (
                                                <form onSubmit={handleVerifyTestOtp} className="space-y-4">
                                                    <Alert className="border-blue-200 bg-blue-50">
                                                        <Mail className="h-4 w-4 text-blue-600" />
                                                        <AlertDescription className="text-blue-800">
                                                            Test verification code sent! Check your email and enter the 6-digit code below.
                                                        </AlertDescription>
                                                    </Alert>
                                                    
                                                    <div className="space-y-2">
                                                        <Label htmlFor="test_otp">Verification Code</Label>
                                                        <Input
                                                            id="test_otp"
                                                            value={testData.otp_code}
                                                            onChange={(e) => setTestData('otp_code', e.target.value)}
                                                            placeholder="000000"
                                                            maxLength={6}
                                                            className="text-center text-lg tracking-widest"
                                                        />
                                                    </div>
                                                    
                                                    <div className="flex gap-2">
                                                        <Button 
                                                            type="submit" 
                                                            disabled={testData.otp_code.length !== 6 || testProcessing}
                                                            className="flex-1"
                                                        >
                                                            {testProcessing ? 'Verifying...' : 'Verify Code'}
                                                        </Button>
                                                        <Button 
                                                            type="button" 
                                                            variant="outline" 
                                                            onClick={() => {
                                                                setTestOtpSent(false);
                                                                resetTest();
                                                            }}
                                                        >
                                                            Cancel
                                                        </Button>
                                                    </div>
                                                </form>
                                            )}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="sessions" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Key className="h-5 w-5" />
                                        Active Verification Sessions
                                    </CardTitle>
                                    <CardDescription>
                                        Actions you've recently verified and don't need to re-verify
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {active_sessions.length > 0 ? (
                                        <div className="space-y-4">
                                            <div className="space-y-3">
                                                {active_sessions.map((session, index) => (
                                                    <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                                                        <div>
                                                            <h4 className="font-medium">{formatActionName(session.action)}</h4>
                                                            <p className="text-sm text-muted-foreground">
                                                                Verified: {new Date(session.verified_at).toLocaleString()}
                                                            </p>
                                                        </div>
                                                        <div className="text-right">
                                                            <Badge variant="outline" className="border-green-500 text-green-700">
                                                                <Clock className="h-3 w-3 mr-1" />
                                                                Active
                                                            </Badge>
                                                            <p className="text-xs text-muted-foreground mt-1">
                                                                Expires: {new Date(session.expires_at).toLocaleString()}
                                                            </p>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                            
                                            <Button 
                                                onClick={handleClearSessions} 
                                                variant="outline" 
                                                className="w-full"
                                            >
                                                <Trash2 className="h-4 w-4 mr-2" />
                                                Clear All Sessions
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Key className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                            <h3 className="text-lg font-medium text-foreground mb-2">No Active Sessions</h3>
                                            <p className="text-muted-foreground">
                                                You haven't verified any admin actions recently.
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="activity" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Activity className="h-5 w-5" />
                                        Recent Verification Activity
                                    </CardTitle>
                                    <CardDescription>
                                        Your recent verification attempts and their outcomes
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {recent_verifications.length > 0 ? (
                                        <div className="space-y-3">
                                            {recent_verifications.map((verification) => (
                                                <div key={verification.id} className="flex justify-between items-center p-3 border rounded-lg">
                                                    <div>
                                                        <h4 className="font-medium">{formatActionName(verification.action)}</h4>
                                                        <p className="text-sm text-muted-foreground">
                                                            {new Date(verification.created_at).toLocaleString()} • {verification.ip_address}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        {verification.success ? (
                                                            <Badge variant="default" className="bg-green-100 text-green-800">
                                                                <CheckCircle className="h-3 w-3 mr-1" />
                                                                Success
                                                            </Badge>
                                                        ) : (
                                                            <Badge variant="destructive">
                                                                <XCircle className="h-3 w-3 mr-1" />
                                                                Failed
                                                            </Badge>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                            <h3 className="text-lg font-medium text-foreground mb-2">No Recent Activity</h3>
                                            <p className="text-muted-foreground">
                                                No verification attempts recorded yet.
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
