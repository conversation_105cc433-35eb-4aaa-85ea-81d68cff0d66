import { Head, <PERSON>, router } from '@inertiajs/react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { CalendarDays, Filter, PlusCircle, RotateCw, Search, CreditCard } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

// Simple pagination component
function Pagination({ currentPage, pageCount, onPageChange }: { currentPage: number, pageCount: number, onPageChange: (page: number) => void }) {
  // Only show a limited number of pages to avoid overwhelming UI
  const getVisiblePages = () => {
    // Always show first, last, current, and 1-2 pages around current
    let visible = [1, currentPage - 1, currentPage, currentPage + 1, pageCount]
      .filter(page => page > 0 && page <= pageCount);
    
    // Remove duplicates
    visible = [...new Set(visible)].sort((a, b) => a - b);
    
    // Add ellipsis indicators
    const result = [];
    for (let i = 0; i < visible.length; i++) {
      result.push(visible[i]);
      
      // Add ellipsis if there's a gap
      if (i < visible.length - 1 && visible[i + 1] - visible[i] > 1) {
        result.push('...');
      }
    }
    
    return result;
  };
  
  return (
    <div className="flex items-center justify-between">
      <Button 
        variant="outline" 
        size="sm" 
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        Previous
      </Button>
      
      <div className="flex gap-1">
        {getVisiblePages().map((page, i) => 
          typeof page === 'number' ? (
            <Button 
              key={i}
              variant={currentPage === page ? "default" : "outline"} 
              size="sm"
              onClick={() => onPageChange(page)}
              className="w-8 h-8 p-0"
            >
              {page}
            </Button>
          ) : (
            <span key={i} className="flex items-center justify-center w-8 h-8 text-muted-foreground">...</span>
          )
        )}
      </div>
      
      <Button 
        variant="outline" 
        size="sm" 
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === pageCount}
      >
        Next
      </Button>
    </div>
  );
}

interface UserData {
  id: number;
  name: string;
  email: string;
}

interface SubscriptionData {
  id: number;
  plan_name: string;
  status: string;
  created_at: string;
  current_period_end: string;
  user: UserData;
}

interface Filters {
  status: string;
  plan: string;
  search: string;
  sort_by: string;
  sort_direction: string;
}

interface Counts {
  total: number;
  active: number;
  cancelled: number;
  expired: number;
}

interface Props {
  subscriptions: {
    data: SubscriptionData[];
    links?: {
      first: string;
      last: string;
      prev: string | null;
      next: string | null;
    };
    meta?: {
      current_page: number;
      from: number;
      last_page: number;
      path: string;
      per_page: number;
      to: number;
      total: number;
    };
  };
  filters?: Filters;
  counts?: Counts;
}

export default function Index({
  subscriptions = { data: [] },
  filters,
  counts = { total: 0, active: 0, cancelled: 0, expired: 0 }
}: Props) {
  const [currentFilters, setCurrentFilters] = useState<Filters>(filters || { status: '', plan: '', search: '', sort_by: 'created_at', sort_direction: 'desc' });

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...currentFilters, [key]: value };
    setCurrentFilters(newFilters);
    
    router.get(route('admin.subscriptions.index'), newFilters, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const clearFilters = () => {
    const clearedFilters = {
      status: '',
      plan: '',
      search: '',
      sort_by: 'created_at',
      sort_direction: 'desc',
    };
    setCurrentFilters(clearedFilters);
    
    router.get(route('admin.subscriptions.index'), clearedFilters, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const handleSort = (column: string) => {
    const direction = currentFilters.sort_by === column && currentFilters.sort_direction === 'asc' ? 'desc' : 'asc';
    handleFilterChange('sort_by', column);
    handleFilterChange('sort_direction', direction);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>;
      case 'expired':
        return <Badge variant="outline" className="border-yellow-500 text-yellow-700">Expired</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPlanBadge = (plan: string) => {
    switch (plan) {
      case 'premium':
        return <Badge className="bg-amber-500 hover:bg-amber-600">Premium</Badge>;
      default:
        return <Badge variant="outline">Free</Badge>;
    }
  };

  return (
    <AppLayout>
      <Head title="Subscription Management" />
      
      <div className="flex h-full flex-1 flex-col p-4 space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Subscription Management</h1>
            <p className="text-muted-foreground mt-2">
              Manage user subscriptions, view status, and modify plans
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Link href={route('admin.dashboard')}>
              <Button variant="outline" size="sm">
                <RotateCw className="mr-2 h-4 w-4" />
                Dashboard
              </Button>
            </Link>
            <Link href={route('admin.subscriptions.create')}>
              <Button size="sm">
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Subscription
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 grid-cols-2 sm:grid-cols-4">
          <Card>
            <CardHeader className="py-3">
              <CardTitle className="text-sm font-medium flex items-center justify-between">
                Total Subscriptions
                <CreditCard className="h-4 w-4 text-blue-600" />
              </CardTitle>
            </CardHeader>
            <CardContent className="py-4">
              <div className="text-2xl font-bold">{counts.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="py-3">
              <CardTitle className="text-sm font-medium flex items-center justify-between">
                Active
                <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="py-4">
              <div className="text-2xl font-bold">{counts.active}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="py-3">
              <CardTitle className="text-sm font-medium flex items-center justify-between">
                Cancelled
                <Badge variant="destructive">Cancelled</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="py-4">
              <div className="text-2xl font-bold">{counts.cancelled}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="py-3">
              <CardTitle className="text-sm font-medium flex items-center justify-between">
                Expired
                <Badge variant="outline" className="border-yellow-500 text-yellow-700">Expired</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="py-4">
              <div className="text-2xl font-bold">{counts.expired}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <Filter className="h-4 w-4 mr-2" />
              Filter Subscriptions
            </CardTitle>
            <CardDescription>
              Filter subscription list by various criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium mb-1 block">Status</label>
                <select
                  value={currentFilters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                >
                  <option value="">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="expired">Expired</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium mb-1 block">Plan</label>
                <select
                  value={currentFilters.plan}
                  onChange={(e) => handleFilterChange('plan', e.target.value)}
                  className="w-full rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                >
                  <option value="">All Plans</option>
                  <option value="free">Free</option>
                  <option value="premium">Premium</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium mb-1 block">Search</label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="User name or email..."
                    className="pl-8"
                    value={currentFilters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                  />
                </div>
              </div>
              <div className="flex items-end">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={clearFilters}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Subscriptions Table */}
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-muted/50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer" onClick={() => handleSort('user.name')}>
                      User
                      {currentFilters.sort_by === 'user.name' && (
                        <span className="ml-1">{currentFilters.sort_direction === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer" onClick={() => handleSort('plan_name')}>
                      Plan
                      {currentFilters.sort_by === 'plan_name' && (
                        <span className="ml-1">{currentFilters.sort_direction === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer" onClick={() => handleSort('status')}>
                      Status
                      {currentFilters.sort_by === 'status' && (
                        <span className="ml-1">{currentFilters.sort_direction === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer" onClick={() => handleSort('created_at')}>
                      Created
                      {currentFilters.sort_by === 'created_at' && (
                        <span className="ml-1">{currentFilters.sort_direction === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer" onClick={() => handleSort('current_period_end')}>
                      Expires
                      {currentFilters.sort_by === 'current_period_end' && (
                        <span className="ml-1">{currentFilters.sort_direction === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-card divide-y divide-border">
                  {(subscriptions.data || []).map((subscription) => (
                    <tr key={subscription.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className="text-sm font-medium text-foreground">
                              {subscription.user.name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {subscription.user.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getPlanBadge(subscription.plan_name)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(subscription.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <CalendarDays className="h-3 w-3 mr-1" />
                          {new Date(subscription.created_at).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                        {subscription.status === 'active' ? (
                          <div className="flex items-center">
                            <CalendarDays className="h-3 w-3 mr-1" />
                            {new Date(subscription.current_period_end).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-xs italic">—</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end gap-2">
                          <Link href={route('admin.subscriptions.show', subscription.id)}>
                            <Button variant="outline" size="sm">View</Button>
                          </Link>
                          <Link href={route('admin.subscriptions.edit', subscription.id)}>
                            <Button variant="outline" size="sm">Edit</Button>
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))}

                  {(subscriptions.data || []).length === 0 && (
                    <tr>
                      <td colSpan={6} className="px-6 py-10 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <CreditCard className="h-8 w-8 mb-2" />
                          <p>No subscriptions found matching the filters.</p>
                          <Button variant="outline" size="sm" onClick={clearFilters} className="mt-2">
                            Clear Filters
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            
            {/* Pagination */}
            {subscriptions.meta && subscriptions.meta.last_page > 1 && (
              <div className="px-6 py-4">
                <Pagination
                  currentPage={subscriptions.meta.current_page}
                  pageCount={subscriptions.meta.last_page}
                  onPageChange={(page: number) => {
                    router.get(route('admin.subscriptions.index', {
                      ...currentFilters,
                      page
                    }), {}, {
                      preserveState: true,
                      preserveScroll: true,
                    });
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
} 