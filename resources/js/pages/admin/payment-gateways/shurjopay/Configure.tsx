import { Head, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    Settings,
    CheckCircle,
    XCircle,
    Eye,
    EyeOff,
    ExternalLink,
    Shield,
    Globe,
    Zap,
    Info,
    CreditCard,
    Bug,
    Loader2
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState, useEffect } from 'react';

interface ShurjoPayConfig {
    environment: string;
    username: string;
    password: string;
    prefix: string;
    api_url: string;
    currency: string;
    ssl_verify: boolean;
    logging_enabled: boolean;
    logging_level: string;
    debug: boolean;
}

interface ConnectionStatus {
    success: boolean;
    message: string;
    store_id?: string;
    token_type?: string;
}

interface Props {
    config: ShurjoPayConfig;
    connection_status: ConnectionStatus;
    supported_environments: string[];
    supported_currencies: string[];
    api_urls: Record<string, string>;
}

export default function Configure({
    config,
    connection_status,
    supported_environments,
    supported_currencies,
    api_urls
}: Props) {
    const [showPassword, setShowPassword] = useState(false);
    const [debugLogs, setDebugLogs] = useState<string[]>([]);

    const { data, setData, post, processing, errors, reset } = useForm({
        environment: config.environment,
        username: config.username,
        password: config.password,
        prefix: config.prefix,
        currency: config.currency,
        ssl_verify: config.ssl_verify,
        logging_enabled: config.logging_enabled,
        logging_level: config.logging_level,
        debug: config.debug,
    });

    // Development logging utility
    const logDebug = (message: string, data?: any) => {
        if (process.env.NODE_ENV === 'development') {
            const timestamp = new Date().toISOString();
            const logMessage = `[${timestamp}] ShurjoPay: ${message}`;
            console.log(logMessage, data || '');

            if (data?.debug) {
                setDebugLogs(prev => [...prev.slice(-9), `${timestamp}: ${message}`]);
            }
        }
    };

    // Log initial configuration load
    useEffect(() => {
        logDebug('Configuration page loaded', { config, connection_status });
    }, [config, connection_status]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        logDebug('Form submission started', data);

        post(route('admin.payment-gateways.shurjopay.store'), {
            onSuccess: () => {
                logDebug('Configuration saved successfully');
            },
            onError: (errors) => {
                logDebug('Configuration save failed', errors);
            }
        });
    };

    const getStatusBadge = (status: ConnectionStatus) => {
        if (status.success) {
            return (
                <Badge className="bg-primary/10 text-primary border-primary/20">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Connected
                </Badge>
            );
        }
        return (
            <Badge variant="outline" className="text-destructive border-destructive/30">
                <XCircle className="w-3 h-3 mr-1" />
                Disconnected
            </Badge>
        );
    };

    return (
        <AppLayout>
            <Head title="Configure ShurjoPay" />

            <div className="flex h-full flex-1 flex-col space-y-8 p-6 bg-background">
                {/* Header Section */}
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-3">
                                <div className="w-12 h-12 rounded-xl bg-primary flex items-center justify-center shadow-sm">
                                    <CreditCard className="w-6 h-6 text-primary-foreground" />
                                </div>
                                <div>
                                    <h1 className="text-4xl font-bold tracking-tight text-foreground">ShurjoPay Configuration</h1>
                                    <p className="text-lg text-muted-foreground max-w-2xl">
                                        Configure your ShurjoPay payment gateway settings for Bangladesh market
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            {getStatusBadge(connection_status)}
                            <Badge variant="outline" className="text-sm px-3 py-1 text-muted-foreground border-border">
                                <Globe className="w-4 h-4 mr-2" />
                                Bangladesh
                            </Badge>
                        </div>
                    </div>

                    {/* Connection Status Alert */}
                    {connection_status.success ? (
                        <Alert className="border-primary/20 bg-primary/5">
                            <CheckCircle className="h-4 w-4 text-primary" />
                            <AlertDescription className="text-primary">
                                <strong>Connection successful!</strong> {connection_status.message}
                                {connection_status.store_id && (
                                    <span className="ml-2">Store ID: {connection_status.store_id}</span>
                                )}
                            </AlertDescription>
                        </Alert>
                    ) : (
                        <Alert className="border-destructive/20 bg-destructive/5">
                            <XCircle className="h-4 w-4 text-destructive" />
                            <AlertDescription className="text-destructive">
                                <strong>Connection failed:</strong> {connection_status.message}
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Debug Information */}
                    {data.debug && debugLogs.length > 0 && (
                        <Alert className="border-chart-3/20 bg-chart-3/5">
                            <Bug className="w-4 h-4 text-chart-3" />
                            <AlertDescription className="text-chart-3">
                                <div className="font-medium mb-2">Debug Information:</div>
                                <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
                                    {debugLogs.map((log, index) => (
                                        <div key={index} className="font-mono">{log}</div>
                                    ))}
                                </div>
                            </AlertDescription>
                        </Alert>
                    )}
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Configuration Form */}
                    <div className="lg:col-span-2">
                        <Card className="shadow-sm border">
                            <CardHeader className="bg-primary text-primary-foreground rounded-t-lg pb-6">
                                <CardTitle className="text-2xl font-semibold flex items-center">
                                    <Settings className="w-6 h-6 mr-3" />
                                    Gateway Settings
                                </CardTitle>
                                <CardDescription className="text-primary-foreground/80">
                                    Configure your ShurjoPay API credentials and preferences
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    {/* Environment Selection */}
                                    <div className="space-y-2">
                                        <Label htmlFor="environment" className="text-sm font-medium text-foreground">
                                            Environment
                                        </Label>
                                        <Select value={data.environment} onValueChange={(value) => {
                                            setData('environment', value);
                                            logDebug(`Environment changed to: ${value}`);
                                        }}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select environment" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {supported_environments.map((env) => (
                                                    <SelectItem key={env} value={env}>
                                                        {env.charAt(0).toUpperCase() + env.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.environment && (
                                            <p className="text-sm text-destructive">{errors.environment}</p>
                                        )}
                                    </div>

                                    {/* API Credentials */}
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="username" className="text-sm font-medium text-foreground">
                                                Username
                                            </Label>
                                            <Input
                                                id="username"
                                                type="text"
                                                value={data.username}
                                                onChange={(e) => setData('username', e.target.value)}
                                                placeholder="sp_sandbox"
                                                className="w-full"
                                            />
                                            {errors.username && (
                                                <p className="text-sm text-destructive">{errors.username}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="password" className="text-sm font-medium text-foreground">
                                                Password
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="password"
                                                    type={showPassword ? "text" : "password"}
                                                    value={data.password}
                                                    onChange={(e) => setData('password', e.target.value)}
                                                    placeholder="Enter password"
                                                    className="w-full pr-10"
                                                />
                                                <button
                                                    type="button"
                                                    onClick={() => setShowPassword(!showPassword)}
                                                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                                >
                                                    {showPassword ? (
                                                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                                                    ) : (
                                                        <Eye className="h-4 w-4 text-muted-foreground" />
                                                    )}
                                                </button>
                                            </div>
                                            {errors.password && (
                                                <p className="text-sm text-destructive">{errors.password}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="prefix" className="text-sm font-medium text-foreground">
                                                Order Prefix
                                            </Label>
                                            <Input
                                                id="prefix"
                                                type="text"
                                                value={data.prefix}
                                                onChange={(e) => setData('prefix', e.target.value)}
                                                placeholder="NOK"
                                                className="w-full"
                                            />
                                            {errors.prefix && (
                                                <p className="text-sm text-destructive">{errors.prefix}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="currency" className="text-sm font-medium text-foreground">
                                                Default Currency
                                            </Label>
                                            <Select value={data.currency} onValueChange={(value) => setData('currency', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select currency" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {supported_currencies.map((currency) => (
                                                        <SelectItem key={currency} value={currency}>
                                                            {currency}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.currency && (
                                                <p className="text-sm text-destructive">{errors.currency}</p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Advanced Settings */}
                                    <div className="space-y-4 pt-6 border-t border-border">
                                        <h3 className="text-lg font-medium text-foreground">Advanced Settings</h3>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="flex items-center justify-between">
                                                <div className="space-y-0.5">
                                                    <Label className="text-sm font-medium text-foreground">SSL Verification</Label>
                                                    <p className="text-xs text-muted-foreground">Enable SSL certificate verification</p>
                                                </div>
                                                <Switch
                                                    checked={data.ssl_verify}
                                                    onCheckedChange={(checked) => setData('ssl_verify', checked)}
                                                />
                                            </div>

                                            <div className="flex items-center justify-between">
                                                <div className="space-y-0.5">
                                                    <Label className="text-sm font-medium text-foreground">Debug Mode</Label>
                                                    <p className="text-xs text-muted-foreground">Enable detailed logging</p>
                                                </div>
                                                <Switch
                                                    checked={data.debug}
                                                    onCheckedChange={(checked) => setData('debug', checked)}
                                                />
                                            </div>
                                        </div>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="flex items-center justify-between">
                                                <div className="space-y-0.5">
                                                    <Label className="text-sm font-medium text-foreground">Enable Logging</Label>
                                                    <p className="text-xs text-muted-foreground">Log API requests and responses</p>
                                                </div>
                                                <Switch
                                                    checked={data.logging_enabled}
                                                    onCheckedChange={(checked) => setData('logging_enabled', checked)}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="logging_level" className="text-sm font-medium text-foreground">
                                                    Logging Level
                                                </Label>
                                                <Select value={data.logging_level} onValueChange={(value) => setData('logging_level', value)}>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select level" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="debug">Debug</SelectItem>
                                                        <SelectItem value="info">Info</SelectItem>
                                                        <SelectItem value="warning">Warning</SelectItem>
                                                        <SelectItem value="error">Error</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Submit Button */}
                                    <div className="flex items-center gap-4 pt-6">
                                        <Button
                                            type="submit"
                                            disabled={processing}
                                            className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-md transition-colors"
                                        >
                                            {processing ? (
                                                <>
                                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                    Saving...
                                                </>
                                            ) : (
                                                <>
                                                    <Settings className="w-4 h-4 mr-2" />
                                                    Save Configuration
                                                </>
                                            )}
                                        </Button>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => reset()}
                                            disabled={processing}
                                            className="border-border text-foreground hover:bg-muted"
                                        >
                                            Reset
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Information Sidebar */}
                    <div className="space-y-6">
                        {/* API Information */}
                        <Card className="border-l-4 border-l-primary shadow-sm">
                            <CardHeader>
                                <CardTitle className="text-lg font-semibold text-foreground flex items-center">
                                    <Info className="w-5 h-5 mr-2 text-primary" />
                                    API Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label className="text-sm font-medium text-foreground">Current API URL</Label>
                                    <p className="text-sm text-muted-foreground mt-1 break-all">
                                        {api_urls[data.environment] || config.api_url}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-foreground">Webhook URL</Label>
                                    <p className="text-sm text-muted-foreground mt-1 break-all">
                                        {window.location.origin}/webhooks/shurjopay
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-foreground">Success URL</Label>
                                    <p className="text-sm text-muted-foreground mt-1 break-all">
                                        {window.location.origin}/shurjopay/success
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-foreground">Cancel URL</Label>
                                    <p className="text-sm text-muted-foreground mt-1 break-all">
                                        {window.location.origin}/shurjopay/cancel
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Documentation */}
                        <Card className="border-l-4 border-l-chart-2 shadow-sm">
                            <CardHeader>
                                <CardTitle className="text-lg font-semibold text-foreground flex items-center">
                                    <Zap className="w-5 h-5 mr-2 text-chart-2" />
                                    Documentation
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <a
                                    href="https://shurjopay.com.bd/developers"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center text-sm text-primary hover:text-primary/80 transition-colors"
                                >
                                    <ExternalLink className="w-4 h-4 mr-2" />
                                    Developer Documentation
                                </a>
                                <a
                                    href="https://shurjopay.com.bd/developers/shurjopay-restapi"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center text-sm text-primary hover:text-primary/80 transition-colors"
                                >
                                    <ExternalLink className="w-4 h-4 mr-2" />
                                    REST API Guide
                                </a>
                                <a
                                    href="https://github.com/shurjopay-plugins/sp-plugin-php"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center text-sm text-primary hover:text-primary/80 transition-colors"
                                >
                                    <ExternalLink className="w-4 h-4 mr-2" />
                                    PHP Plugin
                                </a>
                            </CardContent>
                        </Card>

                        {/* Security Notice */}
                        <Card className="border-l-4 border-l-chart-3 shadow-sm bg-chart-3/5">
                            <CardHeader>
                                <CardTitle className="text-lg font-semibold text-foreground flex items-center">
                                    <Shield className="w-5 h-5 mr-2 text-chart-3" />
                                    Security Notice
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-foreground">
                                    Keep your API credentials secure and never share them publicly.
                                    Use sandbox environment for testing and switch to production only when ready.
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
