// Sample data structure for payment gateways
// This would typically come from a backend API

export interface PaymentGateway {
    id: string;
    name: string;
    description: string;
    status: 'active' | 'inactive' | 'configured' | 'not_configured';
    logo?: string;
    features: string[];
    supported_currencies: string[];
    configuration_url: string;
    documentation_url?: string;
}

export const paymentGateways: PaymentGateway[] = [
    {
        id: 'paddle',
        name: 'Paddle',
        description: 'Complete payment solution with global tax compliance',
        status: 'configured',
        logo: '/images/paddle-logo.png',
        features: ['Subscription Billing', 'Global Tax Compliance', 'Fraud Protection', 'Analytics'],
        supported_currencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK'],
        configuration_url: '/admin/payment-gateways/paddle/configure',
        documentation_url: 'https://developer.paddle.com/getting-started'
    },
    {
        id: 'shurjopay',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        description: 'Leading payment gateway for Bangladesh with local banking support',
        status: 'configured',
        logo: '/images/shurjopay-logo.png',
        features: ['Mobile Banking', 'Internet Banking', 'Card Payments', 'Local Support'],
        supported_currencies: ['BDT', 'USD', 'EUR', 'GBP'],
        configuration_url: '/admin/payment-gateways/shurjopay/configure',
        documentation_url: 'https://shurjopay.com.bd/developers'
    },
    {
        id: 'coinbase',
        name: 'Coinbase Commerce',
        description: 'Next-generation crypto payments with Onchain Payment Protocol - automatic USDC settlement, hundreds of currencies, instant confirmation',
        status: 'configured',
        logo: '/images/coinbase-logo.png',
        features: [
            'Onchain Payment Protocol',
            'Auto USDC Settlement',
            'Hundreds of Currencies',
            'Instant Confirmation',
            'Base & Polygon Networks',
            'Volatility Protection',
            'Enterprise Security',
            'Real-time Updates'
        ],
        supported_currencies: ['BTC', 'ETH', 'LTC', 'BCH', 'USDC', 'DAI', '+100s more'],
        configuration_url: '/admin/payment-gateways/coinbase/configure',
        documentation_url: 'https://docs.cdp.coinbase.com/commerce/introduction/welcome'
    }
];

export const getPaddleConfig = () => ({
    id: 1,
    is_enabled: true,
    environment: 'sandbox' as const,
    vendor_id: '12345',
    vendor_auth_code: '',
    public_key: '',
    webhook_secret: '',
    default_currency: 'USD',
    supported_currencies: ['USD', 'EUR', 'GBP'],
    webhook_url: `${window.location.origin}/webhooks/paddle`
});
