import { Head, useForm } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
    Settings,
    CheckCircle,
    XCircle,
    Copy,
    ExternalLink,
    Shield,
    Zap,
    Globe,
    Bitcoin,
    Loader2,
    Eye,
    EyeOff,
    Bug
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface CoinbaseCommerceConfig {
    id?: number;
    is_enabled: boolean;
    api_key: string;
    webhook_secret: string;
    debug_mode: boolean;
    auto_activate_subscriptions: boolean;
    supported_currencies: string[];
    webhook_url: string;
}

interface Props {
    config?: CoinbaseCommerceConfig;
    isConfigured: boolean;
}

export default function Configure({ config, isConfigured }: Props) {
    const [showApiKey, setShowApiKey] = useState(false);
    const [showWebhookSecret, setShowWebhookSecret] = useState(false);
    const [testingConnection, setTestingConnection] = useState(false);
    const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
    const [debugLogs, setDebugLogs] = useState<string[]>([]);

    const { data, setData, post, processing, errors, reset } = useForm({
        is_enabled: config?.is_enabled ?? false,
        api_key: config?.api_key ?? '',
        webhook_secret: config?.webhook_secret ?? '',
        debug_mode: config?.debug_mode ?? false,
        auto_activate_subscriptions: config?.auto_activate_subscriptions ?? true,
        supported_currencies: config?.supported_currencies ?? ['BTC', 'ETH', 'USDC', 'DAI'],
        webhook_url: config?.webhook_url ?? `${window.location.origin}/webhooks/coinbase-commerce`,
    });

    // Development logging utility
    const logDebug = (message: string, data?: any) => {
        if (process.env.NODE_ENV === 'development') {
            const timestamp = new Date().toISOString();
            const logMessage = `[${timestamp}] Coinbase Commerce: ${message}`;
            console.log(logMessage, data || '');

            if (data?.debug_mode) {
                setDebugLogs(prev => [...prev.slice(-9), `${timestamp}: ${message}`]);
            }
        }
    };

    // Log initial configuration load
    useEffect(() => {
        logDebug('Configuration page loaded', { config, isConfigured });
    }, [config, isConfigured]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        logDebug('Form submission started', data);

        post(route('admin.payment-gateways.coinbase.store'), {
            onSuccess: () => {
                logDebug('Configuration saved successfully');
                setTestResult({ success: true, message: 'Configuration saved successfully!' });
            },
            onError: (errors) => {
                logDebug('Configuration save failed', errors);
                setTestResult({ success: false, message: 'Failed to save configuration. Please check your settings.' });
            }
        });
    };

    const testConnection = async () => {
        if (!data.api_key) {
            logDebug('Test connection failed: No API key provided');
            setTestResult({ success: false, message: 'Please enter an API key first.' });
            return;
        }

        logDebug('Testing connection started', { api_key_length: data.api_key.length });
        setTestingConnection(true);
        setTestResult(null);

        try {
            const response = await fetch(route('admin.payment-gateways.coinbase.test'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ api_key: data.api_key }),
            });

            const result = await response.json();
            logDebug('Test connection response', { success: result.success, status: response.status });

            setTestResult({
                success: result.success,
                message: result.success ? 'Connection successful!' : result.error || 'Connection failed'
            });
        } catch (error) {
            logDebug('Test connection error', error);
            setTestResult({ success: false, message: 'Failed to test connection. Please try again.' });
        } finally {
            setTestingConnection(false);
        }
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
    };

    const supportedCurrencies = [
        { code: 'BTC', name: 'Bitcoin', icon: '₿' },
        { code: 'ETH', name: 'Ethereum', icon: 'Ξ' },
        { code: 'LTC', name: 'Litecoin', icon: 'Ł' },
        { code: 'BCH', name: 'Bitcoin Cash', icon: '₿' },
        { code: 'USDC', name: 'USD Coin', icon: '$' },
        { code: 'DAI', name: 'Dai', icon: '◈' },
    ];

    return (
        <AppLayout>
            <Head title="Configure Coinbase Commerce" />

            <div className="flex h-full flex-1 flex-col space-y-8 p-6 bg-background">
                {/* Header Section */}
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-4">
                                <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center shadow-lg ring-2 ring-orange-200 dark:ring-orange-800">
                                    <Bitcoin className="w-8 h-8 text-white" />
                                </div>
                                <div>
                                    <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent dark:from-orange-400 dark:to-amber-400">
                                        Coinbase Commerce
                                    </h1>
                                    <p className="text-lg text-muted-foreground">
                                        Next-generation crypto payments with Onchain Payment Protocol
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            <Badge variant={isConfigured ? "default" : "outline"} className={`px-4 py-2 ${
                                isConfigured
                                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 dark:from-green-950 dark:to-emerald-950 dark:text-green-300 dark:border-green-800'
                                    : 'bg-gradient-to-r from-gray-50 to-slate-50 text-gray-700 border-gray-200 dark:from-gray-950 dark:to-slate-950 dark:text-gray-300 dark:border-gray-800'
                            }`}>
                                {isConfigured ? (
                                    <>
                                        <CheckCircle className="w-4 h-4 mr-2" />
                                        Configured
                                    </>
                                ) : (
                                    <>
                                        <XCircle className="w-4 h-4 mr-2" />
                                        Not Configured
                                    </>
                                )}
                            </Badge>
                            <Badge variant="outline" className="px-4 py-2 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-amber-950 dark:text-orange-300 dark:border-orange-800">
                                <Zap className="w-4 h-4 mr-2" />
                                Onchain Protocol
                            </Badge>
                        </div>
                    </div>

                    {/* Enhanced Status Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <Card className="border-l-4 border-l-orange-500 dark:border-l-orange-400 bg-gradient-to-br from-orange-50/50 to-amber-50/30 dark:from-orange-950/20 dark:to-amber-950/10 shadow-lg hover:shadow-xl transition-all duration-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">Status</p>
                                        <p className="text-3xl font-bold text-foreground mt-2">
                                            {data.is_enabled ? 'Enabled' : 'Disabled'}
                                        </p>
                                    </div>
                                    <div className={`p-4 rounded-xl shadow-sm ${
                                        data.is_enabled
                                            ? 'bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/20'
                                            : 'bg-gradient-to-br from-gray-100 to-slate-100 dark:from-gray-900/30 dark:to-slate-900/20'
                                    }`}>
                                        {data.is_enabled ? (
                                            <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                                        ) : (
                                            <XCircle className="w-6 h-6 text-gray-600 dark:text-gray-400" />
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-l-4 border-l-blue-500 dark:border-l-blue-400 bg-gradient-to-br from-blue-50/50 to-indigo-50/30 dark:from-blue-950/20 dark:to-indigo-950/10 shadow-lg hover:shadow-xl transition-all duration-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">Currencies</p>
                                        <p className="text-3xl font-bold text-foreground mt-2">{data.supported_currencies.length}</p>
                                        <p className="text-xs text-muted-foreground mt-1 flex items-center">
                                            <Globe className="w-3 h-3 mr-1 text-blue-600" />
                                            Supported
                                        </p>
                                    </div>
                                    <div className="p-4 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/20 rounded-xl shadow-sm">
                                        <Globe className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-l-4 border-l-purple-500 dark:border-l-purple-400 bg-gradient-to-br from-purple-50/50 to-violet-50/30 dark:from-purple-950/20 dark:to-violet-950/10 shadow-lg hover:shadow-xl transition-all duration-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">Debug Mode</p>
                                        <p className="text-3xl font-bold text-foreground mt-2">
                                            {data.debug_mode ? 'On' : 'Off'}
                                        </p>
                                        <p className="text-xs text-muted-foreground mt-1 flex items-center">
                                            <Bug className="w-3 h-3 mr-1 text-purple-600" />
                                            Development
                                        </p>
                                    </div>
                                    <div className={`p-4 rounded-xl shadow-sm ${
                                        data.debug_mode
                                            ? 'bg-gradient-to-br from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/20'
                                            : 'bg-gradient-to-br from-gray-100 to-slate-100 dark:from-gray-900/30 dark:to-slate-900/20'
                                    }`}>
                                        <Settings className={`w-6 h-6 ${
                                            data.debug_mode
                                                ? 'text-purple-600 dark:text-purple-400'
                                                : 'text-gray-600 dark:text-gray-400'
                                        }`} />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Enhanced Test Result Alert */}
                {testResult && (
                    <Alert className={`border-2 shadow-lg ${
                        testResult.success
                            ? "border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 dark:border-green-800 dark:from-green-950/30 dark:to-emerald-950/20"
                            : "border-red-200 bg-gradient-to-r from-red-50 to-rose-50 dark:border-red-800 dark:from-red-950/30 dark:to-rose-950/20"
                    }`}>
                        <div className="flex items-center">
                            {testResult.success ? (
                                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                            ) : (
                                <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                            )}
                            <AlertDescription className={`ml-3 font-medium ${
                                testResult.success
                                    ? "text-green-800 dark:text-green-200"
                                    : "text-red-800 dark:text-red-200"
                            }`}>
                                {testResult.message}
                            </AlertDescription>
                        </div>
                    </Alert>
                )}

                {/* Enhanced Debug Information */}
                {data.debug_mode && debugLogs.length > 0 && (
                    <Alert className="border-2 border-purple-200 bg-gradient-to-r from-purple-50 to-violet-50 dark:border-purple-800 dark:from-purple-950/30 dark:to-violet-950/20 shadow-lg">
                        <Bug className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                        <AlertDescription className="text-purple-800 dark:text-purple-200">
                            <div className="font-semibold mb-3 flex items-center">
                                <Zap className="w-4 h-4 mr-2" />
                                Debug Information:
                            </div>
                            <div className="text-sm space-y-1 max-h-32 overflow-y-auto bg-purple-100/50 dark:bg-purple-900/20 rounded-lg p-3">
                                {debugLogs.map((log, index) => (
                                    <div key={index} className="font-mono text-purple-700 dark:text-purple-300">{log}</div>
                                ))}
                            </div>
                        </AlertDescription>
                    </Alert>
                )}

                {/* Configuration Form */}
                <form onSubmit={handleSubmit} className="space-y-8">
                    <div className="grid gap-8 md:grid-cols-2">
                        {/* API Configuration */}
                        <Card className="shadow-sm border">
                            <CardHeader className="bg-primary text-primary-foreground rounded-t-lg">
                                <CardTitle className="flex items-center text-xl">
                                    <Shield className="w-5 h-5 mr-2" />
                                    API Configuration
                                </CardTitle>
                                <CardDescription className="text-primary-foreground/80">
                                    Configure your Coinbase Commerce API credentials
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-6 space-y-6">
                                {/* Enable/Disable */}
                                <div className="flex items-center justify-between">
                                    <div className="space-y-1">
                                        <Label htmlFor="is_enabled" className="text-sm font-medium">
                                            Enable Coinbase Commerce
                                        </Label>
                                        <p className="text-xs text-gray-500">
                                            Enable cryptocurrency payments for your application
                                        </p>
                                    </div>
                                    <Switch
                                        id="is_enabled"
                                        checked={data.is_enabled}
                                        onCheckedChange={(checked) => setData('is_enabled', checked)}
                                    />
                                </div>

                                <Separator />

                                {/* API Key */}
                                <div className="space-y-2">
                                    <Label htmlFor="api_key" className="text-sm font-medium">
                                        API Key *
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="api_key"
                                            type={showApiKey ? "text" : "password"}
                                            value={data.api_key}
                                            onChange={(e) => setData('api_key', e.target.value)}
                                            placeholder="Enter your Coinbase Commerce API key"
                                            className="pr-10"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setShowApiKey(!showApiKey)}
                                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                        >
                                            {showApiKey ? (
                                                <EyeOff className="w-4 h-4 text-gray-400" />
                                            ) : (
                                                <Eye className="w-4 h-4 text-gray-400" />
                                            )}
                                        </button>
                                    </div>
                                    {errors.api_key && (
                                        <p className="text-sm text-red-600">{errors.api_key}</p>
                                    )}
                                    <p className="text-xs text-muted-foreground">
                                        Get your API key from{' '}
                                        <a
                                            href="https://commerce.coinbase.com/settings/security"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-primary hover:text-primary/80 inline-flex items-center transition-colors"
                                        >
                                            Coinbase Commerce Dashboard
                                            <ExternalLink className="w-3 h-3 ml-1" />
                                        </a>
                                    </p>
                                </div>

                                {/* Test Connection */}
                                <Button
                                    type="button"
                                    onClick={testConnection}
                                    disabled={testingConnection || !data.api_key}
                                    variant="outline"
                                    className="w-full border-primary text-primary hover:bg-primary hover:text-primary-foreground transition-colors"
                                >
                                    {testingConnection ? (
                                        <>
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            Testing Connection...
                                        </>
                                    ) : (
                                        <>
                                            <Zap className="w-4 h-4 mr-2" />
                                            Test Connection
                                        </>
                                    )}
                                </Button>
                            </CardContent>
                        </Card>

                        {/* Webhook Configuration */}
                        <Card className="shadow-sm border">
                            <CardHeader className="bg-accent text-accent-foreground rounded-t-lg">
                                <CardTitle className="flex items-center text-xl">
                                    <Globe className="w-5 h-5 mr-2" />
                                    Webhook Configuration
                                </CardTitle>
                                <CardDescription className="text-accent-foreground/80">
                                    Configure webhook settings for real-time updates
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-6 space-y-6">
                                {/* Webhook URL */}
                                <div className="space-y-2">
                                    <Label htmlFor="webhook_url" className="text-sm font-medium">
                                        Webhook URL
                                    </Label>
                                    <div className="flex">
                                        <Input
                                            id="webhook_url"
                                            value={data.webhook_url}
                                            readOnly
                                            className="flex-1 bg-muted"
                                        />
                                        <Button
                                            type="button"
                                            onClick={() => copyToClipboard(data.webhook_url)}
                                            variant="outline"
                                            size="sm"
                                            className="ml-2"
                                        >
                                            <Copy className="w-4 h-4" />
                                        </Button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Add this URL to your Coinbase Commerce webhook settings
                                    </p>
                                </div>

                                {/* Webhook Secret */}
                                <div className="space-y-2">
                                    <Label htmlFor="webhook_secret" className="text-sm font-medium">
                                        Webhook Secret (Optional)
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="webhook_secret"
                                            type={showWebhookSecret ? "text" : "password"}
                                            value={data.webhook_secret}
                                            onChange={(e) => setData('webhook_secret', e.target.value)}
                                            placeholder="Enter webhook secret for verification"
                                            className="pr-10"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setShowWebhookSecret(!showWebhookSecret)}
                                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                        >
                                            {showWebhookSecret ? (
                                                <EyeOff className="w-4 h-4 text-gray-400" />
                                            ) : (
                                                <Eye className="w-4 h-4 text-gray-400" />
                                            )}
                                        </button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Recommended for security. Generate a random string and add it to your webhook settings.
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Advanced Settings */}
                    <Card className="shadow-sm border">
                        <CardHeader className="bg-secondary text-secondary-foreground rounded-t-lg">
                            <CardTitle className="flex items-center text-xl">
                                <Settings className="w-5 h-5 mr-2" />
                                Advanced Settings
                            </CardTitle>
                            <CardDescription className="text-secondary-foreground/80">
                                Configure advanced options and supported cryptocurrencies
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-6 space-y-6">
                            {/* Debug Mode */}
                            <div className="flex items-center justify-between">
                                <div className="space-y-1">
                                    <Label htmlFor="debug_mode" className="text-sm font-medium">
                                        Debug Mode
                                    </Label>
                                    <p className="text-xs text-muted-foreground">
                                        Enable detailed logging for development and troubleshooting
                                    </p>
                                </div>
                                <Switch
                                    id="debug_mode"
                                    checked={data.debug_mode}
                                    onCheckedChange={(checked) => setData('debug_mode', checked)}
                                />
                            </div>

                            <Separator />

                            {/* Auto-activate Subscriptions */}
                            <div className="flex items-center justify-between">
                                <div className="space-y-1">
                                    <Label htmlFor="auto_activate_subscriptions" className="text-sm font-medium">
                                        Auto-activate Subscriptions
                                    </Label>
                                    <p className="text-xs text-muted-foreground">
                                        Automatically activate subscriptions when payments are confirmed
                                    </p>
                                </div>
                                <Switch
                                    id="auto_activate_subscriptions"
                                    checked={data.auto_activate_subscriptions}
                                    onCheckedChange={(checked) => setData('auto_activate_subscriptions', checked)}
                                />
                            </div>

                            <Separator />

                            {/* Supported Currencies */}
                            <div className="space-y-4">
                                <Label className="text-sm font-medium">Supported Cryptocurrencies</Label>
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    {supportedCurrencies.map((currency) => (
                                        <div
                                            key={currency.code}
                                            className={`p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-sm ${
                                                data.supported_currencies.includes(currency.code)
                                                    ? 'border-primary bg-primary/5 shadow-sm'
                                                    : 'border-border bg-muted hover:border-primary/50'
                                            }`}
                                            onClick={() => {
                                                const currencies = data.supported_currencies.includes(currency.code)
                                                    ? data.supported_currencies.filter(c => c !== currency.code)
                                                    : [...data.supported_currencies, currency.code];
                                                setData('supported_currencies', currencies);
                                                logDebug(`Currency ${currency.code} ${data.supported_currencies.includes(currency.code) ? 'removed' : 'added'}`);
                                            }}
                                        >
                                            <div className="flex items-center space-x-2">
                                                <span className="text-lg">{currency.icon}</span>
                                                <div>
                                                    <p className="text-sm font-medium text-foreground">{currency.code}</p>
                                                    <p className="text-xs text-muted-foreground">{currency.name}</p>
                                                </div>
                                                {data.supported_currencies.includes(currency.code) && (
                                                    <CheckCircle className="w-4 h-4 text-primary ml-auto" />
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Select which cryptocurrencies customers can use for payments
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Save Button */}
                    <div className="flex justify-end space-x-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => reset()}
                            disabled={processing}
                        >
                            Reset
                        </Button>
                        <Button
                            type="submit"
                            disabled={processing}
                            className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-md transition-colors"
                        >
                            {processing ? (
                                <>
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Settings className="w-4 h-4 mr-2" />
                                    Save Configuration
                                </>
                            )}
                        </Button>
                    </div>
                </form>

                {/* Information Section */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card className="border-l-4 border-l-primary shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-foreground flex items-center">
                                <Shield className="w-5 h-5 mr-2 text-primary" />
                                Onchain Payment Protocol
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                Next-generation crypto payments with automatic USDC settlement
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="text-foreground">
                            <ul className="space-y-3 text-sm">
                                <li className="flex items-start">
                                    <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-primary flex-shrink-0" />
                                    <div>
                                        <strong>Automatic USDC Settlement:</strong> Avoid volatility with guaranteed settlement
                                    </div>
                                </li>
                                <li className="flex items-start">
                                    <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-primary flex-shrink-0" />
                                    <div>
                                        <strong>Hundreds of Currencies:</strong> Accept payments in any supported cryptocurrency
                                    </div>
                                </li>
                                <li className="flex items-start">
                                    <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-primary flex-shrink-0" />
                                    <div>
                                        <strong>Instant Confirmation:</strong> Low-cost transactions on Base & Polygon networks
                                    </div>
                                </li>
                                <li className="flex items-start">
                                    <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-primary flex-shrink-0" />
                                    <div>
                                        <strong>Enterprise Security:</strong> Built on Coinbase's secure infrastructure
                                    </div>
                                </li>
                            </ul>
                        </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-chart-2 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-foreground flex items-center">
                                <Zap className="w-5 h-5 mr-2 text-chart-2" />
                                Setup Guide
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                Quick setup guide for Coinbase Commerce integration
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="text-foreground">
                            <ul className="space-y-3 text-sm">
                                <li className="flex items-start">
                                    <div className="w-5 h-5 rounded-full bg-chart-2/10 text-chart-2 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0">1</div>
                                    Create a Coinbase Commerce account
                                </li>
                                <li className="flex items-start">
                                    <div className="w-5 h-5 rounded-full bg-chart-2/10 text-chart-2 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0">2</div>
                                    Generate an API key in your dashboard
                                </li>
                                <li className="flex items-start">
                                    <div className="w-5 h-5 rounded-full bg-chart-2/10 text-chart-2 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0">3</div>
                                    Configure webhook URL and test connection
                                </li>
                                <li className="flex items-start">
                                    <div className="w-5 h-5 rounded-full bg-chart-2/10 text-chart-2 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0">4</div>
                                    Enable crypto payments and start accepting payments
                                </li>
                            </ul>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
