import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Settings,
    ExternalLink,
    CheckCircle,
    XCircle,
    CreditCard,
    Shield,
    Globe,
    Zap,
    Star,
    TrendingUp
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { paymentGateways, type PaymentGateway } from './data';

interface Props {
    paymentGateways?: PaymentGateway[];
}

export default function Index({ paymentGateways: propGateways }: Props) {
    // Use prop data if available, otherwise use sample data
    const gateways = propGateways || paymentGateways;

    // Separate configured and available gateways
    const configuredGateways = gateways.filter(g => g.status === 'configured' || g.status === 'active');
    const availableGateways = gateways.filter(g => g.status === 'not_configured' || g.status === 'inactive');

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
            case 'configured':
                return (
                    <Badge className="bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 hover:from-green-100 hover:to-emerald-100 dark:from-green-950 dark:to-emerald-950 dark:text-green-300 dark:border-green-800 shadow-sm">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Active
                    </Badge>
                );
            case 'inactive':
            case 'not_configured':
                return (
                    <Badge variant="outline" className="text-muted-foreground border-border bg-muted/30 hover:bg-muted/50 transition-colors">
                        <XCircle className="w-3 h-3 mr-1" />
                        Not Configured
                    </Badge>
                );
            default:
                return <Badge variant="outline" className="bg-gray-50 dark:bg-gray-900">Unknown</Badge>;
        }
    };

    const getGatewayIcon = (gatewayId: string) => {
        const iconClass = "w-12 h-12 rounded-xl flex items-center justify-center font-bold text-sm shadow-lg transition-all duration-200 hover:scale-105";
        switch (gatewayId) {
            case 'paddle':
                return (
                    <div className={`${iconClass} bg-gradient-to-br from-primary to-primary/80 text-primary-foreground ring-2 ring-primary/20`}>
                        P
                    </div>
                );
            case 'shurjopay':
                return (
                    <div className={`${iconClass} bg-gradient-to-br from-green-600 to-green-700 text-white dark:from-green-500 dark:to-green-600 ring-2 ring-green-200 dark:ring-green-800`}>
                        SP
                    </div>
                );
            case 'coinbase':
                return (
                    <div className={`${iconClass} bg-gradient-to-br from-orange-500 to-orange-600 text-white dark:from-orange-400 dark:to-orange-500 relative overflow-hidden ring-2 ring-orange-200 dark:ring-orange-800`}>
                        <span className="relative z-10 font-bold text-lg">₿</span>
                        <div className="absolute inset-0 bg-gradient-to-br from-orange-300/30 to-orange-800/30"></div>
                    </div>
                );
            default:
                return (
                    <div className={`${iconClass} bg-gradient-to-br from-muted to-muted/80 text-muted-foreground ring-2 ring-border`}>
                        <CreditCard className="w-6 h-6" />
                    </div>
                );
        }
    };

    return (
        <AppLayout>
            <Head title="Payment Gateways" />

            <div className="flex h-full flex-1 flex-col space-y-8 p-6">
                {/* Header Section */}
                <div className="space-y-6">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Payment Gateways</h1>
                            <p className="text-muted-foreground max-w-2xl">
                                Manage and configure payment processing integrations to accept payments from customers worldwide
                            </p>
                        </div>
                        <div className="flex items-center gap-3 flex-wrap">
                            <Badge variant="outline" className="text-sm px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200 dark:from-blue-950 dark:to-indigo-950 dark:text-blue-300 dark:border-blue-800">
                                <Shield className="w-4 h-4 mr-2" />
                                Enterprise Security
                            </Badge>
                            <Badge variant="outline" className="text-sm px-4 py-2 bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 dark:from-green-950 dark:to-emerald-950 dark:text-green-300 dark:border-green-800">
                                <Globe className="w-4 h-4 mr-2" />
                                Global Coverage
                            </Badge>
                            <Badge variant="outline" className="text-sm px-4 py-2 bg-gradient-to-r from-purple-50 to-violet-50 text-purple-700 border-purple-200 dark:from-purple-950 dark:to-violet-950 dark:text-purple-300 dark:border-purple-800">
                                <Zap className="w-4 h-4 mr-2" />
                                Real-time Processing
                            </Badge>
                        </div>
                    </div>

                    {/* Enhanced Stats Overview */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card className="border-l-4 border-l-green-500 dark:border-l-green-400 bg-gradient-to-br from-green-50/50 to-emerald-50/30 dark:from-green-950/20 dark:to-emerald-950/10 hover:shadow-lg transition-all duration-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">Active Gateways</p>
                                        <p className="text-3xl font-bold text-foreground mt-2">{configuredGateways.length}</p>
                                        <p className="text-xs text-muted-foreground mt-2 flex items-center">
                                            <CheckCircle className="w-3 h-3 mr-1 text-green-600" />
                                            Ready to process payments
                                        </p>
                                    </div>
                                    <div className="p-4 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/20 rounded-xl shadow-sm">
                                        <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-l-4 border-l-primary bg-gradient-to-br from-blue-50/50 to-indigo-50/30 dark:from-blue-950/20 dark:to-indigo-950/10 hover:shadow-lg transition-all duration-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">Available</p>
                                        <p className="text-3xl font-bold text-foreground mt-2">{availableGateways.length}</p>
                                        <p className="text-xs text-muted-foreground mt-2 flex items-center">
                                            <Zap className="w-3 h-3 mr-1 text-primary" />
                                            Ready to configure
                                        </p>
                                    </div>
                                    <div className="p-4 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl shadow-sm">
                                        <Zap className="w-6 h-6 text-primary" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-l-4 border-l-purple-500 dark:border-l-purple-400 bg-gradient-to-br from-purple-50/50 to-violet-50/30 dark:from-purple-950/20 dark:to-violet-950/10 hover:shadow-lg transition-all duration-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">Total Options</p>
                                        <p className="text-3xl font-bold text-foreground mt-2">{gateways.length}</p>
                                        <p className="text-xs text-muted-foreground mt-2 flex items-center">
                                            <CreditCard className="w-3 h-3 mr-1 text-purple-600" />
                                            Payment solutions
                                        </p>
                                    </div>
                                    <div className="p-4 bg-gradient-to-br from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/20 rounded-xl shadow-sm">
                                        <CreditCard className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-l-4 border-l-orange-500 dark:border-l-orange-400 bg-gradient-to-br from-orange-50/50 to-amber-50/30 dark:from-orange-950/20 dark:to-amber-950/10 hover:shadow-lg transition-all duration-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">Coverage</p>
                                        <p className="text-3xl font-bold text-foreground mt-2">190+</p>
                                        <p className="text-xs text-muted-foreground mt-2 flex items-center">
                                            <Globe className="w-3 h-3 mr-1 text-orange-600" />
                                            Countries supported
                                        </p>
                                    </div>
                                    <div className="p-4 bg-gradient-to-br from-orange-100 to-amber-100 dark:from-orange-900/30 dark:to-amber-900/20 rounded-xl shadow-sm">
                                        <TrendingUp className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Active Gateways Section */}
                {configuredGateways.length > 0 && (
                    <div className="space-y-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <h2 className="text-2xl font-semibold text-foreground">Active Payment Gateways</h2>
                                <Badge className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800">
                                    {configuredGateways.length} Active
                                </Badge>
                            </div>
                        </div>

                        <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                            {configuredGateways.map((gateway) => (
                                <Card
                                    key={gateway.id}
                                    className={`relative hover:shadow-xl transition-all duration-300 group border-0 shadow-lg ${
                                        gateway.id === 'coinbase'
                                            ? 'ring-2 ring-orange-200 dark:ring-orange-800 bg-gradient-to-br from-orange-50/80 to-amber-50/40 dark:from-orange-950/30 dark:to-amber-950/20'
                                            : gateway.id === 'shurjopay'
                                            ? 'ring-2 ring-green-200 dark:ring-green-800 bg-gradient-to-br from-green-50/80 to-emerald-50/40 dark:from-green-950/30 dark:to-emerald-950/20'
                                            : 'ring-2 ring-primary/20 bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/5'
                                    } hover:ring-4 ${
                                        gateway.id === 'coinbase'
                                            ? 'hover:ring-orange-300 dark:hover:ring-orange-700'
                                            : gateway.id === 'shurjopay'
                                            ? 'hover:ring-green-300 dark:hover:ring-green-700'
                                            : 'hover:ring-primary/30'
                                    }`}
                                >
                                    {gateway.id === 'coinbase' && (
                                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                                            <Badge className="bg-gradient-to-r from-orange-600 to-orange-500 text-white border-0 shadow-lg dark:from-orange-500 dark:to-orange-400 px-3 py-1">
                                                <Zap className="w-3 h-3 mr-1" />
                                                Onchain Protocol
                                            </Badge>
                                        </div>
                                    )}
                                    <CardHeader className="pb-4">
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-center space-x-4">
                                                {getGatewayIcon(gateway.id)}
                                                <div className="min-w-0 flex-1">
                                                    <CardTitle className={`text-xl font-bold transition-colors ${
                                                        gateway.id === 'coinbase'
                                                            ? 'text-foreground group-hover:text-orange-600 dark:group-hover:text-orange-400'
                                                            : gateway.id === 'shurjopay'
                                                            ? 'text-foreground group-hover:text-green-600 dark:group-hover:text-green-400'
                                                            : 'text-foreground group-hover:text-primary'
                                                    }`}>
                                                        {gateway.name}
                                                    </CardTitle>
                                                    <CardDescription className="text-muted-foreground mt-2 line-clamp-2 text-sm leading-relaxed">
                                                        {gateway.description}
                                                    </CardDescription>
                                                </div>
                                            </div>
                                            {getStatusBadge(gateway.status)}
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-8 pt-2">
                                        {/* Features */}
                                        <div>
                                            <h4 className="text-sm font-bold text-foreground mb-4 flex items-center">
                                                <Star className="w-4 h-4 mr-2 text-yellow-500 dark:text-yellow-400" />
                                                Key Features
                                            </h4>
                                            <div className="flex flex-wrap gap-2">
                                                {gateway.features.slice(0, 4).map((feature, index) => (
                                                    <Badge
                                                        key={index}
                                                        variant="outline"
                                                        className={`text-xs px-3 py-1 font-medium transition-all hover:scale-105 ${
                                                            gateway.id === 'coinbase'
                                                                ? 'bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200 hover:from-orange-100 hover:to-amber-100 dark:from-orange-950/30 dark:to-amber-950/20 dark:text-orange-300 dark:border-orange-800'
                                                                : gateway.id === 'shurjopay'
                                                                ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 hover:from-green-100 hover:to-emerald-100 dark:from-green-950/30 dark:to-emerald-950/20 dark:text-green-300 dark:border-green-800'
                                                                : 'bg-gradient-to-r from-primary/10 to-primary/5 text-primary border-primary/20 hover:from-primary/20 hover:to-primary/10'
                                                        }`}
                                                    >
                                                        {feature}
                                                    </Badge>
                                                ))}
                                                {gateway.features.length > 4 && (
                                                    <Badge variant="outline" className="text-xs bg-gradient-to-r from-muted to-muted/80 text-muted-foreground border-border px-3 py-1">
                                                        +{gateway.features.length - 4} more
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>

                                        {/* Supported Currencies */}
                                        <div>
                                            <h4 className="text-sm font-bold text-foreground mb-4 flex items-center">
                                                <Globe className="w-4 h-4 mr-2 text-green-500 dark:text-green-400" />
                                                {gateway.id === 'coinbase' ? 'Cryptocurrencies' : 'Currencies'}
                                                <span className="ml-2 text-xs bg-muted text-muted-foreground px-2 py-1 rounded-full">
                                                    {gateway.supported_currencies.length}
                                                </span>
                                            </h4>
                                            <div className="flex flex-wrap gap-2">
                                                {gateway.supported_currencies.slice(0, 6).map((currency, index) => (
                                                    <Badge
                                                        key={index}
                                                        className={`text-xs px-3 py-1 font-medium transition-all hover:scale-105 ${
                                                            gateway.id === 'coinbase'
                                                                ? 'bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 border-orange-300 hover:from-orange-200 hover:to-amber-200 dark:from-orange-900/40 dark:to-amber-900/30 dark:text-orange-200 dark:border-orange-700'
                                                                : 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-300 hover:from-green-200 hover:to-emerald-200 dark:from-green-900/40 dark:to-emerald-900/30 dark:text-green-200 dark:border-green-700'
                                                        }`}
                                                    >
                                                        {currency}
                                                    </Badge>
                                                ))}
                                                {gateway.supported_currencies.length > 6 && (
                                                    <Badge className="text-xs bg-gradient-to-r from-muted to-muted/80 text-muted-foreground border-border px-3 py-1">
                                                        +{gateway.supported_currencies.length - 6} more
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>

                                        {/* Actions */}
                                        <div className="flex items-center gap-3 pt-6 border-t border-border/50">
                                            {gateway.id === 'paddle' ? (
                                                <Link href={route('admin.payment-gateways.paddle.configure')} className="flex-1">
                                                    <Button className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 text-white shadow-lg hover:shadow-xl transition-all duration-200" size="sm">
                                                        <Settings className="w-4 h-4 mr-2" />
                                                        Configure Gateway
                                                    </Button>
                                                </Link>
                                            ) : gateway.id === 'shurjopay' ? (
                                                <Link href={route('admin.payment-gateways.shurjopay.configure')} className="flex-1">
                                                    <Button className="w-full bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-200" size="sm">
                                                        <Settings className="w-4 h-4 mr-2" />
                                                        Configure Gateway
                                                    </Button>
                                                </Link>
                                            ) : gateway.id === 'coinbase' ? (
                                                <Link href={route('admin.payment-gateways.coinbase.configure')} className="flex-1">
                                                    <Button className="w-full bg-gradient-to-r from-orange-600 to-orange-500 hover:from-orange-700 hover:to-orange-600 text-white shadow-lg hover:shadow-xl transition-all duration-200" size="sm">
                                                        <Settings className="w-4 h-4 mr-2" />
                                                        Configure Crypto Gateway
                                                    </Button>
                                                </Link>
                                            ) : (
                                                <Button className="flex-1 bg-gradient-to-r from-muted to-muted/80 text-muted-foreground" variant="outline" size="sm" disabled>
                                                    <Settings className="w-4 h-4 mr-2" />
                                                    Coming Soon
                                                </Button>
                                            )}
                                            {gateway.documentation_url && (
                                                <Link href={gateway.documentation_url} target="_blank">
                                                    <Button variant="outline" size="sm" title="View Documentation" className="border-2 hover:bg-accent/50 transition-all duration-200">
                                                        <ExternalLink className="w-4 h-4" />
                                                    </Button>
                                                </Link>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                )}

                {/* Available Gateways Section */}
                {availableGateways.length > 0 && (
                    <div className="space-y-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <h2 className="text-2xl font-semibold text-foreground">Available Payment Gateways</h2>
                                <Badge variant="outline" className="text-muted-foreground border-border">
                                    {availableGateways.length} Available
                                </Badge>
                            </div>
                        </div>

                        <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                            {availableGateways.map((gateway) => (
                                <Card key={gateway.id} className="relative border-2 border-dashed border-border hover:border-primary/50 transition-all duration-300 bg-muted/30 hover:bg-background group">
                                    <CardHeader>
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-center space-x-4">
                                                {getGatewayIcon(gateway.id)}
                                                <div className="min-w-0 flex-1">
                                                    <CardTitle className="text-lg font-semibold text-muted-foreground group-hover:text-primary transition-colors">
                                                        {gateway.name}
                                                    </CardTitle>
                                                    <CardDescription className="text-muted-foreground/80 mt-1 line-clamp-2">
                                                        {gateway.description}
                                                    </CardDescription>
                                                </div>
                                            </div>
                                            {getStatusBadge(gateway.status)}
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        {/* Features Preview */}
                                        <div>
                                            <h4 className="text-sm font-medium text-muted-foreground mb-2">Key Features</h4>
                                            <div className="flex flex-wrap gap-2">
                                                {gateway.features.slice(0, 3).map((feature, index) => (
                                                    <Badge key={index} variant="outline" className="text-xs text-muted-foreground border-border">
                                                        {feature}
                                                    </Badge>
                                                ))}
                                                {gateway.features.length > 3 && (
                                                    <Badge variant="outline" className="text-xs text-muted-foreground/60">
                                                        +{gateway.features.length - 3} more
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>

                                        {/* Actions */}
                                        <div className="flex items-center gap-2 pt-4">
                                            <Button className="flex-1" variant="outline" size="sm" disabled>
                                                <Settings className="w-4 h-4 mr-2" />
                                                Coming Soon
                                            </Button>
                                            {gateway.documentation_url && (
                                                <Link href={gateway.documentation_url} target="_blank">
                                                    <Button variant="outline" size="sm" title="View Documentation">
                                                        <ExternalLink className="w-4 h-4" />
                                                    </Button>
                                                </Link>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                )}

                {/* Information Section */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card className="border-l-4 border-l-primary">
                        <CardHeader>
                            <CardTitle className="text-foreground flex items-center">
                                <Shield className="w-5 h-5 mr-2 text-primary" />
                                Security & Compliance
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                Enterprise-grade security for all payment processing
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="text-foreground">
                            <ul className="space-y-3 text-sm">
                                <li className="flex items-start">
                                    <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0" />
                                    PCI DSS Level 1 compliant payment processing
                                </li>
                                <li className="flex items-start">
                                    <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0" />
                                    End-to-end encryption for all transactions
                                </li>
                                <li className="flex items-start">
                                    <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0" />
                                    Advanced fraud detection and prevention
                                </li>
                                <li className="flex items-start">
                                    <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0" />
                                    Real-time transaction monitoring
                                </li>
                            </ul>
                        </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-green-500 dark:border-l-green-400">
                        <CardHeader>
                            <CardTitle className="text-foreground flex items-center">
                                <Zap className="w-5 h-5 mr-2 text-green-600 dark:text-green-400" />
                                Integration Guide
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                Quick setup guide for payment gateway configuration
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="text-foreground">
                            <ul className="space-y-3 text-sm">
                                <li className="flex items-start">
                                    <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0">1</div>
                                    Choose your preferred payment gateway
                                </li>
                                <li className="flex items-start">
                                    <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0">2</div>
                                    Obtain API credentials from the provider
                                </li>
                                <li className="flex items-start">
                                    <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0">3</div>
                                    Configure gateway settings and test in sandbox
                                </li>
                                <li className="flex items-start">
                                    <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 text-xs flex items-center justify-center mr-2 mt-0.5 font-semibold flex-shrink-0">4</div>
                                    Enable live mode and start accepting payments
                                </li>
                            </ul>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
