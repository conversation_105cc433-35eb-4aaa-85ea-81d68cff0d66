import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import ToastDemo from '@/components/toast-demo';

export default function ToastDemoPage() {
    return (
        <AppLayout>
            <Head title="Toast Demo - Admin" />
            
            <div className="space-y-6 p-4">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Toast Notifications Demo</h1>
                    <p className="text-muted-foreground">
                        Test the Sonner toast implementation with center-positioned delete confirmations
                    </p>
                </div>

                <ToastDemo />
            </div>
        </AppLayout>
    );
}
