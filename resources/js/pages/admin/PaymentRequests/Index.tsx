import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    DollarSign, 
    CreditCard, 
    Clock, 
    CheckCircle,
    XCircle,
    Search,
    Filter,
    MoreHorizontal,
    Eye,
    Check,
    X,
    FileText,
    User,
    Calendar
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface PaymentRequest {
    id: number;
    amount: number;
    currency: string;
    payment_method: string;
    status: 'pending' | 'approved' | 'rejected' | 'processed';
    subscription_plan: 'free' | 'premium';
    notes: string | null;
    proof_of_payment: string | null;
    requested_at: string;
    approved_at: string | null;
    admin_notes: string | null;
    user: {
        id: number;
        name: string;
        email: string;
        subscription_plan: string;
    };
    approved_by?: {
        id: number;
        name: string;
    };
}

interface Stats {
    total_requests: number;
    pending_requests: number;
    approved_requests: number;
    rejected_requests: number;
    total_amount_pending: number;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface Props {
    paymentRequests: {
        data: PaymentRequest[];
        links: PaginationLink[];
        meta: PaginationMeta;
    };
    stats: Stats;
    filters: {
        search?: string;
        status?: string;
        payment_method?: string;
        subscription_plan?: string;
        date_from?: string;
        date_to?: string;
        sort_by?: string;
        sort_order?: string;
    };
}

const StatusBadge = ({ status }: { status: PaymentRequest['status'] }) => {
    const variants = {
        pending: 'bg-yellow-100 text-yellow-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800',
        processed: 'bg-blue-100 text-blue-800',
    };

    const icons = {
        pending: Clock,
        approved: CheckCircle,
        rejected: XCircle,
        processed: CreditCard,
    };

    const Icon = icons[status];

    return (
        <Badge className={`${variants[status]} flex items-center gap-1`}>
            <Icon className="h-3 w-3" />
            {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
    );
};

const PaymentMethodBadge = ({ method }: { method: string }) => {
    const variants: Record<string, string> = {
        bank_transfer: 'bg-blue-100 text-blue-800',
        cash: 'bg-green-100 text-green-800',
        check: 'bg-purple-100 text-purple-800',
        paypal: 'bg-indigo-100 text-indigo-800',
        stripe: 'bg-gray-100 text-gray-800',
    };

    return (
        <Badge className={variants[method] || 'bg-gray-100 text-gray-800'}>
            {method.replace('_', ' ').toUpperCase()}
        </Badge>
    );
};

export default function PaymentRequestsIndex({ paymentRequests, stats, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || 'all');
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(filters.payment_method || 'all');
    const [selectedSubscriptionPlan, setSelectedSubscriptionPlan] = useState(filters.subscription_plan || 'all');

    const handleSearch = () => {
        router.get('/admin/payment-requests', {
            ...filters,
            search: searchTerm,
            status: selectedStatus === 'all' ? '' : selectedStatus,
            payment_method: selectedPaymentMethod === 'all' ? '' : selectedPaymentMethod,
            subscription_plan: selectedSubscriptionPlan === 'all' ? '' : selectedSubscriptionPlan,
        });
    };

    const handleApprove = (paymentRequestId: number) => {
        const adminNotes = prompt('Add admin notes (optional):');
        router.post(`/admin/payment-requests/${paymentRequestId}/approve`, {
            admin_notes: adminNotes || null,
        }, {
            preserveScroll: true,
        });
    };

    const handleReject = (paymentRequestId: number) => {
        const adminNotes = prompt('Please provide a reason for rejection:');
        if (adminNotes) {
            router.post(`/admin/payment-requests/${paymentRequestId}/reject`, {
                admin_notes: adminNotes,
            }, {
                preserveScroll: true,
            });
        }
    };

    const handleBulkApprove = () => {
        const selectedIds = Array.from(document.querySelectorAll('input[name="payment_request_ids"]:checked'))
            .map((input) => (input as HTMLInputElement).value);
        
        if (selectedIds.length === 0) {
            alert('Please select payment requests to approve.');
            return;
        }

        const adminNotes = prompt('Add admin notes for bulk approval (optional):');
        router.post('/admin/payment-requests/bulk-approve', {
            payment_request_ids: selectedIds,
            admin_notes: adminNotes || null,
        }, {
            preserveScroll: true,
        });
    };

    const formatCurrency = (amount: number, currency: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
        }).format(amount);
    };

    return (
        <AppLayout>
            <Head title="Payment Requests" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Payment Requests</h1>
                            <p className="text-muted-foreground mt-2">
                                Manage offline payment requests and subscription activations
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button onClick={handleBulkApprove} variant="default">
                                <Check className="h-4 w-4 mr-2" />
                                Bulk Approve
                            </Button>
                        </div>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
                                <FileText className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.total_requests || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Pending</CardTitle>
                                <Clock className="h-4 w-4 text-yellow-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.pending_requests || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Approved</CardTitle>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.approved_requests || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Rejected</CardTitle>
                                <XCircle className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.rejected_requests || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Pending Amount</CardTitle>
                                <DollarSign className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">
                                    {formatCurrency(stats?.total_amount_pending || 0, 'USD')}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filters */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filters
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-5">
                                <div className="flex gap-2">
                                    <Input
                                        placeholder="Search by user..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="flex-1"
                                    />
                                    <Button onClick={handleSearch}>
                                        <Search className="h-4 w-4" />
                                    </Button>
                                </div>

                                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Statuses</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                        <SelectItem value="approved">Approved</SelectItem>
                                        <SelectItem value="rejected">Rejected</SelectItem>
                                        <SelectItem value="processed">Processed</SelectItem>
                                    </SelectContent>
                                </Select>

                                <Select value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Payment Method" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Methods</SelectItem>
                                        <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                                        <SelectItem value="cash">Cash</SelectItem>
                                        <SelectItem value="check">Check</SelectItem>
                                        <SelectItem value="paypal">PayPal</SelectItem>
                                    </SelectContent>
                                </Select>

                                <Select value={selectedSubscriptionPlan} onValueChange={setSelectedSubscriptionPlan}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Plan" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Plans</SelectItem>
                                        <SelectItem value="free">Free</SelectItem>
                                        <SelectItem value="premium">Premium</SelectItem>
                                    </SelectContent>
                                </Select>

                                <Button onClick={handleSearch} className="w-full">
                                    Apply Filters
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Payment Requests Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Payment Requests</CardTitle>
                            <CardDescription>
                                {paymentRequests.meta?.total || paymentRequests.data?.length || 0} payment requests found
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {(paymentRequests.data || []).map((request) => (
                                    <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
                                        <div className="flex items-center gap-4">
                                            <input
                                                type="checkbox"
                                                name="payment_request_ids"
                                                value={request.id}
                                                className="rounded border-gray-300"
                                                disabled={request.status !== 'pending'}
                                            />
                                            <div className="space-y-1">
                                                <div className="flex items-center gap-2">
                                                    <p className="text-sm font-medium leading-none text-foreground">
                                                        {formatCurrency(request.amount, request.currency)}
                                                    </p>
                                                    <StatusBadge status={request.status} />
                                                    <PaymentMethodBadge method={request.payment_method} />
                                                    {request.subscription_plan === 'premium' && (
                                                        <Badge className="bg-purple-100 text-purple-800">Premium</Badge>
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                                    <User className="h-3 w-3" />
                                                    <span>{request.user.name} ({request.user.email})</span>
                                                </div>
                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="h-3 w-3" />
                                                        <span>Requested: {new Date(request.requested_at).toLocaleDateString()}</span>
                                                    </div>
                                                    {request.approved_at && (
                                                        <div className="flex items-center gap-1">
                                                            <CheckCircle className="h-3 w-3" />
                                                            <span>Approved: {new Date(request.approved_at).toLocaleDateString()}</span>
                                                        </div>
                                                    )}
                                                    {request.approved_by && (
                                                        <span>by {request.approved_by.name}</span>
                                                    )}
                                                </div>
                                                {request.notes && (
                                                    <p className="text-xs text-muted-foreground">
                                                        Note: {request.notes}
                                                    </p>
                                                )}
                                                {request.admin_notes && (
                                                    <p className="text-xs text-blue-600">
                                                        Admin: {request.admin_notes}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Link href={`/admin/payment-requests/${request.id}`}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </Link>

                                            {request.status === 'pending' && (
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="outline" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem onClick={() => handleApprove(request.id)}>
                                                            <Check className="h-4 w-4 mr-2" />
                                                            Approve Payment
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => handleReject(request.id)}>
                                                            <X className="h-4 w-4 mr-2" />
                                                            Reject Payment
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            )}
                                        </div>
                                    </div>
                                ))}

                                {(!paymentRequests.data || paymentRequests.data.length === 0) && (
                                    <div className="text-center py-12">
                                        <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-foreground mb-2">No payment requests found</h3>
                                        <p className="text-muted-foreground">
                                            No payment requests match your current filters.
                                        </p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
