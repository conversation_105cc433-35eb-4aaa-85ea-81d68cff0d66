import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
    ArrowLeft,
    DollarSign,
    User,
    Calendar,
    FileText,
    CheckCircle,
    XCircle,
    Clock,
    CreditCard,
    Download,
    Check,
    X
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface PaymentRequest {
    id: number;
    amount: number;
    currency: string;
    payment_method: string;
    status: 'pending' | 'approved' | 'rejected' | 'processed';
    subscription_plan: 'free' | 'premium';
    notes: string | null;
    proof_of_payment: string | null;
    requested_at: string;
    approved_at: string | null;
    admin_notes: string | null;
    user: {
        id: number;
        name: string;
        email: string;
        subscription_plan: string;
        status: string;
    };
    approved_by?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Props {
    paymentRequest: PaymentRequest;
}

const StatusBadge = ({ status }: { status: PaymentRequest['status'] }) => {
    const variants = {
        pending: 'bg-yellow-100 text-yellow-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800',
        processed: 'bg-blue-100 text-blue-800',
    };

    const icons = {
        pending: Clock,
        approved: CheckCircle,
        rejected: XCircle,
        processed: CreditCard,
    };

    const Icon = icons[status];

    return (
        <Badge className={`${variants[status]} flex items-center gap-1`}>
            <Icon className="h-4 w-4" />
            {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
    );
};

export default function PaymentRequestShow({ paymentRequest }: Props) {
    const [adminNotes, setAdminNotes] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);

    const handleApprove = () => {
        setIsProcessing(true);
        router.post(`/admin/payment-requests/${paymentRequest.id}/approve`, {
            admin_notes: adminNotes || null,
        }, {
            onFinish: () => setIsProcessing(false),
        });
    };

    const handleReject = () => {
        if (!adminNotes.trim()) {
            alert('Please provide admin notes for rejection.');
            return;
        }

        setIsProcessing(true);
        router.post(`/admin/payment-requests/${paymentRequest.id}/reject`, {
            admin_notes: adminNotes,
        }, {
            onFinish: () => setIsProcessing(false),
        });
    };

    const formatCurrency = (amount: number, currency: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
        }).format(amount);
    };

    const downloadProofOfPayment = () => {
        if (paymentRequest.proof_of_payment) {
            window.open(paymentRequest.proof_of_payment, '_blank');
        }
    };

    return (
        <AppLayout>
            <Head title={`Payment Request #${paymentRequest.id}`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href="/admin/payment-requests">
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Payment Requests
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground">
                                    Payment Request #{paymentRequest.id}
                                </h1>
                                <p className="text-muted-foreground mt-1">
                                    {formatCurrency(paymentRequest.amount, paymentRequest.currency)} • {paymentRequest.payment_method.replace('_', ' ')}
                                </p>
                            </div>
                        </div>
                        <StatusBadge status={paymentRequest.status} />
                    </div>

                    {/* Main Content */}
                    <div className="grid gap-6 lg:grid-cols-3">
                        {/* Payment Details */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Payment Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <DollarSign className="h-5 w-5" />
                                        Payment Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div>
                                            <Label className="text-sm font-medium text-muted-foreground">Amount</Label>
                                            <p className="text-2xl font-bold text-foreground">
                                                {formatCurrency(paymentRequest.amount, paymentRequest.currency)}
                                            </p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-muted-foreground">Payment Method</Label>
                                            <p className="text-lg font-medium text-foreground capitalize">
                                                {paymentRequest.payment_method.replace('_', ' ')}
                                            </p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-muted-foreground">Subscription Plan</Label>
                                            <p className="text-lg font-medium text-foreground capitalize">
                                                {paymentRequest.subscription_plan}
                                            </p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                                            <div className="mt-1">
                                                <StatusBadge status={paymentRequest.status} />
                                            </div>
                                        </div>
                                    </div>

                                    {paymentRequest.notes && (
                                        <div>
                                            <Label className="text-sm font-medium text-muted-foreground">User Notes</Label>
                                            <p className="text-sm text-foreground mt-1 p-3 bg-gray-50 rounded-lg">
                                                {paymentRequest.notes}
                                            </p>
                                        </div>
                                    )}

                                    {paymentRequest.proof_of_payment && (
                                        <div>
                                            <Label className="text-sm font-medium text-muted-foreground">Proof of Payment</Label>
                                            <div className="mt-2">
                                                <Button onClick={downloadProofOfPayment} variant="outline">
                                                    <Download className="h-4 w-4 mr-2" />
                                                    View Proof of Payment
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Timeline */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Calendar className="h-5 w-5" />
                                        Timeline
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex items-start gap-3">
                                            <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">Payment Request Created</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {new Date(paymentRequest.requested_at).toLocaleString()}
                                                </p>
                                            </div>
                                        </div>

                                        {paymentRequest.approved_at && (
                                            <div className="flex items-start gap-3">
                                                <div className={`w-2 h-2 rounded-full mt-2 ${
                                                    paymentRequest.status === 'approved' ? 'bg-green-600' : 'bg-red-600'
                                                }`}></div>
                                                <div>
                                                    <p className="text-sm font-medium text-foreground">
                                                        Payment {paymentRequest.status === 'approved' ? 'Approved' : 'Rejected'}
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {new Date(paymentRequest.approved_at).toLocaleString()}
                                                        {paymentRequest.approved_by && ` by ${paymentRequest.approved_by.name}`}
                                                    </p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {paymentRequest.admin_notes && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <FileText className="h-5 w-5" />
                                            Admin Notes
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-sm text-foreground p-3 bg-blue-50 rounded-lg">
                                            {paymentRequest.admin_notes}
                                        </p>
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* User Information & Actions */}
                        <div className="lg:col-span-1 space-y-6">
                            {/* User Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        User Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Name</Label>
                                        <p className="text-sm font-medium text-foreground">{paymentRequest.user.name}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                                        <p className="text-sm text-foreground">{paymentRequest.user.email}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Current Plan</Label>
                                        <p className="text-sm font-medium text-foreground capitalize">
                                            {paymentRequest.user.subscription_plan}
                                        </p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Account Status</Label>
                                        <Badge className={
                                            paymentRequest.user.status === 'active' ? 'bg-green-100 text-green-800' :
                                            paymentRequest.user.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-red-100 text-red-800'
                                        }>
                                            {paymentRequest.user.status}
                                        </Badge>
                                    </div>
                                    <div className="pt-2">
                                        <Link href={`/admin/users/${paymentRequest.user.id}`}>
                                            <Button variant="outline" size="sm" className="w-full">
                                                View User Profile
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Actions */}
                            {paymentRequest.status === 'pending' && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Actions</CardTitle>
                                        <CardDescription>
                                            Review and process this payment request
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <Label htmlFor="admin_notes">Admin Notes</Label>
                                            <Textarea
                                                id="admin_notes"
                                                placeholder="Add notes about this payment request..."
                                                value={adminNotes}
                                                onChange={(e) => setAdminNotes(e.target.value)}
                                                rows={3}
                                            />
                                        </div>
                                        <div className="flex flex-col gap-2">
                                            <Button
                                                onClick={handleApprove}
                                                disabled={isProcessing}
                                                className="w-full"
                                            >
                                                <Check className="h-4 w-4 mr-2" />
                                                {isProcessing ? 'Processing...' : 'Approve Payment'}
                                            </Button>
                                            <Button
                                                onClick={handleReject}
                                                variant="destructive"
                                                disabled={isProcessing}
                                                className="w-full"
                                            >
                                                <X className="h-4 w-4 mr-2" />
                                                {isProcessing ? 'Processing...' : 'Reject Payment'}
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Approval Information */}
                            {paymentRequest.approved_by && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Approval Information</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div>
                                            <Label className="text-sm font-medium text-muted-foreground">Approved By</Label>
                                            <p className="text-sm font-medium text-foreground">
                                                {paymentRequest.approved_by.name}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {paymentRequest.approved_by.email}
                                            </p>
                                        </div>
                                        {paymentRequest.approved_at && (
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Date</Label>
                                                <p className="text-sm text-foreground">
                                                    {new Date(paymentRequest.approved_at).toLocaleString()}
                                                </p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
