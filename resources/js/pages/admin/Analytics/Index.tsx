import { Head, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
    Users, 
    Search, 
    Activity, 
    DollarSign,
    Download,
    BarChart3,
    Calendar,
    Clock
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface AnalyticsData {
    overview: {
        total_users: number;
        active_users: number;
        new_users: number;
        user_retention_rate: number;
        total_sessions: number;
        avg_sessions_per_user: number;
    };
    daily_activity: {
        daily_logins: Array<{
            date: string;
            unique_users: number;
            total_logins: number;
        }>;
        daily_searches: Array<{
            date: string;
            unique_users: number;
            total_searches: number;
        }>;
    };
    feature_usage: {
        feature_usage: Array<{
            activity_type: string;
            usage_count: number;
            unique_users: number;
        }>;
        search_types: Array<{
            search_type: string;
            usage_count: number;
        }>;
    };
    user_segments: {
        subscription_segments: Array<{
            subscription_plan: string;
            user_count: number;
            active_users: number;
        }>;
        activity_segments: Array<{
            activity_level: string;
            user_count: number;
            avg_logins: number;
        }>;
    };
    search_analytics: {
        total_searches: number;
        avg_searches_per_user: number;
        popular_searches: Array<{
            search_query: string;
            search_count: number;
        }>;
        search_success_rate: number;
    };
    subscription_analytics: {
        total_revenue: number;
        subscription_conversions: number;
        pending_payments: number;
        revenue_by_plan: Array<{
            subscription_plan: string;
            payment_count: number;
            total_revenue: number;
        }>;
    };
}

interface Props {
    analytics: AnalyticsData;
    days: number;
}

export default function AnalyticsIndex({ analytics, days }: Props) {
    const [selectedDays, setSelectedDays] = useState(days.toString());

    const handleDaysChange = (value: string) => {
        setSelectedDays(value);
        router.get(route('admin.analytics.index'), { days: value });
    };

    const handleExport = (type: string) => {
        window.open(`${route('admin.analytics.export')}?type=${type}&days=${selectedDays}`, '_blank');
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const formatPercentage = (value: number) => {
        return `${value}%`;
    };

    return (
        <AppLayout>
            <Head title="Analytics Dashboard" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Analytics Dashboard</h1>
                            <p className="text-muted-foreground mt-2">
                                Comprehensive insights into user engagement and system performance
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Select value={selectedDays} onValueChange={handleDaysChange}>
                                <SelectTrigger className="w-32">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="7">Last 7 days</SelectItem>
                                    <SelectItem value="30">Last 30 days</SelectItem>
                                    <SelectItem value="90">Last 90 days</SelectItem>
                                    <SelectItem value="365">Last year</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button onClick={() => handleExport('engagement')} variant="outline">
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                        </div>
                    </div>

                    {/* Overview Stats */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                                <Users className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{analytics.overview.total_users}</div>
                                <p className="text-xs text-muted-foreground">
                                    {analytics.overview.new_users} new in last {days} days
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                                <Activity className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{analytics.overview.active_users}</div>
                                <p className="text-xs text-muted-foreground">
                                    {formatPercentage(analytics.overview.user_retention_rate)} retention rate
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
                                <Clock className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{analytics.overview.total_sessions}</div>
                                <p className="text-xs text-muted-foreground">
                                    {analytics.overview.avg_sessions_per_user} avg per user
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Searches</CardTitle>
                                <Search className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{analytics.search_analytics.total_searches}</div>
                                <p className="text-xs text-muted-foreground">
                                    {formatPercentage(analytics.search_analytics.search_success_rate)} success rate
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                                <DollarSign className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {formatCurrency(analytics.subscription_analytics.total_revenue)}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    {analytics.subscription_analytics.subscription_conversions} conversions
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
                                <Calendar className="h-4 w-4 text-yellow-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {analytics.subscription_analytics.pending_payments}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Awaiting approval
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Detailed Analytics Tabs */}
                    <Tabs defaultValue="features" className="space-y-4">
                        <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="features">Feature Usage</TabsTrigger>
                            <TabsTrigger value="users">User Segments</TabsTrigger>
                            <TabsTrigger value="searches">Search Analytics</TabsTrigger>
                            <TabsTrigger value="revenue">Revenue Analytics</TabsTrigger>
                        </TabsList>

                        <TabsContent value="features" className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <BarChart3 className="h-5 w-5" />
                                            Feature Usage
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {analytics.feature_usage.feature_usage.slice(0, 8).map((feature, index) => (
                                                <div key={index} className="flex items-center justify-between">
                                                    <span className="text-sm font-medium">
                                                        {feature.activity_type.replace('_', ' ').toUpperCase()}
                                                    </span>
                                                    <div className="text-right">
                                                        <div className="text-sm font-bold">{feature.usage_count}</div>
                                                        <div className="text-xs text-muted-foreground">
                                                            {feature.unique_users} users
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Search className="h-5 w-5" />
                                            Search Types
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {analytics.feature_usage.search_types.slice(0, 6).map((searchType, index) => (
                                                <div key={index} className="flex items-center justify-between">
                                                    <span className="text-sm font-medium">
                                                        {searchType.search_type || 'All'}
                                                    </span>
                                                    <span className="text-sm font-bold">{searchType.usage_count}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="users" className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Subscription Segments</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {analytics.user_segments.subscription_segments.map((segment, index) => (
                                                <div key={index} className="flex items-center justify-between">
                                                    <span className="text-sm font-medium capitalize">
                                                        {segment.subscription_plan}
                                                    </span>
                                                    <div className="text-right">
                                                        <div className="text-sm font-bold">{segment.user_count}</div>
                                                        <div className="text-xs text-muted-foreground">
                                                            {segment.active_users} active
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle>Activity Segments</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {analytics.user_segments.activity_segments.map((segment, index) => (
                                                <div key={index} className="flex items-center justify-between">
                                                    <span className="text-sm font-medium">
                                                        {segment.activity_level.replace('_', ' ').toUpperCase()}
                                                    </span>
                                                    <div className="text-right">
                                                        <div className="text-sm font-bold">{segment.user_count}</div>
                                                        <div className="text-xs text-muted-foreground">
                                                            {Math.round(segment.avg_logins)} avg logins
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="searches" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Popular Search Terms</CardTitle>
                                    <CardDescription>
                                        Most frequently searched terms in the last {days} days
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {analytics.search_analytics.popular_searches.slice(0, 10).map((search, index) => (
                                            <div key={index} className="flex items-center justify-between">
                                                <span className="text-sm font-medium">
                                                    {search.search_query || 'Empty search'}
                                                </span>
                                                <span className="text-sm font-bold">{search.search_count}</span>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="revenue" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Revenue by Plan</CardTitle>
                                    <CardDescription>
                                        Revenue breakdown by subscription plan
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {analytics.subscription_analytics.revenue_by_plan.map((plan, index) => (
                                            <div key={index} className="flex items-center justify-between">
                                                <span className="text-sm font-medium capitalize">
                                                    {plan.subscription_plan}
                                                </span>
                                                <div className="text-right">
                                                    <div className="text-sm font-bold">
                                                        {formatCurrency(plan.total_revenue)}
                                                    </div>
                                                    <div className="text-xs text-muted-foreground">
                                                        {plan.payment_count} payments
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
