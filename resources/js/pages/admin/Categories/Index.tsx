import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Plus,
    Edit,
    Trash2,
    Eye,
    Package,
    ExternalLink,
    ChevronLeft,
    ChevronRight,
    Search,
    Filter,
    X,
    ArrowUpDown,
    ArrowUp,
    ArrowDown,
    Grid,
    List,
    Table,
    Download,
    Upload,
    FileText
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';
import { useState, useRef } from 'react';

interface Category {
    id: number;
    name: string;
    slug?: string;
    description: string | null;
    parent_id: number | null;
    sort_order: number;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    parent?: Category;
    children?: Category[];
    parts_count?: number;
}

interface PaginatedCategories {
    data: Category[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface FilterOptions {
    parentCategories: { id: number; name: string }[];
}

interface QueryParams {
    search?: string;
    parent_id?: string;
    status?: string;
    sort_by?: string;
    sort_order?: string;
    view?: string;
}

interface Props {
    categories: PaginatedCategories;
    filters: FilterOptions;
    queryParams: QueryParams;
}

export default function Index({ categories, filters, queryParams }: Props) {
    const { showDeleteConfirmation } = useDeleteConfirmation();

    // Search and filter state
    const [searchTerm, setSearchTerm] = useState(queryParams.search || '');
    const [selectedParent, setSelectedParent] = useState(queryParams.parent_id || 'all');
    const [selectedStatus, setSelectedStatus] = useState(queryParams.status || 'all');
    const [sortBy, setSortBy] = useState(queryParams.sort_by || 'sort_order');
    const [sortOrder, setSortOrder] = useState(queryParams.sort_order || 'asc');
    const [showFilters, setShowFilters] = useState(false);

    // View mode state
    const [viewMode, setViewMode] = useState<'list' | 'grid' | 'table'>(
        (queryParams.view as 'list' | 'grid' | 'table') || 'table'
    );

    // Export/Import state
    const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
    const [isImporting, setIsImporting] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handlePageChange = (page: number) => {
        const params = {
            page,
            ...(searchTerm && { search: searchTerm }),
            ...(selectedParent !== 'all' && { parent_id: selectedParent }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'sort_order' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/categories', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSearch = () => {
        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedParent !== 'all' && { parent_id: selectedParent }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'sort_order' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/categories', params, {
            preserveState: true,
            preserveScroll: false,
        });
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setSelectedParent('all');
        setSelectedStatus('all');
        setSortBy('sort_order');
        setSortOrder('asc');
        setViewMode('table');

        router.get('/admin/categories', {}, {
            preserveState: true,
            preserveScroll: false,
        });
    };

    const handleSort = (field: string) => {
        const newSortOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortOrder(newSortOrder);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedParent !== 'all' && { parent_id: selectedParent }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            sort_by: field,
            sort_order: newSortOrder,
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/categories', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getSortIcon = (field: string) => {
        if (sortBy !== field) return <ArrowUpDown className="h-4 w-4" />;
        return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
    };

    const handleViewModeChange = (newViewMode: 'list' | 'grid' | 'table') => {
        setViewMode(newViewMode);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedParent !== 'all' && { parent_id: selectedParent }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'sort_order' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(newViewMode !== 'table' && { view: newViewMode }),
        };

        router.get('/admin/categories', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDelete = (category: Category) => {
        const description = category.parts_count && category.parts_count > 0
            ? `This category has ${category.parts_count} part(s). This action cannot be undone.`
            : 'This action cannot be undone.';

        showDeleteConfirmation({
            title: `Delete "${category.name}"?`,
            description: description,
            onConfirm: () => {
                router.delete(`/admin/categories/${category.id}`, {
                    onSuccess: () => {
                        toast.success(`Category "${category.name}" has been deleted successfully.`);
                    },
                    onError: (errors) => {
                        const errorMessage = errors.message || 'Failed to delete category. It may have associated parts.';
                        toast.error(errorMessage);
                    }
                });
            },
            onCancel: () => {
                toast.info('Delete cancelled');
            }
        });
    };

    // Export/Import handlers
    const handleExportAll = () => {
        console.log('📥 EXPORT ALL BUTTON CLICKED');
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (selectedParent !== 'all') params.append('parent_id', selectedParent);
        if (selectedStatus !== 'all') params.append('status', selectedStatus);
        if (sortBy !== 'sort_order') params.append('sort_by', sortBy);
        if (sortOrder !== 'asc') params.append('sort_order', sortOrder);

        window.location.href = `/admin/categories/export?${params.toString()}`;
        toast.success('Export started. Your download will begin shortly.');
    };

    const handleDownloadTemplate = () => {
        console.log('📄 TEMPLATE DOWNLOAD BUTTON CLICKED');
        window.location.href = '/admin/categories/template/download';
        toast.success('Template download started.');
    };

    const handleImport = () => {
        console.log('🔵 handleImport called');
        console.log('Testing router object:', router);
        console.log('Router methods:', Object.keys(router));
        console.log('fileInputRef.current:', fileInputRef.current);
        fileInputRef.current?.click();
        console.log('File input click triggered');
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        console.log('🟡 handleFileChange called');
        console.log('Event:', event);
        console.log('Files:', event.target.files);

        const file = event.target.files?.[0];
        console.log('Selected file:', file);

        if (!file) {
            console.log('No file selected, returning');
            return;
        }

        if (!file.name.toLowerCase().endsWith('.csv')) {
            console.log('Invalid file type:', file.name);
            toast.error('Please select a CSV file');
            return;
        }

        console.log('Setting selected file and starting import');
        setSelectedFile(file);
        handleImportConfirm(file);
    };

    const handleImportConfirm = (file: File) => {
        console.log('=== CATEGORY IMPORT DEBUG START ===');
        console.log('selectedFile:', file);

        if (!file) {
            console.log('No file provided, returning early');
            return;
        }

        console.log('Setting importing state...');
        setIsImporting(true);

        try {
            console.log('Creating FormData...');
            const formData = new FormData();
            formData.append('file', file);

            // Add CSRF token manually when using forceFormData
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            if (csrfToken) {
                formData.append('_token', csrfToken);
            }

            console.log('FormData created successfully');
            console.log('File name:', file.name);
            console.log('File size:', file.size);
            console.log('File type:', file.type);

            console.log('About to call router.post...');

            router.post('/admin/bulk-import/categories', formData, {
                forceFormData: true,
                onStart: () => {
                    console.log('🚀 Request started - this should appear if router.post is called');
                },
                onProgress: (progress) => {
                    console.log('📊 Upload progress:', progress);
                },
                onSuccess: (page) => {
                    console.log('✅ Success callback called');
                    console.log('Page received:', page);

                    // Check if there are import errors in the flash data
                    const importErrors = (page.props as { flash?: { import_errors?: string[] } })?.flash?.import_errors;

                    if (importErrors && importErrors.length > 0) {
                        console.log('Import errors found:', importErrors);
                        // Show success message but also warn about errors
                        toast.success('Categories imported with some issues. Check the details below.');

                        // Show detailed errors
                        importErrors.forEach((error: string, index: number) => {
                            setTimeout(() => {
                                toast.error(error, { duration: 8000 });
                            }, index * 100); // Stagger the error messages
                        });
                    } else {
                        console.log('No import errors, showing success');
                        toast.success('All categories imported successfully!');
                    }

                    setIsImporting(false);
                    setSelectedFile(null);
                    // Reset file input
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                },
                onError: (errors) => {
                    console.log('❌ Error callback called');
                    console.error('Import errors:', errors);

                    // Handle validation errors
                    if (errors.file) {
                        toast.error(`File error: ${errors.file}`);
                    } else if (errors.message) {
                        toast.error(errors.message);
                    } else {
                        toast.error('Import failed. Please check your CSV format and try again.');
                    }

                    setIsImporting(false);
                    setSelectedFile(null);
                    // Reset file input
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                },
                onFinish: () => {
                    console.log('🏁 Request finished');
                }
            });

            console.log('router.post call completed');

        } catch (error) {
            console.error('❌ Exception in handleImportConfirm:', error);
            toast.error('An unexpected error occurred. Please try again.');
            setIsImporting(false);
            setSelectedFile(null);
        }

        console.log('=== CATEGORY IMPORT DEBUG END ===');
    };

    // Grid View Component
    const CategoryGridCard = ({ category }: { category: Category }) => (
        <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
                <div className="space-y-3">
                    <div className="flex items-start justify-between">
                        <div className="flex-1">
                            <h3 className="font-semibold text-lg text-gray-900 mb-1 line-clamp-1">
                                {category.name}
                            </h3>
                            {category.description && (
                                <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                                    {category.description}
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="flex flex-wrap gap-2">
                        <Badge variant={category.is_active ? "default" : "secondary"}>
                            {category.is_active ? "Active" : "Inactive"}
                        </Badge>
                        {category.parent && (
                            <Badge variant="outline">
                                Child of {category.parent.name}
                            </Badge>
                        )}
                        {category.parts_count !== undefined && (
                            <Badge variant="outline">
                                {category.parts_count} parts
                            </Badge>
                        )}
                        {category.children && category.children.length > 0 && (
                            <Badge variant="outline">
                                {category.children.length} subcategories
                            </Badge>
                        )}
                    </div>

                    <div className="text-xs text-muted-foreground">
                        Sort Order: {category.sort_order}
                    </div>

                    <div className="flex items-center gap-1 pt-2">
                        <Link href={route('categories.show', category.slug || category.id)}>
                            <Button variant="outline" size="sm" title="View Public Page">
                                <ExternalLink className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/categories/${category.id}`}>
                            <Button variant="outline" size="sm" title="Admin View">
                                <Eye className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/categories/${category.id}/edit`}>
                            <Button variant="outline" size="sm" title="Edit">
                                <Edit className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Button
                            variant="outline"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDelete(category)}
                            title="Delete Category"
                        >
                            <Trash2 className="h-3 w-3" />
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    // Table View Component
    const CategoryTableView = () => (
        <div className="border rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="bg-muted/50">
                        <tr>
                            <th className="text-left p-3 font-medium">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSort('name')}
                                    className="flex items-center gap-1 h-auto p-0 font-medium"
                                >
                                    Name {getSortIcon('name')}
                                </Button>
                            </th>
                            <th className="text-left p-3 font-medium">Description</th>
                            <th className="text-left p-3 font-medium">Parent</th>
                            <th className="text-left p-3 font-medium">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSort('sort_order')}
                                    className="flex items-center gap-1 h-auto p-0 font-medium"
                                >
                                    Sort Order {getSortIcon('sort_order')}
                                </Button>
                            </th>
                            <th className="text-left p-3 font-medium">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSort('parts_count')}
                                    className="flex items-center gap-1 h-auto p-0 font-medium"
                                >
                                    Parts {getSortIcon('parts_count')}
                                </Button>
                            </th>
                            <th className="text-left p-3 font-medium">Status</th>
                            <th className="text-left p-3 font-medium">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSort('created_at')}
                                    className="flex items-center gap-1 h-auto p-0 font-medium"
                                >
                                    Created {getSortIcon('created_at')}
                                </Button>
                            </th>
                            <th className="text-right p-3 font-medium">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {categories.data.map((category) => (
                            <tr key={category.id} className="border-t hover:bg-muted/25">
                                <td className="p-3">
                                    <div className="font-medium">{category.name}</div>
                                </td>
                                <td className="p-3 text-sm text-muted-foreground">
                                    {category.description ? (
                                        <span className="line-clamp-1">{category.description}</span>
                                    ) : (
                                        '-'
                                    )}
                                </td>
                                <td className="p-3 text-sm text-muted-foreground">
                                    {category.parent ? category.parent.name : 'Root'}
                                </td>
                                <td className="p-3 text-sm text-muted-foreground">
                                    {category.sort_order}
                                </td>
                                <td className="p-3">
                                    <Badge variant="outline">
                                        {category.parts_count || 0}
                                    </Badge>
                                </td>
                                <td className="p-3">
                                    <Badge variant={category.is_active ? "default" : "secondary"}>
                                        {category.is_active ? "Active" : "Inactive"}
                                    </Badge>
                                </td>
                                <td className="p-3 text-sm text-muted-foreground">
                                    {new Date(category.created_at).toLocaleDateString()}
                                </td>
                                <td className="p-3">
                                    <div className="flex items-center gap-1 justify-end">
                                        <Link href={route('categories.show', category.slug || category.id)}>
                                            <Button variant="outline" size="sm" title="View Public Page">
                                                <ExternalLink className="h-3 w-3" />
                                            </Button>
                                        </Link>
                                        <Link href={`/admin/categories/${category.id}`}>
                                            <Button variant="outline" size="sm" title="Admin View">
                                                <Eye className="h-3 w-3" />
                                            </Button>
                                        </Link>
                                        <Link href={`/admin/categories/${category.id}/edit`}>
                                            <Button variant="outline" size="sm" title="Edit">
                                                <Edit className="h-3 w-3" />
                                            </Button>
                                        </Link>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="text-destructive hover:text-destructive"
                                            onClick={() => handleDelete(category)}
                                            title="Delete Category"
                                        >
                                            <Trash2 className="h-3 w-3" />
                                        </Button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );

    return (
        <AppLayout>
            <Head title="Categories - Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Categories</h1>
                        <p className="text-muted-foreground">
                            Manage mobile part categories
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        {/* Export/Import Buttons */}
                        <div className="flex items-center gap-3">
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors"
                                onClick={() => {
                                    console.log('📄 TEMPLATE DOWNLOAD BUTTON CLICKED');
                                    handleDownloadTemplate();
                                }}
                                title="Download CSV Template"
                            >
                                <FileText className="h-4 w-4 mr-2" />
                                Template
                            </Button>
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors disabled:opacity-50"
                                onClick={() => {
                                    console.log('📤 IMPORT BUTTON CLICKED');
                                    handleImport();
                                }}
                                disabled={isImporting}
                                title="Import Categories from CSV"
                            >
                                <Upload className="h-4 w-4 mr-2" />
                                {isImporting ? 'Importing...' : 'Import'}
                            </Button>
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors"
                                onClick={() => {
                                    console.log('📥 EXPORT ALL BUTTON CLICKED');
                                    handleExportAll();
                                }}
                                title="Export All Categories to CSV"
                            >
                                <Download className="h-4 w-4 mr-2" />
                                Export All
                            </Button>
                        </div>

                        <Link href="/admin/categories/create">
                            <Button>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Category
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Hidden file input for import */}
                <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    className="hidden"
                />

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Search className="h-5 w-5" />
                            Search & Filter
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Search Bar */}
                            <div className="flex gap-3">
                                <div className="flex-1 relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search categories by name, description, or parent category..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                        className="pl-10"
                                    />
                                </div>
                                <Button onClick={handleSearch}>
                                    Search
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => setShowFilters(!showFilters)}
                                >
                                    <Filter className="h-4 w-4 mr-2" />
                                    Filters
                                </Button>
                                {(searchTerm || selectedParent !== 'all' || selectedStatus !== 'all') && (
                                    <Button
                                        variant="outline"
                                        onClick={handleClearFilters}
                                    >
                                        <X className="h-4 w-4 mr-2" />
                                        Clear
                                    </Button>
                                )}
                            </div>

                            {/* Filters */}
                            {showFilters && (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg bg-muted/20">
                                    <div className="space-y-2">
                                        <Label htmlFor="parent-filter">Parent Category</Label>
                                        <Select value={selectedParent} onValueChange={setSelectedParent}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="All categories" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All categories</SelectItem>
                                                <SelectItem value="root">Root categories only</SelectItem>
                                                <SelectItem value="child">Child categories only</SelectItem>
                                                {filters.parentCategories.map((parent) => (
                                                    <SelectItem key={parent.id} value={parent.id.toString()}>
                                                        {parent.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="status-filter">Status</Label>
                                        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="All statuses" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All statuses</SelectItem>
                                                <SelectItem value="active">Active only</SelectItem>
                                                <SelectItem value="inactive">Inactive only</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            )}

                            {/* View Mode and Sort Options */}
                            <div className="flex items-center gap-4">
                                {/* View Mode Toggle */}
                                <div className="flex items-center gap-2">
                                    <span className="text-sm text-muted-foreground">View:</span>
                                    <Button
                                        variant={viewMode === 'list' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleViewModeChange('list')}
                                        title="List View"
                                    >
                                        <List className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'grid' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleViewModeChange('grid')}
                                        title="Grid View"
                                    >
                                        <Grid className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'table' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleViewModeChange('table')}
                                        title="Table View"
                                    >
                                        <Table className="h-4 w-4" />
                                    </Button>
                                </div>

                                {/* Sort Options */}
                                <div className="flex items-center gap-2">
                                    <span className="text-sm text-muted-foreground">Sort by:</span>
                                    <Button
                                        variant={sortBy === 'name' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleSort('name')}
                                        className="flex items-center gap-1"
                                    >
                                        Name {getSortIcon('name')}
                                    </Button>
                                    <Button
                                        variant={sortBy === 'sort_order' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleSort('sort_order')}
                                        className="flex items-center gap-1"
                                    >
                                        Order {getSortIcon('sort_order')}
                                    </Button>
                                    <Button
                                        variant={sortBy === 'created_at' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleSort('created_at')}
                                        className="flex items-center gap-1"
                                    >
                                        Date {getSortIcon('created_at')}
                                    </Button>
                                    <Button
                                        variant={sortBy === 'parts_count' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleSort('parts_count')}
                                        className="flex items-center gap-1"
                                    >
                                        Parts {getSortIcon('parts_count')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Categories List */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Package className="h-5 w-5" />
                            All Categories
                        </CardTitle>
                        <CardDescription>
                            {categories.total} categories total
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {categories.data.length > 0 ? (
                            <>
                                {/* Conditional rendering based on view mode */}
                                {viewMode === 'table' ? (
                                    <CategoryTableView />
                                ) : viewMode === 'grid' ? (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {categories.data.map((category) => (
                                            <CategoryGridCard key={category.id} category={category} />
                                        ))}
                                    </div>
                                ) : (
                                    /* List View (default) */
                                    <div className="space-y-4">
                                        {categories.data.map((category) => (
                                            <div key={category.id} className="flex items-center justify-between p-4 border rounded-lg">
                                                <div className="space-y-1">
                                                    <div className="flex items-center gap-2">
                                                        <h3 className="font-medium">{category.name}</h3>
                                                        <Badge variant={category.is_active ? "default" : "secondary"}>
                                                            {category.is_active ? "Active" : "Inactive"}
                                                        </Badge>
                                                        {category.parent && (
                                                            <Badge variant="outline">
                                                                Child of {category.parent.name}
                                                            </Badge>
                                                        )}
                                                    </div>
                                                    {category.description && (
                                                        <p className="text-sm text-muted-foreground">
                                                            {category.description}
                                                        </p>
                                                    )}
                                                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                        <span>Sort Order: {category.sort_order}</span>
                                                        {category.parts_count !== undefined && (
                                                            <span>{category.parts_count} parts</span>
                                                        )}
                                                        {category.children && category.children.length > 0 && (
                                                            <span>{category.children.length} subcategories</span>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Link href={route('categories.show', category.slug || category.id)}>
                                                        <Button variant="outline" size="sm" title="View Public Page">
                                                            <ExternalLink className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/categories/${category.id}`}>
                                                        <Button variant="outline" size="sm" title="Admin View">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/categories/${category.id}/edit`}>
                                                        <Button variant="outline" size="sm" title="Edit">
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="text-destructive hover:text-destructive"
                                                        onClick={() => handleDelete(category)}
                                                        title="Delete"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="text-center py-8">
                                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <h3 className="text-lg font-medium mb-2">No categories found</h3>
                                <p className="text-muted-foreground mb-4">
                                    {searchTerm || selectedParent !== 'all' || selectedStatus !== 'all'
                                        ? 'Try adjusting your search or filter criteria.'
                                        : 'Get started by creating your first category.'
                                    }
                                </p>
                                {!(searchTerm || selectedParent !== 'all' || selectedStatus !== 'all') && (
                                    <Link href="/admin/categories/create">
                                        <Button>
                                            <Plus className="h-4 w-4 mr-2" />
                                            Add Category
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pagination */}
                {categories.last_page > 1 && (
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-muted-foreground">
                                    Showing {categories.from} to {categories.to} of {categories.total} categories
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(categories.current_page - 1)}
                                        disabled={categories.current_page === 1}
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        Previous
                                    </Button>

                                    <div className="flex items-center gap-1">
                                        {/* Show page numbers */}
                                        {Array.from({ length: Math.min(5, categories.last_page) }, (_, i) => {
                                            const page = Math.max(1, Math.min(categories.last_page - 4, categories.current_page - 2)) + i;
                                            if (page > categories.last_page) return null;

                                            return (
                                                <Button
                                                    key={page}
                                                    variant={page === categories.current_page ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => handlePageChange(page)}
                                                    className="w-8 h-8 p-0"
                                                >
                                                    {page}
                                                </Button>
                                            );
                                        })}
                                    </div>

                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(categories.current_page + 1)}
                                        disabled={categories.current_page === categories.last_page}
                                    >
                                        Next
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}
                </div>
            </div>
        </AppLayout>
    );
}
