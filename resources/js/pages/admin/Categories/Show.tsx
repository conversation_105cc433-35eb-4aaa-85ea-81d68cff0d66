import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    ArrowLeft,
    Edit,
    Package,
    Users,
    Calendar,
    ExternalLink
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Category {
    id: number;
    name: string;
    slug?: string;
    description: string | null;
    parent_id: number | null;
    sort_order: number;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    parent?: Category;
    children?: Category[];
    parts?: Part[];
}

interface Part {
    id: number;
    name: string;
    part_number: string | null;
    manufacturer: string | null;
    is_active: boolean;
}

interface Props {
    category: Category;
}

export default function Show({ category }: Props) {
    return (
        <AppLayout>
            <Head title={`${category.name} - Categories - Admin`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href="/admin/categories">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Categories
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">{category.name}</h1>
                            <p className="text-muted-foreground">
                                Category details and associated parts
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link href={route('categories.show', category.slug || category.id)}>
                            <Button variant="outline">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View Public Page
                            </Button>
                        </Link>
                        <Link href={`/admin/categories/${category.id}/edit`}>
                            <Button>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Category
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Category Details */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Category Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Name</label>
                                <p className="text-lg font-medium">{category.name}</p>
                            </div>
                            
                            {category.description && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                                    <p className="text-sm">{category.description}</p>
                                </div>
                            )}
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Status</label>
                                <div className="mt-1">
                                    <Badge variant={category.is_active ? "default" : "secondary"}>
                                        {category.is_active ? "Active" : "Inactive"}
                                    </Badge>
                                </div>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Sort Order</label>
                                <p className="text-sm">{category.sort_order}</p>
                            </div>
                            
                            {category.parent && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Parent Category</label>
                                    <p className="text-sm">{category.parent.name}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Statistics</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <Package className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm font-medium">Parts</p>
                                    <p className="text-2xl font-bold">{category.parts?.length || 0}</p>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-3">
                                <Users className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm font-medium">Subcategories</p>
                                    <p className="text-2xl font-bold">{category.children?.length || 0}</p>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-3">
                                <Calendar className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm font-medium">Created</p>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(category.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Subcategories */}
                {category.children && category.children.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Subcategories</CardTitle>
                            <CardDescription>
                                Categories that belong to {category.name}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {category.children.map((child) => (
                                    <div key={child.id} className="p-4 border rounded-lg">
                                        <div className="flex items-center justify-between">
                                            <h3 className="font-medium">{child.name}</h3>
                                            <Badge variant={child.is_active ? "default" : "secondary"}>
                                                {child.is_active ? "Active" : "Inactive"}
                                            </Badge>
                                        </div>
                                        {child.description && (
                                            <p className="text-sm text-muted-foreground mt-1">
                                                {child.description}
                                            </p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Associated Parts */}
                {category.parts && category.parts.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Associated Parts</CardTitle>
                            <CardDescription>
                                Parts that belong to this category
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {category.parts.map((part) => (
                                    <div key={part.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div>
                                            <h3 className="font-medium">{part.name}</h3>
                                            {part.part_number && (
                                                <p className="text-sm text-muted-foreground">
                                                    Part #: {part.part_number}
                                                </p>
                                            )}
                                            {part.manufacturer && (
                                                <p className="text-sm text-muted-foreground">
                                                    Manufacturer: {part.manufacturer}
                                                </p>
                                            )}
                                        </div>
                                        <Badge variant={part.is_active ? "default" : "secondary"}>
                                            {part.is_active ? "Active" : "Inactive"}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
                </div>
            </div>
        </AppLayout>
    );
}
