import { <PERSON>, <PERSON>, useForm, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
    Bell, 
    ArrowLeft, 
    Send,
    Users,
    Info,
    AlertTriangle,
    CheckCircle,
    AlertCircle,
    Megaphone
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface Props {
    users: User[];
}

interface NotificationFormData {
    user_ids: number[];
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success' | 'error' | 'announcement';
    [key: string]: number[] | string;
}

export default function CreateNotification({ users }: Props) {
    const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
    const [selectAll, setSelectAll] = useState(false);
    const [bulkMode, setBulkMode] = useState(false);
    const [bulkRecipientType, setBulkRecipientType] = useState('all');

    const { data, setData, post, processing, errors } = useForm<NotificationFormData>({
        user_ids: [],
        title: '',
        message: '',
        type: 'info',
    });

    const handleUserToggle = (userId: number) => {
        const newSelectedUsers = selectedUsers.includes(userId)
            ? selectedUsers.filter(id => id !== userId)
            : [...selectedUsers, userId];
        
        setSelectedUsers(newSelectedUsers);
        setData('user_ids', newSelectedUsers);
    };

    const handleSelectAll = () => {
        if (selectAll) {
            setSelectedUsers([]);
            setData('user_ids', []);
        } else {
            const allUserIds = users.map(user => user.id);
            setSelectedUsers(allUserIds);
            setData('user_ids', allUserIds);
        }
        setSelectAll(!selectAll);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (bulkMode) {
            // Use bulk send endpoint with router
            router.post('/admin/notifications/bulk-send', {
                ...data,
                recipient_type: bulkRecipientType,
            });
        } else {
            // Use regular create endpoint
            post('/admin/notifications');
        }
    };



    return (
        <AppLayout>
            <Head title="Send Notification" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href="/admin/notifications">
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground">Send Notification</h1>
                                <p className="text-muted-foreground mt-2">
                                    Send notifications to users
                                </p>
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Notification Content */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Bell className="h-5 w-5" />
                                    Notification Content
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="title">Title</Label>
                                    <Input
                                        id="title"
                                        value={data.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        placeholder="Enter notification title"
                                        className={errors.title ? 'border-red-500' : ''}
                                    />
                                    {errors.title && (
                                        <p className="text-sm text-red-500 mt-1">{errors.title}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="message">Message</Label>
                                    <Textarea
                                        id="message"
                                        value={data.message}
                                        onChange={(e) => setData('message', e.target.value)}
                                        placeholder="Enter notification message"
                                        rows={4}
                                        className={errors.message ? 'border-red-500' : ''}
                                    />
                                    {errors.message && (
                                        <p className="text-sm text-red-500 mt-1">{errors.message}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="type">Type</Label>
                                    <Select value={data.type} onValueChange={(value) => setData('type', value as NotificationFormData['type'])}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select notification type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="info">
                                                <div className="flex items-center gap-2">
                                                    <Info className="h-4 w-4 text-blue-600" />
                                                    Info
                                                </div>
                                            </SelectItem>
                                            <SelectItem value="warning">
                                                <div className="flex items-center gap-2">
                                                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                                    Warning
                                                </div>
                                            </SelectItem>
                                            <SelectItem value="success">
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                    Success
                                                </div>
                                            </SelectItem>
                                            <SelectItem value="error">
                                                <div className="flex items-center gap-2">
                                                    <AlertCircle className="h-4 w-4 text-red-600" />
                                                    Error
                                                </div>
                                            </SelectItem>
                                            <SelectItem value="announcement">
                                                <div className="flex items-center gap-2">
                                                    <Megaphone className="h-4 w-4 text-purple-600" />
                                                    Announcement
                                                </div>
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Recipients */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    Recipients
                                </CardTitle>
                                <CardDescription>
                                    Choose who will receive this notification
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="bulk-mode"
                                        checked={bulkMode}
                                        onCheckedChange={(checked) => setBulkMode(checked === true)}
                                    />
                                    <Label htmlFor="bulk-mode">Send to user groups</Label>
                                </div>

                                {bulkMode ? (
                                    <div>
                                        <Label htmlFor="recipient-type">Recipient Group</Label>
                                        <Select value={bulkRecipientType} onValueChange={setBulkRecipientType}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select recipient group" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Users</SelectItem>
                                                <SelectItem value="active">Active Users</SelectItem>
                                                <SelectItem value="premium">Premium Users</SelectItem>
                                                <SelectItem value="pending">Pending Approval</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                ) : (
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <Label>Select Users</Label>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={handleSelectAll}
                                            >
                                                {selectAll ? 'Deselect All' : 'Select All'}
                                            </Button>
                                        </div>

                                        <div className="max-h-60 overflow-y-auto border rounded-lg p-4 space-y-2">
                                            {users.map((user) => (
                                                <div key={user.id} className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id={`user-${user.id}`}
                                                        checked={selectedUsers.includes(user.id)}
                                                        onCheckedChange={() => handleUserToggle(user.id)}
                                                    />
                                                    <Label htmlFor={`user-${user.id}`} className="flex-1">
                                                        <div>
                                                            <div className="font-medium">{user.name}</div>
                                                            <div className="text-sm text-muted-foreground">{user.email}</div>
                                                        </div>
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>

                                        {!bulkMode && selectedUsers.length === 0 && (
                                            <p className="text-sm text-red-500">Please select at least one user.</p>
                                        )}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Submit */}
                        <div className="flex items-center gap-4">
                            <Button type="submit" disabled={processing || (!bulkMode && selectedUsers.length === 0)}>
                                <Send className="h-4 w-4 mr-2" />
                                {processing ? 'Sending...' : 'Send Notification'}
                            </Button>
                            <Link href="/admin/notifications">
                                <Button type="button" variant="outline">
                                    Cancel
                                </Button>
                            </Link>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}
