import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Bell, 
    ArrowLeft, 
    User,
    Calendar,
    Mail,
    MailOpen,
    Info,
    AlertTriangle,
    CheckCircle,
    AlertCircle,
    Megaphone
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface UserNotification {
    id: number;
    user_id: number;
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success' | 'error' | 'announcement';
    read_at: string | null;
    sent_by: number;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
    };
    sentBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Props {
    notification: UserNotification;
}

const TypeBadge = ({ type }: { type: string }) => {
    const config = {
        info: { icon: Info, className: 'bg-blue-100 text-blue-800' },
        warning: { icon: <PERSON><PERSON><PERSON><PERSON><PERSON>, className: 'bg-yellow-100 text-yellow-800' },
        success: { icon: CheckCircle, className: 'bg-green-100 text-green-800' },
        error: { icon: AlertCircle, className: 'bg-red-100 text-red-800' },
        announcement: { icon: Megaphone, className: 'bg-purple-100 text-purple-800' },
    };

    const { icon: Icon, className } = config[type as keyof typeof config] || config.info;

    return (
        <Badge className={`flex items-center gap-1 ${className}`}>
            <Icon className="h-3 w-3" />
            {type.charAt(0).toUpperCase() + type.slice(1)}
        </Badge>
    );
};

export default function ShowNotification({ notification }: Props) {
    return (
        <AppLayout>
            <Head title={`Notification: ${notification.title}`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href="/admin/notifications">
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Notifications
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground">Notification Details</h1>
                                <p className="text-muted-foreground mt-2">
                                    View notification information
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Notification Details */}
                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Bell className="h-5 w-5" />
                                    Notification Content
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Title</label>
                                    <p className="text-lg font-medium">{notification.title}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Message</label>
                                    <p className="text-sm whitespace-pre-wrap">{notification.message}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Type</label>
                                    <div className="mt-1">
                                        <TypeBadge type={notification.type} />
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                                    <div className="mt-1">
                                        {notification.read_at ? (
                                            <Badge className="bg-gray-100 text-gray-800 flex items-center gap-1 w-fit">
                                                <MailOpen className="h-3 w-3" />
                                                Read
                                            </Badge>
                                        ) : (
                                            <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1 w-fit">
                                                <Mail className="h-3 w-3" />
                                                Unread
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Recipient & Sender
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Recipient</label>
                                    <div className="mt-1">
                                        <p className="font-medium">{notification.user?.name || 'Unknown User'}</p>
                                        <p className="text-sm text-muted-foreground">{notification.user?.email || 'No email'}</p>
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Sent By</label>
                                    <div className="mt-1">
                                        <p className="font-medium">{notification.sentBy?.name || 'System'}</p>
                                        <p className="text-sm text-muted-foreground">{notification.sentBy?.email || 'System notification'}</p>
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Sent At</label>
                                    <div className="mt-1 flex items-center gap-2">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <span>{new Date(notification.created_at).toLocaleString()}</span>
                                    </div>
                                </div>

                                {notification.read_at && (
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Read At</label>
                                        <div className="mt-1 flex items-center gap-2">
                                            <Calendar className="h-4 w-4 text-muted-foreground" />
                                            <span>{new Date(notification.read_at).toLocaleString()}</span>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Actions</CardTitle>
                            <CardDescription>
                                Manage this notification
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-4">
                                {notification.user?.id && (
                                    <Link href={`/admin/users/${notification.user.id}`}>
                                        <Button variant="outline">
                                            <User className="h-4 w-4 mr-2" />
                                            View User Profile
                                        </Button>
                                    </Link>
                                )}
                                
                                <Link href="/admin/notifications">
                                    <Button variant="outline">
                                        <Bell className="h-4 w-4 mr-2" />
                                        All Notifications
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
