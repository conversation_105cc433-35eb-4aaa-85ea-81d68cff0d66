import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Bell, 
    Plus, 
    Search, 
    Filter, 
    Mail,
    MailOpen,
    Calendar,
    Users,
    AlertCircle,
    CheckCircle,
    Info,
    AlertTriangle,
    Megaphone
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface UserNotification {
    id: number;
    user_id: number;
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success' | 'error' | 'announcement';
    read_at: string | null;
    sent_by: number;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
    };
    sentBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Stats {
    total_notifications: number;
    unread_notifications: number;
    notifications_today: number;
    notifications_this_week: number;
}

interface User {
    id: number;
    name: string;
    email: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface Props {
    notifications: {
        data: UserNotification[];
        links: PaginationLink[];
        meta: PaginationMeta;
    };
    stats: Stats;
    users: User[];
    filters: {
        user_id?: string;
        type?: string;
        read_status?: string;
        date_from?: string;
        date_to?: string;
        sort_by?: string;
        sort_order?: string;
    };
}

const TypeBadge = ({ type }: { type: string }) => {
    const config = {
        info: { icon: Info, className: 'bg-blue-100 text-blue-800' },
        warning: { icon: AlertTriangle, className: 'bg-yellow-100 text-yellow-800' },
        success: { icon: CheckCircle, className: 'bg-green-100 text-green-800' },
        error: { icon: AlertCircle, className: 'bg-red-100 text-red-800' },
        announcement: { icon: Megaphone, className: 'bg-purple-100 text-purple-800' },
    };

    const { icon: Icon, className } = config[type as keyof typeof config] || config.info;

    return (
        <Badge className={`flex items-center gap-1 ${className}`}>
            <Icon className="h-3 w-3" />
            {type.charAt(0).toUpperCase() + type.slice(1)}
        </Badge>
    );
};

const ReadStatusBadge = ({ notification }: { notification: UserNotification }) => {
    return notification.read_at ? (
        <Badge className="bg-gray-100 text-gray-800 flex items-center gap-1">
            <MailOpen className="h-3 w-3" />
            Read
        </Badge>
    ) : (
        <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
            <Mail className="h-3 w-3" />
            Unread
        </Badge>
    );
};

export default function NotificationsIndex({ notifications, stats, users, filters }: Props) {
    const [selectedUser, setSelectedUser] = useState(filters.user_id || 'all');
    const [selectedType, setSelectedType] = useState(filters.type || 'all');
    const [selectedReadStatus, setSelectedReadStatus] = useState(filters.read_status || 'all');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');

    const handleSearch = () => {
        router.get('/admin/notifications', {
            ...filters,
            user_id: selectedUser === 'all' ? '' : selectedUser,
            type: selectedType === 'all' ? '' : selectedType,
            read_status: selectedReadStatus === 'all' ? '' : selectedReadStatus,
            date_from: dateFrom,
            date_to: dateTo,
        });
    };

    return (
        <AppLayout>
            <Head title="User Notifications" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">User Notifications</h1>
                            <p className="text-muted-foreground mt-2">
                                Manage and send notifications to users
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Link href="/admin/notifications/create">
                                <Button>
                                    <Plus className="h-4 w-4 mr-2" />
                                    Send Notification
                                </Button>
                            </Link>
                        </div>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Notifications</CardTitle>
                                <Bell className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.total_notifications || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Unread</CardTitle>
                                <Mail className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.unread_notifications || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Today</CardTitle>
                                <Calendar className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.notifications_today || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">This Week</CardTitle>
                                <Users className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.notifications_this_week || 0}</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filters */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filters
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-6">
                                <div>
                                    <label className="text-sm font-medium mb-2 block">User</label>
                                    <Select value={selectedUser} onValueChange={setSelectedUser}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Users" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Users</SelectItem>
                                            {users.map((user) => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Type</label>
                                    <Select value={selectedType} onValueChange={setSelectedType}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Types" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Types</SelectItem>
                                            <SelectItem value="info">Info</SelectItem>
                                            <SelectItem value="warning">Warning</SelectItem>
                                            <SelectItem value="success">Success</SelectItem>
                                            <SelectItem value="error">Error</SelectItem>
                                            <SelectItem value="announcement">Announcement</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Status</label>
                                    <Select value={selectedReadStatus} onValueChange={setSelectedReadStatus}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Statuses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Statuses</SelectItem>
                                            <SelectItem value="read">Read</SelectItem>
                                            <SelectItem value="unread">Unread</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Date From</label>
                                    <Input
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) => setDateFrom(e.target.value)}
                                    />
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Date To</label>
                                    <Input
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) => setDateTo(e.target.value)}
                                    />
                                </div>

                                <div className="flex items-end">
                                    <Button onClick={handleSearch} className="w-full">
                                        <Search className="h-4 w-4 mr-2" />
                                        Apply Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Notifications Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Notifications</CardTitle>
                            <CardDescription>
                                {notifications.meta?.total || notifications.data?.length || 0} notifications found
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {(notifications.data || []).map((notification) => (
                                    <div key={notification.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
                                        <div className="flex items-center gap-4">
                                            <div className="space-y-1">
                                                <div className="flex items-center gap-2">
                                                    <TypeBadge type={notification.type} />
                                                    <ReadStatusBadge notification={notification} />
                                                </div>
                                                <div className="space-y-1">
                                                    <h3 className="font-medium text-foreground">{notification.title}</h3>
                                                    <p className="text-sm text-muted-foreground line-clamp-2">
                                                        {notification.message}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <div className="flex items-center gap-1">
                                                        <Users className="h-3 w-3" />
                                                        <span>To: {notification.user?.name || 'Unknown User'}</span>
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="h-3 w-3" />
                                                        <span>Sent: {new Date(notification.created_at).toLocaleString()}</span>
                                                    </div>
                                                    <span>From: {notification.sentBy?.name || 'System'}</span>
                                                    {notification.read_at && (
                                                        <span>Read: {new Date(notification.read_at).toLocaleString()}</span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Link href={`/admin/notifications/${notification.id}`}>
                                                <Button variant="outline" size="sm">
                                                    View
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}

                                {(!notifications.data || notifications.data.length === 0) && (
                                    <div className="text-center py-12">
                                        <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-foreground mb-2">No notifications found</h3>
                                        <p className="text-muted-foreground">
                                            No notifications match your current filters.
                                        </p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
