import { Head } from '@inertiajs/react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Search,
    Users,
    TrendingUp,
    BarChart3,
    Activity,
    Download,
    RefreshCw,
    Eye,
    Target,
    Clock,
    Globe,
    Smartphone,
    CheckCircle,
    XCircle,
    Zap
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState, useEffect } from 'react';

interface SearchAnalytics {
    overview: {
        total_searches: number;
        user_searches: number;
        guest_searches: number;
        unique_searchers: number;
        avg_searches_per_user: number;
        search_success_rate: number;
    };
    user_searches: {
        searches_by_type: Record<string, number>;
        searches_by_hour: Record<string, number>;
        top_searchers: Array<{
            user_id: number;
            search_count: number;
            user: {
                id: number;
                name: string;
                email: string;
            };
        }>;
    };
    guest_searches: {
        total_searches: number;
        unique_devices: number;
        searches_by_date: Record<string, number>;
        avg_searches_per_device: number;
    };
    search_trends: {
        user_searches_by_date: Record<string, number>;
    };
    popular_searches: {
        popular_queries: Array<{
            search_query: string;
            count: number;
        }>;
        searches_by_type: Array<{
            search_type: string;
            count: number;
        }>;
    };
    search_performance: {
        success_rate: number;
        avg_results_per_search: number;
        zero_result_searches: number;
    };
    real_time_stats: {
        searches_today: number;
        searches_last_hour: number;
        active_searchers_today: number;
        guest_searches_today: number;
    };
}

interface Props {
    analytics: SearchAnalytics;
    days: number;
}

export default function SearchAnalyticsIndex({ analytics, days }: Props) {
    const [selectedDays, setSelectedDays] = useState(days.toString());
    const [realTimeStats, setRealTimeStats] = useState(analytics.real_time_stats);
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Auto-refresh real-time stats every 30 seconds
    useEffect(() => {
        const interval = setInterval(async () => {
            try {
                const response = await fetch('/admin/search-analytics/real-time?type=overview');
                if (response.ok) {
                    const data = await response.json();
                    setRealTimeStats(data);
                }
            } catch (error) {
                console.error('Failed to fetch real-time stats:', error);
            }
        }, 30000);

        return () => clearInterval(interval);
    }, []);

    const handleDaysChange = (value: string) => {
        setSelectedDays(value);
        window.location.href = `/admin/search-analytics?days=${value}`;
    };

    const handleRefresh = async () => {
        setIsRefreshing(true);
        try {
            const response = await fetch('/admin/search-analytics/real-time?type=overview');
            if (response.ok) {
                const data = await response.json();
                setRealTimeStats(data);
            }
        } catch (error) {
            console.error('Failed to refresh stats:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleExport = (format: 'csv' | 'json') => {
        window.location.href = `/admin/search-analytics/export?days=${selectedDays}&format=${format}`;
    };

    const formatNumber = (num: number): string => {
        return new Intl.NumberFormat().format(num);
    };

    const formatPercentage = (num: number): string => {
        return `${num}%`;
    };

    return (
        <AppLayout>
            <Head title="Search Analytics - Admin Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-8">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Search Analytics</h1>
                            <p className="text-gray-600 mt-2">
                                Comprehensive search tracking and performance insights
                            </p>
                        </div>
                        <div className="flex items-center gap-4">
                            <Select value={selectedDays} onValueChange={handleDaysChange}>
                                <SelectTrigger className="w-32">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="7">Last 7 days</SelectItem>
                                    <SelectItem value="30">Last 30 days</SelectItem>
                                    <SelectItem value="90">Last 90 days</SelectItem>
                                    <SelectItem value="365">Last year</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleRefresh}
                                disabled={isRefreshing}
                            >
                                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                                Refresh
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleExport('csv')}
                            >
                                <Download className="h-4 w-4 mr-2" />
                                Export CSV
                            </Button>
                        </div>
                    </div>

                    {/* Real-time Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Searches Today</CardTitle>
                                <Search className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatNumber(realTimeStats.searches_today)}</div>
                                <p className="text-xs text-muted-foreground">
                                    {formatNumber(realTimeStats.searches_last_hour)} in last hour
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Active Searchers</CardTitle>
                                <Users className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatNumber(realTimeStats.active_searchers_today)}</div>
                                <p className="text-xs text-muted-foreground">
                                    Unique users today
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Guest Searches</CardTitle>
                                <Globe className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatNumber(realTimeStats.guest_searches_today)}</div>
                                <p className="text-xs text-muted-foreground">
                                    From guest users today
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                                <Target className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatPercentage(analytics.overview.search_success_rate)}</div>
                                <p className="text-xs text-muted-foreground">
                                    Searches with results
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Overview Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    Search Overview
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Total Searches</span>
                                    <span className="font-medium">{formatNumber(analytics.overview.total_searches)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">User Searches</span>
                                    <span className="font-medium">{formatNumber(analytics.overview.user_searches)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Guest Searches</span>
                                    <span className="font-medium">{formatNumber(analytics.overview.guest_searches)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Unique Searchers</span>
                                    <span className="font-medium">{formatNumber(analytics.overview.unique_searchers)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Avg per User</span>
                                    <span className="font-medium">{analytics.overview.avg_searches_per_user}</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Activity className="h-5 w-5" />
                                    Performance Metrics
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-muted-foreground">Success Rate</span>
                                    <Badge variant="outline" className="text-green-600">
                                        {formatPercentage(analytics.search_performance.success_rate)}
                                    </Badge>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Avg Results</span>
                                    <span className="font-medium">{analytics.search_performance.avg_results_per_search}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Zero Results</span>
                                    <span className="font-medium">{formatNumber(analytics.search_performance.zero_result_searches)}</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Smartphone className="h-5 w-5" />
                                    Guest Analytics
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Total Searches</span>
                                    <span className="font-medium">{formatNumber(analytics.guest_searches.total_searches)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Unique Devices</span>
                                    <span className="font-medium">{formatNumber(analytics.guest_searches.unique_devices)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Avg per Device</span>
                                    <span className="font-medium">{analytics.guest_searches.avg_searches_per_device}</span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Popular Searches */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Popular Search Queries
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {analytics.popular_searches.popular_queries.slice(0, 10).map((query, index) => (
                                        <div key={index} className="flex justify-between items-center">
                                            <span className="text-sm truncate flex-1 mr-4">{query.search_query}</span>
                                            <Badge variant="outline">{formatNumber(query.count)}</Badge>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Eye className="h-5 w-5" />
                                    Top Searchers
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {analytics.user_searches.top_searchers.slice(0, 10).map((searcher, index) => (
                                        <div key={index} className="flex justify-between items-center">
                                            <div className="flex-1 mr-4">
                                                <div className="text-sm font-medium truncate">{searcher.user.name}</div>
                                                <div className="text-xs text-muted-foreground truncate">{searcher.user.email}</div>
                                            </div>
                                            <Badge variant="outline">{formatNumber(searcher.search_count)}</Badge>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Search Types Distribution */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <BarChart3 className="h-5 w-5" />
                                Search Types Distribution
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                {analytics.popular_searches.searches_by_type.map((type, index) => (
                                    <div key={index} className="text-center p-4 border rounded-lg">
                                        <div className="text-2xl font-bold text-blue-600">{formatNumber(type.count)}</div>
                                        <div className="text-sm text-muted-foreground capitalize">{type.search_type}</div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
