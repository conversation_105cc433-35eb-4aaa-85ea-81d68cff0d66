/* cSpell:ignore sendgrid */
import { Head, router, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Mail, 
    Settings, 
    TestTube, 
    CheckCircle, 
    XCircle, 
    Send,
    BarChart3,
    Shield,
    Globe
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface EmailConfig {
    default_provider: string;
    from_address: string;
    from_name: string;
    smtp: {
        host: string;
        port: number;
        username: string;
        encryption: string;
        password_set: boolean;
    };
    sendgrid: {
        api_key_set: boolean;
        api_key_prefix: string | null;
    };
}

interface ProviderStatus {
    provider: string;
    configured: boolean;
    status: string;
}

interface EmailStats {
    total_sent: number;
    total_delivered: number;
    total_bounced: number;
    total_opened: number;
    total_clicked: number;
    delivery_rate: number;
    open_rate: number;
    click_rate: number;
    bounce_rate: number;
    provider: string;
    period_days: number;
}

interface Props {
    config: EmailConfig;
    provider_status: ProviderStatus;
    email_stats: EmailStats;
}

export default function EmailConfigIndex({ config, provider_status, email_stats }: Props) {
    const [testEmail, setTestEmail] = useState('');
    const [isTestingConfig, setIsTestingConfig] = useState(false);
    const [isSendingTest, setIsSendingTest] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        provider: config.default_provider,
        from_address: config.from_address,
        from_name: config.from_name,
        smtp_host: config.smtp.host || '',
        smtp_port: config.smtp.port || 587,
        smtp_username: config.smtp.username || '',
        smtp_password: '',
        smtp_encryption: config.smtp.encryption || 'tls',
        sendgrid_api_key: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/admin/email-config', {
            onSuccess: () => {
                // Reset password field
                setData('smtp_password', '');
                setData('sendgrid_api_key', '');
            }
        });
    };

    const handleTestConfig = () => {
        setIsTestingConfig(true);
        router.post('/admin/email-config/test', { provider: data.provider }, {
            onFinish: () => setIsTestingConfig(false),
            onSuccess: (page) => {
                // Handle success message from flash data
                console.log('Configuration test completed');
            },
            onError: (errors) => {
                console.error('Configuration test failed:', errors);
            }
        });
    };

    const handleSendTestEmail = () => {
        if (!testEmail) return;

        setIsSendingTest(true);
        router.post('/admin/email-config/test-email', { email: testEmail }, {
            onFinish: () => setIsSendingTest(false),
            onSuccess: (page) => {
                // Clear test email field on success
                setTestEmail('');
                console.log('Test email sent successfully');
            },
            onError: (errors) => {
                console.error('Test email failed:', errors);
            }
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'healthy':
                return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Healthy</Badge>;
            case 'unhealthy':
                return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Unhealthy</Badge>;
            default:
                return <Badge variant="secondary">Unknown</Badge>;
        }
    };

    const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

    return (
        <AppLayout>
            <Head title="Email Configuration" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Email Configuration</h1>
                            <p className="text-muted-foreground mt-2">
                                Configure email providers and manage email delivery settings
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button onClick={handleTestConfig} disabled={isTestingConfig} variant="outline">
                                <TestTube className="h-4 w-4 mr-2" />
                                {isTestingConfig ? 'Testing...' : 'Test Config'}
                            </Button>
                        </div>
                    </div>

                    {/* Configuration Status Alert */}
                    {!provider_status.configured && (
                        <Alert className="border-orange-200 bg-orange-50">
                            <AlertDescription className="text-orange-800">
                                <strong>Configuration Required:</strong> Your email provider is not properly configured.
                                Please complete the configuration below to enable email functionality.
                            </AlertDescription>
                        </Alert>
                    )}

                    {provider_status.configured && provider_status.status !== 'healthy' && (
                        <Alert className="border-red-200 bg-red-50">
                            <AlertDescription className="text-red-800">
                                <strong>Connection Issue:</strong> Email provider is configured but connection test failed.
                                Please verify your settings and test the configuration.
                            </AlertDescription>
                        </Alert>
                    )}

                    {provider_status.configured && provider_status.status === 'healthy' && (
                        <Alert className="border-green-200 bg-green-50">
                            <AlertDescription className="text-green-800">
                                <strong>Configuration Valid:</strong> Email provider is properly configured and working correctly.
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Status Overview */}
                    <div className="grid gap-4 md:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Current Provider</CardTitle>
                                <Mail className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold capitalize">{provider_status.provider}</div>
                                <div className="mt-2">
                                    {getStatusBadge(provider_status.status)}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Emails Sent</CardTitle>
                                <Send className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{email_stats.total_sent}</div>
                                <p className="text-xs text-muted-foreground">
                                    Last {email_stats.period_days} days
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Delivery Rate</CardTitle>
                                <BarChart3 className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatPercentage(email_stats.delivery_rate)}</div>
                                <p className="text-xs text-muted-foreground">
                                    {email_stats.total_delivered} delivered
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Open Rate</CardTitle>
                                <Globe className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatPercentage(email_stats.open_rate)}</div>
                                <p className="text-xs text-muted-foreground">
                                    {email_stats.total_opened} opened
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Configuration Tabs */}
                    <Tabs defaultValue="settings" className="space-y-4">
                        <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="settings">Settings</TabsTrigger>
                            <TabsTrigger value="test">Test Email</TabsTrigger>
                            <TabsTrigger value="stats">Statistics</TabsTrigger>
                        </TabsList>

                        <TabsContent value="settings" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Settings className="h-5 w-5" />
                                        Email Provider Configuration
                                    </CardTitle>
                                    <CardDescription>
                                        Configure your email provider settings for reliable email delivery
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        {/* General Settings */}
                                        <div className="space-y-4">
                                            <h3 className="text-lg font-medium">General Settings</h3>
                                            
                                            <div className="grid gap-4 md:grid-cols-2">
                                                <div className="space-y-2">
                                                    <Label htmlFor="provider">Email Provider</Label>
                                                    <Select value={data.provider} onValueChange={(value) => setData('provider', value)}>
                                                        <SelectTrigger>
                                                            <SelectValue />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="smtp">SMTP</SelectItem>
                                                            <SelectItem value="sendgrid">SendGrid</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.provider && <p className="text-sm text-red-600">{errors.provider}</p>}
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="from_address">From Email Address</Label>
                                                    <Input
                                                        id="from_address"
                                                        type="email"
                                                        value={data.from_address}
                                                        onChange={(e) => setData('from_address', e.target.value)}
                                                        placeholder="<EMAIL>"
                                                    />
                                                    {errors.from_address && <p className="text-sm text-red-600">{errors.from_address}</p>}
                                                </div>

                                                <div className="space-y-2 md:col-span-2">
                                                    <Label htmlFor="from_name">From Name</Label>
                                                    <Input
                                                        id="from_name"
                                                        value={data.from_name}
                                                        onChange={(e) => setData('from_name', e.target.value)}
                                                        placeholder="Mobile Parts DB"
                                                    />
                                                    {errors.from_name && <p className="text-sm text-red-600">{errors.from_name}</p>}
                                                </div>
                                            </div>
                                        </div>

                                        {/* SMTP Settings */}
                                        {data.provider === 'smtp' && (
                                            <div className="space-y-4">
                                                <h3 className="text-lg font-medium">SMTP Settings</h3>
                                                
                                                <div className="grid gap-4 md:grid-cols-2">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="smtp_host">SMTP Host</Label>
                                                        <Input
                                                            id="smtp_host"
                                                            value={data.smtp_host}
                                                            onChange={(e) => setData('smtp_host', e.target.value)}
                                                            placeholder="smtp.gmail.com"
                                                        />
                                                        {errors.smtp_host && <p className="text-sm text-red-600">{errors.smtp_host}</p>}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="smtp_port">SMTP Port</Label>
                                                        <Input
                                                            id="smtp_port"
                                                            type="number"
                                                            value={data.smtp_port}
                                                            onChange={(e) => setData('smtp_port', parseInt(e.target.value))}
                                                            placeholder="587"
                                                        />
                                                        {errors.smtp_port && <p className="text-sm text-red-600">{errors.smtp_port}</p>}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="smtp_username">SMTP Username</Label>
                                                        <Input
                                                            id="smtp_username"
                                                            value={data.smtp_username}
                                                            onChange={(e) => setData('smtp_username', e.target.value)}
                                                            placeholder="<EMAIL>"
                                                        />
                                                        {errors.smtp_username && <p className="text-sm text-red-600">{errors.smtp_username}</p>}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="smtp_encryption">Encryption</Label>
                                                        <Select value={data.smtp_encryption} onValueChange={(value) => setData('smtp_encryption', value)}>
                                                            <SelectTrigger>
                                                                <SelectValue />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="tls">TLS</SelectItem>
                                                                <SelectItem value="ssl">SSL</SelectItem>
                                                                <SelectItem value="null">None</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                        {errors.smtp_encryption && <p className="text-sm text-red-600">{errors.smtp_encryption}</p>}
                                                    </div>

                                                    <div className="space-y-2 md:col-span-2">
                                                        <Label htmlFor="smtp_password">
                                                            SMTP Password 
                                                            {config.smtp.password_set && (
                                                                <span className="text-sm text-green-600 ml-2">(Currently set)</span>
                                                            )}
                                                        </Label>
                                                        <Input
                                                            id="smtp_password"
                                                            type="password"
                                                            value={data.smtp_password}
                                                            onChange={(e) => setData('smtp_password', e.target.value)}
                                                            placeholder={config.smtp.password_set ? "Leave blank to keep current password" : "Enter SMTP password"}
                                                        />
                                                        {errors.smtp_password && <p className="text-sm text-red-600">{errors.smtp_password}</p>}
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* SendGrid Settings */}
                                        {data.provider === 'sendgrid' && (
                                            <div className="space-y-4">
                                                <h3 className="text-lg font-medium">SendGrid Settings</h3>
                                                
                                                <div className="space-y-2">
                                                    <Label htmlFor="sendgrid_api_key">
                                                        SendGrid API Key
                                                        {config.sendgrid.api_key_set && (
                                                            <span className="text-sm text-green-600 ml-2">
                                                                (Currently set: {config.sendgrid.api_key_prefix})
                                                            </span>
                                                        )}
                                                    </Label>
                                                    <Input
                                                        id="sendgrid_api_key"
                                                        type="password"
                                                        value={data.sendgrid_api_key}
                                                        onChange={(e) => setData('sendgrid_api_key', e.target.value)}
                                                        placeholder={config.sendgrid.api_key_set ? "Leave blank to keep current API key" : "SG.xxxxxxxxxxxxxxxx"}
                                                    />
                                                    {errors.sendgrid_api_key && <p className="text-sm text-red-600">{errors.sendgrid_api_key}</p>}
                                                </div>

                                                <Alert>
                                                    <Shield className="h-4 w-4" />
                                                    <AlertDescription>
                                                        SendGrid provides better deliverability, analytics, and scalability compared to SMTP. 
                                                        Get your API key from the SendGrid dashboard under Settings → API Keys.
                                                    </AlertDescription>
                                                </Alert>
                                            </div>
                                        )}

                                        <div className="flex justify-end">
                                            <Button type="submit" disabled={processing}>
                                                {processing ? 'Saving...' : 'Save Configuration'}
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="test" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <TestTube className="h-5 w-5" />
                                        Send Test Email
                                    </CardTitle>
                                    <CardDescription>
                                        Send a test email to verify your configuration is working correctly
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="test_email">Test Email Address</Label>
                                            <Input
                                                id="test_email"
                                                type="email"
                                                value={testEmail}
                                                onChange={(e) => setTestEmail(e.target.value)}
                                                placeholder="<EMAIL>"
                                            />
                                        </div>
                                        <Button 
                                            onClick={handleSendTestEmail} 
                                            disabled={!testEmail || isSendingTest}
                                            className="w-full"
                                        >
                                            <Send className="h-4 w-4 mr-2" />
                                            {isSendingTest ? 'Sending...' : 'Send Test Email'}
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="stats" className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Email Performance</CardTitle>
                                        <CardDescription>Last {email_stats.period_days} days</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            <div className="flex justify-between">
                                                <span>Delivery Rate:</span>
                                                <span className="font-bold">{formatPercentage(email_stats.delivery_rate)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Open Rate:</span>
                                                <span className="font-bold">{formatPercentage(email_stats.open_rate)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Click Rate:</span>
                                                <span className="font-bold">{formatPercentage(email_stats.click_rate)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Bounce Rate:</span>
                                                <span className="font-bold">{formatPercentage(email_stats.bounce_rate)}</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle>Email Volume</CardTitle>
                                        <CardDescription>Total emails processed</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            <div className="flex justify-between">
                                                <span>Total Sent:</span>
                                                <span className="font-bold">{email_stats.total_sent}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Delivered:</span>
                                                <span className="font-bold text-green-600">{email_stats.total_delivered}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Bounced:</span>
                                                <span className="font-bold text-red-600">{email_stats.total_bounced}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Opened:</span>
                                                <span className="font-bold text-blue-600">{email_stats.total_opened}</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
