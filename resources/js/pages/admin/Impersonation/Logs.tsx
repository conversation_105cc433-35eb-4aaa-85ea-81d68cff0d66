import { Head, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Shield, 
    Clock, 
    CheckCircle,
    Search,
    Filter,
    User,
    Calendar,
    Activity,
    AlertTriangle
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface ImpersonationLog {
    id: number;
    admin_user_id: number;
    target_user_id: number;
    started_at: string;
    ended_at: string | null;
    ip_address: string | null;
    reason: string | null;
    admin_user: {
        id: number;
        name: string;
        email: string;
    };
    target_user: {
        id: number;
        name: string;
        email: string;
    };
}

interface Stats {
    total_sessions: number;
    active_sessions: number;
    sessions_today: number;
    sessions_this_week: number;
}

interface AdminUser {
    id: number;
    name: string;
    email: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface Props {
    logs: {
        data: ImpersonationLog[];
        links: PaginationLink[];
        meta: PaginationMeta;
    };
    stats: Stats;
    adminUsers: AdminUser[];
    filters: {
        admin_user_id?: string;
        target_user_id?: string;
        date_from?: string;
        date_to?: string;
        active_only?: string;
        sort_by?: string;
        sort_order?: string;
    };
}

const StatusBadge = ({ log }: { log: ImpersonationLog }) => {
    const isActive = !log.ended_at;
    
    return (
        <Badge className={`flex items-center gap-1 ${
            isActive 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
        }`}>
            {isActive ? (
                <>
                    <Activity className="h-3 w-3" />
                    Active
                </>
            ) : (
                <>
                    <CheckCircle className="h-3 w-3" />
                    Ended
                </>
            )}
        </Badge>
    );
};

export default function ImpersonationLogs({ logs, stats, adminUsers, filters }: Props) {
    const [selectedAdmin, setSelectedAdmin] = useState(filters.admin_user_id || 'all');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');
    const [activeOnly, setActiveOnly] = useState(filters.active_only === '1');

    const handleSearch = () => {
        router.get('/admin/impersonation/logs', {
            ...filters,
            admin_user_id: selectedAdmin === 'all' ? '' : selectedAdmin,
            date_from: dateFrom,
            date_to: dateTo,
            active_only: activeOnly ? '1' : '',
        });
    };

    const formatDuration = (startedAt: string, endedAt: string | null) => {
        const start = new Date(startedAt);
        const end = endedAt ? new Date(endedAt) : new Date();
        const diffMs = end.getTime() - start.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMins / 60);
        
        if (diffHours > 0) {
            return `${diffHours}h ${diffMins % 60}m`;
        }
        return `${diffMins}m`;
    };

    return (
        <AppLayout>
            <Head title="Impersonation Logs" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">Impersonation Logs</h1>
                            <p className="text-muted-foreground mt-2">
                                Monitor and audit user impersonation sessions
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Shield className="h-5 w-5 text-orange-600" />
                        </div>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
                                <Shield className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.total_sessions || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
                                <Activity className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.active_sessions || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Today</CardTitle>
                                <Calendar className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.sessions_today || 0}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">This Week</CardTitle>
                                <Clock className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground">{stats?.sessions_this_week || 0}</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filters */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filters
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-6">
                                <div>
                                    <label className="text-sm font-medium mb-2 block">Admin User</label>
                                    <Select value={selectedAdmin} onValueChange={setSelectedAdmin}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Admins" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Admins</SelectItem>
                                            {adminUsers.map((admin) => (
                                                <SelectItem key={admin.id} value={admin.id.toString()}>
                                                    {admin.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Date From</label>
                                    <Input
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) => setDateFrom(e.target.value)}
                                    />
                                </div>

                                <div>
                                    <label className="text-sm font-medium mb-2 block">Date To</label>
                                    <Input
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) => setDateTo(e.target.value)}
                                    />
                                </div>

                                <div className="flex items-end">
                                    <label className="flex items-center gap-2">
                                        <input
                                            type="checkbox"
                                            checked={activeOnly}
                                            onChange={(e) => setActiveOnly(e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <span className="text-sm font-medium">Active Only</span>
                                    </label>
                                </div>

                                <div className="flex items-end">
                                    <Button onClick={handleSearch} className="w-full">
                                        <Search className="h-4 w-4 mr-2" />
                                        Apply Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Logs Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Impersonation Sessions</CardTitle>
                            <CardDescription>
                                {logs.meta?.total || logs.data?.length || 0} sessions found
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {(logs.data || []).map((log) => (
                                    <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
                                        <div className="flex items-center gap-4">
                                            <div className="space-y-1">
                                                <div className="flex items-center gap-2">
                                                    <StatusBadge log={log} />
                                                    {log.reason && (
                                                        <Badge variant="outline" className="text-xs">
                                                            {log.reason}
                                                        </Badge>
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-2 text-sm">
                                                    <User className="h-4 w-4 text-blue-600" />
                                                    <span className="font-medium">{log.admin_user.name}</span>
                                                    <span className="text-muted-foreground">impersonated</span>
                                                    <span className="font-medium">{log.target_user.name}</span>
                                                </div>
                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="h-3 w-3" />
                                                        <span>Started: {new Date(log.started_at).toLocaleString()}</span>
                                                    </div>
                                                    {log.ended_at && (
                                                        <div className="flex items-center gap-1">
                                                            <CheckCircle className="h-3 w-3" />
                                                            <span>Ended: {new Date(log.ended_at).toLocaleString()}</span>
                                                        </div>
                                                    )}
                                                    <div className="flex items-center gap-1">
                                                        <Clock className="h-3 w-3" />
                                                        <span>Duration: {formatDuration(log.started_at, log.ended_at)}</span>
                                                    </div>
                                                    {log.ip_address && (
                                                        <span>IP: {log.ip_address}</span>
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <span>Admin: {log.admin_user.email}</span>
                                                    <span>Target: {log.target_user.email}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {!log.ended_at && (
                                                <Badge className="bg-red-100 text-red-800 flex items-center gap-1">
                                                    <AlertTriangle className="h-3 w-3" />
                                                    Still Active
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                ))}

                                {(!logs.data || logs.data.length === 0) && (
                                    <div className="text-center py-12">
                                        <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-foreground mb-2">No impersonation sessions found</h3>
                                        <p className="text-muted-foreground">
                                            No sessions match your current filters.
                                        </p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
