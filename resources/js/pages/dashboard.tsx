import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    Search,
    Crown,
    TrendingUp,
    Clock,
    Heart,
    Bell,
    Activity,
    BarChart3,
    Zap,
    Target,
    ArrowUpRight,
    Star,
    CheckCircle,
    Settings,
    Eye,
    History
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    const { auth } = usePage<SharedData>().props;
    const user = auth.user;

    // Mock data for demonstration - in real implementation, this would come from the backend
    const mockStats = {
        totalSearches: 156,
        searchesToday: 12,
        successRate: 89,
        remainingSearches: user.subscription_plan === 'premium' ? -1 : Math.max(0, 20 - 8),
        favoriteItems: 23,
        recentActivity: 5
    };

    const mockRecentSearches = [
        { id: 1, query: 'iPhone 15 Pro Max Display', type: 'part', results: 15, date: '2 hours ago' },
        { id: 2, query: 'Samsung Galaxy S24', type: 'model', results: 8, date: '5 hours ago' },
        { id: 3, query: 'Battery', type: 'category', results: 142, date: '1 day ago' },
        { id: 4, query: 'Xiaomi Redmi Note 13', type: 'model', results: 12, date: '2 days ago' },
    ];

    const mockTopCategories = [
        { name: 'Display', count: 45, percentage: 28 },
        { name: 'Battery', count: 38, percentage: 24 },
        { name: 'Camera', count: 32, percentage: 20 },
        { name: 'Charging Port', count: 25, percentage: 16 },
        { name: 'Speaker', count: 16, percentage: 12 },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />

            <div className="bg-gradient-to-br from-background via-muted/20 to-background min-h-screen">
                <div className="container mx-auto px-4 py-6 max-w-7xl">
                    {/* Header Section */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-6">
                            <div>
                                <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center gap-3">
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                        <BarChart3 className="w-6 h-6 text-primary" />
                                    </div>
                                    Welcome back, {user.name}!
                                </h1>
                                <p className="text-muted-foreground text-lg">
                                    Comprehensive insights into your mobile parts search activity
                                </p>
                            </div>
                            <div className="flex gap-3">
                                <Link href={route('search.index')}>
                                    <Button className="gap-2">
                                        <Search className="w-4 h-4" />
                                        Start Searching
                                    </Button>
                                </Link>
                                <Link href={route('subscription.plans')}>
                                    <Button variant="outline" className="gap-2">
                                        <Crown className="w-4 h-4" />
                                        View Plans
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* Stats Overview Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        {/* Current Plan */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Current Plan</CardTitle>
                                <div className="p-2 bg-primary/10 rounded-lg">
                                    <Crown className="h-4 w-4 text-primary" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground mb-1">
                                    {user.subscription_plan === 'premium' ? 'Premium' : 'Free'}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge variant={user.subscription_plan === 'premium' ? 'default' : 'secondary'} className="text-xs">
                                        {user.subscription_plan === 'premium' ? 'Active' : 'Basic'}
                                    </Badge>
                                    {user.subscription_plan === 'premium' && (
                                        <CheckCircle className="w-3 h-3 text-green-500" />
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Today's Searches */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Today's Searches</CardTitle>
                                <div className="p-2 bg-blue-500/10 rounded-lg">
                                    <Zap className="h-4 w-4 text-blue-500" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground mb-1">{mockStats.searchesToday}</div>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    {user.subscription_plan === 'premium' ? (
                                        <span className="text-green-600 font-medium">Unlimited remaining</span>
                                    ) : (
                                        <span>{mockStats.remainingSearches} remaining today</span>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Total Searches */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Total Searches</CardTitle>
                                <div className="p-2 bg-green-500/10 rounded-lg">
                                    <BarChart3 className="h-4 w-4 text-green-500" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground mb-1">{mockStats.totalSearches}</div>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    <ArrowUpRight className="w-3 h-3 text-green-500" />
                                    <span className="text-green-600 font-medium">+12% from last week</span>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Success Rate */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Success Rate</CardTitle>
                                <div className="p-2 bg-purple-500/10 rounded-lg">
                                    <Target className="h-4 w-4 text-purple-500" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground mb-1">{mockStats.successRate}%</div>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    <span>Searches with results</span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Main Content Grid */}
                    <div className="grid lg:grid-cols-3 gap-6 mb-8">
                        {/* Recent Search Activity */}
                        <Card className="lg:col-span-2 shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="pb-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="flex items-center gap-3 text-xl">
                                            <div className="p-2 bg-primary/10 rounded-lg">
                                                <Activity className="w-5 h-5 text-primary" />
                                            </div>
                                            Recent Search Activity
                                        </CardTitle>
                                        <CardDescription className="text-base mt-1">
                                            Your latest searches and their results
                                        </CardDescription>
                                    </div>
                                    <Link href={route('dashboard.history')}>
                                        <Button variant="outline" size="sm" className="gap-2">
                                            <Eye className="w-4 h-4" />
                                            View All
                                        </Button>
                                    </Link>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-1">
                                    {mockRecentSearches.map((search) => (
                                        <div
                                            key={search.id}
                                            className="group flex items-center justify-between p-4 rounded-lg border border-border/50 hover:border-primary/30 hover:bg-muted/30 transition-all duration-200"
                                        >
                                            <div className="flex items-center gap-4 flex-1 min-w-0">
                                                <div className="flex-shrink-0">
                                                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                                                        <Search className="w-4 h-4 text-primary" />
                                                    </div>
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center gap-2 mb-1">
                                                        <h4 className="font-semibold text-foreground truncate">
                                                            {search.query}
                                                        </h4>
                                                        <Badge variant="outline" className="text-xs px-2 py-0.5 flex-shrink-0">
                                                            {search.type}
                                                        </Badge>
                                                    </div>
                                                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                                        <span className="flex items-center gap-1">
                                                            <BarChart3 className="w-3 h-3" />
                                                            {search.results} results
                                                        </span>
                                                        <span className="flex items-center gap-1">
                                                            <Clock className="w-3 h-3" />
                                                            {search.date}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex-shrink-0 ml-4">
                                                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <ArrowUpRight className="w-4 h-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {mockRecentSearches.length === 0 && (
                                    <div className="text-center py-12">
                                        <div className="w-20 h-20 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <Search className="w-8 h-8 text-muted-foreground" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-foreground mb-2">No searches yet</h3>
                                        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                                            Start exploring our mobile parts database to see your search history here
                                        </p>
                                        <Link href={route('search.index')}>
                                            <Button className="gap-2">
                                                <Search className="w-4 h-4" />
                                                Start Your First Search
                                            </Button>
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Quick Actions Sidebar */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="pb-4">
                                <CardTitle className="flex items-center gap-3 text-xl">
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                        <Zap className="w-5 h-5 text-primary" />
                                    </div>
                                    Quick Actions
                                </CardTitle>
                                <CardDescription className="text-base">
                                    Common tasks and shortcuts
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Link href={route('search.index')} className="block">
                                    <Button variant="outline" className="w-full justify-start gap-3 h-12">
                                        <Search className="w-5 h-5" />
                                        <div className="text-left">
                                            <div className="font-medium">Search Parts</div>
                                            <div className="text-xs text-muted-foreground">Find mobile components</div>
                                        </div>
                                    </Button>
                                </Link>

                                <Link href={route('dashboard.favorites')} className="block">
                                    <Button variant="outline" className="w-full justify-start gap-3 h-12">
                                        <Heart className="w-5 h-5" />
                                        <div className="text-left">
                                            <div className="font-medium">Favorites</div>
                                            <div className="text-xs text-muted-foreground">{mockStats.favoriteItems} saved items</div>
                                        </div>
                                    </Button>
                                </Link>

                                <Link href={route('dashboard.history')} className="block">
                                    <Button variant="outline" className="w-full justify-start gap-3 h-12">
                                        <History className="w-5 h-5" />
                                        <div className="text-left">
                                            <div className="font-medium">Search History</div>
                                            <div className="text-xs text-muted-foreground">{mockStats.totalSearches} total searches</div>
                                        </div>
                                    </Button>
                                </Link>

                                <Link href={route('subscription.search-stats')} className="block">
                                    <Button variant="outline" className="w-full justify-start gap-3 h-12">
                                        <TrendingUp className="w-5 h-5" />
                                        <div className="text-left">
                                            <div className="font-medium">Usage Analytics</div>
                                            <div className="text-xs text-muted-foreground">Detailed insights</div>
                                        </div>
                                    </Button>
                                </Link>

                                <Link href={route('notifications.index')} className="block">
                                    <Button variant="outline" className="w-full justify-start gap-3 h-12">
                                        <Bell className="w-5 h-5" />
                                        <div className="text-left">
                                            <div className="font-medium">Notifications</div>
                                            <div className="text-xs text-muted-foreground">{mockStats.recentActivity} new updates</div>
                                        </div>
                                    </Button>
                                </Link>

                                <Link href={route('activity.index')} className="block">
                                    <Button variant="outline" className="w-full justify-start gap-3 h-12">
                                        <Activity className="w-5 h-5" />
                                        <div className="text-left">
                                            <div className="font-medium">Activity Log</div>
                                            <div className="text-xs text-muted-foreground">Recent actions</div>
                                        </div>
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Analytics Section */}
                    <div className="grid lg:grid-cols-2 gap-6">
                        {/* Top Search Categories */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="pb-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="flex items-center gap-3 text-xl">
                                            <div className="p-2 bg-primary/10 rounded-lg">
                                                <BarChart3 className="w-5 h-5 text-primary" />
                                            </div>
                                            Top Search Categories
                                        </CardTitle>
                                        <CardDescription className="text-base mt-1">
                                            Most searched part categories
                                        </CardDescription>
                                    </div>
                                    <Badge variant="secondary" className="text-sm px-3 py-1">
                                        This Month
                                    </Badge>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {mockTopCategories.map((category, index) => (
                                        <div key={category.name} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium text-primary">
                                                    {index + 1}
                                                </div>
                                                <div>
                                                    <div className="font-medium text-foreground">{category.name}</div>
                                                    <div className="text-sm text-muted-foreground">{category.count} searches</div>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-3">
                                                <div className="w-24 bg-muted rounded-full h-2">
                                                    <div
                                                        className="bg-primary h-2 rounded-full transition-all duration-300"
                                                        style={{ width: `${category.percentage}%` }}
                                                    />
                                                </div>
                                                <span className="text-sm font-medium text-foreground w-12 text-right">
                                                    {category.percentage}%
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Account Overview */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="pb-4">
                                <CardTitle className="flex items-center gap-3 text-xl">
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                        <Settings className="w-5 h-5 text-primary" />
                                    </div>
                                    Account Overview
                                </CardTitle>
                                <CardDescription className="text-base mt-1">
                                    Your account status and recommendations
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Account Status */}
                                <div className="bg-muted/30 rounded-lg p-4 border border-border">
                                    <h4 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                        Account Status
                                    </h4>
                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span className="text-muted-foreground">Plan:</span>
                                            <div className="font-medium text-foreground">
                                                {user.subscription_plan === 'premium' ? 'Premium' : 'Free Plan'}
                                            </div>
                                        </div>
                                        <div>
                                            <span className="text-muted-foreground">Status:</span>
                                            <div className="font-medium text-green-600">Active</div>
                                        </div>
                                    </div>
                                </div>

                                {/* Usage Summary */}
                                <div className="bg-muted/30 rounded-lg p-4 border border-border">
                                    <h4 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                                        <BarChart3 className="w-4 h-4 text-primary" />
                                        Usage Summary
                                    </h4>
                                    <div className="space-y-3">
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-muted-foreground">Searches Today</span>
                                            <span className="font-medium text-foreground">{mockStats.searchesToday}</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-muted-foreground">Total Searches</span>
                                            <span className="font-medium text-foreground">{mockStats.totalSearches}</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-muted-foreground">Success Rate</span>
                                            <span className="font-medium text-green-600">{mockStats.successRate}%</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-muted-foreground">Favorites</span>
                                            <span className="font-medium text-foreground">{mockStats.favoriteItems}</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Recommendations */}
                                {user.subscription_plan === 'free' && (
                                    <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                                        <h4 className="font-semibold text-foreground mb-2 flex items-center gap-2">
                                            <Star className="w-4 h-4 text-primary" />
                                            Upgrade Recommendation
                                        </h4>
                                        <p className="text-sm text-muted-foreground mb-3">
                                            Get unlimited searches and advanced features with Premium.
                                        </p>
                                        <Link href={route('subscription.plans')}>
                                            <Button size="sm" className="w-full">
                                                <Crown className="w-4 h-4 mr-2" />
                                                Upgrade to Premium
                                            </Button>
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
