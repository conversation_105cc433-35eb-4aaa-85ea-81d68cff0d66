import { <PERSON>, Link, useForm } from '@inertiajs/react';
import { FormEventHandler, useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
    ArrowLeft,
    CreditCard,
    Upload,
    AlertTriangle,
    CheckCircle,
    Clock,
    Shield,
    Star,
    Lock
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import InputError from '@/components/input-error';

interface PricingPlan {
    id?: number;
    name: string;
    price: number;
    currency: string;
    interval: string;
    features?: string[];
    search_limit?: number;
    is_popular?: boolean;
    formatted_price?: string;
    metadata?: Record<string, unknown>;
}

interface Props {
    payment_methods: Record<string, string>;
    subscription_plans: Record<string, PricingPlan>;
    currencies: Record<string, string>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Payment Requests',
        href: '/payment-requests',
    },
    {
        title: 'New Request',
        href: '#',
    },
];

export default function PaymentRequestCreate({ payment_methods, subscription_plans, currencies }: Props) {
    const { data, setData, post, processing, errors, reset } = useForm({
        amount: '',
        currency: 'USD',
        payment_method: '',
        subscription_plan: Object.keys(subscription_plans)[0] || 'premium',
        notes: '',
        proof_of_payment: null as File | null,
    });

    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    // Cleanup preview URL on component unmount
    useEffect(() => {
        return () => {
            if (previewUrl) {
                URL.revokeObjectURL(previewUrl);
            }
        };
    }, [previewUrl]);

    // Handle subscription plan change and auto-populate amount and currency
    const handlePlanChange = (planKey: string) => {
        setData('subscription_plan', planKey);

        const selectedPlan = subscription_plans[planKey];
        if (selectedPlan) {
            setData(prev => ({
                ...prev,
                subscription_plan: planKey,
                amount: selectedPlan.price.toString(),
                currency: selectedPlan.currency,
            }));
        }
    };

    // Check if current selection is a pricing plan (has price > 0 or is a known plan)
    const isSelectedPricingPlan = Boolean(data.subscription_plan && subscription_plans[data.subscription_plan]);
    const selectedPlan = isSelectedPricingPlan ? subscription_plans[data.subscription_plan] : null;

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('payment-requests.store'), {
            forceFormData: true,
            onSuccess: () => reset(),
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        setData('proof_of_payment', file);

        // Create preview URL for image files
        if (file && file.type.startsWith('image/')) {
            const previewUrl = URL.createObjectURL(file);
            setPreviewUrl(previewUrl);
        } else {
            setPreviewUrl(null);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="New Payment Request" />
            
            <div className="bg-gradient-to-br from-background via-muted/20 to-background">
                <div className="container mx-auto px-4 py-4 max-w-7xl">
                    {/* Compact Header */}
                    <div className="mb-4">
                        <div className="flex items-center justify-between mb-3">
                            <Link href={route('payment-requests.index')}>
                                <Button variant="outline" size="sm" className="gap-2 hover:bg-primary/5">
                                    <ArrowLeft className="w-4 h-4" />
                                    Back to Payment Requests
                                </Button>
                            </Link>
                        </div>

                        <div className="text-center mb-4">
                            <div className="inline-flex items-center justify-center w-10 h-10 bg-primary/10 rounded-full mb-2">
                                <CreditCard className="w-5 h-5 text-primary" />
                            </div>
                            <h1 className="text-2xl font-bold text-foreground mb-1">New Payment Request</h1>
                            <p className="text-muted-foreground max-w-2xl mx-auto text-sm">
                                Submit an offline payment for subscription upgrade and get instant access to premium features
                            </p>
                        </div>
                    </div>

                    {/* Main Content */}
                    <div className="max-w-6xl mx-auto">
                        {/* Payment Details Card */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm mb-4">
                            <CardHeader className="pb-4">
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                        <CreditCard className="w-5 h-5 text-primary" />
                                    </div>
                                    <div>
                                        <CardTitle className="text-lg text-card-foreground">Payment Details</CardTitle>
                                        <CardDescription className="text-muted-foreground text-sm">
                                            Complete your subscription payment request
                                        </CardDescription>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={submit} className="space-y-6">
                                    {/* Two Column Layout for Payment Details */}
                                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                        {/* Left Column */}
                                        <div className="space-y-6">
                                            {/* Subscription Plan Selection */}
                                            <div className="space-y-4">
                                                <div>
                                                    <Label className="text-base font-semibold text-card-foreground">Choose Your Plan</Label>
                                                    <p className="text-sm text-muted-foreground mt-1">Select the subscription plan you want to purchase</p>
                                                </div>

                                                <Select value={data.subscription_plan} onValueChange={handlePlanChange}>
                                                    <SelectTrigger className="h-12 text-left">
                                                        <SelectValue placeholder="Select subscription plan" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(subscription_plans).map(([key, plan]) => (
                                                            <SelectItem key={key} value={key} className="py-3">
                                                                {plan.name} - {plan.formatted_price || `$${plan.price}/${plan.interval}`}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <InputError message={errors.subscription_plan} />

                                                {/* Selected Plan Details */}
                                                {selectedPlan && (
                                                    <div className="p-3 bg-primary/5 border border-primary/20 rounded-lg">
                                                        <div className="flex items-start justify-between">
                                                            <div className="flex-1">
                                                                <div className="flex items-center space-x-2 mb-1">
                                                                    <CheckCircle className="w-4 h-4 text-primary" />
                                                                    <h4 className="font-semibold text-card-foreground">{selectedPlan.name}</h4>
                                                                    {selectedPlan.is_popular && (
                                                                        <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                                                                            <Star className="w-3 h-3 mr-1" />
                                                                            Popular
                                                                        </Badge>
                                                                    )}
                                                                </div>
                                                                <p className="text-lg font-bold text-primary mb-2">
                                                                    {selectedPlan.formatted_price || `$${selectedPlan.price}/${selectedPlan.interval}`}
                                                                </p>
                                                                {selectedPlan.features && selectedPlan.features.length > 0 && (
                                                                    <div className="space-y-1">
                                                                        <p className="text-sm font-medium text-muted-foreground">Features:</p>
                                                                        <ul className="text-sm text-muted-foreground space-y-0.5">
                                                                            {selectedPlan.features.slice(0, 3).map((feature, index) => (
                                                                                <li key={index} className="flex items-center space-x-2">
                                                                                    <CheckCircle className="w-3 h-3 text-primary flex-shrink-0" />
                                                                                    <span>{feature}</span>
                                                                                </li>
                                                                            ))}
                                                                            {selectedPlan.features.length > 3 && (
                                                                                <li className="text-xs text-muted-foreground">
                                                                                    +{selectedPlan.features.length - 3} more features
                                                                                </li>
                                                                            )}
                                                                        </ul>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>

                                            {/* Payment Amount */}
                                            <div className="space-y-3">
                                                <div>
                                                    <Label className="text-base font-semibold text-card-foreground">Payment Amount</Label>
                                                    <p className="text-sm text-muted-foreground mt-1">
                                                        {isSelectedPricingPlan
                                                            ? "Amount is automatically set based on your selected plan"
                                                            : "Enter the amount you want to pay"
                                                        }
                                                    </p>
                                                </div>

                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="amount" className="text-sm font-medium">Amount</Label>
                                                        <div className="relative">
                                                            <Input
                                                                id="amount"
                                                                type="number"
                                                                step="0.01"
                                                                min="0.01"
                                                                value={data.amount}
                                                                onChange={(e) => setData('amount', e.target.value)}
                                                                placeholder="0.00"
                                                                required
                                                                disabled={isSelectedPricingPlan}
                                                                className={`h-10 text-lg font-semibold ${isSelectedPricingPlan ? 'bg-muted/50 cursor-not-allowed' : ''}`}
                                                            />
                                                            {isSelectedPricingPlan && (
                                                                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                                                    <Lock className="w-4 h-4 text-muted-foreground" />
                                                                </div>
                                                            )}
                                                        </div>
                                                        <InputError message={errors.amount} />
                                                        {isSelectedPricingPlan && (
                                                            <p className="text-xs text-muted-foreground flex items-center space-x-1">
                                                                <Lock className="w-3 h-3" />
                                                                <span>Amount locked for subscription plans</span>
                                                            </p>
                                                        )}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="currency" className="text-sm font-medium">Currency</Label>
                                                        <Select value={data.currency} onValueChange={(value) => setData('currency', value)}>
                                                            <SelectTrigger className="h-10">
                                                                <SelectValue placeholder="Select currency" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {Object.entries(currencies).map(([key, label]) => (
                                                                    <SelectItem key={key} value={key}>{label}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                        <InputError message={errors.currency} />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Right Column */}
                                        <div className="space-y-6">
                                            {/* Payment Method */}
                                            <div className="space-y-3">
                                                <div>
                                                    <Label className="text-base font-semibold text-card-foreground">Payment Method</Label>
                                                    <p className="text-sm text-muted-foreground mt-1">How did you make the payment?</p>
                                                </div>

                                                <Select value={data.payment_method} onValueChange={(value) => setData('payment_method', value)}>
                                                    <SelectTrigger className="h-10">
                                                        <SelectValue placeholder="Select payment method" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(payment_methods).map(([key, label]) => (
                                                            <SelectItem key={key} value={key} className="py-2">{label}</SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <InputError message={errors.payment_method} />
                                            </div>

                                            {/* Proof of Payment */}
                                            <div className="space-y-3">
                                                <div>
                                                    <Label className="text-base font-semibold text-card-foreground">Proof of Payment</Label>
                                                    <p className="text-sm text-muted-foreground mt-1">Upload your payment receipt or screenshot</p>
                                                </div>

                                                <div className="border-2 border-dashed border-border rounded-lg p-4 text-center hover:border-primary/50 transition-colors">
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="lg"
                                                        className="w-full h-12 text-base"
                                                        onClick={() => document.getElementById('proof_of_payment')?.click()}
                                                    >
                                                        <Upload className="w-4 h-4 mr-3" />
                                                        {data.proof_of_payment ? (
                                                            <div className="flex flex-col items-center">
                                                                <span className="font-medium">{data.proof_of_payment.name}</span>
                                                                <span className="text-xs text-muted-foreground">Click to change file</span>
                                                            </div>
                                                        ) : (
                                                            <div className="flex flex-col items-center">
                                                                <span className="font-medium">Choose File</span>
                                                                <span className="text-xs text-muted-foreground">JPG, PNG, or PDF (max 5MB)</span>
                                                            </div>
                                                        )}
                                                    </Button>
                                                    <input
                                                        id="proof_of_payment"
                                                        type="file"
                                                        accept=".jpg,.jpeg,.png,.pdf"
                                                        onChange={handleFileChange}
                                                        className="hidden"
                                                    />
                                                </div>

                                                {/* Image Preview */}
                                                {previewUrl && (
                                                    <div className="mt-3">
                                                        <div className="border border-border rounded-lg p-3 bg-muted/30">
                                                            <div className="flex items-center gap-3">
                                                                <div className="flex-shrink-0">
                                                                    <img
                                                                        src={previewUrl}
                                                                        alt="Payment proof preview"
                                                                        className="w-16 h-16 object-cover rounded-md border border-border"
                                                                    />
                                                                </div>
                                                                <div className="flex-1 min-w-0">
                                                                    <p className="text-sm font-medium text-foreground truncate">
                                                                        {data.proof_of_payment?.name}
                                                                    </p>
                                                                    <p className="text-xs text-muted-foreground">
                                                                        {data.proof_of_payment && (
                                                                            `${(data.proof_of_payment.size / 1024 / 1024).toFixed(2)} MB`
                                                                        )}
                                                                    </p>
                                                                </div>
                                                                <Button
                                                                    type="button"
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => {
                                                                        setData('proof_of_payment', null);
                                                                        setPreviewUrl(null);
                                                                        const input = document.getElementById('proof_of_payment') as HTMLInputElement;
                                                                        if (input) input.value = '';
                                                                    }}
                                                                    className="text-muted-foreground hover:text-destructive"
                                                                >
                                                                    ×
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                                <InputError message={errors.proof_of_payment} />
                                            </div>

                                            {/* Additional Notes */}
                                            <div className="space-y-3">
                                                <div>
                                                    <Label className="text-base font-semibold text-card-foreground">Additional Notes</Label>
                                                    <p className="text-sm text-muted-foreground mt-1">Any additional information about your payment (optional)</p>
                                                </div>

                                                <Textarea
                                                    id="notes"
                                                    value={data.notes}
                                                    onChange={(e) => setData('notes', e.target.value)}
                                                    placeholder="Transaction ID, reference number, or any other relevant details..."
                                                    rows={3}
                                                    className="resize-none"
                                                />
                                                <InputError message={errors.notes} />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Submit Section */}
                                    <div className="pt-6 border-t border-border">
                                        <div className="flex flex-col sm:flex-row gap-3 justify-end">
                                            <Link href={route('payment-requests.index')}>
                                                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                                                    Cancel
                                                </Button>
                                            </Link>
                                            <Button
                                                type="submit"
                                                disabled={processing}
                                                size="lg"
                                                className="w-full sm:w-auto bg-primary hover:bg-primary/90"
                                            >
                                                {processing ? (
                                                    <>
                                                        <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                        Submitting...
                                                    </>
                                                ) : (
                                                    <>
                                                        <CreditCard className="w-5 h-5 mr-2" />
                                                        Submit Payment Request
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>

                        {/* Information Cards at Bottom */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                            {/* Secure Process */}
                            <Card className="border-primary/20 bg-primary/5">
                                <CardContent className="p-4">
                                    <div className="flex items-start space-x-3">
                                        <Shield className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                                        <div>
                                            <h3 className="font-semibold text-card-foreground mb-2">Secure Process</h3>
                                            <ul className="text-sm text-muted-foreground space-y-1">
                                                <li className="flex items-center space-x-2">
                                                    <CheckCircle className="w-3 h-3 text-primary flex-shrink-0" />
                                                    <span>SSL encrypted submission</span>
                                                </li>
                                                <li className="flex items-center space-x-2">
                                                    <CheckCircle className="w-3 h-3 text-primary flex-shrink-0" />
                                                    <span>Manual verification process</span>
                                                </li>
                                                <li className="flex items-center space-x-2">
                                                    <CheckCircle className="w-3 h-3 text-primary flex-shrink-0" />
                                                    <span>24-48 hour activation</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Important Notice */}
                            <Card className="border-amber-200 bg-amber-50">
                                <CardContent className="p-4">
                                    <div className="flex items-start space-x-3">
                                        <AlertTriangle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                                        <div>
                                            <h3 className="font-semibold text-amber-900 mb-2">Important Notice</h3>
                                            <p className="text-sm text-amber-800 leading-relaxed">
                                                Please ensure all payment details are accurate. Once submitted, you cannot edit this request.
                                                Our team will review and contact you if any clarification is needed.
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Processing Time */}
                            <Card>
                                <CardContent className="p-4">
                                    <div className="flex items-start space-x-3">
                                        <Clock className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                                        <div>
                                            <h3 className="font-semibold text-card-foreground mb-2">Processing Time</h3>
                                            <p className="text-sm text-muted-foreground leading-relaxed">
                                                Most payment requests are processed within 24-48 hours during business days.
                                                You'll receive an email confirmation once your subscription is activated.
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
