import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    CreditCard, 
    Plus, 
    Clock, 
    CheckCircle, 
    XCircle, 
    FileText,
    Calendar
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface PaymentRequest {
    id: number;
    user_id: number;
    amount: string;
    currency: string;
    payment_method: string;
    status: 'pending' | 'approved' | 'rejected' | 'processed';
    subscription_plan: string;
    notes: string | null;
    proof_of_payment: string | null;
    requested_at: string;
    approved_by: number | null;
    approved_at: string | null;
    admin_notes: string | null;
    created_at: string;
    approvedBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Stats {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface PaymentRequestData {
    data: PaymentRequest[];
    links: PaginationLink[];
    meta: PaginationMeta;
}

interface Props {
    payment_requests: PaymentRequestData;
    stats: Stats;
    filters: {
        status: string;
        per_page: number;
    };
    statuses: Record<string, string>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Payment Requests',
        href: '/payment-requests',
    },
];

const getStatusIcon = (status: string) => {
    switch (status) {
        case 'pending':
            return <Clock className="w-5 h-5 text-yellow-500" />;
        case 'approved':
            return <CheckCircle className="w-5 h-5 text-green-500" />;
        case 'rejected':
            return <XCircle className="w-5 h-5 text-red-500" />;
        case 'processed':
            return <CheckCircle className="w-5 h-5 text-blue-500" />;
        default:
            return <FileText className="w-5 h-5 text-gray-500" />;
    }
};

const getStatusBadgeColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'approved':
            return 'bg-green-100 text-green-800';
        case 'rejected':
            return 'bg-red-100 text-red-800';
        case 'processed':
            return 'bg-blue-100 text-blue-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getPaymentMethodLabel = (method: string) => {
    switch (method) {
        case 'bank_transfer':
            return 'Bank Transfer';
        case 'mobile_money':
            return 'Mobile Money';
        case 'cash_deposit':
            return 'Cash Deposit';
        case 'other':
            return 'Other';
        default:
            return method;
    }
};

export default function PaymentRequestsIndex({ payment_requests, stats, filters, statuses }: Props) {
    const handleFilterChange = (key: string, value: string) => {
        const params = new URLSearchParams(window.location.search);
        if (value === 'all' || value === '') {
            params.delete(key);
        } else {
            params.set(key, value);
        }
        
        router.get(route('payment-requests.index'), Object.fromEntries(params));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatAmount = (amount: string, currency: string) => {
        return `${currency} ${parseFloat(amount).toFixed(2)}`;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Payment Requests" />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Payment Requests</h1>
                        <p className="text-gray-600">Submit and track your offline payment requests</p>
                    </div>
                    <Link href={route('payment-requests.create')}>
                        <Button>
                            <Plus className="w-4 h-4 mr-2" />
                            New Payment Request
                        </Button>
                    </Link>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <CreditCard className="w-8 h-8 text-blue-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Total Requests</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Clock className="w-8 h-8 text-yellow-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Pending</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <CheckCircle className="w-8 h-8 text-green-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Approved</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <XCircle className="w-8 h-8 text-red-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Rejected</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(statuses).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Payment Requests List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Your Payment Requests</CardTitle>
                        <CardDescription>
                            {payment_requests.meta.total > 0 
                                ? `Showing ${payment_requests.meta.from} to ${payment_requests.meta.to} of ${payment_requests.meta.total} requests`
                                : 'No payment requests found'
                            }
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {payment_requests.data.length > 0 ? (
                            <div className="space-y-4">
                                {payment_requests.data.map((request) => (
                                    <div
                                        key={request.id}
                                        className="p-4 border rounded-lg transition-colors hover:bg-gray-50"
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start space-x-3 flex-1">
                                                {getStatusIcon(request.status)}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center gap-2 mb-1">
                                                        <h3 className="text-sm font-medium text-gray-900">
                                                            {formatAmount(request.amount, request.currency)} - {request.subscription_plan}
                                                        </h3>
                                                        <Badge className={`text-xs ${getStatusBadgeColor(request.status)}`}>
                                                            {request.status}
                                                        </Badge>
                                                    </div>
                                                    <p className="text-sm text-gray-600 mb-2">
                                                        Payment Method: {getPaymentMethodLabel(request.payment_method)}
                                                    </p>
                                                    <div className="flex items-center text-xs text-gray-500 space-x-4">
                                                        <span className="flex items-center">
                                                            <Calendar className="w-3 h-3 mr-1" />
                                                            Requested: {formatDate(request.requested_at)}
                                                        </span>
                                                        {request.approved_at && (
                                                            <span className="flex items-center">
                                                                <CheckCircle className="w-3 h-3 mr-1" />
                                                                {request.status === 'approved' ? 'Approved' : 'Processed'}: {formatDate(request.approved_at)}
                                                            </span>
                                                        )}
                                                        {request.approvedBy && (
                                                            <span>By: {request.approvedBy.name}</span>
                                                        )}
                                                    </div>
                                                    {request.notes && (
                                                        <p className="text-xs text-gray-600 mt-2">
                                                            Notes: {request.notes}
                                                        </p>
                                                    )}
                                                    {request.admin_notes && (
                                                        <p className="text-xs text-blue-600 mt-2">
                                                            Admin Notes: {request.admin_notes}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                            <Link href={route('payment-requests.show', request.id)}>
                                                <Button variant="outline" size="sm">
                                                    View Details
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No payment requests</h3>
                                <p className="text-gray-600 mb-4">You haven't submitted any payment requests yet.</p>
                                <Link href={route('payment-requests.create')}>
                                    <Button>
                                        <Plus className="w-4 h-4 mr-2" />
                                        Submit Your First Request
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pagination */}
                {payment_requests.meta.last_page > 1 && (
                    <div className="flex justify-center">
                        <div className="flex space-x-1">
                            {payment_requests.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? "default" : "outline"}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
