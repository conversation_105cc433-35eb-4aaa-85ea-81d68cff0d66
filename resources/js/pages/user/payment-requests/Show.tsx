import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    ArrowLeft,
    CreditCard,
    Download,
    Calendar,
    User,
    FileText,
    DollarSign,
    Clock,
    CheckCircle,
    XCircle,
    AlertTriangle
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface PaymentRequest {
    id: number;
    user_id: number;
    amount: string;
    currency: string;
    payment_method: string;
    status: 'pending' | 'approved' | 'rejected' | 'processed';
    subscription_plan: string;
    notes: string | null;
    proof_of_payment: string | null;
    requested_at: string;
    approved_by: number | null;
    approved_at: string | null;
    admin_notes: string | null;
    created_at: string;
    approvedBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Props {
    payment_request: PaymentRequest;
    can_download_proof: boolean;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Payment Requests',
        href: '/payment-requests',
    },
    {
        title: 'Request Details',
        href: '#',
    },
];

const getStatusIcon = (status: string) => {
    switch (status) {
        case 'pending':
            return <Clock className="w-8 h-8 text-yellow-500" />;
        case 'approved':
            return <CheckCircle className="w-8 h-8 text-green-500" />;
        case 'rejected':
            return <XCircle className="w-8 h-8 text-red-500" />;
        case 'processed':
            return <CheckCircle className="w-8 h-8 text-blue-500" />;
        default:
            return <FileText className="w-8 h-8 text-gray-500" />;
    }
};

const getStatusBadgeColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'approved':
            return 'bg-green-100 text-green-800';
        case 'rejected':
            return 'bg-red-100 text-red-800';
        case 'processed':
            return 'bg-blue-100 text-blue-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getStatusDescription = (status: string) => {
    switch (status) {
        case 'pending':
            return 'Your payment request is being reviewed by our team.';
        case 'approved':
            return 'Your payment has been approved and your subscription is being activated.';
        case 'rejected':
            return 'Your payment request has been rejected. Please check the admin notes below.';
        case 'processed':
            return 'Your payment has been processed and your subscription is now active.';
        default:
            return 'Payment request status unknown.';
    }
};

const getPaymentMethodLabel = (method: string) => {
    switch (method) {
        case 'bank_transfer':
            return 'Bank Transfer';
        case 'mobile_money':
            return 'Mobile Money';
        case 'cash_deposit':
            return 'Cash Deposit';
        case 'other':
            return 'Other';
        default:
            return method;
    }
};

export default function PaymentRequestShow({ payment_request, can_download_proof }: Props) {
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatAmount = (amount: string, currency: string) => {
        return `${currency} ${parseFloat(amount).toFixed(2)}`;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Payment Request #${payment_request.id}`} />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('payment-requests.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Payment Requests
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Payment Request #{payment_request.id}</h1>
                            <p className="text-gray-600">View payment request details and status</p>
                        </div>
                    </div>
                </div>

                {/* Status Card */}
                <Card className={`border-2 ${
                    payment_request.status === 'approved' ? 'border-green-200 bg-green-50' :
                    payment_request.status === 'rejected' ? 'border-red-200 bg-red-50' :
                    payment_request.status === 'processed' ? 'border-blue-200 bg-blue-50' :
                    'border-yellow-200 bg-yellow-50'
                }`}>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                {getStatusIcon(payment_request.status)}
                                <div>
                                    <div className="flex items-center gap-3 mb-2">
                                        <h2 className="text-xl font-semibold text-gray-900">
                                            {payment_request.status.charAt(0).toUpperCase() + payment_request.status.slice(1)}
                                        </h2>
                                        <Badge className={`${getStatusBadgeColor(payment_request.status)}`}>
                                            {payment_request.status}
                                        </Badge>
                                    </div>
                                    <p className="text-gray-700">
                                        {getStatusDescription(payment_request.status)}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Payment Details */}
                <Card className="max-w-4xl">
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <CreditCard className="w-5 h-5 mr-2" />
                            Payment Details
                        </CardTitle>
                        <CardDescription>
                            Information about your payment request
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Payment Information */}
                            <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <DollarSign className="w-4 h-4 mr-2" />
                                    Payment Information
                                </h4>
                                <div className="bg-white border rounded-lg p-4 space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Amount:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {formatAmount(payment_request.amount, payment_request.currency)}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Payment Method:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {getPaymentMethodLabel(payment_request.payment_method)}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Subscription Plan:</span>
                                        <span className="text-sm font-medium text-gray-900 capitalize">
                                            {payment_request.subscription_plan}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Timeline */}
                            <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <Calendar className="w-4 h-4 mr-2" />
                                    Timeline
                                </h4>
                                <div className="bg-white border rounded-lg p-4 space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Requested:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {formatDate(payment_request.requested_at)}
                                        </span>
                                    </div>
                                    {payment_request.approved_at && (
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">
                                                {payment_request.status === 'approved' ? 'Approved:' : 
                                                 payment_request.status === 'rejected' ? 'Rejected:' : 'Processed:'}
                                            </span>
                                            <span className="text-sm font-medium text-gray-900">
                                                {formatDate(payment_request.approved_at)}
                                            </span>
                                        </div>
                                    )}
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Request ID:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            #{payment_request.id}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Approved By */}
                        {payment_request.approvedBy && (
                            <div className="mt-6">
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <User className="w-4 h-4 mr-2" />
                                    Reviewed By
                                </h4>
                                <div className="bg-white border rounded-lg p-4">
                                    <p className="font-medium text-gray-900">{payment_request.approvedBy.name}</p>
                                    <p className="text-sm text-gray-600">{payment_request.approvedBy.email}</p>
                                </div>
                            </div>
                        )}

                        {/* Notes */}
                        {payment_request.notes && (
                            <div className="mt-6">
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <FileText className="w-4 h-4 mr-2" />
                                    Your Notes
                                </h4>
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                                        {payment_request.notes}
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* Admin Notes */}
                        {payment_request.admin_notes && (
                            <div className="mt-6">
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <AlertTriangle className="w-4 h-4 mr-2" />
                                    Admin Notes
                                </h4>
                                <div className={`rounded-lg p-4 ${
                                    payment_request.status === 'rejected' ? 'bg-red-50 border border-red-200' : 'bg-blue-50 border border-blue-200'
                                }`}>
                                    <p className={`text-sm whitespace-pre-wrap ${
                                        payment_request.status === 'rejected' ? 'text-red-700' : 'text-blue-700'
                                    }`}>
                                        {payment_request.admin_notes}
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* Proof of Payment */}
                        {payment_request.proof_of_payment && (
                            <div className="mt-6">
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <FileText className="w-4 h-4 mr-2" />
                                    Proof of Payment
                                </h4>
                                <div className="bg-white border rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">Payment Receipt</p>
                                            <p className="text-xs text-gray-500">Uploaded with your request</p>
                                        </div>
                                        {can_download_proof && (
                                            <Link href={route('payment-requests.download-proof', payment_request.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Download className="w-4 h-4 mr-2" />
                                                    Download
                                                </Button>
                                            </Link>
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Actions */}
                        <div className="mt-6 pt-6 border-t">
                            <div className="flex justify-between items-center">
                                <Link href={route('payment-requests.index')}>
                                    <Button variant="outline">
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Back to All Requests
                                    </Button>
                                </Link>
                                
                                {payment_request.status === 'pending' && (
                                    <div className="text-sm text-gray-600">
                                        Your request is being reviewed. You'll be notified once it's processed.
                                    </div>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
