import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Activity, 
    LogIn, 
    LogOut, 
    Search, 
    Eye, 
    Package,
    Smartphone,
    Grid3X3,
    User,
    CreditCard,
    Settings,
    Mail,
    Calendar
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface ActivityMetadata {
    [key: string]: string | number | boolean | object | null;
}

interface UserActivityLog {
    id: number;
    user_id: number;
    activity_type: string;
    description: string;
    ip_address: string;
    user_agent: string;
    metadata: ActivityMetadata | null;
    performed_by: number | null;
    created_at: string;
    performedBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Stats {
    total_activities: number;
    recent_activities: number;
    login_count: number;
    search_count: number;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface ActivityData {
    data: UserActivityLog[];
    links: PaginationLink[];
    meta: PaginationMeta;
}

interface Props {
    activities: ActivityData;
    stats: Stats;
    activity_type_counts: Record<string, number>;
    filters: {
        activity_type: string;
        date_range: string;
        per_page: number;
    };
    activity_types: Record<string, string>;
    date_ranges: Record<string, string>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Activity Log',
        href: '/activity',
    },
];

const getActivityIcon = (activityType: string) => {
    switch (activityType) {
        case 'login':
            return <LogIn className="w-5 h-5 text-green-500" />;
        case 'logout':
            return <LogOut className="w-5 h-5 text-gray-500" />;
        case 'search':
            return <Search className="w-5 h-5 text-blue-500" />;
        case 'view_part':
            return <Package className="w-5 h-5 text-purple-500" />;
        case 'view_category':
            return <Grid3X3 className="w-5 h-5 text-orange-500" />;
        case 'view_brand':
            return <Smartphone className="w-5 h-5 text-indigo-500" />;
        case 'view_model':
            return <Eye className="w-5 h-5 text-teal-500" />;
        case 'subscription_change':
            return <CreditCard className="w-5 h-5 text-yellow-500" />;
        case 'profile_update':
            return <User className="w-5 h-5 text-pink-500" />;
        case 'password_change':
            return <Settings className="w-5 h-5 text-red-500" />;
        case 'email_verification':
            return <Mail className="w-5 h-5 text-cyan-500" />;
        default:
            return <Activity className="w-5 h-5 text-gray-500" />;
    }
};

const getActivityBadgeColor = (activityType: string) => {
    switch (activityType) {
        case 'login':
            return 'bg-green-100 text-green-800';
        case 'logout':
            return 'bg-gray-100 text-gray-800';
        case 'search':
            return 'bg-blue-100 text-blue-800';
        case 'view_part':
            return 'bg-purple-100 text-purple-800';
        case 'view_category':
            return 'bg-orange-100 text-orange-800';
        case 'view_brand':
            return 'bg-indigo-100 text-indigo-800';
        case 'view_model':
            return 'bg-teal-100 text-teal-800';
        case 'subscription_change':
            return 'bg-yellow-100 text-yellow-800';
        case 'profile_update':
            return 'bg-pink-100 text-pink-800';
        case 'password_change':
            return 'bg-red-100 text-red-800';
        case 'email_verification':
            return 'bg-cyan-100 text-cyan-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

export default function ActivityIndex({ activities, stats, activity_type_counts, filters, activity_types, date_ranges }: Props) {
    const handleFilterChange = (key: string, value: string) => {
        const params = new URLSearchParams(window.location.search);
        if (value === 'all' || value === '') {
            params.delete(key);
        } else {
            params.set(key, value);
        }
        
        router.get(route('activity.index'), Object.fromEntries(params));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatMetadata = (metadata: ActivityMetadata | null) => {
        if (!metadata || typeof metadata !== 'object') return null;
        
        return Object.entries(metadata).map(([key, value]) => (
            <span key={key} className="text-xs text-gray-500">
                {key}: {String(value)}
            </span>
        ));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Activity Log" />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Activity Log</h1>
                        <p className="text-gray-600">Track your account activity and usage history</p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Activity className="w-8 h-8 text-blue-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Total Activities</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.total_activities}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Calendar className="w-8 h-8 text-green-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Recent (7 days)</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.recent_activities}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <LogIn className="w-8 h-8 text-purple-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Logins</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.login_count}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Search className="w-8 h-8 text-orange-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Searches</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.search_count}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Activity Type Breakdown */}
                {Object.keys(activity_type_counts).length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Activity Breakdown</CardTitle>
                            <CardDescription>Activity types for the selected time period</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                                {Object.entries(activity_type_counts).map(([type, count]) => (
                                    <div key={type} className="text-center">
                                        <div className="flex justify-center mb-2">
                                            {getActivityIcon(type)}
                                        </div>
                                        <p className="text-lg font-bold text-gray-900">{count}</p>
                                        <p className="text-xs text-gray-600 capitalize">{type.replace('_', ' ')}</p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Activity Type</label>
                                <Select value={filters.activity_type} onValueChange={(value) => handleFilterChange('activity_type', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select activity type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(activity_types).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div className="flex-1">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                                <Select value={filters.date_range} onValueChange={(value) => handleFilterChange('date_range', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select date range" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(date_ranges).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Activities List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Activity History</CardTitle>
                        <CardDescription>
                            {activities.meta && activities.meta.total > 0
                                ? `Showing ${activities.meta.from} to ${activities.meta.to} of ${activities.meta.total} activities`
                                : 'No activities found'
                            }
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {activities.data && activities.data.length > 0 ? (
                            <div className="space-y-4">
                                {activities.data.map((activity) => (
                                    <div
                                        key={activity.id}
                                        className="p-4 border rounded-lg transition-colors hover:bg-gray-50"
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start space-x-3 flex-1">
                                                {getActivityIcon(activity.activity_type)}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center gap-2 mb-1">
                                                        <h3 className="text-sm font-medium text-gray-900">
                                                            {activity.description}
                                                        </h3>
                                                        <Badge className={`text-xs ${getActivityBadgeColor(activity.activity_type)}`}>
                                                            {activity_types[activity.activity_type] || activity.activity_type}
                                                        </Badge>
                                                    </div>
                                                    <div className="flex items-center text-xs text-gray-500 space-x-4 mb-2">
                                                        <span>{formatDate(activity.created_at)}</span>
                                                        <span>IP: {activity.ip_address}</span>
                                                        {activity.performedBy && (
                                                            <span>By: {activity.performedBy.name}</span>
                                                        )}
                                                    </div>
                                                    {activity.metadata && (
                                                        <div className="flex flex-wrap gap-2">
                                                            {formatMetadata(activity.metadata)}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <Link href={route('activity.show', activity.id)}>
                                                <Button variant="outline" size="sm">
                                                    View Details
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No activities found</h3>
                                <p className="text-gray-600">No activities match your current filters.</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pagination */}
                {activities.meta && activities.meta.last_page > 1 && (
                    <div className="flex justify-center">
                        <div className="flex space-x-1">
                            {activities.links && activities.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? "default" : "outline"}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
