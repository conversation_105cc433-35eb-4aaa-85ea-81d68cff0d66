import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    ArrowLeft,
    Activity, 
    LogIn, 
    LogOut, 
    Search, 
    Eye, 
    Package,
    Smartphone,
    Grid3X3,
    User,
    CreditCard,
    Settings,
    Mail,
    Calendar,
    Globe,
    Monitor     
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface ActivityMetadata {
    [key: string]: string | number | boolean | object | null;
}

interface UserActivityLog {
    id: number;
    user_id: number;
    activity_type: string;
    description: string;
    ip_address: string;
    user_agent: string;
    metadata: ActivityMetadata | null;
    performed_by: number | null;
    created_at: string;
    performedBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Props {
    activity: UserActivityLog;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Activity Log',
        href: '/activity',
    },
    {
        title: 'Activity Details',
        href: '#',
    },
];

const getActivityIcon = (activityType: string) => {
    switch (activityType) {
        case 'login':
            return <LogIn className="w-8 h-8 text-green-500" />;
        case 'logout':
            return <LogOut className="w-8 h-8 text-gray-500" />;
        case 'search':
            return <Search className="w-8 h-8 text-blue-500" />;
        case 'view_part':
            return <Package className="w-8 h-8 text-purple-500" />;
        case 'view_category':
            return <Grid3X3 className="w-8 h-8 text-orange-500" />;
        case 'view_brand':
            return <Smartphone className="w-8 h-8 text-indigo-500" />;
        case 'view_model':
            return <Eye className="w-8 h-8 text-teal-500" />;
        case 'subscription_change':
            return <CreditCard className="w-8 h-8 text-yellow-500" />;
        case 'profile_update':
            return <User className="w-8 h-8 text-pink-500" />;
        case 'password_change':
            return <Settings className="w-8 h-8 text-red-500" />;
        case 'email_verification':
            return <Mail className="w-8 h-8 text-cyan-500" />;
        default:
            return <Activity className="w-8 h-8 text-gray-500" />;
    }
};

const getActivityBadgeColor = (activityType: string) => {
    switch (activityType) {
        case 'login':
            return 'bg-green-100 text-green-800';
        case 'logout':
            return 'bg-gray-100 text-gray-800';
        case 'search':
            return 'bg-blue-100 text-blue-800';
        case 'view_part':
            return 'bg-purple-100 text-purple-800';
        case 'view_category':
            return 'bg-orange-100 text-orange-800';
        case 'view_brand':
            return 'bg-indigo-100 text-indigo-800';
        case 'view_model':
            return 'bg-teal-100 text-teal-800';
        case 'subscription_change':
            return 'bg-yellow-100 text-yellow-800';
        case 'profile_update':
            return 'bg-pink-100 text-pink-800';
        case 'password_change':
            return 'bg-red-100 text-red-800';
        case 'email_verification':
            return 'bg-cyan-100 text-cyan-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getActivityTypeLabel = (activityType: string) => {
    switch (activityType) {
        case 'login':
            return 'Login';
        case 'logout':
            return 'Logout';
        case 'search':
            return 'Search';
        case 'view_part':
            return 'View Part';
        case 'view_category':
            return 'View Category';
        case 'view_brand':
            return 'View Brand';
        case 'view_model':
            return 'View Model';
        case 'subscription_change':
            return 'Subscription Change';
        case 'profile_update':
            return 'Profile Update';
        case 'password_change':
            return 'Password Change';
        case 'email_verification':
            return 'Email Verification';
        default:
            return activityType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
};

export default function ActivityShow({ activity }: Props) {
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        });
    };

    const parseUserAgent = (userAgent: string) => {
        // Simple user agent parsing - in production, you might want to use a library
        const browser = userAgent.includes('Chrome') ? 'Chrome' : 
                       userAgent.includes('Firefox') ? 'Firefox' : 
                       userAgent.includes('Safari') ? 'Safari' : 
                       userAgent.includes('Edge') ? 'Edge' : 'Unknown';
        
        const os = userAgent.includes('Windows') ? 'Windows' : 
                   userAgent.includes('Mac') ? 'macOS' : 
                   userAgent.includes('Linux') ? 'Linux' : 
                   userAgent.includes('Android') ? 'Android' : 
                   userAgent.includes('iOS') ? 'iOS' : 'Unknown';
        
        return { browser, os };
    };

    const { browser, os } = parseUserAgent(activity.user_agent);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Activity: ${activity.description}`} />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('activity.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Activity Log
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Activity Details</h1>
                            <p className="text-gray-600">Detailed information about this activity</p>
                        </div>
                    </div>
                </div>

                {/* Activity Card */}
                <Card className="max-w-4xl">
                    <CardHeader>
                        <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4">
                                {getActivityIcon(activity.activity_type)}
                                <div className="flex-1">
                                    <div className="flex items-center gap-3 mb-2">
                                        <CardTitle className="text-xl">{activity.description}</CardTitle>
                                        <Badge className={`${getActivityBadgeColor(activity.activity_type)}`}>
                                            {getActivityTypeLabel(activity.activity_type)}
                                        </Badge>
                                    </div>
                                    <CardDescription>
                                        Activity performed on {formatDate(activity.created_at)}
                                    </CardDescription>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {/* Activity Details */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            {/* Timing Information */}
                            <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <Calendar className="w-4 h-4 mr-2" />
                                    Timing
                                </h4>
                                <div className="bg-white border rounded-lg p-3">
                                    <div className="flex justify-between mb-2">
                                        <span className="text-sm text-gray-600">Date & Time:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {formatDate(activity.created_at)}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Activity ID:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            #{activity.id}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Location & Device Information */}
                            <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <Globe className="w-4 h-4 mr-2" />
                                    Location & Device
                                </h4>
                                <div className="bg-white border rounded-lg p-3 space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">IP Address:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {activity.ip_address}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Browser:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {browser}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Operating System:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {os}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Performed By Information */}
                        {activity.performedBy && (
                            <div className="mb-6">
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <User className="w-4 h-4 mr-2" />
                                    Performed By
                                </h4>
                                <div className="bg-white border rounded-lg p-3">
                                    <p className="font-medium text-gray-900">{activity.performedBy.name}</p>
                                    <p className="text-sm text-gray-600">{activity.performedBy.email}</p>
                                    <p className="text-xs text-gray-500 mt-1">
                                        This activity was performed by an administrator
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* Metadata */}
                        {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                            <div className="mb-6">
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <Settings className="w-4 h-4 mr-2" />
                                    Additional Details
                                </h4>
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {Object.entries(activity.metadata).map(([key, value]) => (
                                            <div key={key} className="flex justify-between">
                                                <span className="text-sm text-gray-600 capitalize">
                                                    {key.replace('_', ' ')}:
                                                </span>
                                                <span className="text-sm font-medium text-gray-900">
                                                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* User Agent Details */}
                        <div className="mb-6">
                            <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                <Monitor className="w-4 h-4 mr-2" />
                                User Agent
                            </h4>
                            <div className="bg-gray-50 rounded-lg p-3">
                                <p className="text-xs text-gray-600 font-mono break-all">
                                    {activity.user_agent}
                                </p>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="pt-6 border-t">
                            <div className="flex justify-between items-center">
                                <Link href={route('activity.index')}>
                                    <Button variant="outline">
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Back to Activity Log
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
