import { Head, router } from '@inertiajs/react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import {
    Shield,
    ShieldCheck,
    ShieldX,
    Mail,
    Clock,
    AlertTriangle,
    CheckCircle,
    XCircle,
    Key,
    Lock,
    Unlock,
    Send
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface OtpStatus {
    has_otp_pending: boolean;
    otp_expires_at: string | null;
    remaining_attempts: number;
    is_locked_out: boolean;
    lockout_remaining_minutes: number;
    two_factor_enabled: boolean;
}

interface Verification {
    id: number;
    type: string;
    description: string;
    success: boolean;
    ip_address: string;
    created_at: string;
}

interface Props {
    user_2fa_status: OtpStatus;
    two_factor_enabled: boolean;
    two_factor_confirmed_at: string | null;
    recent_verifications: Verification[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Settings',
        href: '/settings/profile',
    },
    {
        title: 'Two-Factor Authentication',
        href: '/settings/two-factor',
    },
];

export default function UserTwoFactorIndex({ 
    user_2fa_status, 
    two_factor_enabled, 
    two_factor_confirmed_at, 
    recent_verifications 
}: Props) {
    const [isEnabling, setIsEnabling] = useState(false);
    const [isDisabling, setIsDisabling] = useState(false);
    const [isSendingTest, setIsSendingTest] = useState(false);
    const [isVerifyingTest, setIsVerifyingTest] = useState(false);
    const [testOtpCode, setTestOtpCode] = useState('');
    const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

    const handleEnable2FA = async () => {
        setIsEnabling(true);
        setMessage(null);
        
        try {
            const response = await fetch(route('settings.two-factor.enable'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });
            
            const data = await response.json();
            
            if (response.ok) {
                setMessage({ type: 'success', text: data.message });
                router.reload();
            } else {
                setMessage({ type: 'error', text: data.error || 'Failed to enable 2FA' });
            }
        } catch {
            setMessage({ type: 'error', text: 'An error occurred while enabling 2FA' });
        } finally {
            setIsEnabling(false);
        }
    };

    const handleDisable2FA = async () => {
        if (!confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {
            return;
        }
        
        setIsDisabling(true);
        setMessage(null);
        
        try {
            const response = await fetch(route('settings.two-factor.disable'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });
            
            const data = await response.json();
            
            if (response.ok) {
                setMessage({ type: 'success', text: data.message });
                router.reload();
            } else {
                setMessage({ type: 'error', text: data.error || 'Failed to disable 2FA' });
            }
        } catch {
            setMessage({ type: 'error', text: 'An error occurred while disabling 2FA' });
        } finally {
            setIsDisabling(false);
        }
    };

    const handleSendTestOtp = async () => {
        setIsSendingTest(true);
        setMessage(null);
        
        try {
            const response = await fetch(route('settings.two-factor.send-test'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });
            
            const data = await response.json();
            
            if (response.ok) {
                setMessage({ type: 'success', text: data.message });
            } else {
                setMessage({ type: 'error', text: data.error || 'Failed to send test code' });
            }
        } catch {
            setMessage({ type: 'error', text: 'An error occurred while sending test code' });
        } finally {
            setIsSendingTest(false);
        }
    };

    const handleVerifyTestOtp = async () => {
        if (!testOtpCode || testOtpCode.length !== 6) {
            setMessage({ type: 'error', text: 'Please enter a 6-digit verification code' });
            return;
        }
        
        setIsVerifyingTest(true);
        setMessage(null);
        
        try {
            const response = await fetch(route('settings.two-factor.verify-test'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ otp_code: testOtpCode }),
            });
            
            const data = await response.json();
            
            if (response.ok) {
                setMessage({ type: 'success', text: data.message });
                setTestOtpCode('');
            } else {
                setMessage({ type: 'error', text: data.error || 'Invalid verification code' });
            }
        } catch {
            setMessage({ type: 'error', text: 'An error occurred while verifying code' });
        } finally {
            setIsVerifyingTest(false);
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getVerificationIcon = (success: boolean) => {
        if (success) {
            return <CheckCircle className="w-4 h-4 text-green-500" />;
        } else {
            return <XCircle className="w-4 h-4 text-red-500" />;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Two-Factor Authentication" />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Two-Factor Authentication</h1>
                        <p className="text-gray-600">Secure your account with email-based verification</p>
                    </div>
                </div>

                {/* Status Card */}
                <Card className={`border-2 ${
                    two_factor_enabled ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'
                }`}>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                {two_factor_enabled ? (
                                    <ShieldCheck className="w-8 h-8 text-green-500" />
                                ) : (
                                    <ShieldX className="w-8 h-8 text-yellow-500" />
                                )}
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">
                                        Two-Factor Authentication is {two_factor_enabled ? 'Enabled' : 'Disabled'}
                                    </h2>
                                    <p className="text-gray-700">
                                        {two_factor_enabled 
                                            ? 'Your account is protected with email-based verification'
                                            : 'Enable 2FA to add an extra layer of security to your account'
                                        }
                                    </p>
                                    {two_factor_confirmed_at && (
                                        <p className="text-sm text-gray-600 mt-1">
                                            Enabled on {formatDate(two_factor_confirmed_at)}
                                        </p>
                                    )}
                                </div>
                            </div>
                            <Badge variant={two_factor_enabled ? "default" : "secondary"}>
                                {two_factor_enabled ? 'Active' : 'Inactive'}
                            </Badge>
                        </div>
                    </CardContent>
                </Card>

                {/* Message Display */}
                {message && (
                    <Card className={`border-2 ${
                        message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                    }`}>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-3">
                                {message.type === 'success' ? (
                                    <CheckCircle className="w-5 h-5 text-green-500" />
                                ) : (
                                    <XCircle className="w-5 h-5 text-red-500" />
                                )}
                                <p className={`text-sm ${
                                    message.type === 'success' ? 'text-green-700' : 'text-red-700'
                                }`}>
                                    {message.text}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Lockout Warning */}
                {user_2fa_status.is_locked_out && (
                    <Card className="border-red-200 bg-red-50">
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-3">
                                <AlertTriangle className="w-5 h-5 text-red-500" />
                                <div>
                                    <h3 className="text-sm font-medium text-red-900">Account Temporarily Locked</h3>
                                    <p className="text-sm text-red-700">
                                        Too many failed verification attempts. Please wait {user_2fa_status.lockout_remaining_minutes} minutes before trying again.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Main Content */}
                <Tabs defaultValue="settings" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="settings">Settings</TabsTrigger>
                        <TabsTrigger value="test">Test & Verify</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="settings" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Key className="w-5 h-5 mr-2" />
                                    2FA Settings
                                </CardTitle>
                                <CardDescription>
                                    Manage your two-factor authentication settings
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {!two_factor_enabled ? (
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-900 mb-2">Enable Two-Factor Authentication</h3>
                                        <p className="text-gray-600 mb-4">
                                            Add an extra layer of security to your account. When enabled, you'll receive verification codes via email for sensitive actions.
                                        </p>
                                        <Button 
                                            onClick={handleEnable2FA}
                                            disabled={isEnabling}
                                            className="flex items-center"
                                        >
                                            {isEnabling ? (
                                                <>
                                                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                    Enabling...
                                                </>
                                            ) : (
                                                <>
                                                    <Lock className="w-4 h-4 mr-2" />
                                                    Enable 2FA
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                ) : (
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-900 mb-2">Disable Two-Factor Authentication</h3>
                                        <p className="text-gray-600 mb-4">
                                            Disabling 2FA will make your account less secure. You can re-enable it at any time.
                                        </p>
                                        <Button 
                                            variant="destructive"
                                            onClick={handleDisable2FA}
                                            disabled={isDisabling}
                                            className="flex items-center"
                                        >
                                            {isDisabling ? (
                                                <>
                                                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                    Disabling...
                                                </>
                                            ) : (
                                                <>
                                                    <Unlock className="w-4 h-4 mr-2" />
                                                    Disable 2FA
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>
                    
                    <TabsContent value="test" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Mail className="w-5 h-5 mr-2" />
                                    Test Verification
                                </CardTitle>
                                <CardDescription>
                                    Test your 2FA setup to ensure it's working correctly
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {!two_factor_enabled ? (
                                    <div className="text-center py-8">
                                        <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-gray-900 mb-2">2FA Not Enabled</h3>
                                        <p className="text-gray-600">
                                            Enable two-factor authentication first to test verification codes.
                                        </p>
                                    </div>
                                ) : (
                                    <>
                                        <div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">Send Test Code</h3>
                                            <p className="text-gray-600 mb-4">
                                                Send a test verification code to your email to ensure 2FA is working properly.
                                            </p>
                                            <Button 
                                                onClick={handleSendTestOtp}
                                                disabled={isSendingTest || user_2fa_status.is_locked_out}
                                                variant="outline"
                                                className="flex items-center"
                                            >
                                                {isSendingTest ? (
                                                    <>
                                                        <div className="w-4 h-4 mr-2 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                                                        Sending...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Send className="w-4 h-4 mr-2" />
                                                        Send Test Code
                                                    </>
                                                )}
                                            </Button>
                                        </div>

                                        {user_2fa_status.has_otp_pending && (
                                            <div>
                                                <h3 className="text-lg font-medium text-gray-900 mb-2">Verify Test Code</h3>
                                                <p className="text-gray-600 mb-4">
                                                    Enter the 6-digit code sent to your email.
                                                </p>
                                                <div className="flex gap-4">
                                                    <div className="flex-1">
                                                        <Label htmlFor="test_otp_code">Verification Code</Label>
                                                        <Input
                                                            id="test_otp_code"
                                                            type="text"
                                                            value={testOtpCode}
                                                            onChange={(e) => setTestOtpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                                                            placeholder="000000"
                                                            maxLength={6}
                                                            className="text-center text-lg tracking-widest"
                                                        />
                                                    </div>
                                                    <div className="flex items-end">
                                                        <Button 
                                                            onClick={handleVerifyTestOtp}
                                                            disabled={isVerifyingTest || testOtpCode.length !== 6}
                                                        >
                                                            {isVerifyingTest ? (
                                                                <>
                                                                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                                    Verifying...
                                                                </>
                                                            ) : (
                                                                'Verify'
                                                            )}
                                                        </Button>
                                                    </div>
                                                </div>
                                                {user_2fa_status.otp_expires_at && (
                                                    <p className="text-xs text-gray-500 mt-2">
                                                        Code expires at {formatDate(user_2fa_status.otp_expires_at)}
                                                    </p>
                                                )}
                                            </div>
                                        )}
                                    </>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>

                {/* Recent Activity */}
                {recent_verifications.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Clock className="w-5 h-5 mr-2" />
                                Recent Activity
                            </CardTitle>
                            <CardDescription>
                                Your recent 2FA verification attempts
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {recent_verifications.map((verification) => (
                                    <div key={verification.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center space-x-3">
                                            {getVerificationIcon(verification.success)}
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">
                                                    {verification.description}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {formatDate(verification.created_at)} • {verification.ip_address}
                                                </p>
                                            </div>
                                        </div>
                                        <Badge variant={verification.success ? "default" : "destructive"}>
                                            {verification.success ? 'Success' : 'Failed'}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
