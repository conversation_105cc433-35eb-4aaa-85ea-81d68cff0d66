import { Head, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
    Search,
    Smartphone,
    ArrowRight,
    Grid3X3,
    List
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url?: string;
    country?: string;
    models_count: number;
}

interface Props {
    brands: Brand[];
}

export default function BrandsList({ brands }: Props) {
    const [searchQuery, setSearchQuery] = useState('');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

    // Filter brands based on search query
    const filteredBrands = brands.filter(brand =>
        brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (brand.country && brand.country.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    const BrandCard = ({ brand }: { brand: Brand }) => (
        <Card className="hover:shadow-lg transition-shadow group">
            <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            {brand.logo_url ? (
                                <img 
                                    src={brand.logo_url} 
                                    alt={brand.name}
                                    className="w-8 h-8 object-contain"
                                />
                            ) : (
                                <Smartphone className="w-6 h-6 text-orange-600" />
                            )}
                        </div>
                        <div>
                            <h3 className="font-semibold text-lg text-gray-900 mb-1">
                                {brand.name}
                            </h3>
                            <div className="flex items-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                    {brand.models_count} models
                                </Badge>
                                {brand.country && (
                                    <Badge variant="secondary" className="text-xs">
                                        {brand.country}
                                    </Badge>
                                )}
                            </div>
                        </div>
                    </div>
                    <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-orange-600 transition-colors" />
                </div>

                <Link href={route('search.brand', brand.slug || brand.id)}>
                    <Button className="w-full">
                        <Search className="w-4 h-4 mr-2" />
                        Search {brand.name} Parts
                    </Button>
                </Link>
            </CardContent>
        </Card>
    );

    const BrandListItem = ({ brand }: { brand: Brand }) => (
        <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                            {brand.logo_url ? (
                                <img 
                                    src={brand.logo_url} 
                                    alt={brand.name}
                                    className="w-6 h-6 object-contain"
                                />
                            ) : (
                                <Smartphone className="w-5 h-5 text-orange-600" />
                            )}
                        </div>
                        <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-1">
                                {brand.name}
                            </h3>
                            <div className="flex items-center gap-3 text-sm text-gray-600">
                                <Badge variant="outline" className="text-xs">
                                    {brand.models_count} models
                                </Badge>
                                {brand.country && (
                                    <Badge variant="secondary" className="text-xs">
                                        {brand.country}
                                    </Badge>
                                )}
                            </div>
                        </div>
                    </div>
                    <Link href={route('search.brand', brand.slug || brand.id)}>
                        <Button>
                            <Search className="w-4 h-4 mr-2" />
                            Search
                        </Button>
                    </Link>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout>
            <Head title="Search in Brand" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center gap-3 mb-4">
                            <Smartphone className="w-8 h-8 text-orange-600" />
                            <h1 className="text-3xl font-bold text-gray-900">
                                Search in Brand
                            </h1>
                        </div>
                        <p className="text-gray-600 text-lg">
                            Select a brand to search for parts from that specific manufacturer
                        </p>
                    </div>

                    {/* Search and View Controls */}
                    <Card className="mb-6">
                        <CardContent className="p-6">
                            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
                                <div className="flex-1 relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                    <Input
                                        type="text"
                                        placeholder="Search brands..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant={viewMode === 'grid' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setViewMode('grid')}
                                    >
                                        <Grid3X3 className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'list' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setViewMode('list')}
                                    >
                                        <List className="w-4 h-4" />
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Results Header */}
                    <div className="flex items-center justify-between mb-6">
                        <div>
                            <h2 className="text-xl font-semibold text-gray-900">
                                Available Brands
                            </h2>
                            <p className="text-gray-600">
                                {filteredBrands.length} brands found
                            </p>
                        </div>
                    </div>

                    {/* Brands List */}
                    {filteredBrands.length > 0 ? (
                        <div className={
                            viewMode === 'grid'
                                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                                : 'space-y-4'
                        }>
                            {filteredBrands.map((brand) => (
                                viewMode === 'grid'
                                    ? <BrandCard key={brand.id} brand={brand} />
                                    : <BrandListItem key={brand.id} brand={brand} />
                            ))}
                        </div>
                    ) : (
                        <Card>
                            <CardContent className="text-center py-12">
                                <Smartphone className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    No brands found
                                </h3>
                                <p className="text-gray-600 mb-6">
                                    Try adjusting your search terms
                                </p>
                                <Button onClick={() => setSearchQuery('')}>
                                    <Search className="w-4 h-4 mr-2" />
                                    Clear Search
                                </Button>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
