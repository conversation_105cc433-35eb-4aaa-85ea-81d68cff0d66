import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    ArrowLeft,
    Building,
    Search,
    MapPin,
    Globe,
    ExternalLink
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country: string | null;
    website: string | null;
    is_active: boolean;
    models: Array<{
        id: number;
        name: string;
        slug?: string;
        model_number: string | null;
        release_year: number | null;
        parts: Array<{
            id: number;
            name: string;
            slug?: string;
            category: {
                id: number;
                name: string;
            };
        }>;
    }>;
}

interface Props {
    brand: Brand;
}

export default function BrandDetails({ brand }: Props) {
    return (
        <AppLayout>
            <Head title={brand.name} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Breadcrumb */}
                    <div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
                        <Link href={route('search.index')} className="hover:text-gray-900">
                            Search
                        </Link>
                        <span>/</span>
                        <span className="text-gray-900">{brand.name}</span>
                    </div>

                    <div className="grid lg:grid-cols-3 gap-8">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Header */}
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4 mb-4">
                                        {brand.logo_url && (
                                            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                                                <img 
                                                    src={brand.logo_url} 
                                                    alt={`${brand.name} logo`}
                                                    className="w-full h-full object-contain"
                                                />
                                            </div>
                                        )}
                                        <div className="flex-1">
                                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                                {brand.name}
                                            </h1>
                                            <div className="flex items-center gap-4 text-sm text-gray-600">
                                                {brand.country && (
                                                    <span className="flex items-center gap-1">
                                                        <MapPin className="w-4 h-4" />
                                                        {brand.country}
                                                    </span>
                                                )}
                                                <Badge variant={brand.is_active ? "default" : "secondary"}>
                                                    {brand.is_active ? "Active" : "Inactive"}
                                                </Badge>
                                            </div>
                                        </div>
                                        {brand.website && (
                                            <Link href={brand.website} target="_blank" rel="noopener noreferrer">
                                                <Button variant="outline" size="sm">
                                                    <Globe className="w-4 h-4 mr-2" />
                                                    Website
                                                    <ExternalLink className="w-3 h-3 ml-1" />
                                                </Button>
                                            </Link>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>


                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Brand Info */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Building className="w-5 h-5" />
                                        Brand Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {brand.country && (
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Country:</span>
                                                <span className="ml-2 text-gray-900">{brand.country}</span>
                                            </div>
                                        )}
                                        {brand.website && (
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Website:</span>
                                                <a
                                                    href={brand.website}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="ml-2 text-blue-600 hover:text-blue-800"
                                                >
                                                    Visit Website
                                                </a>
                                            </div>
                                        )}
                                        <div>
                                            <span className="text-sm font-medium text-gray-600">Status:</span>
                                            <span className="ml-2 text-gray-900">
                                                {brand.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Actions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Actions</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <Link href={route('search.brand', brand.slug || brand.id)}>
                                            <Button className="w-full">
                                                <Search className="w-4 h-4 mr-2" />
                                                Search {brand.name} Parts
                                            </Button>
                                        </Link>
                                        <Link href={route('search.index')}>
                                            <Button className="w-full" variant="outline">
                                                <ArrowLeft className="w-4 h-4 mr-2" />
                                                Back to Search
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
