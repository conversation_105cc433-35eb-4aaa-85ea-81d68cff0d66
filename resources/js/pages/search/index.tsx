import { Head, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Smartphone,
    Package,
    Tag,
    TrendingUp
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { UnifiedSearchInterface } from '@/components/unified-search-interface';

interface Category {
    id: number;
    name: string;
}

interface Brand {
    id: number;
    name: string;
}

interface Filters {
    categories: Category[];
    brands: Brand[];
    manufacturers: string[];
    release_years: number[];
}



interface Props {
    filters: Filters;
}

export default function SearchIndex({ filters }: Props) {
    const [searchQuery, setSearchQuery] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const popularSearches = [
        { label: 'iPhone Display', type: 'part' },
        { label: 'Samsung Battery', type: 'part' },
        { label: 'Camera Module', type: 'category' },
        { label: 'Charging IC', type: 'category' },
        { label: 'OnePlus', type: 'brand' },
        { label: 'Xiaomi', type: 'brand' },
    ];

    return (
        <AppLayout>
            <Head title="Search Parts" />

            <div className="py-12">
                <div className="max-w-6xl mx-auto sm:px-6 lg:px-8">
                    {/* Hero Section */}
                    <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Find Mobile Parts
                        </h1>
                        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                            Search through our comprehensive database of mobile device parts and components
                        </p>
                    </div>

                    {/* Search Form */}
                    <Card className="mb-8">
                        <CardContent className="p-6">
                            <UnifiedSearchInterface
                                searchQuery={searchQuery}
                                setSearchQuery={setSearchQuery}
                                isAuthenticated={true}
                                isLoading={isLoading}
                                setIsLoading={setIsLoading}
                                showFilters={true}
                                showSuggestions={true}
                                size="lg"
                                filters={filters}
                            />
                        </CardContent>
                    </Card>

                    {/* Popular Searches */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="w-5 h-5" />
                                Popular Searches
                            </CardTitle>
                            <CardDescription>
                                Quick access to commonly searched items
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-2">
                                {popularSearches.map((search, index) => (
                                    <Button
                                        key={index}
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                            setSearchQuery(search.label);
                                            setSearchType(search.type);
                                        }}
                                        className="text-sm"
                                    >
                                        {search.label}
                                    </Button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Package className="w-12 h-12 mx-auto mb-4 text-green-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">10,000+</h3>
                                <p className="text-gray-600">Parts Available</p>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Smartphone className="w-12 h-12 mx-auto mb-4 text-blue-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">500+</h3>
                                <p className="text-gray-600">Mobile Models</p>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Tag className="w-12 h-12 mx-auto mb-4 text-purple-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">50+</h3>
                                <p className="text-gray-600">Categories</p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
