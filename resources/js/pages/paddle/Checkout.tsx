import React, { useEffect, useState } from 'react';
import { Head } from '@inertiajs/react';
import { usePaddle, PaddleProvider } from '@/contexts/PaddleContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Props {
    transaction_id: string;
}

function CheckoutContent({ transaction_id }: Props) {
    const { isLoaded, error, config } = usePaddle();
    const [isLoading, setIsLoading] = useState(true);
    const [checkoutError, setCheckoutError] = useState<string | null>(null);

    useEffect(() => {
        console.log('Checkout component mounted', {
            transaction_id,
            isLoaded,
            error,
            config,
            hasConfig: !!config
        });

        // If there's an initial error, stop loading
        if (error) {
            console.log('Paddle error detected:', error);
            setIsLoading(false);
            return;
        }

        // Wait for Paddle to be loaded
        if (!isLoaded) {
            console.log('Paddle not loaded yet, waiting...');
            return;
        }

        console.log('Paddle loaded, checking configuration:', config);

        // Check if we're in development mode
        if (config && (config.development_mode || config.mock_mode)) {
            console.log('Development mode detected, redirecting to mock checkout');
            if (transaction_id) {
                // Redirect to mock checkout page
                window.location.href = `/paddle/mock-checkout?transaction=${transaction_id}`;
                return;
            } else {
                setCheckoutError('No transaction ID provided for development checkout.');
                setIsLoading(false);
                return;
            }
        }

        // If we reach here, we're in production mode with real Paddle
        // The actual Paddle checkout would be handled here
        console.log('Production mode - would initialize real Paddle checkout');
        setIsLoading(false);
    }, [isLoaded, error, config, transaction_id]);

    if (checkoutError) {
        return (
            <AppLayout>
                <Head title="Checkout Error" />
                <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center">
                    <Card className="w-full max-w-md">
                        <CardHeader className="text-center">
                            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                                <AlertCircle className="w-6 h-6 text-red-600" />
                            </div>
                            <CardTitle className="text-red-900">Checkout Error</CardTitle>
                            <CardDescription>{checkoutError}</CardDescription>
                        </CardHeader>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    if (isLoading) {
        return (
            <AppLayout>
                <Head title="Loading Checkout..." />
                <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center">
                    <Card className="w-full max-w-md">
                        <CardHeader className="text-center">
                            <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                <Loader2 className="w-6 h-6 text-blue-600 animate-spin" />
                            </div>
                            <CardTitle>Loading Checkout...</CardTitle>
                            <CardDescription>
                                Please wait while we prepare your checkout session.
                            </CardDescription>
                        </CardHeader>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    // Production mode checkout UI would go here
    return (
        <AppLayout>
            <Head title="Checkout" />
            <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center">
                <Card className="w-full max-w-md">
                    <CardHeader className="text-center">
                        <CardTitle>Paddle Checkout</CardTitle>
                        <CardDescription>
                            Transaction ID: {transaction_id}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <p className="text-center text-muted-foreground">
                            Production checkout would be initialized here.
                        </p>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}

export default function Checkout(props: Props) {
    return (
        <PaddleProvider>
            <CheckoutContent {...props} />
        </PaddleProvider>
    );
}
