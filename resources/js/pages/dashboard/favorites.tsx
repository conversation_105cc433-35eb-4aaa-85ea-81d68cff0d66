import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Heart,
    Search,
    Package,
    Smartphone,
    Eye,
    Grid3X3,
    List
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface Favorite {
    id: number;
    favoritable_type: string;
    favoritable_id: number;
    created_at: string;
    favoritable: Part | MobileModel | null;
}

interface Part {
    id: number;
    name: string;
    slug?: string;
    part_number?: string;
    manufacturer?: string;
    description?: string;
    category?: {
        id: number;
        name: string;
        slug?: string;
    } | null;
    models?: Array<{
        id: number;
        name: string;
        slug?: string;
        brand?: {
            id: number;
            name: string;
            slug?: string;
        } | null;
    }>;
}

interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number?: string;
    release_year?: number;
    brand?: {
        id: number;
        name: string;
        slug?: string;
    } | null;
}

interface PaginatedFavorites {
    data: Favorite[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    favorites: PaginatedFavorites;
}

export default function FavoritesPage({ favorites }: Props) {
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [filterType, setFilterType] = useState<'all' | 'parts' | 'models'>('all');

    const handleRemoveFavorite = (favorite: Favorite) => {
        if (confirm('Are you sure you want to remove this item from your favorites?')) {
            const type = favorite.favoritable_type.includes('Part') ? 'part' : 'model';
            const itemName = favorite.favoritable?.name || 'Item';

            router.delete(route('dashboard.remove-favorite'), {
                data: {
                    type: type,
                    id: favorite.favoritable_id,
                },
                onSuccess: () => {
                    // Success message will be shown via backend flash message
                    // Refresh the page to update the list
                    router.reload();
                },
                onError: (errors) => {
                    // Error messages will be shown via backend flash message
                }
            });
        }
    };

    const filteredFavorites = favorites.data.filter(favorite => {
        // First filter out any favorites with null favoritable items
        if (!favorite.favoritable) return false;

        if (filterType === 'all') return true;
        if (filterType === 'parts') return favorite.favoritable_type.includes('Part');
        if (filterType === 'models') return favorite.favoritable_type.includes('Model');
        return true;
    });

    const FavoriteCard = ({ favorite }: { favorite: Favorite }) => {
        const isPart = favorite.favoritable_type.includes('Part');
        const item = favorite.favoritable;

        // Safety check - if item is null, don't render
        if (!item) {
            return null;
        }

        return (
            <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                                {isPart ? (
                                    <Package className="w-4 h-4 text-blue-500" />
                                ) : (
                                    <Smartphone className="w-4 h-4 text-green-500" />
                                )}
                                <Badge variant="outline" className="text-xs">
                                    {isPart ? 'Part' : 'Model'}
                                </Badge>
                            </div>
                            <h3 className="font-semibold text-lg text-gray-900 mb-1">
                                {item?.name || 'Unknown Item'}
                            </h3>
                            
                            {isPart && (item as Part).part_number && (
                                <p className="text-sm text-gray-600 mb-1">
                                    Part #: {(item as Part).part_number}
                                </p>
                            )}
                            
                            {isPart && (item as Part).manufacturer && (
                                <p className="text-sm text-gray-600 mb-2">
                                    by {(item as Part).manufacturer}
                                </p>
                            )}
                            
                            {!isPart && (item as MobileModel).brand?.name && (
                                <p className="text-sm text-gray-600 mb-2">
                                    {(item as MobileModel).brand?.name}
                                </p>
                            )}
                        </div>
                        <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleRemoveFavorite(favorite)}
                            className="text-red-500 hover:text-red-700"
                        >
                            <Heart className="w-4 h-4 fill-current" />
                        </Button>
                    </div>

                    <div className="mb-3">
                        {isPart && (item as Part).category?.name && (
                            <Badge variant="outline" className="mb-2">
                                {(item as Part).category?.name}
                            </Badge>
                        )}
                        
                        {isPart && (item as Part).description && (
                            <p className="text-sm text-gray-600 line-clamp-2">
                                {(item as Part).description}
                            </p>
                        )}
                        
                        {!isPart && (item as MobileModel).model_number && (
                            <p className="text-sm text-gray-600">
                                Model: {(item as MobileModel).model_number}
                            </p>
                        )}
                        
                        {!isPart && (item as MobileModel).release_year && (
                            <p className="text-sm text-gray-600">
                                Released: {(item as MobileModel).release_year}
                            </p>
                        )}
                    </div>

                    {isPart && (item as Part).models && (item as Part).models!.length > 0 && (
                        <div className="mb-4">
                            <p className="text-xs text-gray-500 mb-1">Compatible with:</p>
                            <div className="flex flex-wrap gap-1">
                                {(item as Part).models!.slice(0, 3).map((model) => (
                                    <Badge key={model.id} variant="secondary" className="text-xs">
                                        {model.brand?.name || 'Unknown'} {model.name}
                                    </Badge>
                                ))}
                                {(item as Part).models!.length > 3 && (
                                    <Badge variant="secondary" className="text-xs">
                                        +{(item as Part).models!.length - 3} more
                                    </Badge>
                                )}
                            </div>
                        </div>
                    )}

                    <div className="flex justify-between items-center">
                        <Link href={isPart ? route('parts.show', (item as Part).slug || item.id) : route('models.show', (item as MobileModel).slug || item.id)}>
                            <Button size="sm">
                                <Eye className="w-4 h-4 mr-2" />
                                View Details
                            </Button>
                        </Link>
                        <p className="text-xs text-gray-500">
                            Added {new Date(favorite.created_at).toLocaleDateString()}
                        </p>
                    </div>
                </CardContent>
            </Card>
        );
    };

    const FavoriteListItem = ({ favorite }: { favorite: Favorite }) => {
        const isPart = favorite.favoritable_type.includes('Part');
        const item = favorite.favoritable;

        // Safety check - if item is null, don't render
        if (!item) {
            return null;
        }

        return (
            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <div className="flex items-start gap-4">
                                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                    {isPart ? (
                                        <Package className="w-8 h-8 text-gray-400" />
                                    ) : (
                                        <Smartphone className="w-8 h-8 text-gray-400" />
                                    )}
                                </div>
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                        <h3 className="font-semibold text-lg text-gray-900">
                                            {item?.name || 'Unknown Item'}
                                        </h3>
                                        <Badge variant="outline" className="text-xs">
                                            {isPart ? 'Part' : 'Model'}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                        {isPart && (item as Part).part_number && (
                                            <span>Part #: {(item as Part).part_number}</span>
                                        )}
                                        {isPart && (item as Part).manufacturer && (
                                            <span>by {(item as Part).manufacturer}</span>
                                        )}
                                        {!isPart && (item as MobileModel).brand?.name && (
                                            <span>{(item as MobileModel).brand?.name}</span>
                                        )}
                                        {isPart && (item as Part).category?.name && (
                                            <Badge variant="outline">{(item as Part).category?.name}</Badge>
                                        )}
                                    </div>
                                    <p className="text-xs text-gray-500">
                                        Added {new Date(favorite.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleRemoveFavorite(favorite)}
                                className="text-red-500 hover:text-red-700"
                            >
                                <Heart className="w-4 h-4 fill-current" />
                            </Button>
                            <Link href={isPart ? route('parts.show', (item as Part).slug || item.id) : route('models.show', (item as MobileModel).slug || item.id)}>
                                <Button size="sm">
                                    <Eye className="w-4 h-4 mr-2" />
                                    View Details
                                </Button>
                            </Link>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    };

    return (
        <AppLayout>
            <Head title="My Favorites" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">My Favorites</h1>
                            <p className="text-gray-600">
                                {favorites.total} saved items
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <select 
                                value={filterType} 
                                onChange={(e) => setFilterType(e.target.value as 'all' | 'parts' | 'models')}
                                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                            >
                                <option value="all">All Items</option>
                                <option value="parts">Parts Only</option>
                                <option value="models">Models Only</option>
                            </select>
                            <Button
                                variant={viewMode === 'grid' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('grid')}
                            >
                                <Grid3X3 className="w-4 h-4" />
                            </Button>
                            <Button
                                variant={viewMode === 'list' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('list')}
                            >
                                <List className="w-4 h-4" />
                            </Button>
                        </div>
                    </div>

                    {/* Content */}
                    {filteredFavorites.length > 0 ? (
                        <div className={
                            viewMode === 'grid' 
                                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                                : 'space-y-4'
                        }>
                            {filteredFavorites.map((favorite) => (
                                viewMode === 'grid' 
                                    ? <FavoriteCard key={favorite.id} favorite={favorite} />
                                    : <FavoriteListItem key={favorite.id} favorite={favorite} />
                            ))}
                        </div>
                    ) : (
                        <Card>
                            <CardContent className="text-center py-12">
                                <Heart className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    No favorites yet
                                </h3>
                                <p className="text-gray-600 mb-6">
                                    Start adding parts and models to your favorites while browsing
                                </p>
                                <Link href={route('search.index')}>
                                    <Button>
                                        <Search className="w-4 h-4 mr-2" />
                                        Start Searching
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
