import { Head, <PERSON>, router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    History,
    Search,
    Calendar,
    Filter,
    Trash2,
    CheckCircle,
    XCircle,
    Clock,
    TrendingUp,
    RefreshCw,
    BarChart3,
    Target,
    Activity,
    ArrowUpRight,
    Download,
    Settings,
    Eye,
    ChevronDown,
    MoreHorizontal,
    AlertTriangle,
    Zap
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface SearchRecord {
    id: number;
    search_query: string;
    search_type: string;
    results_count: number;
    created_at: string;
}

interface PaginatedSearches {
    data: SearchRecord[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface SearchStats {
    total_searches: number;
    successful_searches: number;
    recent_searches: number;
    most_searched_term: string;
}

interface Props {
    searches: PaginatedSearches;
    stats: SearchStats;
    filters: {
        type: string;
        range: string;
    };
    search_types: Record<string, string>;
    date_ranges: Record<string, string>;
}

export default function HistoryPage({ searches, stats, filters, search_types, date_ranges }: Props) {
    const [selectedType, setSelectedType] = useState(filters.type);
    const [selectedRange, setSelectedRange] = useState(filters.range);

    const handleFilterChange = () => {
        const params = new URLSearchParams();
        if (selectedType !== 'all') params.set('type', selectedType);
        if (selectedRange !== '30d') params.set('range', selectedRange);
        
        const url = route('dashboard.history') + (params.toString() ? '?' + params.toString() : '');
        router.get(url);
    };

    const handleClearHistory = (range: string = 'all') => {
        const message = range === 'all' 
            ? 'Are you sure you want to clear your entire search history? This action cannot be undone.'
            : `Are you sure you want to clear your search history from the ${date_ranges[range]}? This action cannot be undone.`;
            
        if (confirm(message)) {
            router.delete(route('dashboard.clear-history'), {
                data: { range },
                onSuccess: () => {
                    router.reload();
                },
                onError: (errors) => {
                    console.error('Error clearing history:', errors);
                }
            });
        }
    };

    const handleRepeatSearch = (search: SearchRecord) => {
        const params = new URLSearchParams({
            q: search.search_query,
            type: search.search_type,
        });
        
        router.get(route('search.results') + '?' + params.toString());
    };

    const getSuccessRate = () => {
        return stats.total_searches > 0 
            ? Math.round((stats.successful_searches / stats.total_searches) * 100)
            : 0;
    };

    const formatSearchType = (type: string) => {
        return search_types[type] || type.charAt(0).toUpperCase() + type.slice(1);
    };

    const SearchItem = ({ search }: { search: SearchRecord }) => (
        <Card className="group shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm hover:border-primary/30 hover:shadow-xl transition-all duration-300">
            <CardContent className="p-2">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                        {/* Search Icon & Status */}
                        <div className="flex-shrink-0">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                search.results_count > 0
                                    ? 'bg-green-500/10 border border-green-500/20'
                                    : 'bg-red-500/10 border border-red-500/20'
                            }`}>
                                {search.results_count > 0 ? (
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                ) : (
                                    <XCircle className="w-4 h-4 text-red-500" />
                                )}
                            </div>
                        </div>

                        {/* Search Details */}
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-semibold text-foreground truncate group-hover:text-primary transition-colors">
                                    {search.search_query}
                                </h3>
                                <Badge variant="outline" className="text-xs px-2 py-0.5 flex-shrink-0 border-primary/20 text-primary bg-primary/5">
                                    {formatSearchType(search.search_type)}
                                </Badge>
                            </div>

                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <div className="flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    <span>{new Date(search.created_at).toLocaleString()}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <BarChart3 className="w-3 h-3" />
                                    <span className={search.results_count > 0 ? 'text-green-600 font-medium' : 'text-red-600'}>
                                        {search.results_count > 0 ? `${search.results_count} results` : 'No results'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-2 flex-shrink-0 ml-3">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRepeatSearch(search)}
                            className="gap-1 opacity-70 group-hover:opacity-100 transition-opacity h-8 px-3"
                        >
                            <RefreshCw className="w-3 h-3" />
                            Repeat
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                        >
                            <MoreHorizontal className="w-3 h-3" />
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout>
            <Head title="Search History" />

            <div className="bg-gradient-to-br from-background via-muted/20 to-background min-h-screen">
                <div className="container mx-auto px-4 py-6 max-w-7xl">
                    {/* Enhanced Header Section */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-6">
                            <div>
                                <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center gap-3">
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                        <History className="w-6 h-6 text-primary" />
                                    </div>
                                    Search History
                                </h1>
                                <p className="text-muted-foreground text-lg">
                                    Comprehensive overview of your search activity and patterns
                                </p>
                            </div>
                            <div className="flex items-center gap-3">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleClearHistory('30d')}
                                    className="gap-2"
                                >
                                    <Trash2 className="w-4 h-4" />
                                    Clear Last 30 Days
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleClearHistory('all')}
                                    className="gap-2 text-destructive hover:text-destructive"
                                >
                                    <Trash2 className="w-4 h-4" />
                                    Clear All
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Stats Overview Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        {/* Total Searches */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Total Searches</CardTitle>
                                <div className="p-2 bg-blue-500/10 rounded-lg">
                                    <Search className="h-4 w-4 text-blue-500" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground mb-1">{stats.total_searches}</div>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    <span>All time searches</span>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Success Rate */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Success Rate</CardTitle>
                                <div className="p-2 bg-green-500/10 rounded-lg">
                                    <Target className="h-4 w-4 text-green-500" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground mb-1">{getSuccessRate()}%</div>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    <CheckCircle className="w-3 h-3 text-green-500" />
                                    <span>{stats.successful_searches} successful searches</span>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Recent Activity */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">This Week</CardTitle>
                                <div className="p-2 bg-purple-500/10 rounded-lg">
                                    <Activity className="h-4 w-4 text-purple-500" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-foreground mb-1">{stats.recent_searches}</div>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    <Calendar className="w-3 h-3" />
                                    <span>Recent searches</span>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Top Search Term */}
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Most Searched</CardTitle>
                                <div className="p-2 bg-orange-500/10 rounded-lg">
                                    <TrendingUp className="h-4 w-4 text-orange-500" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-lg font-bold text-foreground mb-1 truncate" title={stats.most_searched_term}>
                                    {stats.most_searched_term || 'No searches yet'}
                                </div>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    <span>Popular search term</span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Enhanced Filters Section */}
                    <Card className="mb-8 shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="flex items-center gap-3 text-xl">
                                        <div className="p-2 bg-primary/10 rounded-lg">
                                            <Filter className="w-5 h-5 text-primary" />
                                        </div>
                                        Search Filters
                                    </CardTitle>
                                    <CardDescription className="text-base mt-1">
                                        Filter your search history by type and date range
                                    </CardDescription>
                                </div>
                                <Badge variant="secondary" className="text-sm px-3 py-1">
                                    {searches.total} results
                                </Badge>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {/* Search Type Filter */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-foreground flex items-center gap-2">
                                        <Search className="w-4 h-4 text-primary" />
                                        Search Type
                                    </label>
                                    <div className="relative">
                                        <select
                                            value={selectedType}
                                            onChange={(e) => setSelectedType(e.target.value)}
                                            className="w-full px-4 py-3 bg-background border-2 border-border rounded-lg text-sm font-medium text-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all appearance-none cursor-pointer"
                                        >
                                            {Object.entries(search_types).map(([value, label]) => (
                                                <option key={value} value={value}>{label}</option>
                                            ))}
                                        </select>
                                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                                    </div>
                                </div>

                                {/* Date Range Filter */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-foreground flex items-center gap-2">
                                        <Calendar className="w-4 h-4 text-primary" />
                                        Date Range
                                    </label>
                                    <div className="relative">
                                        <select
                                            value={selectedRange}
                                            onChange={(e) => setSelectedRange(e.target.value)}
                                            className="w-full px-4 py-3 bg-background border-2 border-border rounded-lg text-sm font-medium text-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all appearance-none cursor-pointer"
                                        >
                                            {Object.entries(date_ranges).map(([value, label]) => (
                                                <option key={value} value={value}>{label}</option>
                                            ))}
                                        </select>
                                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                                    </div>
                                </div>

                                {/* Apply Button */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-transparent">Apply</label>
                                    <Button
                                        onClick={handleFilterChange}
                                        className="w-full h-12 gap-2 font-medium"
                                    >
                                        <Zap className="w-4 h-4" />
                                        Apply Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Search History Results */}
                    {searches.data.length > 0 ? (
                        <>
                            {/* Results Header */}
                            <div className="mb-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h2 className="text-xl font-semibold text-foreground flex items-center gap-2">
                                            <Eye className="w-5 h-5 text-primary" />
                                            Search Results
                                        </h2>
                                        <p className="text-muted-foreground mt-1">
                                            Showing {searches.from} to {searches.to} of {searches.total} searches
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button variant="outline" size="sm" className="gap-2">
                                            <Download className="w-4 h-4" />
                                            Export
                                        </Button>
                                        <Button variant="outline" size="sm" className="gap-2">
                                            <Settings className="w-4 h-4" />
                                            Options
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            {/* Search Items */}
                            <div className="space-y-4 mb-8">
                                {searches.data.map((search) => (
                                    <SearchItem key={search.id} search={search} />
                                ))}
                            </div>

                            {/* Enhanced Pagination */}
                            {searches.last_page > 1 && (
                                <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                                    <CardContent className="p-6">
                                        <div className="flex items-center justify-between">
                                            <div className="text-sm text-muted-foreground">
                                                <span className="font-medium text-foreground">
                                                    {searches.from}-{searches.to}
                                                </span>
                                                {' '}of{' '}
                                                <span className="font-medium text-foreground">
                                                    {searches.total}
                                                </span>
                                                {' '}searches
                                            </div>
                                            <div className="flex items-center gap-3">
                                                {searches.current_page > 1 && (
                                                    <Link href={`${route('dashboard.history')}?page=${searches.current_page - 1}`}>
                                                        <Button variant="outline" size="sm" className="gap-2">
                                                            <ArrowUpRight className="w-4 h-4 rotate-180" />
                                                            Previous
                                                        </Button>
                                                    </Link>
                                                )}

                                                <div className="flex items-center gap-2">
                                                    <span className="text-sm text-muted-foreground">Page</span>
                                                    <Badge variant="secondary" className="px-3 py-1">
                                                        {searches.current_page} of {searches.last_page}
                                                    </Badge>
                                                </div>

                                                {searches.current_page < searches.last_page && (
                                                    <Link href={`${route('dashboard.history')}?page=${searches.current_page + 1}`}>
                                                        <Button variant="outline" size="sm" className="gap-2">
                                                            Next
                                                            <ArrowUpRight className="w-4 h-4" />
                                                        </Button>
                                                    </Link>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </>
                    ) : (
                        <Card className="shadow-lg border-2 border-border bg-card/50 backdrop-blur-sm">
                            <CardContent className="text-center py-16">
                                <div className="max-w-md mx-auto">
                                    <div className="w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-6">
                                        {filters.type !== 'all' || filters.range !== '30d' ? (
                                            <AlertTriangle className="w-10 h-10 text-muted-foreground" />
                                        ) : (
                                            <History className="w-10 h-10 text-muted-foreground" />
                                        )}
                                    </div>

                                    <h3 className="text-2xl font-bold text-foreground mb-3">
                                        {filters.type !== 'all' || filters.range !== '30d'
                                            ? 'No matching searches found'
                                            : 'No search history yet'
                                        }
                                    </h3>

                                    <p className="text-muted-foreground text-lg mb-8 leading-relaxed">
                                        {filters.type !== 'all' || filters.range !== '30d'
                                            ? 'Try adjusting your filters to see more results, or start a new search to add to your history.'
                                            : 'Start exploring our comprehensive mobile parts database to build your search history and track your activity.'
                                        }
                                    </p>

                                    <div className="flex items-center justify-center gap-4">
                                        <Link href={route('search.index')}>
                                            <Button size="lg" className="gap-2">
                                                <Search className="w-5 h-5" />
                                                Start Searching
                                            </Button>
                                        </Link>
                                        {(filters.type !== 'all' || filters.range !== '30d') && (
                                            <Link href={route('dashboard.history')}>
                                                <Button variant="outline" size="lg" className="gap-2">
                                                    <Filter className="w-5 h-5" />
                                                    Clear Filters
                                                </Button>
                                            </Link>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
