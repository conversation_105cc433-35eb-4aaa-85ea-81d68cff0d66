import React, { useState, useEffect, useCallback } from 'react';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type PluginCallback = (...args: any[]) => any;

interface Plugin {
    name: string;
    version: string;
    description: string;
    author: string;
    enabled: boolean;
    hooks: Record<string, PluginCallback[]>;
}

interface PluginHook {
    name: string;
    callback: PluginCallback;
    priority: number;
}

class PluginManager {
    private plugins: Map<string, Plugin> = new Map();
    private hooks: Map<string, PluginHook[]> = new Map();

    /**
     * Register a plugin.
     */
    registerPlugin(plugin: Plugin): void {
        this.plugins.set(plugin.name, plugin);
        
        // Register plugin hooks
        if (plugin.hooks) {
            Object.entries(plugin.hooks).forEach(([hookName, callbacks]) => {
                callbacks.forEach((callback, index) => {
                    this.addHook(hookName, callback, index * 10);
                });
            });
        }
    }

    /**
     * Add a hook.
     */
    addHook(name: string, callback: PluginCallback, priority: number = 10): void {
        if (!this.hooks.has(name)) {
            this.hooks.set(name, []);
        }

        const hooks = this.hooks.get(name)!;
        hooks.push({ name, callback, priority });

        // Sort by priority
        hooks.sort((a, b) => a.priority - b.priority);
        this.hooks.set(name, hooks);
    }

    /**
     * Remove a hook.
     */
    removeHook(name: string, callback: PluginCallback): void {
        const hooks = this.hooks.get(name);
        if (hooks) {
            const index = hooks.findIndex(h => h.callback === callback);
            if (index > -1) {
                hooks.splice(index, 1);
            }
        }
    }

    /**
     * Execute hooks.
     */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    executeHook(name: string, initialValue: any, ...args: any[]): any {
        const hooks = this.hooks.get(name);
        if (!hooks) return initialValue;

        let result = initialValue;
        
        for (const hook of hooks) {
            try {
                result = hook.callback(result, ...args);
            } catch (error) {
                console.error(`Error executing hook ${name}:`, error);
            }
        }

        return result;
    }

    /**
     * Get all plugins.
     */
    getPlugins(): Plugin[] {
        return Array.from(this.plugins.values());
    }

    /**
     * Get enabled plugins.
     */
    getEnabledPlugins(): Plugin[] {
        return this.getPlugins().filter(plugin => plugin.enabled);
    }

    /**
     * Check if plugin exists.
     */
    hasPlugin(name: string): boolean {
        return this.plugins.has(name);
    }

    /**
     * Get plugin by name.
     */
    getPlugin(name: string): Plugin | undefined {
        return this.plugins.get(name);
    }

    /**
     * Enable plugin.
     */
    enablePlugin(name: string): boolean {
        const plugin = this.plugins.get(name);
        if (plugin) {
            plugin.enabled = true;
            return true;
        }
        return false;
    }

    /**
     * Disable plugin.
     */
    disablePlugin(name: string): boolean {
        const plugin = this.plugins.get(name);
        if (plugin) {
            plugin.enabled = false;
            return true;
        }
        return false;
    }
}

// Global plugin manager instance
const pluginManager = new PluginManager();

/**
 * Hook for managing plugins in React components.
 */
export function usePlugins() {
    const [plugins, setPlugins] = useState<Plugin[]>([]);

    useEffect(() => {
        setPlugins(pluginManager.getPlugins());
    }, []);

    const registerPlugin = useCallback((plugin: Plugin) => {
        pluginManager.registerPlugin(plugin);
        setPlugins(pluginManager.getPlugins());
    }, []);

    const enablePlugin = useCallback((name: string) => {
        pluginManager.enablePlugin(name);
        setPlugins(pluginManager.getPlugins());
    }, []);

    const disablePlugin = useCallback((name: string) => {
        pluginManager.disablePlugin(name);
        setPlugins(pluginManager.getPlugins());
    }, []);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const executeHook = useCallback((name: string, initialValue: any, ...args: any[]) => {
        return pluginManager.executeHook(name, initialValue, ...args);
    }, []);

    const addHook = useCallback((name: string, callback: PluginCallback, priority: number = 10) => {
        pluginManager.addHook(name, callback, priority);
    }, []);

    return {
        plugins,
        enabledPlugins: plugins.filter(p => p.enabled),
        registerPlugin,
        enablePlugin,
        disablePlugin,
        executeHook,
        addHook,
        hasPlugin: (name: string) => pluginManager.hasPlugin(name),
        getPlugin: (name: string) => pluginManager.getPlugin(name),
    };
}

/**
 * Hook for executing plugin hooks.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function usePluginHook(hookName: string, initialValue: any, dependencies: any[] = []) {
    const [value, setValue] = useState(initialValue);

    useEffect(() => {
        const result = pluginManager.executeHook(hookName, initialValue);
        setValue(result);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [hookName, initialValue, ...dependencies]);

    return value;
}

/**
 * Hook for registering a plugin hook.
 */
export function useRegisterHook(hookName: string, callback: PluginCallback, priority: number = 10) {
    useEffect(() => {
        pluginManager.addHook(hookName, callback, priority);
        
        // Cleanup function to remove hook when component unmounts
        return () => {
            pluginManager.removeHook(hookName, callback);
        };
    }, [hookName, callback, priority]);
}

/**
 * Component wrapper for plugin functionality.
 */
export function withPlugins<T extends object>(Component: React.ComponentType<T>) {
    return function PluginWrappedComponent(props: T) {
        const pluginProps = usePlugins();

        return React.createElement(Component, { ...props, ...pluginProps });
    };
}

// Export the plugin manager for direct access if needed
export { pluginManager };

// Default export
export default usePlugins;
