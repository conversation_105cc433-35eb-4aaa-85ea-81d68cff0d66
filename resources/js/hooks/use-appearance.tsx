import { useCallback, useEffect, useState } from 'react';

export type Appearance = 'light' | 'dark' | 'system';

const prefersDark = () => {
    if (typeof window === 'undefined') {
        return false;
    }

    return window.matchMedia('(prefers-color-scheme: dark)').matches;
};

const setCookie = (name: string, value: string, days = 365) => {
    if (typeof document === 'undefined') {
        return;
    }

    const maxAge = days * 24 * 60 * 60;
    document.cookie = `${name}=${value};path=/;max-age=${maxAge};SameSite=Lax`;
};

const applyTheme = (appearance: Appearance) => {
    if (typeof document === 'undefined') {
        return;
    }

    const isDark = appearance === 'dark' || (appearance === 'system' && prefersDark());

    console.log('Applying theme:', appearance, 'isDark:', isDark); // Debug log

    // Remove existing theme classes
    document.documentElement.classList.remove('light', 'dark');

    // Add the appropriate theme class
    if (isDark) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.add('light');
    }

    // Also set the data attribute for CSS selectors
    document.documentElement.setAttribute('data-theme', appearance);
};

const mediaQuery = () => {
    if (typeof window === 'undefined') {
        return null;
    }

    return window.matchMedia('(prefers-color-scheme: dark)');
};

const handleSystemThemeChange = () => {
    const currentAppearance = (localStorage.getItem('appearance') as Appearance) || 'system';
    if (currentAppearance === 'system') {
        applyTheme(currentAppearance);
    }
};

export function initializeTheme() {
    const savedAppearance = (localStorage.getItem('appearance') as Appearance) || 'system';

    applyTheme(savedAppearance);

    // Add the event listener for system theme changes...
    mediaQuery()?.addEventListener('change', handleSystemThemeChange);
}

export function useAppearance() {
    const [appearance, setAppearanceState] = useState<Appearance>(() => {
        // Initialize with the saved appearance or 'system' as default
        if (typeof window !== 'undefined') {
            const saved = localStorage.getItem('appearance') as Appearance;
            return saved || 'system';
        }
        return 'system';
    });

    const setAppearance = useCallback((mode: Appearance) => {
        console.log('Setting appearance to:', mode); // Debug log
        setAppearanceState(mode);

        // Store in localStorage for client-side persistence...
        if (typeof window !== 'undefined') {
            localStorage.setItem('appearance', mode);
        }

        // Store in cookie for SSR...
        setCookie('appearance', mode);

        // Apply the theme immediately
        applyTheme(mode);
    }, []);

    useEffect(() => {
        // Apply the current theme on mount and when appearance changes
        console.log('Applying theme:', appearance); // Debug log
        applyTheme(appearance);
    }, [appearance]);

    useEffect(() => {
        // Listen for system theme changes
        const mq = mediaQuery();
        const handleChange = () => {
            if (appearance === 'system') {
                applyTheme('system');
            }
        };

        if (mq) {
            mq.addEventListener('change', handleChange);
            return () => mq.removeEventListener('change', handleChange);
        }
    }, [appearance]);

    return { appearance, setAppearance } as const;
}
