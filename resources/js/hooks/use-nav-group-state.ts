import { useAccordion } from '@/contexts/accordion-context';

/**
 * Custom hook for managing navigation group expanded/collapsed state
 * Works with accordion context for single-group expansion behavior
 */
export function useNavGroupState(groupId: string) {
    const { expandedGroupId, expandGroup } = useAccordion();

    // This group is expanded if it's the currently expanded group in the accordion
    const isExpanded = expandedGroupId === groupId;

    const toggleExpanded = () => {
        // In accordion mode, we expand this group (which may collapse others)
        expandGroup(groupId);
    };

    return {
        isExpanded,
        toggleExpanded
    };
}
