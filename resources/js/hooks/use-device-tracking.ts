import { useState, useEffect, useCallback } from 'react';

interface SearchStatus {
    has_searched: boolean;
    searches_remaining: number;
    message: string;
}

export function useDeviceTracking() {
    const [deviceId, setDeviceId] = useState<string>('');
    const [searchStatus, setSearchStatus] = useState<SearchStatus | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    // Generate or retrieve device ID
    useEffect(() => {
        const generateDeviceId = () => {
            let id = localStorage.getItem('mobile_parts_device_id');
            if (!id) {
                // Create a unique device ID based on browser fingerprint
                const timestamp = Date.now();
                const random = Math.random().toString(36).substr(2, 9);
                const userAgent = navigator.userAgent.slice(0, 20).replace(/[^a-zA-Z0-9]/g, '');
                id = `device_${timestamp}_${random}_${userAgent}`;
                localStorage.setItem('mobile_parts_device_id', id);
            }
            return id;
        };

        const id = generateDeviceId();
        setDeviceId(id);
    }, []);

    const checkSearchStatus = useCallback(async () => {
        if (!deviceId) return;

        try {
            setIsLoading(true);
            const response = await fetch(`/guest/search/status?device_id=${encodeURIComponent(deviceId)}`);

            if (response.ok) {
                const data = await response.json();
                setSearchStatus(data);
            }
        } catch (error) {
            console.error('Failed to check search status:', error);
            // Set default status if API fails
            setSearchStatus({
                has_searched: false,
                searches_remaining: 1,
                message: 'You have 1 free search available.'
            });
        } finally {
            setIsLoading(false);
        }
    }, [deviceId]);

    // Check search status when device ID is available
    useEffect(() => {
        if (deviceId) {
            checkSearchStatus();
        }
    }, [deviceId, checkSearchStatus]);

    const performGuestSearch = async (query: string, searchType: string = 'all') => {
        if (!deviceId) {
            throw new Error('Device ID not available');
        }

        if (searchStatus?.has_searched) {
            throw new Error('Search limit exceeded');
        }

        const params = new URLSearchParams({
            q: query,
            type: searchType,
            device_id: deviceId
        });

        const response = await fetch(`/guest/search?${params}`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || 'Search failed');
        }

        // Update search status after successful search
        await checkSearchStatus();

        return data;
    };

    const resetSearchStatus = () => {
        // This would typically be called after user signs up
        localStorage.removeItem('mobile_parts_device_id');
        setDeviceId('');
        setSearchStatus(null);
        // Generate new device ID
        const generateDeviceId = () => {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            const userAgent = navigator.userAgent.slice(0, 20).replace(/[^a-zA-Z0-9]/g, '');
            const id = `device_${timestamp}_${random}_${userAgent}`;
            localStorage.setItem('mobile_parts_device_id', id);
            return id;
        };
        setDeviceId(generateDeviceId());
    };

    return {
        deviceId,
        searchStatus,
        isLoading,
        checkSearchStatus,
        performGuestSearch,
        resetSearchStatus
    };
}
