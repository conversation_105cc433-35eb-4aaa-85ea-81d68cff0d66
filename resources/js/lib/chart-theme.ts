// Chart theme configuration using application colors
// This ensures consistent theming across all charts in both light and dark modes

export const chartColors = {
  light: {
    primary: 'oklch(0.623 0.214 259.815)',
    chart1: 'oklch(0.646 0.222 41.116)',    // Orange/Red
    chart2: 'oklch(0.6 0.118 184.704)',     // Teal/Cyan
    chart3: 'oklch(0.398 0.07 227.392)',    // Blue
    chart4: 'oklch(0.828 0.189 84.429)',    // Yellow/Green
    chart5: 'oklch(0.769 0.188 70.08)',     // Green
    background: 'oklch(1 0 0)',
    foreground: 'oklch(0.141 0.005 285.823)',
    muted: 'oklch(0.552 0.016 285.938)',
    border: 'oklch(0.92 0.004 286.32)',
  },
  dark: {
    primary: 'oklch(0.546 0.245 262.881)',
    chart1: 'oklch(0.488 0.243 264.376)',   // Purple/Blue
    chart2: 'oklch(0.696 0.17 162.48)',     // Teal
    chart3: 'oklch(0.769 0.188 70.08)',     // Green
    chart4: 'oklch(0.627 0.265 303.9)',     // Purple
    chart5: 'oklch(0.645 0.246 16.439)',    // Orange/Red
    background: 'oklch(0.141 0.005 285.823)',
    foreground: 'oklch(0.985 0 0)',
    muted: 'oklch(0.705 0.015 286.067)',
    border: 'oklch(1 0 0 / 10%)',
  }
};

// Get current theme colors based on dark mode
export const getCurrentThemeColors = (isDark: boolean = false) => {
  return isDark ? chartColors.dark : chartColors.light;
};

// Chart color palette for different chart types
export const getChartColorPalette = (isDark: boolean = false) => {
  const colors = getCurrentThemeColors(isDark);
  return [
    colors.chart1,
    colors.chart2,
    colors.chart3,
    colors.chart4,
    colors.chart5,
    colors.primary,
  ];
};

// Specific color configurations for different chart types
export const chartTheme = {
  // Common chart styling
  common: (isDark: boolean = false) => {
    const colors = getCurrentThemeColors(isDark);
    return {
      backgroundColor: colors.background,
      textColor: colors.foreground,
      gridColor: colors.border,
      tooltipBackground: colors.background,
      tooltipBorder: colors.border,
      tooltipText: colors.foreground,
    };
  },

  // Bar chart specific styling
  bar: (isDark: boolean = false) => ({
    ...chartTheme.common(isDark),
    colors: getChartColorPalette(isDark),
  }),

  // Pie chart specific styling
  pie: (isDark: boolean = false) => ({
    ...chartTheme.common(isDark),
    colors: getChartColorPalette(isDark),
  }),

  // Line chart specific styling
  line: (isDark: boolean = false) => {
    const colors = getCurrentThemeColors(isDark);
    return {
      ...chartTheme.common(isDark),
      strokeColor: colors.primary,
      fillColor: `${colors.primary}20`, // 20% opacity
      colors: getChartColorPalette(isDark),
    };
  },

  // Area chart specific styling
  area: (isDark: boolean = false) => {
    const colors = getCurrentThemeColors(isDark);
    return {
      ...chartTheme.common(isDark),
      fillColor: `${colors.primary}30`, // 30% opacity
      strokeColor: colors.primary,
      colors: getChartColorPalette(isDark),
    };
  },
};

// Utility function to detect dark mode
export const isDarkMode = () => {
  if (typeof window !== 'undefined') {
    return document.documentElement.classList.contains('dark');
  }
  return false;
};

// Custom tooltip styling
export const getTooltipStyle = (isDark: boolean = false) => {
  const colors = getCurrentThemeColors(isDark);
  return {
    backgroundColor: colors.background,
    border: `1px solid ${colors.border}`,
    borderRadius: '8px',
    color: colors.foreground,
    fontSize: '14px',
    padding: '12px',
    boxShadow: isDark 
      ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
      : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  };
};

// Grid styling
export const getGridStyle = (isDark: boolean = false) => {
  const colors = getCurrentThemeColors(isDark);
  return {
    stroke: colors.border,
    strokeDasharray: '3 3',
    strokeOpacity: isDark ? 0.3 : 0.5,
  };
};

// Axis styling
export const getAxisStyle = (isDark: boolean = false) => {
  const colors = getCurrentThemeColors(isDark);
  return {
    stroke: colors.border,
    fontSize: '12px',
    fill: colors.muted,
  };
};

// Legend styling
export const getLegendStyle = (isDark: boolean = false) => {
  const colors = getCurrentThemeColors(isDark);
  return {
    fontSize: '12px',
    fill: colors.foreground,
  };
};

// Debug logging for development
export const logChartTheme = (chartType: string, isDark: boolean = false) => {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🎨 Chart Theme - ${chartType}`);
    console.log('Dark Mode:', isDark);
    console.log('Colors:', getChartColorPalette(isDark));
    console.log('Theme:', chartTheme[chartType as keyof typeof chartTheme]?.(isDark) || chartTheme.common(isDark));
    console.groupEnd();
  }
};
