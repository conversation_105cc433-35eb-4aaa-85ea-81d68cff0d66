<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Verification Code</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
        }
        .otp-code {
            background-color: #f3f4f6;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 30px 0;
        }
        .otp-number {
            font-size: 36px;
            font-weight: bold;
            color: #1f2937;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
        }
        .action-info {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .action-title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 5px;
        }
        .action-description {
            color: #92400e;
            font-size: 14px;
        }
        .warning {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .warning-title {
            font-weight: 600;
            color: #dc2626;
            margin-bottom: 5px;
        }
        .warning-text {
            color: #dc2626;
            font-size: 14px;
        }
        .info {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .info-text {
            color: #1e40af;
            font-size: 14px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .expiry {
            color: #ef4444;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📱 Mobile Parts DB</div>
            <div class="title">Admin Action Verification</div>
        </div>

        <p>Hello {{ $user->name }},</p>

        <p>You have requested to perform an admin action that requires two-factor authentication. Please use the verification code below to proceed.</p>

        <div class="action-info">
            <div class="action-title">Action Requested:</div>
            <div class="action-description">{{ ucwords(str_replace('_', ' ', $action)) }}</div>
        </div>

        <div class="otp-code">
            <div style="font-size: 14px; color: #6b7280; margin-bottom: 10px;">Your Verification Code</div>
            <div class="otp-number">{{ $otpCode }}</div>
        </div>

        <div class="info">
            <div class="info-text">
                <strong>Important:</strong> This code will expire in <span class="expiry">{{ $expiryMinutes }} minutes</span>. 
                Enter this code in the admin interface to complete your action.
            </div>
        </div>

        <div class="warning">
            <div class="warning-title">Security Notice</div>
            <div class="warning-text">
                • Never share this code with anyone<br>
                • This code is only valid for the specific admin action you requested<br>
                • If you didn't request this action, please contact your system administrator immediately<br>
                • You have 3 attempts to enter the correct code before being locked out
            </div>
        </div>

        <p>If you're having trouble with verification, please contact your system administrator.</p>

        <div class="footer">
            <p>This email was sent from Mobile Parts DB Admin System</p>
            <p>Time: {{ now()->format('Y-m-d H:i:s T') }}</p>
            <p>IP Address: {{ request()->ip() }}</p>
        </div>
    </div>
</body>
</html>
