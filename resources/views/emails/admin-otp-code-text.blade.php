Mobile Parts DB - Admin Action Verification
==========================================

Hello {{ $user->name }},

You have requested to perform an admin action that requires two-factor authentication.

ACTION REQUESTED: {{ ucwords(str_replace('_', ' ', $action)) }}

YOUR VERIFICATION CODE: {{ $otpCode }}

IMPORTANT INFORMATION:
- This code will expire in {{ $expiryMinutes }} minutes
- Enter this code in the admin interface to complete your action
- You have 3 attempts to enter the correct code before being locked out

SECURITY NOTICE:
- Never share this code with anyone
- This code is only valid for the specific admin action you requested
- If you didn't request this action, please contact your system administrator immediately

If you're having trouble with verification, please contact your system administrator.

---
This email was sent from Mobile Parts DB Admin System
Time: {{ now()->format('Y-m-d H:i:s T') }}
IP Address: {{ request()->ip() }}
