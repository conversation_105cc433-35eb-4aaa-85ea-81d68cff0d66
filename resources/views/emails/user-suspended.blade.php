@extends('emails.layout')

@section('title', 'Account Suspension Notice - Mobile Parts DB')

@section('content')
    <div class="greeting">
        Hello {{ $user->name }},
    </div>

    <div class="info-box warning">
        <strong>Account Suspension Notice:</strong> Your account has been temporarily suspended.
    </div>

    <div class="message">
        <p>We are writing to inform you that your Mobile Parts DB account has been suspended due to a violation of our terms of service or community guidelines.</p>
        
        <div class="info-box error">
            <strong>Suspension Reason:</strong><br>
            {{ $reason }}
        </div>
        
        @if($expiresAt)
        <div class="info-box">
            <strong>Suspension Duration:</strong><br>
            Your account will be automatically reactivated on {{ $expiresAt->format('F j, Y \a\t g:i A') }}.
        </div>
        @else
        <div class="info-box error">
            <strong>Suspension Duration:</strong><br>
            This is an indefinite suspension. Please contact our support team to discuss reactivation.
        </div>
        @endif
        
        <p><strong>During the suspension period:</strong></p>
        <ul>
            <li>You will not be able to access your account</li>
            <li>Search functionality will be disabled</li>
            <li>Your saved data will be preserved</li>
            <li>You can still contact our support team</li>
        </ul>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ $contactUrl }}" class="button warning">Contact Support</a>
    </div>

    <div class="details-table">
        <table class="details-table">
            <tr>
                <th>Suspension Details</th>
                <th></th>
            </tr>
            <tr>
                <td><strong>Account:</strong></td>
                <td>{{ $user->name }} ({{ $user->email }})</td>
            </tr>
            <tr>
                <td><strong>Suspension Date:</strong></td>
                <td>{{ $user->suspended_at->format('F j, Y \a\t g:i A') }}</td>
            </tr>
            @if($expiresAt)
            <tr>
                <td><strong>Reactivation Date:</strong></td>
                <td>{{ $expiresAt->format('F j, Y \a\t g:i A') }}</td>
            </tr>
            @endif
            <tr>
                <td><strong>Suspended By:</strong></td>
                <td>{{ $suspendedBy->name }}</td>
            </tr>
        </table>
    </div>

    <div class="message">
        <p>If you believe this suspension was issued in error or if you have any questions, please contact our support team immediately.</p>
        
        <p>We take our community guidelines seriously to ensure a safe and productive environment for all users.</p>
        
        <p>Best regards,<br>
        The Mobile Parts DB Team</p>
    </div>
@endsection
