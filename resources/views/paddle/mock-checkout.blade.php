<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Paddle Checkout - Development Mode</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon {
            width: 80px;
            height: 80px;
            background: #f0f0f0;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .info-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .info-box h3 {
            margin-top: 0;
            color: #495057;
            font-size: 16px;
        }
        .info-box p {
            margin-bottom: 0;
            color: #6c757d;
            line-height: 1.5;
        }
        .transaction-id {
            font-family: 'Monaco', 'Menlo', monospace;
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            word-break: break-all;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🛠️</div>
        <h1>Development Mode</h1>
        <p class="subtitle">Mock Paddle Checkout</p>
        
        <div class="warning">
            <strong>⚠️ Development Mode Active</strong><br>
            This is a mock checkout page. No real payment will be processed.
        </div>

        <div class="info-box">
            <h3>Transaction Details</h3>
            <p><strong>Transaction ID:</strong></p>
            <div class="transaction-id">{{ $transaction_id ?? 'N/A' }}</div>
        </div>

        <div class="info-box">
            <h3>Development Information</h3>
            <p>{{ $message }}</p>
            <br>
            <p><strong>Next Steps:</strong></p>
            <p>{{ $help }}</p>
        </div>

        <div class="info-box">
            <h3>How to Configure Real Paddle Credentials</h3>
            <p>1. Sign up for a Paddle account at <a href="https://paddle.com" target="_blank">paddle.com</a></p>
            <p>2. Get your sandbox API key and client token from the Paddle dashboard</p>
            <p>3. Update your .env file:</p>
            <div class="transaction-id" style="margin-top: 10px;">
                PADDLE_API_KEY=your_sandbox_api_key<br>
                PADDLE_CLIENT_TOKEN=your_sandbox_client_token
            </div>
        </div>

        <a href="{{ url('/subscription/checkout') }}" class="btn">← Back to Checkout</a>
    </div>
</body>
</html>
