<?php

namespace Database\Factories;

use App\Models\Media;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Media>
 */
class MediaFactory extends Factory
{
    protected $model = Media::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $filename = $this->faker->uuid() . '.jpg';
        
        return [
            'filename' => $filename,
            'original_filename' => $this->faker->word() . '.jpg',
            'mime_type' => 'image/jpeg',
            'size' => $this->faker->numberBetween(10000, 5000000),
            'path' => 'media/' . date('Y/m') . '/' . $filename,
            'disk' => 'public',
            'alt_text' => $this->faker->sentence(3),
            'title' => $this->faker->words(3, true),
            'description' => $this->faker->sentence(),
            'width' => $this->faker->numberBetween(100, 2000),
            'height' => $this->faker->numberBetween(100, 2000),
            'uploaded_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the media is an image.
     */
    public function image(): static
    {
        return $this->state(fn (array $attributes) => [
            'mime_type' => $this->faker->randomElement(['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
            'width' => $this->faker->numberBetween(100, 2000),
            'height' => $this->faker->numberBetween(100, 2000),
        ]);
    }

    /**
     * Indicate that the media is a document.
     */
    public function document(): static
    {
        return $this->state(fn (array $attributes) => [
            'mime_type' => $this->faker->randomElement(['application/pdf', 'application/msword', 'text/plain']),
            'width' => null,
            'height' => null,
            'original_filename' => $this->faker->word() . '.pdf',
            'filename' => $this->faker->uuid() . '.pdf',
            'path' => 'media/' . date('Y/m') . '/' . $this->faker->uuid() . '.pdf',
        ]);
    }
}
