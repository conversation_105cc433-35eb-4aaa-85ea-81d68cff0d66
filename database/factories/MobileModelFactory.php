<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MobileModel>
 */
class MobileModelFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'brand_id' => \App\Models\Brand::factory(),
            'name' => $this->faker->words(2, true),
            'model_number' => $this->faker->bothify('??-####'),
            'release_year' => $this->faker->numberBetween(2015, 2024),
            'specifications' => [
                'display_size' => $this->faker->randomFloat(1, 5.0, 7.0) . '"',
                'storage' => $this->faker->randomElement(['64GB', '128GB', '256GB', '512GB']),
                'ram' => $this->faker->randomElement(['4GB', '6GB', '8GB', '12GB']),
            ],
            'images' => [
                $this->faker->imageUrl(400, 600, 'technics'),
            ],
            'is_active' => true,
        ];
    }
}
