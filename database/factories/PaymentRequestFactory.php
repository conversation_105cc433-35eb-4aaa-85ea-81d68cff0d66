<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentRequest>
 */
class PaymentRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'amount' => fake()->randomFloat(2, 10, 500),
            'currency' => 'USD',
            'payment_method' => fake()->randomElement(['bank_transfer', 'cash', 'check', 'paypal']),
            'status' => fake()->randomElement(['pending', 'approved', 'rejected', 'processed']),
            'subscription_plan' => fake()->randomElement(['free', 'premium']),
            'notes' => fake()->optional()->sentence(),
            'requested_at' => fake()->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the payment request is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the payment request is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'approved_by' => User::factory(),
            'approved_at' => now(),
        ]);
    }

    /**
     * Indicate that the payment request is for premium subscription.
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_plan' => 'premium',
            'amount' => 99.99,
        ]);
    }
}
