<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Display',
                'description' => 'LCD screens, OLED displays, touch panels',
                'sort_order' => 1,
            ],
            [
                'name' => 'IC Components',
                'description' => 'Integrated circuits and chips',
                'sort_order' => 2,
                'children' => [
                    ['name' => 'Network IC', 'description' => 'WiFi, Bluetooth, cellular chips'],
                    ['name' => 'Charging IC', 'description' => 'Power management and charging circuits'],
                    ['name' => 'Audio IC', 'description' => 'Sound processing chips'],
                ]
            ],
            [
                'name' => 'Battery',
                'description' => 'Lithium-ion batteries and power cells',
                'sort_order' => 3,
            ],
            [
                'name' => 'Camera',
                'description' => 'Front and rear camera modules',
                'sort_order' => 4,
                'children' => [
                    ['name' => 'Front Camera', 'description' => 'Selfie camera modules'],
                    ['name' => 'Rear Camera', 'description' => 'Main camera modules'],
                    ['name' => 'Camera Lens', 'description' => 'Camera lens components'],
                ]
            ],
            [
                'name' => 'Speaker',
                'description' => 'Audio speakers and sound components',
                'sort_order' => 5,
            ],
            [
                'name' => 'Microphone',
                'description' => 'Microphone components',
                'sort_order' => 6,
            ],
            [
                'name' => 'Sensors',
                'description' => 'Various sensors and detection components',
                'sort_order' => 7,
                'children' => [
                    ['name' => 'Fingerprint Sensor', 'description' => 'Biometric fingerprint sensors'],
                    ['name' => 'Proximity Sensor', 'description' => 'Distance detection sensors'],
                    ['name' => 'Accelerometer', 'description' => 'Motion detection sensors'],
                ]
            ],
            [
                'name' => 'Connectors',
                'description' => 'Charging ports, headphone jacks, and connectors',
                'sort_order' => 8,
                'children' => [
                    ['name' => 'USB-C Port', 'description' => 'USB-C charging and data ports'],
                    ['name' => 'Lightning Port', 'description' => 'Apple Lightning connectors'],
                    ['name' => 'Headphone Jack', 'description' => '3.5mm audio jacks'],
                ]
            ],
            [
                'name' => 'Housing & Frame',
                'description' => 'Phone cases, frames, and structural components',
                'sort_order' => 9,
            ],
        ];

        foreach ($categories as $categoryData) {
            $children = $categoryData['children'] ?? [];
            unset($categoryData['children']);

            $category = Category::create($categoryData);

            foreach ($children as $childData) {
                $childData['parent_id'] = $category->id;
                Category::create($childData);
            }
        }
    }
}
