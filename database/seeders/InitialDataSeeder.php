<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class InitialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedCategories();
        $this->seedBrands();
        $this->seedModels();
        $this->seedParts();
    }

    private function seedCategories(): void
    {
        $categories = [
            ['name' => 'Display', 'description' => 'LCD, OLED, and other display components'],
            ['name' => 'Battery', 'description' => 'Lithium-ion batteries and power components'],
            ['name' => 'Camera', 'description' => 'Front and rear camera modules'],
            ['name' => 'Speaker', 'description' => 'Audio speakers and sound components'],
            ['name' => 'Charging Port', 'description' => 'USB-C, Lightning, and other charging ports'],
            ['name' => 'Home Button', 'description' => 'Physical and virtual home buttons'],
            ['name' => 'Back Cover', 'description' => 'Rear housing and back panels'],
            ['name' => 'Frame', 'description' => 'Metal and plastic frames'],
            ['name' => 'Flex Cable', 'description' => 'Internal connection cables'],
            ['name' => 'Sensor', 'description' => 'Proximity, ambient light, and other sensors'],
        ];

        foreach ($categories as $index => $category) {
            Category::create([
                'name' => $category['name'],
                'description' => $category['description'],
                'sort_order' => $index + 1,
                'is_active' => true,
            ]);
        }
    }

    private function seedBrands(): void
    {
        $brands = [
            ['name' => 'Apple', 'country' => 'United States', 'website' => 'https://apple.com'],
            ['name' => 'Samsung', 'country' => 'South Korea', 'website' => 'https://samsung.com'],
            ['name' => 'Xiaomi', 'country' => 'China', 'website' => 'https://mi.com'],
            ['name' => 'Huawei', 'country' => 'China', 'website' => 'https://huawei.com'],
            ['name' => 'OnePlus', 'country' => 'China', 'website' => 'https://oneplus.com'],
            ['name' => 'Google', 'country' => 'United States', 'website' => 'https://google.com'],
            ['name' => 'Sony', 'country' => 'Japan', 'website' => 'https://sony.com'],
            ['name' => 'LG', 'country' => 'South Korea', 'website' => 'https://lg.com'],
            ['name' => 'Motorola', 'country' => 'United States', 'website' => 'https://motorola.com'],
            ['name' => 'Nokia', 'country' => 'Finland', 'website' => 'https://nokia.com'],
        ];

        foreach ($brands as $brand) {
            Brand::create([
                'name' => $brand['name'],
                'country' => $brand['country'],
                'website' => $brand['website'],
                'is_active' => true,
            ]);
        }
    }

    private function seedModels(): void
    {
        $modelsData = [
            'Apple' => [
                ['name' => 'iPhone 15 Pro Max', 'model_number' => 'A3108', 'release_year' => 2023],
                ['name' => 'iPhone 15 Pro', 'model_number' => 'A3105', 'release_year' => 2023],
                ['name' => 'iPhone 15', 'model_number' => 'A3090', 'release_year' => 2023],
                ['name' => 'iPhone 14 Pro Max', 'model_number' => 'A2895', 'release_year' => 2022],
                ['name' => 'iPhone 14 Pro', 'model_number' => 'A2890', 'release_year' => 2022],
                ['name' => 'iPhone 14', 'model_number' => 'A2882', 'release_year' => 2022],
                ['name' => 'iPhone 13 Pro Max', 'model_number' => 'A2484', 'release_year' => 2021],
                ['name' => 'iPhone 13 Pro', 'model_number' => 'A2483', 'release_year' => 2021],
                ['name' => 'iPhone 13', 'model_number' => 'A2482', 'release_year' => 2021],
            ],
            'Samsung' => [
                ['name' => 'Galaxy S24 Ultra', 'model_number' => 'SM-S928', 'release_year' => 2024],
                ['name' => 'Galaxy S24+', 'model_number' => 'SM-S926', 'release_year' => 2024],
                ['name' => 'Galaxy S24', 'model_number' => 'SM-S921', 'release_year' => 2024],
                ['name' => 'Galaxy S23 Ultra', 'model_number' => 'SM-S918', 'release_year' => 2023],
                ['name' => 'Galaxy S23+', 'model_number' => 'SM-S916', 'release_year' => 2023],
                ['name' => 'Galaxy S23', 'model_number' => 'SM-S911', 'release_year' => 2023],
                ['name' => 'Galaxy Note 20 Ultra', 'model_number' => 'SM-N986', 'release_year' => 2020],
            ],
            'Xiaomi' => [
                ['name' => 'Mi 14 Ultra', 'model_number' => '2405CPX3DG', 'release_year' => 2024],
                ['name' => 'Mi 14', 'model_number' => '2312DRA50C', 'release_year' => 2023],
                ['name' => 'Mi 13 Ultra', 'model_number' => '2304FPN6DC', 'release_year' => 2023],
                ['name' => 'Mi 13 Pro', 'model_number' => '2210132C', 'release_year' => 2022],
                ['name' => 'Mi 13', 'model_number' => '2211133C', 'release_year' => 2022],
            ],
        ];

        foreach ($modelsData as $brandName => $models) {
            $brand = Brand::where('name', $brandName)->first();

            foreach ($models as $model) {
                MobileModel::create([
                    'brand_id' => $brand->id,
                    'name' => $model['name'],
                    'model_number' => $model['model_number'],
                    'release_year' => $model['release_year'],
                    'specifications' => [
                        'display_size' => fake()->randomFloat(1, 5.0, 7.0) . '"',
                        'storage' => fake()->randomElement(['128GB', '256GB', '512GB', '1TB']),
                        'ram' => fake()->randomElement(['6GB', '8GB', '12GB', '16GB']),
                    ],
                    'is_active' => true,
                ]);
            }
        }
    }

    private function seedParts(): void
    {
        $categories = Category::all();
        $models = MobileModel::all();

        foreach ($categories as $category) {
            foreach ($models->take(10) as $model) {
                $part = Part::create([
                    'category_id' => $category->id,
                    'name' => $model->brand->name . ' ' . $model->name . ' ' . $category->name,
                    'part_number' => fake()->bothify('??###-####'),
                    'manufacturer' => fake()->randomElement([$model->brand->name, 'OEM', 'Compatible']),
                    'description' => "High-quality {$category->name} replacement for {$model->brand->name} {$model->name}",
                    'specifications' => [
                        'material' => fake()->randomElement(['Plastic', 'Metal', 'Glass', 'Ceramic']),
                        'color' => fake()->randomElement(['Black', 'White', 'Silver', 'Gold']),
                        'warranty' => fake()->randomElement(['6 months', '1 year', '2 years']),
                    ],
                    'images' => [
                        fake()->imageUrl(400, 400, 'technics'),
                        fake()->imageUrl(400, 400, 'technics'),
                    ],
                    'is_active' => true,
                ]);

                // Attach part to model with compatibility info
                $part->models()->attach($model->id, [
                    'compatibility_notes' => 'Direct replacement part',
                    'is_verified' => fake()->boolean(80),
                ]);
            }
        }
    }
}
