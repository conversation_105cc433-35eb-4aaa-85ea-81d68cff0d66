<?php

namespace Database\Seeders;

use App\Models\PricingPlan;
use Illuminate\Database\Seeder;

class UpdatePremiumPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plan = PricingPlan::find(2);
        if ($plan) {
            $plan->update([
                'shurjopay_price_id_monthly' => 'shurjo_premium_monthly_19',
                'shurjopay_price_id_yearly' => 'shurjo_premium_yearly_190',
            ]);
            
            echo "Premium plan updated with ShurjoPay integration\n";
        } else {
            echo "Premium plan not found\n";
        }
    }
}
