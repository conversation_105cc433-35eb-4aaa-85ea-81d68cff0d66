<?php

namespace Database\Seeders;

use App\Models\PricingPlan;
use Illuminate\Database\Seeder;

class PricingPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Free Plan
        PricingPlan::updateOrCreate(
            ['name' => 'free'],
            [
                'display_name' => 'Free Plan',
                'description' => 'Perfect for getting started with basic mobile parts search',
                'price' => 0,
                'currency' => 'USD',
                'interval' => 'month',
                'features' => [
                    '20 searches per day',
                    'Basic part information',
                    'Standard resolution images',
                    'Email support',
                ],
                'search_limit' => 20,
                'is_active' => true,
                'is_default' => true,
                'is_popular' => false,
                'sort_order' => 1,
                'metadata' => [
                    'color' => 'gray',
                    'recommended' => false,
                ],
            ]
        );

        // Create Premium Plan
        PricingPlan::updateOrCreate(
            ['name' => 'premium'],
            [
                'display_name' => 'Premium Plan',
                'description' => 'Unlimited access with advanced features for professionals',
                'price' => 19.00,
                'currency' => 'USD',
                'interval' => 'month',
                'features' => [
                    'Unlimited searches',
                    'Detailed specifications',
                    'High-resolution images',
                    'Priority support',
                    'Advanced filters',
                    'Export functionality',
                    'API access',
                ],
                'search_limit' => -1, // Unlimited
                'is_active' => true,
                'is_default' => false,
                'is_popular' => true,
                'sort_order' => 2,
                'metadata' => [
                    'color' => 'blue',
                    'recommended' => true,
                    'stripe_price_id' => null, // To be set when Stripe is configured
                ],
            ]
        );

        // Create Enterprise Plan (for future use)
        PricingPlan::updateOrCreate(
            ['name' => 'enterprise'],
            [
                'display_name' => 'Enterprise Plan',
                'description' => 'Custom solutions for large organizations',
                'price' => 99.00,
                'currency' => 'USD',
                'interval' => 'month',
                'features' => [
                    'Everything in Premium',
                    'Custom integrations',
                    'Dedicated support',
                    'SLA guarantee',
                    'Custom reporting',
                    'Team management',
                    'White-label options',
                ],
                'search_limit' => -1, // Unlimited
                'is_active' => false, // Not active yet
                'is_default' => false,
                'is_popular' => false,
                'sort_order' => 3,
                'metadata' => [
                    'color' => 'purple',
                    'recommended' => false,
                    'contact_sales' => true,
                ],
            ]
        );
    }
}
