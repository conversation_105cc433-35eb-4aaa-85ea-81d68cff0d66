<?php

namespace Database\Seeders;

use App\Models\PricingPlan;
use Illuminate\Database\Seeder;

class ActivateEnterprisePlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Activate the Enterprise plan to make it visible in user view
        $enterprisePlan = PricingPlan::where('name', 'enterprise')->first();
        
        if ($enterprisePlan) {
            $enterprisePlan->update([
                'is_active' => true,
            ]);
            
            $this->command->info('Enterprise plan has been activated and will now be visible in user view.');
        } else {
            $this->command->warn('Enterprise plan not found in database.');
        }
        
        // Also ensure all payment method fields are properly set to avoid null errors
        PricingPlan::whereNull('online_payment_enabled')->update([
            'online_payment_enabled' => true,
        ]);
        
        PricingPlan::whereNull('offline_payment_enabled')->update([
            'offline_payment_enabled' => true,
        ]);
        
        $this->command->info('Payment method settings have been updated for all plans.');
    }
}
