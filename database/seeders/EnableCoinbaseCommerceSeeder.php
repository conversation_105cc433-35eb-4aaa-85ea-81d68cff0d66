<?php

namespace Database\Seeders;

use App\Models\PricingPlan;
use Illuminate\Database\Seeder;

class EnableCoinbaseCommerceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Enable crypto payments for Premium plan
        $premiumPlan = PricingPlan::where('name', 'premium')->first();
        if ($premiumPlan) {
            $premiumPlan->update([
                'crypto_payment_enabled' => true,
                'coinbase_commerce_product_id' => 'pro_premium_mobile_parts_crypto',
                'coinbase_commerce_price_id_monthly' => 'pri_premium_monthly_crypto_19',
                'coinbase_commerce_price_id_yearly' => 'pri_premium_yearly_crypto_190',
            ]);
            $this->command->info('Enabled crypto payments for Premium plan.');
        }

        // Enable crypto payments for Enterprise plan
        $enterprisePlan = PricingPlan::where('name', 'enterprise')->first();
        if ($enterprisePlan) {
            $enterprisePlan->update([
                'crypto_payment_enabled' => true,
                'coinbase_commerce_product_id' => 'pro_enterprise_mobile_parts_crypto',
                'coinbase_commerce_price_id_monthly' => 'pri_enterprise_monthly_crypto_99',
                'coinbase_commerce_price_id_yearly' => 'pri_enterprise_yearly_crypto_990',
            ]);
            $this->command->info('Enabled crypto payments for Enterprise plan.');
        }

        // Free plan doesn't need crypto payments as it's free
        $freePlan = PricingPlan::where('name', 'free')->first();
        if ($freePlan) {
            $freePlan->update([
                'crypto_payment_enabled' => false,
            ]);
            $this->command->info('Crypto payments disabled for Free plan (not applicable).');
        }

        $this->command->info('Coinbase Commerce integration enabled for applicable pricing plans.');
    }
}
