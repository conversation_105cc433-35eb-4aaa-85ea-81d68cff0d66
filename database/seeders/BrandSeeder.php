<?php

namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $brands = [
            [
                'name' => 'Apple',
                'country' => 'United States',
                'website' => 'https://www.apple.com',
            ],
            [
                'name' => 'Samsung',
                'country' => 'South Korea',
                'website' => 'https://www.samsung.com',
            ],
            [
                'name' => 'Xiaomi',
                'country' => 'China',
                'website' => 'https://www.mi.com',
            ],
            [
                'name' => 'OnePlus',
                'country' => 'China',
                'website' => 'https://www.oneplus.com',
            ],
            [
                'name' => 'Google',
                'country' => 'United States',
                'website' => 'https://store.google.com',
            ],
            [
                'name' => 'Huawei',
                'country' => 'China',
                'website' => 'https://www.huawei.com',
            ],
            [
                'name' => 'Oppo',
                'country' => 'China',
                'website' => 'https://www.oppo.com',
            ],
            [
                'name' => 'Vivo',
                'country' => 'China',
                'website' => 'https://www.vivo.com',
            ],
            [
                'name' => 'Realme',
                'country' => 'China',
                'website' => 'https://www.realme.com',
            ],
            [
                'name' => 'Nothing',
                'country' => 'United Kingdom',
                'website' => 'https://www.nothing.tech',
            ],
        ];

        foreach ($brands as $brandData) {
            Brand::create($brandData);
        }
    }
}
