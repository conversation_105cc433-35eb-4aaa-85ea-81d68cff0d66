<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shurjopay_transactions', function (Blueprint $table) {
            // Change checkout_url from VARCHAR(255) to TEXT to accommodate longer URLs
            $table->text('checkout_url')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shurjopay_transactions', function (Blueprint $table) {
            // Revert back to string (VARCHAR(255))
            $table->string('checkout_url')->nullable()->change();
        });
    }
};
