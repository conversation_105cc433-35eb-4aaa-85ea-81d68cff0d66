<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shurjopay_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('shurjopay_order_id')->unique(); // sp_order_id from ShurjoPay
            $table->string('merchant_order_id')->unique(); // Our generated order ID
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('pricing_plan_id')->nullable()->constrained()->onDelete('set null');
            $table->string('status'); // pending, completed, failed, cancelled, declined
            $table->string('currency', 3);
            $table->decimal('amount', 10, 2);
            $table->decimal('payable_amount', 10, 2)->nullable();
            $table->decimal('discount_amount', 10, 2)->nullable();
            $table->decimal('received_amount', 10, 2)->nullable();
            $table->integer('disc_percent')->nullable();
            $table->string('sp_code')->nullable(); // ShurjoPay response code
            $table->string('sp_message')->nullable(); // ShurjoPay response message
            $table->string('method')->nullable(); // Payment method used
            $table->string('bank_trx_id')->nullable();
            $table->string('invoice_no')->nullable();
            $table->string('bank_status')->nullable();
            $table->json('customer_details')->nullable(); // Store customer information
            $table->json('shurjopay_data')->nullable(); // Store full ShurjoPay response
            $table->string('checkout_url')->nullable(); // ShurjoPay checkout URL
            $table->timestamp('shurjopay_created_at')->nullable();
            $table->timestamps();

            $table->index('shurjopay_order_id');
            $table->index('merchant_order_id');
            $table->index('status');
            $table->index('sp_code');
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shurjopay_transactions');
    }
};
