<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coinbase_commerce_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('coinbase_charge_id')->unique(); // Coinbase Commerce charge ID
            $table->string('merchant_order_id')->unique(); // Our generated order ID
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('pricing_plan_id')->nullable()->constrained()->onDelete('set null');
            $table->string('status'); // pending, completed, failed, expired, cancelled
            $table->string('currency', 10); // Crypto currency (BTC, ETH, USDC, etc.)
            $table->decimal('amount', 20, 8); // Requested amount in USD
            $table->decimal('crypto_amount', 30, 18)->nullable(); // Amount in cryptocurrency
            $table->decimal('received_amount', 30, 18)->nullable(); // Actually received amount
            $table->json('addresses')->nullable(); // Payment addresses for different cryptocurrencies
            $table->json('timeline')->nullable(); // Payment timeline events
            $table->json('metadata')->nullable(); // Additional metadata
            $table->json('coinbase_data')->nullable(); // Store full Coinbase Commerce response
            $table->string('hosted_url')->nullable(); // Coinbase Commerce hosted payment URL
            $table->timestamp('expires_at')->nullable(); // When the charge expires
            $table->timestamp('confirmed_at')->nullable(); // When payment was confirmed
            $table->timestamp('coinbase_created_at')->nullable(); // Coinbase creation timestamp
            $table->timestamp('coinbase_updated_at')->nullable(); // Coinbase last update timestamp
            $table->timestamps();

            // Indexes for performance
            $table->index('coinbase_charge_id');
            $table->index('merchant_order_id');
            $table->index('status');
            $table->index(['user_id', 'status']);
            $table->index(['subscription_id', 'status']);
            $table->index(['pricing_plan_id', 'status']);
            $table->index('expires_at');
            $table->index('confirmed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coinbase_commerce_transactions');
    }
};
