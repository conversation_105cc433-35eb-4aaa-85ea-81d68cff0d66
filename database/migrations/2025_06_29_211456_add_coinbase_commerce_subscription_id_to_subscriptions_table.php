<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->string('coinbase_commerce_subscription_id')->nullable()->after('payment_gateway');
            $table->index('coinbase_commerce_subscription_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex(['coinbase_commerce_subscription_id']);
            $table->dropColumn('coinbase_commerce_subscription_id');
        });
    }
};
