<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // User status and approval status fields
            $table->enum('status', ['active', 'pending', 'suspended', 'banned'])->default('active')->after('email_verified_at');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('approved')->after('status');
            $table->foreignId('approved_by')->nullable()->constrained('users')->after('approval_status');
            $table->timestamp('approved_at')->nullable()->after('approved_by');

            // Suspension fields
            $table->timestamp('suspended_at')->nullable()->after('approved_at');
            $table->foreignId('suspended_by')->nullable()->constrained('users')->after('suspended_at');
            $table->text('suspension_reason')->nullable()->after('suspended_by');
            $table->timestamp('suspension_expires_at')->nullable()->after('suspension_reason');

            // Activity tracking fields
            $table->timestamp('last_login_at')->nullable()->after('suspension_expires_at');
            $table->integer('login_count')->default(0)->after('last_login_at');

            // Add indexes for performance
            $table->index(['status', 'approval_status']);
            $table->index('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['status', 'approval_status']);
            $table->dropIndex(['last_login_at']);

            // Drop foreign key constraints
            $table->dropForeign(['approved_by']);
            $table->dropForeign(['suspended_by']);

            // Drop columns
            $table->dropColumn([
                'status',
                'approval_status',
                'approved_by',
                'approved_at',
                'suspended_at',
                'suspended_by',
                'suspension_reason',
                'suspension_expires_at',
                'last_login_at',
                'login_count'
            ]);
        });
    }
};
