<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('coinbase_commerce_customer_id')->nullable()->after('paddle_customer_id');
            $table->index('coinbase_commerce_customer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['coinbase_commerce_customer_id']);
            $table->dropColumn('coinbase_commerce_customer_id');
        });
    }
};
