<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->boolean('online_payment_enabled')->default(true)->after('paddle_product_id');
            $table->boolean('offline_payment_enabled')->default(true)->after('online_payment_enabled');

            $table->index('online_payment_enabled');
            $table->index('offline_payment_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->dropIndex(['online_payment_enabled']);
            $table->dropIndex(['offline_payment_enabled']);

            $table->dropColumn([
                'online_payment_enabled',
                'offline_payment_enabled'
            ]);
        });
    }
};
