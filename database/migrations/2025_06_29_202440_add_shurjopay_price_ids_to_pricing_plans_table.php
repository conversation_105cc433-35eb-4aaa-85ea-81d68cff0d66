<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->string('shurjopay_price_id_monthly')->nullable()->after('paddle_product_id');
            $table->string('shurjopay_price_id_yearly')->nullable()->after('shurjopay_price_id_monthly');
            $table->string('shurjopay_product_id')->nullable()->after('shurjopay_price_id_yearly');

            $table->index('shurjopay_price_id_monthly');
            $table->index('shurjopay_price_id_yearly');
            $table->index('shurjopay_product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->dropIndex(['shurjopay_price_id_monthly']);
            $table->dropIndex(['shurjopay_price_id_yearly']);
            $table->dropIndex(['shurjopay_product_id']);

            $table->dropColumn([
                'shurjopay_price_id_monthly',
                'shurjopay_price_id_yearly',
                'shurjopay_product_id'
            ]);
        });
    }
};
