<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paddle_webhooks', function (Blueprint $table) {
            $table->id();
            $table->string('paddle_event_id')->unique();
            $table->string('event_type');
            $table->string('status')->default('pending'); // pending, processed, failed
            $table->json('payload'); // Store the full webhook payload
            $table->string('signature')->nullable();
            $table->timestamp('paddle_occurred_at');
            $table->timestamp('processed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamps();

            $table->index('paddle_event_id');
            $table->index('event_type');
            $table->index('status');
            $table->index('paddle_occurred_at');
            $table->index(['event_type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paddle_webhooks');
    }
};
