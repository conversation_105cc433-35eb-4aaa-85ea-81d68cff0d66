<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->string('shurjopay_subscription_id')->nullable()->after('paddle_subscription_id');
            $table->string('payment_gateway')->default('paddle')->after('shurjopay_subscription_id'); // paddle, shurjopay, offline

            $table->index('shurjopay_subscription_id');
            $table->index('payment_gateway');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex(['shurjopay_subscription_id']);
            $table->dropIndex(['payment_gateway']);

            $table->dropColumn([
                'shurjopay_subscription_id',
                'payment_gateway'
            ]);
        });
    }
};
