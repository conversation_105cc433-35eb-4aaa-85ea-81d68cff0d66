<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->foreignId('pricing_plan_id')->nullable()->after('plan_name')->constrained('pricing_plans')->onDelete('set null');
            $table->index('pricing_plan_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropForeign(['pricing_plan_id']);
            $table->dropIndex(['pricing_plan_id']);
            $table->dropColumn('pricing_plan_id');
        });
    }
};
