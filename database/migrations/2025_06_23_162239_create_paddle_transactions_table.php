<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paddle_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('paddle_transaction_id')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->string('paddle_customer_id');
            $table->string('paddle_subscription_id')->nullable();
            $table->string('status'); // pending, completed, failed, cancelled
            $table->string('currency', 3);
            $table->decimal('amount', 10, 2);
            $table->decimal('tax_amount', 10, 2)->nullable();
            $table->decimal('total_amount', 10, 2);
            $table->json('items'); // Store transaction items
            $table->json('billing_details')->nullable();
            $table->json('checkout_details')->nullable();
            $table->json('paddle_data')->nullable(); // Store full Paddle response
            $table->timestamp('paddle_created_at')->nullable();
            $table->timestamp('paddle_updated_at')->nullable();
            $table->timestamps();

            $table->index('paddle_transaction_id');
            $table->index('paddle_customer_id');
            $table->index('paddle_subscription_id');
            $table->index('status');
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paddle_transactions');
    }
};
