<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->string('paddle_price_id_monthly')->nullable()->after('metadata');
            $table->string('paddle_price_id_yearly')->nullable()->after('paddle_price_id_monthly');
            $table->string('paddle_product_id')->nullable()->after('paddle_price_id_yearly');

            $table->index('paddle_price_id_monthly');
            $table->index('paddle_price_id_yearly');
            $table->index('paddle_product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->dropIndex(['paddle_price_id_monthly']);
            $table->dropIndex(['paddle_price_id_yearly']);
            $table->dropIndex(['paddle_product_id']);

            $table->dropColumn([
                'paddle_price_id_monthly',
                'paddle_price_id_yearly',
                'paddle_product_id'
            ]);
        });
    }
};
