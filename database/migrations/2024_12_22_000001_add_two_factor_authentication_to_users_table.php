<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('two_factor_enabled')->default(false)->after('password');
            $table->timestamp('two_factor_confirmed_at')->nullable()->after('two_factor_enabled');
            $table->string('current_otp_code', 6)->nullable()->after('two_factor_confirmed_at');
            $table->timestamp('otp_expires_at')->nullable()->after('current_otp_code');
            $table->integer('otp_attempts')->default(0)->after('otp_expires_at');
            $table->timestamp('otp_last_attempt_at')->nullable()->after('otp_attempts');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'two_factor_enabled',
                'two_factor_confirmed_at',
                'current_otp_code',
                'otp_expires_at',
                'otp_attempts',
                'otp_last_attempt_at'
            ]);
        });
    }
};
