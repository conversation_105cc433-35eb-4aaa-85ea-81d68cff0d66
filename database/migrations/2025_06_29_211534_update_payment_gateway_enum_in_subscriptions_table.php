<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if we're using SQLite (for tests) or MySQL/PostgreSQL
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // SQLite doesn't support MODIFY COLUMN, but the column is already VARCHAR
            // so we don't need to do anything for SQLite
            return;
        }

        // For MySQL/PostgreSQL, modify the column to support longer values
        if ($driver === 'mysql') {
            DB::statement("ALTER TABLE subscriptions MODIFY COLUMN payment_gateway VARCHAR(50) DEFAULT 'paddle'");
        } elseif ($driver === 'pgsql') {
            DB::statement("ALTER TABLE subscriptions ALTER COLUMN payment_gateway TYPE VARCHAR(50)");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to revert as we're just expanding the column size
        // The original column was already VARCHAR, we're just making it longer
    }
};
