<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if we're using SQLite (for testing) or MySQL (for production)
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // SQLite doesn't support MODIFY COLUMN, so we'll recreate the table
            Schema::table('user_notifications', function (Blueprint $table) {
                // For SQLite, we'll just ensure the column exists with the right type
                // SQLite doesn't enforce ENUM constraints anyway
                if (!Schema::hasColumn('user_notifications', 'type')) {
                    $table->string('type')->default('info');
                }
            });
        } else {
            // MySQL/MariaDB - use MODIFY COLUMN
            DB::statement("ALTER TABLE user_notifications MODIFY COLUMN type ENUM('info', 'warning', 'success', 'error', 'announcement') DEFAULT 'info'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if we're using SQLite (for testing) or MySQL (for production)
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // For SQLite, we don't need to do anything as it doesn't enforce ENUM constraints
            // The column will remain as a string type
        } else {
            // MySQL/MariaDB - revert the enum (this will fail if there are 'announcement' records)
            DB::statement("ALTER TABLE user_notifications MODIFY COLUMN type ENUM('info', 'warning', 'success', 'error') DEFAULT 'info'");
        }
    }
};
