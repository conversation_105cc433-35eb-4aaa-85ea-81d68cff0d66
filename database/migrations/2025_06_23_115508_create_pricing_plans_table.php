<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricing_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'free', 'premium', 'enterprise'
            $table->string('display_name'); // e.g., 'Free Plan', 'Premium Plan'
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2)->default(0); // Monthly price
            $table->string('currency', 3)->default('USD');
            $table->enum('interval', ['month', 'year'])->default('month');
            $table->json('features')->nullable(); // Array of feature descriptions
            $table->integer('search_limit')->default(20); // -1 for unlimited
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // For new users
            $table->boolean('is_popular')->default(false); // Highlight in UI
            $table->integer('sort_order')->default(0); // Display order
            $table->json('metadata')->nullable(); // Additional configuration
            $table->timestamps();

            // Indexes for performance
            $table->index(['is_active', 'sort_order']);
            $table->index('is_default');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pricing_plans');
    }
};
