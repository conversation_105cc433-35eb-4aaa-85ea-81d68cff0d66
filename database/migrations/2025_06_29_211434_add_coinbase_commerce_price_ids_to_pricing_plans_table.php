<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->string('coinbase_commerce_price_id_monthly')->nullable()->after('offline_payment_enabled');
            $table->string('coinbase_commerce_price_id_yearly')->nullable()->after('coinbase_commerce_price_id_monthly');
            $table->string('coinbase_commerce_product_id')->nullable()->after('coinbase_commerce_price_id_yearly');
            $table->boolean('crypto_payment_enabled')->default(false)->after('coinbase_commerce_product_id');

            $table->index('coinbase_commerce_price_id_monthly');
            $table->index('coinbase_commerce_price_id_yearly');
            $table->index('coinbase_commerce_product_id');
            $table->index('crypto_payment_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->dropIndex(['coinbase_commerce_price_id_monthly']);
            $table->dropIndex(['coinbase_commerce_price_id_yearly']);
            $table->dropIndex(['coinbase_commerce_product_id']);
            $table->dropIndex(['crypto_payment_enabled']);

            $table->dropColumn([
                'coinbase_commerce_price_id_monthly',
                'coinbase_commerce_price_id_yearly',
                'coinbase_commerce_product_id',
                'crypto_payment_enabled'
            ]);
        });
    }
};
