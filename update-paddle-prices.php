<?php

/**
 * Update Paddle Price IDs
 * 
 * This script updates the pricing plan with real Paddle price IDs.
 */

require_once 'vendor/autoload.php';

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔧 Updating Paddle Price IDs...\n\n";

// Get the pricing plan
$plan = App\Models\PricingPlan::find(2);

if (!$plan) {
    echo "❌ Pricing plan not found!\n";
    exit(1);
}

echo "📋 Current Configuration:\n";
echo "   Plan: " . $plan->name . "\n";
echo "   Current Monthly Price ID: " . $plan->paddle_price_id_monthly . "\n";
echo "   Current Yearly Price ID: " . $plan->paddle_price_id_yearly . "\n\n";

// Update with real price IDs
echo "🔄 Updating with real Paddle price IDs...\n";

try {
    $plan->update([
        'paddle_price_id_monthly' => 'pri_01jyzyttcz601pzerg007fwyaf',
        // Keep yearly as placeholder for now, or add your yearly price ID if you have one
        'paddle_price_id_yearly' => $plan->paddle_price_id_yearly
    ]);
    
    // Refresh the model to get updated values
    $plan = $plan->fresh();
    
    echo "✅ SUCCESS: Price IDs updated!\n\n";
    
    echo "📋 Updated Configuration:\n";
    echo "   Plan: " . $plan->name . "\n";
    echo "   New Monthly Price ID: " . $plan->paddle_price_id_monthly . "\n";
    echo "   Yearly Price ID: " . $plan->paddle_price_id_yearly . "\n\n";
    
    // Test if the new price ID is recognized as real (not placeholder)
    $paddleService = app(App\Services\PaddleService::class);
    $isPlaceholder = $paddleService->isPlaceholderValue($plan->paddle_price_id_monthly);
    
    echo "🧪 Validation:\n";
    echo "   Monthly Price ID is placeholder: " . ($isPlaceholder ? "YES ❌" : "NO ✅") . "\n";
    
    if (!$isPlaceholder) {
        echo "\n🎉 EXCELLENT! Your Paddle integration is now ready for real payments!\n";
        echo "   You can now test the checkout flow with real Paddle checkout sessions.\n";
    } else {
        echo "\n⚠️  The price ID is still being detected as a placeholder.\n";
        echo "   This might be due to the detection logic. Let's test the checkout anyway.\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: Failed to update price IDs\n";
    echo "   Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🚀 Next steps:\n";
echo "   1. Test the checkout flow in your browser\n";
echo "   2. If you have a yearly price ID, update it too\n";
echo "   3. Configure webhook URL in Paddle dashboard: http://127.0.0.1:8000/webhooks/paddle\n\n";
