/.phpunit.cache
/bootstrap/ssr
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
/screenshots
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/auth.json
/.fleet
/.idea
/.nova
/.vscode
/.zed
/screenshot

# Compressed files
*.zip
*.rar
*.7z
*.gz
*.tar
*.tar.gz

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Laravel specific
/public/css
/public/js
/public/mix-manifest.json
/public/sitemap.xml
/storage/debugbar
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/logs/*
/storage/app/public/*

# Dependency managers
/composer.phar
/.php-cs-fixer.cache
/package-lock.json
/yarn.lock
/pnpm-lock.yaml

# Testing
/coverage
/coverage-report
/phpunit.xml
.phpcs.xml

# Local development
docker-compose.override.yml
Vagrantfile
.vagrant/

# Build tools
/.sass-cache
/.phpcs-cache
/node_modules
/bower_components

# Git Commit Message Text
git-message.txt