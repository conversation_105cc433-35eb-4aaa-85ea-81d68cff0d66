<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Plugin Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the plugin system.
    | You can register plugins here or they can be auto-discovered.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Plugin System
    |--------------------------------------------------------------------------
    |
    | Enable or disable the entire plugin system.
    |
    */
    'enabled' => env('PLUGIN_SYSTEM_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Plugin Discovery
    |--------------------------------------------------------------------------
    |
    | Enable automatic plugin discovery from the plugins directory.
    |
    */
    'auto_discovery' => env('PLUGIN_AUTO_DISCOVERY', false),

    /*
    |--------------------------------------------------------------------------
    | Plugin Directory
    |--------------------------------------------------------------------------
    |
    | The directory where plugins are stored.
    |
    */
    'directory' => app_path('Plugins'),

    /*
    |--------------------------------------------------------------------------
    | Registered Plugins
    |--------------------------------------------------------------------------
    |
    | Manually registered plugins. Each plugin should have a service provider
    | that extends the BasePlugin class.
    |
    */
    'registered' => [
        'simple' => [
            'service_provider' => \App\Plugins\Simple\SimplePlugin::class,
            'enabled' => env('PLUGIN_SIMPLE_ENABLED', false), // Disabled by default
            'version' => '1.0.0',
            'description' => 'A simple plugin for testing the plugin system',
            'author' => 'Mobile Parts DB Team',
            'dependencies' => [],
            'config' => [],
        ],

        'analytics' => [
            'service_provider' => \App\Plugins\Analytics\AnalyticsPlugin::class,
            'enabled' => env('PLUGIN_ANALYTICS_ENABLED', false), // Disabled temporarily
            'version' => '1.0.0',
            'description' => 'Provides analytics and tracking functionality',
            'author' => 'Mobile Parts DB Team',
            'dependencies' => [],
            'config' => [
                'track_searches' => true,
                'track_part_views' => true,
                'track_subscriptions' => true,
                'retention_days' => 90,
            ],
        ],

        // Example of a disabled plugin
        'example_plugin' => [
            'service_provider' => \App\Plugins\Example\ExamplePlugin::class,
            'enabled' => false,
            'version' => '1.0.0',
            'description' => 'Example plugin for demonstration',
            'author' => 'Mobile Parts DB Team',
            'dependencies' => [],
            'config' => [],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Plugin Hooks
    |--------------------------------------------------------------------------
    |
    | Available hooks that plugins can register for.
    |
    */
    'hooks' => [
        // Search hooks
        'search_before' => 'Executed before search is performed',
        'search_after' => 'Executed after search is performed',
        'search_results' => 'Modify search results',
        'search_filters' => 'Modify search filters',

        // Part hooks
        'part_before_view' => 'Executed before part is viewed',
        'part_after_view' => 'Executed after part is viewed',
        'part_details' => 'Modify part details',
        'part_related' => 'Modify related parts',

        // User hooks
        'user_registered' => 'Executed when user registers',
        'user_subscribed' => 'Executed when user subscribes',
        'user_profile' => 'Modify user profile data',

        // Admin hooks
        'admin_dashboard' => 'Modify admin dashboard',
        'admin_stats' => 'Modify admin statistics',
        'admin_menu' => 'Modify admin menu',

        // Frontend hooks
        'frontend_head' => 'Add content to HTML head',
        'frontend_footer' => 'Add content to footer',
        'frontend_sidebar' => 'Add content to sidebar',
    ],

    /*
    |--------------------------------------------------------------------------
    | Plugin Security
    |--------------------------------------------------------------------------
    |
    | Security settings for the plugin system.
    |
    */
    'security' => [
        'verify_signatures' => env('PLUGIN_VERIFY_SIGNATURES', false),
        'allowed_authors' => [
            'Mobile Parts DB Team',
            // Add trusted plugin authors here
        ],
        'sandbox_mode' => env('PLUGIN_SANDBOX_MODE', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Plugin Cache
    |--------------------------------------------------------------------------
    |
    | Cache settings for plugin discovery and loading.
    |
    */
    'cache' => [
        'enabled' => env('PLUGIN_CACHE_ENABLED', true),
        'ttl' => env('PLUGIN_CACHE_TTL', 3600), // 1 hour
        'key_prefix' => 'plugins:',
    ],

    /*
    |--------------------------------------------------------------------------
    | Plugin API
    |--------------------------------------------------------------------------
    |
    | Settings for plugin API endpoints.
    |
    */
    'api' => [
        'enabled' => env('PLUGIN_API_ENABLED', true),
        'prefix' => 'api/plugins',
        'middleware' => ['auth:sanctum', 'admin'],
    ],
];
