<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Coinbase Commerce API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Coinbase Commerce payment gateway integration.
    | This includes API credentials, webhook settings, and other options.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | API Key
    |--------------------------------------------------------------------------
    |
    | Your Coinbase Commerce API key. You can get this from your Coinbase
    | Commerce dashboard at https://commerce.coinbase.com/
    |
    */
    'api_key' => env('COINBASE_COMMERCE_API_KEY', ''),

    /*
    |--------------------------------------------------------------------------
    | Base URL
    |--------------------------------------------------------------------------
    |
    | The base URL for Coinbase Commerce API. This should not need to be
    | changed unless Coinbase Commerce changes their API endpoint.
    |
    */
    'base_url' => env('COINBASE_COMMERCE_BASE_URL', 'https://api.commerce.coinbase.com'),

    /*
    |--------------------------------------------------------------------------
    | Webhook Secret
    |--------------------------------------------------------------------------
    |
    | The webhook secret used to verify webhook signatures from Coinbase
    | Commerce. This is optional but recommended for security.
    |
    */
    'webhook_secret' => env('COINBASE_COMMERCE_WEBHOOK_SECRET', ''),

    /*
    |--------------------------------------------------------------------------
    | Debug Mode
    |--------------------------------------------------------------------------
    |
    | Enable debug mode to log detailed information about API requests and
    | responses. This should be disabled in production.
    |
    */
    'debug' => env('COINBASE_COMMERCE_DEBUG', env('APP_DEBUG', false)),

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | The default currency for charges when not specified. This should match
    | your application's default currency.
    |
    */
    'default_currency' => env('COINBASE_COMMERCE_DEFAULT_CURRENCY', 'USD'),

    /*
    |--------------------------------------------------------------------------
    | Supported Cryptocurrencies
    |--------------------------------------------------------------------------
    |
    | List of cryptocurrencies supported by your application. With the new
    | Onchain Payment Protocol, customers can pay in hundreds of currencies
    | that are automatically converted to USDC for settlement.
    |
    */
    'supported_currencies' => [
        'BTC' => 'Bitcoin',
        'ETH' => 'Ethereum',
        'LTC' => 'Litecoin',
        'BCH' => 'Bitcoin Cash',
        'USDC' => 'USD Coin',
        'DAI' => 'Dai',
    ],

    /*
    |--------------------------------------------------------------------------
    | Onchain Payment Protocol Features
    |--------------------------------------------------------------------------
    |
    | Configuration for the new Onchain Payment Protocol features that provide
    | enhanced payment capabilities and automatic USDC settlement.
    |
    */
    'onchain_protocol' => [
        'auto_usdc_settlement' => env('COINBASE_COMMERCE_AUTO_USDC_SETTLEMENT', true),
        'supported_networks' => ['base', 'polygon', 'ethereum'],
        'instant_confirmation' => env('COINBASE_COMMERCE_INSTANT_CONFIRMATION', true),
        'hundreds_of_currencies' => true,
        'volatility_protection' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Charge Expiration
    |--------------------------------------------------------------------------
    |
    | Default expiration time for charges in minutes. Coinbase Commerce
    | charges expire after a certain time if not paid.
    |
    */
    'charge_expiration_minutes' => env('COINBASE_COMMERCE_CHARGE_EXPIRATION', 60),

    /*
    |--------------------------------------------------------------------------
    | Webhook URL
    |--------------------------------------------------------------------------
    |
    | The URL where Coinbase Commerce will send webhook notifications.
    | This is automatically generated but can be overridden if needed.
    |
    */
    'webhook_url' => env('COINBASE_COMMERCE_WEBHOOK_URL', null),

    /*
    |--------------------------------------------------------------------------
    | Auto-activate Subscriptions
    |--------------------------------------------------------------------------
    |
    | Whether to automatically activate subscriptions when a payment is
    | confirmed. Set to false if you want to manually review payments.
    |
    */
    'auto_activate_subscriptions' => env('COINBASE_COMMERCE_AUTO_ACTIVATE', true),

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for logging Coinbase Commerce events and transactions.
    |
    */
    'logging' => [
        'enabled' => env('COINBASE_COMMERCE_LOGGING_ENABLED', true),
        'level' => env('COINBASE_COMMERCE_LOGGING_LEVEL', 'info'),
        'channel' => env('COINBASE_COMMERCE_LOGGING_CHANNEL', 'stack'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration for rate limiting API requests to Coinbase Commerce.
    |
    */
    'rate_limiting' => [
        'enabled' => env('COINBASE_COMMERCE_RATE_LIMITING_ENABLED', true),
        'max_requests_per_minute' => env('COINBASE_COMMERCE_MAX_REQUESTS_PER_MINUTE', 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | Retry Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for retrying failed API requests.
    |
    */
    'retry' => [
        'enabled' => env('COINBASE_COMMERCE_RETRY_ENABLED', true),
        'max_attempts' => env('COINBASE_COMMERCE_MAX_RETRY_ATTEMPTS', 3),
        'delay_seconds' => env('COINBASE_COMMERCE_RETRY_DELAY', 1),
    ],
];
