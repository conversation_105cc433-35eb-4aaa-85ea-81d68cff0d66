<?php

return [
    /*
    |--------------------------------------------------------------------------
    | ShurjoPay Environment
    |--------------------------------------------------------------------------
    |
    | This value determines which ShurjoPay environment your application is
    | currently running in. This may determine how you will configure various
    | services that your application utilizes. Set this in your ".env" file.
    |
    | Supported: "sandbox", "production"
    |
    */

    'environment' => env('SHURJOPAY_ENVIRONMENT', 'sandbox'),

    /*
    |--------------------------------------------------------------------------
    | ShurjoPay API Configuration
    |--------------------------------------------------------------------------
    |
    | These are your ShurjoPay API credentials which are used for server-side
    | operations such as authentication, creating payments, and verifying
    | transactions. These should be kept secret.
    |
    */

    'username' => env('SHURJOPAY_USERNAME', 'sp_sandbox'),
    'password' => env('SHURJOPAY_PASSWORD', 'pyyk97hu&6u6'),
    'prefix' => env('SHURJOPAY_PREFIX', 'NOK'),

    /*
    |--------------------------------------------------------------------------
    | ShurjoPay API URLs
    |--------------------------------------------------------------------------
    |
    | These are the API endpoints for ShurjoPay. The sandbox URL is used
    | for testing while the production URL is used for live transactions.
    |
    */

    'api_url' => env('SHURJOPAY_API_URL', 'https://sandbox.shurjopayment.com'),

    'urls' => [
        'sandbox' => 'https://sandbox.shurjopayment.com',
        'production' => 'https://engine.shurjopayment.com',
    ],

    /*
    |--------------------------------------------------------------------------
    | Callback URLs
    |--------------------------------------------------------------------------
    |
    | These URLs are used for redirecting customers after payment completion
    | or cancellation. They should be publicly accessible endpoints.
    |
    */

    'callback_url' => env('SHURJOPAY_CALLBACK_URL', env('APP_URL') . '/shurjopay/callback'),
    'success_url' => env('APP_URL') . '/subscription/success',
    'cancel_url' => env('APP_URL') . '/subscription/cancel',

    /*
    |--------------------------------------------------------------------------
    | IPN (Instant Payment Notification) Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for handling ShurjoPay IPN notifications.
    |
    */

    'ipn_url' => env('APP_URL') . '/webhooks/shurjopay',

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | This is the default currency for your ShurjoPay transactions.
    | ShurjoPay primarily supports BDT (Bangladeshi Taka).
    |
    */

    'currency' => env('SHURJOPAY_CURRENCY', 'BDT'),

    /*
    |--------------------------------------------------------------------------
    | SSL Configuration
    |--------------------------------------------------------------------------
    |
    | SSL verification settings for API requests. Set to false only for
    | local development environments.
    |
    */

    'ssl_verify' => env('SHURJOPAY_SSL_VERIFY', true),

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Enable logging for ShurjoPay operations. This is useful for debugging
    | and monitoring your ShurjoPay integration.
    |
    */

    'logging' => [
        'enabled' => env('SHURJOPAY_LOGGING_ENABLED', true),
        'channel' => env('SHURJOPAY_LOGGING_CHANNEL', 'daily'),
        'level' => env('SHURJOPAY_LOGGING_LEVEL', 'info'),
        'location' => env('SHURJOPAY_LOG_LOCATION', storage_path('logs/shurjopay')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Timeout Configuration
    |--------------------------------------------------------------------------
    |
    | HTTP timeout configuration for ShurjoPay API requests.
    |
    */

    'timeout' => [
        'connect' => 10,
        'request' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    |
    | List of currencies supported by ShurjoPay.
    |
    */

    'supported_currencies' => ['BDT', 'USD', 'EUR', 'GBP'],

    /*
    |--------------------------------------------------------------------------
    | Debug Configuration
    |--------------------------------------------------------------------------
    |
    | Enable debug mode for development environment to get detailed
    | error messages and request/response logging.
    |
    */

    'debug' => env('APP_DEBUG', false),
];
