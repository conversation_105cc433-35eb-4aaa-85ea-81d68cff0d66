<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Paddle Environment
    |--------------------------------------------------------------------------
    |
    | This value determines which Paddle environment your application is
    | currently running in. This may determine how you will configure various
    | services that your application utilizes. Set this in your ".env" file.
    |
    | Supported: "sandbox", "production"
    |
    */

    'environment' => env('PADDLE_ENVIRONMENT', 'sandbox'),

    /*
    |--------------------------------------------------------------------------
    | Paddle API Key
    |--------------------------------------------------------------------------
    |
    | This is your Paddle API key which is used for server-side operations
    | such as creating checkout sessions, managing subscriptions, and
    | handling webhooks. This should be kept secret.
    |
    */

    'api_key' => env('PADDLE_API_KEY'),

    /*
    |--------------------------------------------------------------------------
    | Paddle Client Token
    |--------------------------------------------------------------------------
    |
    | This is your Paddle client-side token which is used for frontend
    | operations such as initializing Paddle.js and creating checkout
    | sessions from the browser.
    |
    */

    'client_token' => env('PADDLE_CLIENT_TOKEN'),

    /*
    |--------------------------------------------------------------------------
    | Paddle Webhook Secret
    |--------------------------------------------------------------------------
    |
    | This is your Paddle webhook secret which is used to verify that
    | webhook requests are actually coming from Paddle. This should
    | be kept secret.
    |
    */

    'webhook_secret' => env('PADDLE_WEBHOOK_SECRET'),

    /*
    |--------------------------------------------------------------------------
    | Paddle Webhook URL
    |--------------------------------------------------------------------------
    |
    | This is the URL that Paddle will send webhook events to. This should
    | be publicly accessible and point to your webhook handler endpoint.
    |
    */

    'webhook_url' => env('PADDLE_WEBHOOK_URL', env('APP_URL') . '/webhooks/paddle'),

    /*
    |--------------------------------------------------------------------------
    | Paddle Vendor ID
    |--------------------------------------------------------------------------
    |
    | This is your Paddle vendor ID which identifies your account.
    | This is used for some API operations.
    |
    */

    'vendor_id' => env('PADDLE_VENDOR_ID'),

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | This is the default currency for your Paddle transactions.
    | This should match your primary business currency.
    |
    */

    'currency' => env('PADDLE_CURRENCY', 'USD'),

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Enable logging for Paddle operations. This is useful for debugging
    | and monitoring your Paddle integration.
    |
    */

    'logging' => [
        'enabled' => env('PADDLE_LOGGING_ENABLED', true),
        'channel' => env('PADDLE_LOGGING_CHANNEL', 'daily'),
        'level' => env('PADDLE_LOGGING_LEVEL', 'info'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Retry Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for retrying failed Paddle API requests.
    |
    */

    'retry' => [
        'max_attempts' => env('PADDLE_RETRY_MAX_ATTEMPTS', 3),
        'delay_ms' => env('PADDLE_RETRY_DELAY_MS', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Timeout Configuration
    |--------------------------------------------------------------------------
    |
    | HTTP timeout configuration for Paddle API requests.
    |
    */

    'timeout' => [
        'connect' => env('PADDLE_CONNECT_TIMEOUT', 10),
        'request' => env('PADDLE_REQUEST_TIMEOUT', 30),
    ],
];
