<?php

namespace Tests\Feature;

use App\Models\PricingPlan;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PricingPlanVisibilityTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test pricing plans
        PricingPlan::create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 20,
            'features' => ['20 searches per day', 'Basic part information'],
            'is_active' => true,
            'sort_order' => 1,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
        ]);

        PricingPlan::create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 19,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => -1,
            'features' => ['Unlimited searches', 'Detailed specifications'],
            'is_active' => true,
            'sort_order' => 2,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
        ]);

        PricingPlan::create([
            'name' => 'enterprise',
            'display_name' => 'Enterprise Plan',
            'price' => 99,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => -1,
            'features' => ['Everything in Premium', 'Custom integrations'],
            'is_active' => true, // This should be true to show in user view
            'sort_order' => 3,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
        ]);
    }

    #[Test]
    public function all_three_pricing_plans_are_visible_in_user_view()
    {
        $subscriptionService = new SubscriptionService();
        $plans = $subscriptionService->getPlans();

        // Should have all 3 plans
        $this->assertCount(3, $plans);

        // Should have all expected plan names
        $this->assertArrayHasKey('free', $plans);
        $this->assertArrayHasKey('premium', $plans);
        $this->assertArrayHasKey('enterprise', $plans);
    }

    #[Test]
    public function admin_view_shows_all_pricing_plans()
    {
        $allPlans = PricingPlan::withCount('subscriptions')->ordered()->get();

        // Should have all 3 plans
        $this->assertCount(3, $allPlans);
    }

    #[Test]
    public function user_view_shows_same_number_of_plans_as_admin_view()
    {
        $subscriptionService = new SubscriptionService();
        $userPlans = $subscriptionService->getPlans();
        $adminPlans = PricingPlan::withCount('subscriptions')->ordered()->get();

        // User view should show same number of plans as admin view
        $this->assertCount($adminPlans->count(), $userPlans);
    }

    #[Test]
    public function enterprise_plan_is_active_and_visible()
    {
        $enterprisePlan = PricingPlan::where('name', 'enterprise')->first();

        $this->assertNotNull($enterprisePlan);
        $this->assertTrue($enterprisePlan->is_active);

        // Should be included in active plans
        $activePlans = PricingPlan::active()->get();
        $this->assertTrue($activePlans->contains('name', 'enterprise'));
    }
}
