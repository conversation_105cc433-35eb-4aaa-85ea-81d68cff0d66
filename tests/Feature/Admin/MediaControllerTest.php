<?php

namespace Tests\Feature\Admin;

use App\Models\Media;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class MediaControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);
        
        // Fake storage
        Storage::fake('public');
    }

    public function test_admin_can_access_media_select_endpoint()
    {
        // Create test media
        $media = Media::factory()->count(3)->create();

        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'filename',
                        'original_filename',
                        'mime_type',
                        'size',
                        'path',
                        'alt_text',
                        'title',
                        'description',
                        'width',
                        'height',
                        'url',
                        'formatted_size',
                        'created_at',
                    ]
                ],
                'current_page',
                'last_page',
                'per_page',
                'total',
            ]);

        $this->assertEquals(3, $response->json('total'));
    }

    public function test_non_admin_cannot_access_media_select_endpoint()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($user)
            ->getJson('/admin/media/select');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_media_select_endpoint()
    {
        $response = $this->getJson('/admin/media/select');

        $response->assertStatus(401);
    }

    public function test_media_select_filters_by_type()
    {
        // Create different types of media
        Media::factory()->create(['mime_type' => 'image/jpeg']);
        Media::factory()->create(['mime_type' => 'image/png']);
        Media::factory()->create(['mime_type' => 'application/pdf']);
        Media::factory()->create(['mime_type' => 'text/plain']);

        // Test images filter
        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select?type=images');

        $response->assertStatus(200);
        $this->assertEquals(2, $response->json('total'));

        // Test all files
        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select?type=all');

        $response->assertStatus(200);
        $this->assertEquals(4, $response->json('total'));
    }

    public function test_media_select_supports_search()
    {
        Media::factory()->create([
            'original_filename' => 'test-image.jpg',
            'title' => 'Test Image',
        ]);
        Media::factory()->create([
            'original_filename' => 'document.pdf',
            'title' => 'Important Document',
        ]);
        Media::factory()->create([
            'original_filename' => 'another-file.txt',
            'title' => 'Another File',
        ]);

        // Search by filename
        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select?search=test');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('total'));

        // Search by title
        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select?search=document');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('total'));
    }

    public function test_media_select_supports_sorting()
    {
        $oldMedia = Media::factory()->create([
            'original_filename' => 'a-file.jpg',
            'created_at' => now()->subDays(2),
        ]);
        $newMedia = Media::factory()->create([
            'original_filename' => 'z-file.jpg',
            'created_at' => now(),
        ]);

        // Test sort by created_at desc (default)
        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select?sort_by=created_at&sort_order=desc');

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals($newMedia->id, $data[0]['id']);

        // Test sort by name asc
        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select?sort_by=name&sort_order=asc');

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals($oldMedia->id, $data[0]['id']);
    }

    public function test_media_select_pagination()
    {
        // Create more than 24 media items (default per page)
        Media::factory()->count(30)->create();

        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select');

        $response->assertStatus(200)
            ->assertJson([
                'current_page' => 1,
                'per_page' => 24,
                'total' => 30,
            ]);

        $this->assertCount(24, $response->json('data'));

        // Test second page
        $response = $this->actingAs($this->adminUser)
            ->getJson('/admin/media/select?page=2');

        $response->assertStatus(200)
            ->assertJson([
                'current_page' => 2,
                'per_page' => 24,
                'total' => 30,
            ]);

        $this->assertCount(6, $response->json('data'));
    }

    public function test_admin_can_upload_media()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(1024);

        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/media', [
                'files' => [$file],
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Files uploaded successfully',
            ]);

        $this->assertDatabaseHas('media', [
            'original_filename' => 'test.jpg',
            'mime_type' => 'image/jpeg',
        ]);

        Storage::disk('public')->assertExists('media/' . $file->hashName());
    }

    public function test_media_upload_validates_file_types()
    {
        $invalidFile = UploadedFile::fake()->create('malicious.exe', 1024);

        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/media', [
                'files' => [$invalidFile],
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['files.0']);
    }

    public function test_media_upload_validates_file_size()
    {
        // Create file larger than 10MB
        $largeFile = UploadedFile::fake()->image('large.jpg')->size(11 * 1024);

        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/media', [
                'files' => [$largeFile],
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['files.0']);
    }

    public function test_media_upload_handles_multiple_files()
    {
        $file1 = UploadedFile::fake()->image('test1.jpg');
        $file2 = UploadedFile::fake()->image('test2.png');

        $response = $this->actingAs($this->adminUser)
            ->postJson('/admin/media', [
                'files' => [$file1, $file2],
            ]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('media', [
            'original_filename' => 'test1.jpg',
        ]);
        $this->assertDatabaseHas('media', [
            'original_filename' => 'test2.png',
        ]);
    }

    public function test_media_model_includes_required_accessors()
    {
        $media = Media::factory()->create([
            'path' => 'media/test.jpg',
            'size' => 1024000,
        ]);

        $mediaArray = $media->toArray();

        $this->assertArrayHasKey('url', $mediaArray);
        $this->assertArrayHasKey('formatted_size', $mediaArray);
        $this->assertStringContains('test.jpg', $mediaArray['url']);
        $this->assertEquals('1.0 MB', $mediaArray['formatted_size']);
    }

    public function test_media_url_normalizes_localhost()
    {
        config(['app.url' => 'http://127.0.0.1:8000']);
        
        $media = Media::factory()->create([
            'path' => 'media/test.jpg',
        ]);

        $url = $media->url;
        $this->assertStringStartsWith('http://localhost:8000', $url);
    }
}
