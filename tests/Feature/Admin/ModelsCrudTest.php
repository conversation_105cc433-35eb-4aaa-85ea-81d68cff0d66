<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ModelsCrudTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user for authentication
        $this->user = $this->createAdminUser();
        $this->actingAs($this->user);
    }

    public function test_models_index_page_loads()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $response = $this->get('/admin/models');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Models/Index')
                ->has('models')
                ->where('models.data', fn ($models) =>
                    collect($models)->contains('name', $model->name)
                )
        );
    }

    public function test_models_create_page_loads()
    {
        Brand::factory()->create();

        $response = $this->get('/admin/models/create');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Models/Create')
                ->has('brands', 1)
        );
    }

    public function test_can_create_model()
    {
        $brand = Brand::factory()->create();

        $modelData = [
            'brand_id' => $brand->id,
            'name' => 'Test Model',
            'model_number' => 'TM-001',
            'release_year' => 2024,
            'specifications' => [
                'display_size' => '6.1 inches',
                'storage' => '128GB',
                'ram' => '8GB'
            ],
            'images' => ['https://example.com/image1.jpg'],
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/models', $modelData);

        $response->assertRedirect('/admin/models');
        $this->assertDatabaseHas('models', [
            'name' => 'Test Model',
            'model_number' => 'TM-001',
            'brand_id' => $brand->id,
        ]);
    }

    public function test_models_show_page_loads()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $response = $this->get("/admin/models/{$model->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Models/Show')
                ->where('model.id', $model->id)
                ->where('model.name', $model->name)
        );
    }

    public function test_models_edit_page_loads()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $response = $this->get("/admin/models/{$model->id}/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Models/Edit')
                ->where('model.id', $model->id)
                ->has('brands', 1)
        );
    }

    public function test_can_update_model()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $updateData = [
            'brand_id' => $brand->id,
            'name' => 'Updated Model Name',
            'model_number' => 'UM-002',
            'release_year' => 2025,
            'specifications' => [
                'display_size' => '6.5 inches',
                'storage' => '256GB',
                'ram' => '12GB'
            ],
            'images' => ['https://example.com/updated-image.jpg'],
            'is_active' => false,
        ];

        $response = $this->putWithCsrf("/admin/models/{$model->id}", $updateData);

        $response->assertRedirect('/admin/models');
        $this->assertDatabaseHas('models', [
            'id' => $model->id,
            'name' => 'Updated Model Name',
            'model_number' => 'UM-002',
        ]);
    }

    public function test_can_delete_model()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $response = $this->deleteWithCsrf("/admin/models/{$model->id}");

        $response->assertRedirect('/admin/models');
        $this->assertDatabaseMissing('models', [
            'id' => $model->id,
        ]);
    }

    public function test_cannot_delete_model_with_parts()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);
        
        // Create a part and associate it with the model
        $part = \App\Models\Part::factory()->create();
        $model->parts()->attach($part->id);

        $response = $this->deleteWithCsrf("/admin/models/{$model->id}");

        $response->assertRedirect();
        $this->assertDatabaseHas('models', [
            'id' => $model->id,
        ]);
    }
}
