<?php

namespace Tests\Feature\Admin;

use App\Models\Category;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartPaginationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Category $category1;
    protected Category $category2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user for authentication
        $this->user = $this->createAdminUser();

        // Create categories
        $this->category1 = Category::factory()->create(['name' => 'Display']);
        $this->category2 = Category::factory()->create(['name' => 'Battery']);
    }

    public function test_parts_index_returns_paginated_results()
    {
        // Create 25 parts to test pagination (15 per page = 2 pages)
        Part::factory()->count(25)->create([
            'category_id' => $this->category1->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/parts');

        $response->assertStatus(200);

        // Check that the response contains pagination data and filters
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->has('parts.data', 15) // First page should have 15 items
                ->where('parts.current_page', 1)
                ->where('parts.last_page', 2)
                ->where('parts.total', 25)
                ->where('parts.per_page', 15)
                ->has('filters.categories')
                ->has('filters.manufacturers')
                ->has('queryParams')
        );
    }

    public function test_parts_search_functionality()
    {
        // Create parts with specific names
        Part::factory()->create([
            'name' => 'iPhone Screen',
            'category_id' => $this->category1->id,
        ]);
        Part::factory()->create([
            'name' => 'Samsung Battery',
            'category_id' => $this->category2->id,
        ]);
        Part::factory()->create([
            'name' => 'Generic Cable',
            'category_id' => $this->category1->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/parts?search=iPhone');

        $response->assertStatus(200);

        // Should only return the iPhone screen
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->has('parts.data', 1)
                ->where('parts.total', 1)
                ->where('queryParams.search', 'iPhone')
        );
    }

    public function test_parts_category_filter()
    {
        // Create parts in different categories
        Part::factory()->count(3)->create([
            'category_id' => $this->category1->id,
        ]);
        Part::factory()->count(2)->create([
            'category_id' => $this->category2->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/parts?category_id=' . $this->category1->id);

        $response->assertStatus(200);

        // Should only return parts from category1
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->has('parts.data', 3)
                ->where('parts.total', 3)
                ->where('queryParams.category_id', (string) $this->category1->id)
        );
    }

    public function test_parts_manufacturer_filter()
    {
        // Create parts with different manufacturers
        Part::factory()->create([
            'manufacturer' => 'Apple',
            'category_id' => $this->category1->id,
        ]);
        Part::factory()->create([
            'manufacturer' => 'Samsung',
            'category_id' => $this->category1->id,
        ]);
        Part::factory()->create([
            'manufacturer' => 'Apple',
            'category_id' => $this->category2->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/parts?manufacturer=Apple');

        $response->assertStatus(200);

        // Should only return Apple parts
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->has('parts.data', 2)
                ->where('parts.total', 2)
                ->where('queryParams.manufacturer', 'Apple')
        );
    }

    public function test_parts_status_filter()
    {
        // Create active and inactive parts
        Part::factory()->count(2)->create([
            'is_active' => true,
            'category_id' => $this->category1->id,
        ]);
        Part::factory()->create([
            'is_active' => false,
            'category_id' => $this->category1->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/parts?status=active');

        $response->assertStatus(200);

        // Should only return active parts
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->has('parts.data', 2)
                ->where('parts.total', 2)
                ->where('queryParams.status', 'active')
        );
    }

    public function test_parts_sorting()
    {
        // Create parts with different names
        Part::factory()->create([
            'name' => 'Zebra Part',
            'category_id' => $this->category1->id,
        ]);
        Part::factory()->create([
            'name' => 'Alpha Part',
            'category_id' => $this->category1->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/parts?sort_by=name&sort_order=asc');

        $response->assertStatus(200);

        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->where('queryParams.sort_by', 'name')
                ->where('queryParams.sort_order', 'asc')
        );
    }

    public function test_combined_search_and_filters()
    {
        // Create parts with specific attributes
        Part::factory()->create([
            'name' => 'iPhone Display',
            'manufacturer' => 'Apple',
            'category_id' => $this->category1->id,
            'is_active' => true,
        ]);
        Part::factory()->create([
            'name' => 'iPhone Battery',
            'manufacturer' => 'Apple',
            'category_id' => $this->category2->id,
            'is_active' => true,
        ]);
        Part::factory()->create([
            'name' => 'Samsung Display',
            'manufacturer' => 'Samsung',
            'category_id' => $this->category1->id,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/parts?search=iPhone&manufacturer=Apple&category_id=' . $this->category1->id);

        $response->assertStatus(200);

        // Should only return iPhone Display (matches all criteria)
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->has('parts.data', 1)
                ->where('parts.total', 1)
                ->where('queryParams.search', 'iPhone')
                ->where('queryParams.manufacturer', 'Apple')
                ->where('queryParams.category_id', (string) $this->category1->id)
        );
    }

    public function test_view_mode_parameter()
    {
        // Create some test parts
        Part::factory()->count(3)->create([
            'category_id' => $this->category1->id,
        ]);

        // Test default view (table)
        $response = $this->actingAs($this->user)
            ->get('/admin/parts');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->has('queryParams')
                ->missing('queryParams.view') // Default should not have view parameter
        );

        // Test grid view
        $response = $this->actingAs($this->user)
            ->get('/admin/parts?view=grid');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->where('queryParams.view', 'grid')
        );

        // Test table view
        $response = $this->actingAs($this->user)
            ->get('/admin/parts?view=table');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->where('queryParams.view', 'table')
        );
    }

    public function test_view_mode_with_other_parameters()
    {
        // Create parts for testing
        Part::factory()->count(5)->create([
            'category_id' => $this->category1->id,
            'manufacturer' => 'Apple',
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/parts?view=grid&manufacturer=Apple&sort_by=name&sort_order=desc');

        $response->assertStatus(200);

        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Index')
                ->where('queryParams.view', 'grid')
                ->where('queryParams.manufacturer', 'Apple')
                ->where('queryParams.sort_by', 'name')
                ->where('queryParams.sort_order', 'desc')
        );
    }
}
