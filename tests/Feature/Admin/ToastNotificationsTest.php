<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ToastNotificationsTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user for authentication
        $this->user = $this->createAdminUser();
        $this->actingAs($this->user);
    }

    public function test_brand_creation_redirects_to_index()
    {
        $brandData = [
            'name' => 'Test Brand',
            'logo_url' => 'https://example.com/logo.png',
            'country' => 'United States',
            'website' => 'https://testbrand.com',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/brands', $brandData);

        $response->assertRedirect('/admin/brands');
        $response->assertSessionHas('success');
    }

    public function test_brand_update_redirects_to_index()
    {
        $brand = Brand::factory()->create();

        $updateData = [
            'name' => 'Updated Brand Name',
            'logo_url' => 'https://example.com/updated-logo.png',
            'country' => 'Canada',
            'website' => 'https://updatedbrand.com',
            'is_active' => false,
        ];

        $response = $this->putWithCsrf("/admin/brands/{$brand->id}", $updateData);

        $response->assertRedirect('/admin/brands');
        $response->assertSessionHas('success');
    }

    public function test_brand_deletion_redirects_to_index()
    {
        $brand = Brand::factory()->create();

        $response = $this->deleteWithCsrf("/admin/brands/{$brand->id}");

        $response->assertRedirect('/admin/brands');
        $response->assertSessionHas('success');
    }

    public function test_brand_deletion_fails_with_models()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $response = $this->deleteWithCsrf("/admin/brands/{$brand->id}");

        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('brands', ['id' => $brand->id]);
    }

    public function test_model_creation_redirects_to_index()
    {
        $brand = Brand::factory()->create();

        $modelData = [
            'brand_id' => $brand->id,
            'name' => 'Test Model',
            'model_number' => 'TM-001',
            'release_year' => 2024,
            'specifications' => [
                'display_size' => '6.1 inches',
                'storage' => '128GB',
                'ram' => '8GB'
            ],
            'images' => ['https://example.com/image1.jpg'],
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/models', $modelData);

        $response->assertRedirect('/admin/models');
        $response->assertSessionHas('success');
    }

    public function test_model_update_redirects_to_index()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $updateData = [
            'brand_id' => $brand->id,
            'name' => 'Updated Model Name',
            'model_number' => 'UM-002',
            'release_year' => 2025,
            'specifications' => [
                'display_size' => '6.5 inches',
                'storage' => '256GB',
                'ram' => '12GB'
            ],
            'images' => ['https://example.com/updated-image.jpg'],
            'is_active' => false,
        ];

        $response = $this->putWithCsrf("/admin/models/{$model->id}", $updateData);

        $response->assertRedirect('/admin/models');
        $response->assertSessionHas('success');
    }

    public function test_model_deletion_redirects_to_index()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $response = $this->deleteWithCsrf("/admin/models/{$model->id}");

        $response->assertRedirect('/admin/models');
        $response->assertSessionHas('success');
    }

    public function test_form_validation_errors_return_to_form()
    {
        // Test brand creation with invalid data
        $response = $this->postWithCsrf('/admin/brands', [
            'name' => '', // Required field empty
            'logo_url' => 'invalid-url', // Invalid URL
        ]);

        $response->assertSessionHasErrors(['name', 'logo_url']);
    }

    public function test_unique_validation_works()
    {
        $existingBrand = Brand::factory()->create(['name' => 'Existing Brand']);

        $response = $this->postWithCsrf('/admin/brands', [
            'name' => 'Existing Brand', // Duplicate name
            'is_active' => true,
        ]);

        $response->assertSessionHasErrors(['name']);
    }
}
