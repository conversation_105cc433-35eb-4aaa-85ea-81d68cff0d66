<?php

namespace Tests\Feature\Admin;

use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;
use Tests\TestCase;

class CategoryPaginationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->createAdminUser();
    }

    public function test_categories_index_returns_paginated_results()
    {
        // Create 20 categories to test pagination
        Category::factory()->count(20)->create();

        $response = $this->actingAs($this->user)
            ->get('/admin/categories');

        $response->assertStatus(200);
        
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->has('categories.data', 15) // Default pagination is 15
                ->where('categories.current_page', 1)
                ->where('categories.per_page', 15)
                ->where('categories.total', 20)
                ->has('filters.parentCategories')
                ->has('queryParams')
        );
    }

    public function test_categories_search_functionality()
    {
        Category::factory()->create(['name' => 'iPhone Display']);
        Category::factory()->create(['name' => 'Samsung Battery']);
        Category::factory()->create(['description' => 'iPhone related parts']);

        $response = $this->actingAs($this->user)
            ->get('/admin/categories?search=iPhone');

        $response->assertStatus(200);
        
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->has('categories.data', 2) // Should find 2 categories
                ->where('queryParams.search', 'iPhone')
        );
    }

    public function test_categories_parent_filter()
    {
        $parentCategory = Category::factory()->create(['name' => 'Electronics']);
        $childCategory = Category::factory()->create([
            'name' => 'Mobile Parts',
            'parent_id' => $parentCategory->id
        ]);
        Category::factory()->create(['name' => 'Accessories']); // Root category

        // Test root categories filter
        $response = $this->actingAs($this->user)
            ->get('/admin/categories?parent_id=root');

        $response->assertStatus(200);
        
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->has('categories.data', 2) // Should find 2 root categories
                ->where('queryParams.parent_id', 'root')
        );

        // Test child categories filter
        $response = $this->actingAs($this->user)
            ->get('/admin/categories?parent_id=child');

        $response->assertStatus(200);
        
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->has('categories.data', 1) // Should find 1 child category
                ->where('queryParams.parent_id', 'child')
        );
    }

    public function test_categories_status_filter()
    {
        Category::factory()->create(['name' => 'Active Category', 'is_active' => true]);
        Category::factory()->create(['name' => 'Inactive Category', 'is_active' => false]);

        $response = $this->actingAs($this->user)
            ->get('/admin/categories?status=active');

        $response->assertStatus(200);
        
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->has('categories.data', 1)
                ->where('queryParams.status', 'active')
        );
    }

    public function test_categories_sorting()
    {
        Category::factory()->create(['name' => 'Zebra Category', 'sort_order' => 1]);
        Category::factory()->create(['name' => 'Alpha Category', 'sort_order' => 2]);

        // Test name sorting
        $response = $this->actingAs($this->user)
            ->get('/admin/categories?sort_by=name&sort_order=asc');

        $response->assertStatus(200);
        
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->where('queryParams.sort_by', 'name')
                ->where('queryParams.sort_order', 'asc')
        );
    }

    public function test_view_mode_parameter()
    {
        // Create some test categories
        Category::factory()->count(3)->create();

        // Test default view (table)
        $response = $this->actingAs($this->user)
            ->get('/admin/categories');

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->has('queryParams')
                ->missing('queryParams.view') // Default should not have view parameter
        );

        // Test grid view
        $response = $this->actingAs($this->user)
            ->get('/admin/categories?view=grid');

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->where('queryParams.view', 'grid')
        );

        // Test list view
        $response = $this->actingAs($this->user)
            ->get('/admin/categories?view=list');

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->where('queryParams.view', 'list')
        );
    }

    public function test_combined_search_and_filters()
    {
        $parentCategory = Category::factory()->create(['name' => 'Electronics']);
        
        Category::factory()->create([
            'name' => 'iPhone Display',
            'parent_id' => $parentCategory->id,
            'is_active' => true,
        ]);
        Category::factory()->create([
            'name' => 'iPhone Battery',
            'parent_id' => null,
            'is_active' => true,
        ]);
        Category::factory()->create([
            'name' => 'Samsung Display',
            'parent_id' => $parentCategory->id,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/categories?search=iPhone&parent_id=' . $parentCategory->id . '&status=active');

        $response->assertStatus(200);
        
        // Should only return iPhone Display (matches all criteria)
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->has('categories.data', 1)
                ->where('categories.total', 1)
                ->where('queryParams.search', 'iPhone')
                ->where('queryParams.parent_id', (string) $parentCategory->id)
                ->where('queryParams.status', 'active')
        );
    }

    public function test_view_mode_with_other_parameters()
    {
        // Create categories for testing
        Category::factory()->count(5)->create(['is_active' => true]);

        $response = $this->actingAs($this->user)
            ->get('/admin/categories?view=grid&status=active&sort_by=name&sort_order=desc');

        $response->assertStatus(200);
        
        $response->assertInertia(fn (Assert $page) => 
            $page->component('admin/Categories/Index')
                ->where('queryParams.view', 'grid')
                ->where('queryParams.status', 'active')
                ->where('queryParams.sort_by', 'name')
                ->where('queryParams.sort_order', 'desc')
        );
    }
}
