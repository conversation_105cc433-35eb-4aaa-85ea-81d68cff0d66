<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class BrandsCrudTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user for authentication
        $this->user = $this->createAdminUser();
        $this->actingAs($this->user);
    }

    public function test_brands_index_page_loads()
    {
        $response = $this->get('/admin/brands');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Brands/Index')
                ->has('brands')
        );
    }

    public function test_brands_create_page_loads()
    {
        $response = $this->get('/admin/brands/create');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Brands/Create')
        );
    }

    public function test_can_create_brand()
    {
        $brandData = [
            'name' => 'Test Brand',
            'logo_url' => 'https://example.com/logo.png',
            'country' => 'United States',
            'website' => 'https://testbrand.com',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/brands', $brandData);

        $response->assertRedirect('/admin/brands');
        $this->assertDatabaseHas('brands', [
            'name' => 'Test Brand',
            'country' => 'United States',
            'website' => 'https://testbrand.com',
        ]);
    }

    public function test_brand_name_is_required()
    {
        $brandData = [
            'name' => '',
            'logo_url' => 'https://example.com/logo.png',
            'country' => 'United States',
            'website' => 'https://testbrand.com',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/brands', $brandData);

        $response->assertSessionHasErrors(['name']);
    }

    public function test_brand_name_must_be_unique()
    {
        $existingBrand = Brand::factory()->create(['name' => 'Existing Brand']);

        $brandData = [
            'name' => 'Existing Brand',
            'logo_url' => 'https://example.com/logo.png',
            'country' => 'United States',
            'website' => 'https://testbrand.com',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/brands', $brandData);

        $response->assertSessionHasErrors(['name']);
    }

    public function test_logo_url_must_be_valid_url()
    {
        $brandData = [
            'name' => 'Test Brand',
            'logo_url' => 'invalid-url',
            'country' => 'United States',
            'website' => 'https://testbrand.com',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/brands', $brandData);

        $response->assertSessionHasErrors(['logo_url']);
    }

    public function test_website_must_be_valid_url()
    {
        $brandData = [
            'name' => 'Test Brand',
            'logo_url' => 'https://example.com/logo.png',
            'country' => 'United States',
            'website' => 'invalid-url',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/brands', $brandData);

        $response->assertSessionHasErrors(['website']);
    }

    public function test_brands_show_page_loads()
    {
        $brand = Brand::factory()->create();

        $response = $this->get("/admin/brands/{$brand->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Brands/Show')
                ->where('brand.id', $brand->id)
                ->where('brand.name', $brand->name)
        );
    }

    public function test_brands_edit_page_loads()
    {
        $brand = Brand::factory()->create();

        $response = $this->get("/admin/brands/{$brand->id}/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Brands/Edit')
                ->where('brand.id', $brand->id)
        );
    }

    public function test_can_update_brand()
    {
        $brand = Brand::factory()->create();

        $updateData = [
            'name' => 'Updated Brand Name',
            'logo_url' => 'https://example.com/updated-logo.png',
            'country' => 'Canada',
            'website' => 'https://updatedbrand.com',
            'is_active' => false,
        ];

        $response = $this->putWithCsrf("/admin/brands/{$brand->id}", $updateData);

        $response->assertRedirect('/admin/brands');
        $response->assertSessionHasNoErrors();
        $this->assertDatabaseHas('brands', [
            'id' => $brand->id,
            'name' => 'Updated Brand Name',
            'country' => 'Canada',
            'website' => 'https://updatedbrand.com',
            'is_active' => false,
        ]);
    }

    public function test_can_delete_brand()
    {
        $brand = Brand::factory()->create();

        $response = $this->deleteWithCsrf("/admin/brands/{$brand->id}");

        $response->assertRedirect('/admin/brands');
        $response->assertSessionHasNoErrors();
        $this->assertDatabaseMissing('brands', [
            'id' => $brand->id,
        ]);
    }

    public function test_cannot_delete_brand_with_models()
    {
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $response = $this->deleteWithCsrf("/admin/brands/{$brand->id}");

        $response->assertRedirect();
        $this->assertDatabaseHas('brands', [
            'id' => $brand->id,
        ]);
    }

    public function test_brands_show_page_displays_associated_models()
    {
        $brand = Brand::factory()->create();
        $model1 = MobileModel::factory()->create(['brand_id' => $brand->id]);
        $model2 = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $response = $this->get("/admin/brands/{$brand->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Brands/Show')
                ->where('brand.id', $brand->id)
                ->has('brand.models', 2)
                ->where('brand.models.0.id', $model1->id)
                ->where('brand.models.1.id', $model2->id)
        );
    }

    public function test_brands_index_shows_models_count()
    {
        $brand = Brand::factory()->create(['name' => 'Test Brand With Models']);
        MobileModel::factory()->count(3)->create(['brand_id' => $brand->id]);

        $response = $this->get('/admin/brands');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Brands/Index')
                ->has('brands')
        );

        // Verify the brand exists in the database with the correct models count
        $this->assertDatabaseHas('brands', [
            'name' => 'Test Brand With Models',
        ]);

        $this->assertEquals(3, $brand->fresh()->models()->count());
    }

    public function test_brands_export_returns_csv()
    {
        // Create test brands
        Brand::factory()->create([
            'name' => 'Apple',
            'country' => 'United States',
            'website' => 'https://apple.com',
            'is_active' => true,
        ]);
        Brand::factory()->create([
            'name' => 'Samsung',
            'country' => 'South Korea',
            'website' => 'https://samsung.com',
            'is_active' => true,
        ]);

        $response = $this->get('/admin/brands/export');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        // Check that content-disposition header contains expected filename pattern
        $contentDisposition = $response->headers->get('content-disposition');
        $this->assertStringContainsString('brands_export_', $contentDisposition);
        $this->assertStringContainsString('.csv', $contentDisposition);

        // For streaming responses, we can't easily test content, but we can verify
        // the response is properly formatted as a CSV download
        $this->assertStringContainsString('attachment', $contentDisposition);
    }

    public function test_brands_export_with_filters()
    {
        // Create test brands
        Brand::factory()->create([
            'name' => 'Apple',
            'country' => 'United States',
            'is_active' => true,
        ]);
        Brand::factory()->create([
            'name' => 'Samsung',
            'country' => 'South Korea',
            'is_active' => false,
        ]);

        // Export only active brands
        $response = $this->get('/admin/brands/export?status=active');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        // Verify it's a proper CSV download
        $contentDisposition = $response->headers->get('content-disposition');
        $this->assertStringContainsString('brands_export_', $contentDisposition);
    }

    public function test_brands_export_selected_ids()
    {
        // Create test brands
        $brand1 = Brand::factory()->create(['name' => 'Apple']);
        $brand2 = Brand::factory()->create(['name' => 'Samsung']);
        $brand3 = Brand::factory()->create(['name' => 'Google']);

        // Export only selected brands
        $response = $this->get("/admin/brands/export?ids[]={$brand1->id}&ids[]={$brand3->id}");

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');

        // Verify it's a proper CSV download
        $contentDisposition = $response->headers->get('content-disposition');
        $this->assertStringContainsString('brands_export_', $contentDisposition);
    }

    public function test_brands_download_template()
    {
        $response = $this->get('/admin/brands/template/download');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
        $response->assertHeader('content-disposition', 'attachment; filename="brands_import_template.csv"');

        // For template downloads, we can verify it's a proper CSV response
        // The actual content testing would require more complex setup for streaming responses
    }

    public function test_brands_export_requires_authentication()
    {
        // Test without authentication
        auth()->logout();

        $response = $this->get('/admin/brands/export');
        $response->assertRedirect('/login');

        $response = $this->get('/admin/brands/template/download');
        $response->assertRedirect('/login');
    }
}
