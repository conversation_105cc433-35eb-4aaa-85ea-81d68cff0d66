<?php

namespace Tests\Feature\Admin;

use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategoryControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        $this->adminUser = $this->createAdminUser();
    }

    public function test_admin_can_view_categories_index(): void
    {
        // Arrange
        Category::factory()->count(3)->create();

        // Act
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.categories.index'));

        // Assert
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Categories/Index')
                 ->has('categories')
        );
    }

    public function test_admin_can_view_create_category_form(): void
    {
        // Arrange
        Category::factory()->create(['name' => 'Parent Category']);

        // Act
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.categories.create'));

        // Assert
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Categories/Create')
                 ->has('parentCategories')
        );
    }

    public function test_admin_can_create_category(): void
    {
        // Arrange
        $categoryData = [
            'name' => 'Test Category',
            'description' => 'Test description',
            'sort_order' => 1,
            'is_active' => true,
        ];

        // Act
        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf(route('admin.categories.store'), $categoryData);

        // Assert
        $response->assertRedirect(route('admin.categories.index'));
        $this->assertDatabaseHas('categories', [
            'name' => 'Test Category',
            'description' => 'Test description',
        ]);
    }

    public function test_admin_cannot_create_category_with_duplicate_name(): void
    {
        // Arrange
        Category::factory()->create(['name' => 'Existing Category']);

        $categoryData = [
            'name' => 'Existing Category',
            'description' => 'Test description',
        ];

        // Act
        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf(route('admin.categories.store'), $categoryData);

        // Assert
        $response->assertSessionHasErrors('name');
    }

    public function test_admin_can_view_category_details(): void
    {
        // Arrange
        $category = Category::factory()->create();

        // Act
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.categories.show', $category));

        // Assert
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Categories/Show')
                 ->where('category.id', $category->id)
        );
    }

    public function test_admin_can_edit_category(): void
    {
        // Arrange
        $category = Category::factory()->create(['name' => 'Original Name']);

        // Act
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.categories.edit', $category));

        // Assert
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Categories/Edit')
                 ->where('category.id', $category->id)
        );
    }

    public function test_admin_can_update_category(): void
    {
        // Arrange
        $category = Category::factory()->create(['name' => 'Original Name']);

        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'sort_order' => 2,
            'is_active' => false,
        ];

        // Act
        $response = $this->actingAs($this->adminUser)
            ->putWithCsrf(route('admin.categories.update', $category), $updateData);

        // Assert
        $response->assertRedirect(route('admin.categories.index'));
        $this->assertDatabaseHas('categories', [
            'id' => $category->id,
            'name' => 'Updated Name',
            'description' => 'Updated description',
        ]);
    }

    public function test_admin_can_delete_empty_category(): void
    {
        // Arrange
        $category = Category::factory()->create();

        // Act
        $response = $this->actingAs($this->adminUser)
            ->deleteWithCsrf(route('admin.categories.destroy', $category));

        // Assert
        $response->assertRedirect(route('admin.categories.index'));
        $this->assertDatabaseMissing('categories', ['id' => $category->id]);
    }

    public function test_admin_cannot_delete_category_with_parts(): void
    {
        // Arrange
        $category = Category::factory()->hasParts(1)->create();

        // Act
        $response = $this->actingAs($this->adminUser)
            ->deleteWithCsrf(route('admin.categories.destroy', $category));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('categories', ['id' => $category->id]);
    }

    public function test_admin_cannot_delete_category_with_children(): void
    {
        // Arrange
        $parentCategory = Category::factory()->create();
        $childCategory = Category::factory()->create(['parent_id' => $parentCategory->id]);

        // Act
        $response = $this->actingAs($this->adminUser)
            ->deleteWithCsrf(route('admin.categories.destroy', $parentCategory));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('categories', ['id' => $parentCategory->id]);
    }

    public function test_category_validation_rules(): void
    {
        // Test required name
        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf(route('admin.categories.store'), []);

        $response->assertSessionHasErrors('name');

        // Test name length
        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf(route('admin.categories.store'), [
                'name' => str_repeat('a', 256),
            ]);

        $response->assertSessionHasErrors('name');
    }

    public function test_unauthenticated_user_cannot_access_admin_categories(): void
    {
        $response = $this->get(route('admin.categories.index'));

        $response->assertRedirect(route('login'));
    }
}
