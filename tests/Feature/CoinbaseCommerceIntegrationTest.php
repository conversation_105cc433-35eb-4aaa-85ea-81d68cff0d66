<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\CoinbaseCommerceTransaction;
use App\Services\CoinbaseCommerceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class CoinbaseCommerceIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test configuration
        config([
            'coinbase_commerce.api_key' => 'test_api_key',
            'coinbase_commerce.base_url' => 'https://api.commerce.coinbase.com',
            'coinbase_commerce.debug' => true,
        ]);
    }

    public function test_coinbase_commerce_service_is_configured(): void
    {
        $service = app(CoinbaseCommerceService::class);

        $this->assertTrue($service->isConfigured());

        $config = $service->getConfiguration();
        $this->assertTrue($config['is_configured']);
        $this->assertEquals('https://api.commerce.coinbase.com', $config['base_url']);
        $this->assertTrue($config['debug_mode']);
    }

    public function test_coinbase_commerce_transaction_model(): void
    {
        $user = User::factory()->create();
        $plan = PricingPlan::create([
            'name' => 'test_plan',
            'display_name' => 'Test Plan',
            'description' => 'Test plan for Coinbase Commerce',
            'price' => 100.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['feature1', 'feature2'],
            'search_limit' => 1000,
            'is_active' => true,
            'crypto_payment_enabled' => true,
            'coinbase_commerce_price_id_monthly' => 'test_price_id'
        ]);

        $transaction = CoinbaseCommerceTransaction::create([
            'coinbase_charge_id' => 'test_charge_id',
            'merchant_order_id' => 'TEST_ORDER_123',
            'user_id' => $user->id,
            'pricing_plan_id' => $plan->id,
            'status' => 'pending',
            'currency' => 'BTC',
            'amount' => 100.00,
            'hosted_url' => 'https://commerce.coinbase.com/charges/test_charge_id',
            'expires_at' => now()->addHour(),
        ]);

        $this->assertDatabaseHas('coinbase_commerce_transactions', [
            'coinbase_charge_id' => 'test_charge_id',
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        $this->assertTrue($transaction->isPending());
        $this->assertFalse($transaction->isCompleted());
        $this->assertTrue($transaction->isValid());
        $this->assertEquals('$100.00', $transaction->formatted_amount);
    }

    public function test_pricing_plan_coinbase_commerce_integration(): void
    {
        $plan = PricingPlan::create([
            'name' => 'test_plan_crypto',
            'display_name' => 'Test Crypto Plan',
            'description' => 'Test plan for Coinbase Commerce integration',
            'price' => 200.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['crypto_feature1', 'crypto_feature2'],
            'search_limit' => 2000,
            'is_active' => true,
            'crypto_payment_enabled' => true,
            'coinbase_commerce_price_id_monthly' => 'monthly_price_id',
            'coinbase_commerce_price_id_yearly' => 'yearly_price_id',
            'coinbase_commerce_product_id' => 'product_id',
        ]);

        $this->assertTrue($plan->hasCryptoPaymentEnabled());
        $this->assertTrue($plan->hasCoinbaseCommerceIntegration());
        $this->assertTrue($plan->supportsCryptoPayment());
        $this->assertTrue($plan->supportsCoinbaseCommerceBillingCycle('month'));
        $this->assertTrue($plan->supportsCoinbaseCommerceBillingCycle('year'));
        $this->assertEquals('monthly_price_id', $plan->getCoinbaseCommercePriceId('month'));
        $this->assertEquals('yearly_price_id', $plan->getCoinbaseCommercePriceId('year'));
    }

    public function test_user_coinbase_commerce_relationship(): void
    {
        $user = User::factory()->create();
        $plan = PricingPlan::create([
            'name' => 'test_plan_relationship',
            'display_name' => 'Test Relationship Plan',
            'description' => 'Test plan for relationship testing',
            'price' => 150.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['relationship_feature'],
            'search_limit' => 1500,
            'is_active' => true,
        ]);

        $transaction = CoinbaseCommerceTransaction::create([
            'coinbase_charge_id' => 'test_charge_id',
            'merchant_order_id' => 'TEST_ORDER_123',
            'user_id' => $user->id,
            'pricing_plan_id' => $plan->id,
            'status' => 'completed',
            'currency' => 'BTC',
            'amount' => 100.00,
        ]);

        $this->assertCount(1, $user->coinbaseCommerceTransactions);
        $this->assertEquals($transaction->id, $user->coinbaseCommerceTransactions->first()->id);
    }

    public function test_coinbase_commerce_configuration_page_loads(): void
    {
        $admin = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->actingAs($admin)
            ->get(route('admin.payment-gateways.coinbase.configure'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/payment-gateways/coinbase/Configure')
        );
    }

    public function test_webhook_endpoint_exists(): void
    {
        Http::fake([
            '*' => Http::response(['success' => true], 200)
        ]);

        $response = $this->post('/webhooks/coinbase-commerce', [
            'event' => [
                'type' => 'charge:confirmed',
                'data' => [
                    'id' => 'test_charge_id',
                    'status' => 'completed'
                ]
            ]
        ]);

        // Should not return 404
        $this->assertNotEquals(404, $response->status());
    }

    public function test_create_charge_endpoint_requires_authentication(): void
    {
        $response = $this->post(route('coinbase-commerce.charge'), [
            'pricing_plan_id' => 1,
            'billing_cycle' => 'month'
        ]);

        $response->assertStatus(401);
    }

    public function test_payment_gateway_data_includes_coinbase(): void
    {
        $admin = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->actingAs($admin)
            ->get(route('admin.payment-gateways.index'));

        $response->assertStatus(200);

        // Check that Coinbase Commerce is included in the payment gateways
        $response->assertSee('Coinbase Commerce');
        $response->assertSee('Accept cryptocurrency payments');
    }
}
