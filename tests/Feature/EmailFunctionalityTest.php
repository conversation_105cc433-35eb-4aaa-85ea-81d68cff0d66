<?php

namespace Tests\Feature;

use App\Mail\UserApproved;
use App\Models\User;
use App\Services\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class EmailFunctionalityTest extends TestCase
{
    use RefreshDatabase;

    private EmailService $emailService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->emailService = app(EmailService::class);
    }

    /** @test */
    public function it_can_validate_smtp_configuration()
    {
        // Test with valid configuration
        Config::set('mail.mailers.smtp.host', 'smtp.gmail.com');
        Config::set('mail.mailers.smtp.port', 587);
        Config::set('mail.mailers.smtp.username', '<EMAIL>');
        Config::set('mail.mailers.smtp.password', 'password');
        Config::set('mail.mailers.smtp.encryption', 'tls');

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertTrue($result['success']);
        $this->assertEquals('smtp', $result['provider']);
        $this->assertArrayHasKey('details', $result);
    }

    /** @test */
    public function it_detects_incomplete_smtp_configuration()
    {
        // Test with missing configuration
        Config::set('mail.mailers.smtp.host', '');
        Config::set('mail.mailers.smtp.port', '');
        Config::set('mail.mailers.smtp.username', '');
        Config::set('mail.mailers.smtp.password', '');

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertFalse($result['success']);
        $this->assertStringContains('incomplete', $result['message']);
        $this->assertArrayHasKey('details', $result);
    }

    /** @test */
    public function it_can_send_email_via_smtp()
    {
        Mail::fake();

        $user = User::factory()->create();
        $admin = User::factory()->create(['is_admin' => true]);
        $mailable = new UserApproved($user, $admin);

        $result = $this->emailService->send($mailable, $user->email);

        $this->assertTrue($result);
        Mail::assertSent(UserApproved::class);
    }

    /** @test */
    public function it_handles_email_sending_failures_gracefully()
    {
        // Configure invalid SMTP settings to force failure
        Config::set('mail.mailers.smtp.host', 'invalid-host.example.com');
        Config::set('mail.mailers.smtp.port', 9999);

        $user = User::factory()->create();
        $admin = User::factory()->create(['is_admin' => true]);
        $mailable = new UserApproved($user, $admin);

        $result = $this->emailService->send($mailable, $user->email);

        $this->assertFalse($result);
    }

    /** @test */
    public function it_validates_email_addresses()
    {
        $this->assertTrue($this->emailService->validateEmail('<EMAIL>'));
        $this->assertTrue($this->emailService->validateEmail('<EMAIL>'));
        
        $this->assertFalse($this->emailService->validateEmail('invalid-email'));
        $this->assertFalse($this->emailService->validateEmail('test@'));
        $this->assertFalse($this->emailService->validateEmail('@example.com'));
    }

    /** @test */
    public function it_returns_provider_status()
    {
        $status = $this->emailService->getProviderStatus();

        $this->assertArrayHasKey('provider', $status);
        $this->assertArrayHasKey('configured', $status);
        $this->assertArrayHasKey('status', $status);
    }

    /** @test */
    public function admin_can_access_email_configuration_page()
    {
        $admin = User::factory()->create(['is_admin' => true]);

        $response = $this->actingAs($admin)->get('/admin/email-config');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/EmailConfig/Index')
                ->has('config')
                ->has('provider_status')
                ->has('email_stats')
        );
    }

    /** @test */
    public function admin_can_update_email_configuration()
    {
        $admin = User::factory()->create(['is_admin' => true]);

        $response = $this->actingAs($admin)->post('/admin/email-config', [
            'provider' => 'smtp',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test App',
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'password',
            'smtp_encryption' => 'tls',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
    }

    /** @test */
    public function admin_can_test_email_configuration()
    {
        $admin = User::factory()->create(['is_admin' => true]);

        $response = $this->actingAs($admin)->post('/admin/email-config/test', [
            'provider' => 'smtp'
        ]);

        $response->assertRedirect();
        $response->assertSessionHasAny(['success', 'error']);
    }

    /** @test */
    public function admin_can_send_test_email()
    {
        Mail::fake();
        $admin = User::factory()->create(['is_admin' => true]);

        $response = $this->actingAs($admin)->post('/admin/email-config/test-email', [
            'email' => '<EMAIL>'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        Mail::assertSent(UserApproved::class);
    }

    /** @test */
    public function non_admin_cannot_access_email_configuration()
    {
        $user = User::factory()->create(['is_admin' => false]);

        $response = $this->actingAs($user)->get('/admin/email-config');

        $response->assertStatus(403);
    }

    /** @test */
    public function email_configuration_requires_valid_data()
    {
        $admin = User::factory()->create(['is_admin' => true]);

        $response = $this->actingAs($admin)->post('/admin/email-config', [
            'provider' => 'invalid',
            'from_address' => 'invalid-email',
            'from_name' => '',
        ]);

        $response->assertSessionHasErrors(['provider', 'from_address', 'from_name']);
    }

    /** @test */
    public function test_email_requires_valid_email_address()
    {
        $admin = User::factory()->create(['is_admin' => true]);

        $response = $this->actingAs($admin)->post('/admin/email-config/test-email', [
            'email' => 'invalid-email'
        ]);

        $response->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function email_service_logs_sending_attempts()
    {
        Mail::fake();
        
        $user = User::factory()->create();
        $admin = User::factory()->create(['is_admin' => true]);
        $mailable = new UserApproved($user, $admin);

        $this->emailService->send($mailable, $user->email);

        // Check that logs were created (this would require log testing setup)
        $this->assertTrue(true); // Placeholder for log assertion
    }

    /** @test */
    public function email_service_handles_different_providers()
    {
        Mail::fake();

        // Test SMTP
        Config::set('mail.default', 'smtp');
        $user = User::factory()->create();
        $admin = User::factory()->create(['is_admin' => true]);
        $mailable = new UserApproved($user, $admin);

        $result = $this->emailService->send($mailable, $user->email);
        $this->assertTrue($result);

        // Test SendGrid (would require SendGrid configuration)
        Config::set('mail.default', 'sendgrid');
        Config::set('services.sendgrid.api_key', 'test-key');
        
        // This would fail without proper SendGrid setup, but tests the code path
        $result = $this->emailService->send($mailable, $user->email);
        // Result depends on SendGrid configuration
    }

    /** @test */
    public function subscription_index_route_exists_for_email_templates()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('subscription.index'));

        $response->assertStatus(200);
    }

    /** @test */
    public function required_routes_exist_for_email_templates()
    {
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('login'));
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('dashboard'));
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('subscription.index'));
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('contact'));
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('home'));
    }

    /** @test */
    public function email_service_provides_detailed_error_information()
    {
        // Test with invalid SMTP configuration
        Config::set('mail.mailers.smtp.host', 'invalid-host.test');
        Config::set('mail.mailers.smtp.port', 9999);
        Config::set('mail.mailers.smtp.username', '<EMAIL>');
        Config::set('mail.mailers.smtp.password', 'password');

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('error_type', $result);
        $this->assertArrayHasKey('details', $result);
    }

    /** @test */
    public function email_service_logs_configuration_details()
    {
        Mail::fake();

        $user = User::factory()->create();
        $admin = User::factory()->create(['is_admin' => true]);
        $mailable = new UserApproved($user, $admin);

        // This should log the attempt with configuration details
        $this->emailService->send($mailable, $user->email);

        // Verify the email was processed
        Mail::assertSent(UserApproved::class);
    }

    /** @test */
    public function smtp_port_587_with_tls_encryption_is_properly_configured()
    {
        Config::set('mail.mailers.smtp.host', 'smtp.gmail.com');
        Config::set('mail.mailers.smtp.port', 587);
        Config::set('mail.mailers.smtp.encryption', 'tls');
        Config::set('mail.mailers.smtp.username', '<EMAIL>');
        Config::set('mail.mailers.smtp.password', 'password');

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertTrue($result['success']);
        $this->assertEquals(587, $result['details']['port']);
        $this->assertEquals('tls', $result['details']['encryption']);
    }
}
