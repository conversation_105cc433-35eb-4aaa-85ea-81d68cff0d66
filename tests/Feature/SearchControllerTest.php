<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create([
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved'
        ]);
    }

    public function test_search_index_page_loads(): void
    {
        $response = $this->actingAs($this->user)->get(route('search.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/index')
                 ->has('filters')
        );
    }

    public function test_search_results_with_valid_query(): void
    {
        // Arrange
        $category = Category::factory()->create(['name' => 'Display']);
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'iPhone Display',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'iPhone', 'type' => 'all']));

        // Assert
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/results')
                 ->has('results')
                 ->where('query', 'iPhone')
                 ->where('search_type', 'all')
        );
    }

    public function test_search_results_with_free_user_exceeding_limit(): void
    {
        // Arrange
        $freeUser = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 20,
            'daily_reset' => today(),
        ]);

        // Act
        $response = $this->actingAs($freeUser)
            ->get(route('search.results', ['q' => 'test']));

        // Assert
        $response->assertStatus(429);
        $response->assertJson([
            'error' => 'Daily search limit exceeded',
        ]);
    }

    public function test_search_suggestions_endpoint(): void
    {
        // Arrange
        Part::factory()->create(['name' => 'iPhone Display']);
        Brand::factory()->create(['name' => 'iPhone Brand']);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('search.suggestions', ['q' => 'iPhone']));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => ['type', 'value']
        ]);
    }

    public function test_search_suggestions_with_short_query(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->get(route('search.suggestions', ['q' => 'i']));

        // Assert
        $response->assertStatus(200);
        $response->assertJson([]);
    }

    public function test_part_details_page(): void
    {
        // Arrange
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);
        $part = Part::factory()->create(['category_id' => $category->id]);

        $part->models()->attach($model->id);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('parts.show', $part));

        // Assert
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                 ->has('part')
                 ->has('relatedParts')
                 ->where('part.id', $part->id)
        );
    }

    public function test_filters_endpoint(): void
    {
        // Arrange
        Category::factory()->create(['name' => 'Display']);
        Brand::factory()->create(['name' => 'Apple']);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('search.filters'));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'categories' => [
                '*' => ['id', 'name']
            ],
            'brands' => [
                '*' => ['id', 'name']
            ],
            'manufacturers',
            'release_years',
        ]);
    }

    public function test_search_with_category_filter(): void
    {
        // Arrange
        $category = Category::factory()->create(['name' => 'Battery']);
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Lithium Battery',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('search.results', [
                'q' => 'battery',
                'category_id' => $category->id,
            ]));

        // Assert
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->has('results')
                 ->where('applied_filters.category_id', (string)$category->id)
        );
    }

    public function test_search_with_brand_filter(): void
    {
        // Arrange
        $category = Category::factory()->create();
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);
        $part = Part::factory()->create(['category_id' => $category->id]);

        $part->models()->attach($model->id);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('search.results', [
                'q' => 'part',
                'brand_id' => $brand->id,
            ]));

        // Assert
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->has('results')
                 ->where('applied_filters.brand_id', (string)$brand->id)
        );
    }

    public function test_unauthenticated_user_cannot_access_search(): void
    {
        $response = $this->get(route('search.index'));

        $response->assertRedirect(route('login'));
    }
}
