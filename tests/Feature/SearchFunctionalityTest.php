<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Part;
use App\Models\Category;
use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\UserSearch;
use App\Services\SearchService;
use App\Services\GuestSearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SearchFunctionalityTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $admin;
    protected $category;
    protected $brand;
    protected $model;
    protected $part;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'subscription_plan' => 'free',
        ]);

        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'subscription_plan' => 'premium',
        ]);

        $this->category = Category::factory()->create([
            'name' => 'Display',
            'slug' => 'display',
        ]);

        $this->brand = Brand::factory()->create([
            'name' => 'Apple',
            'slug' => 'apple',
        ]);

        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 14',
            'slug' => 'iphone-14',
            'brand_id' => $this->brand->id,
        ]);

        $this->part = Part::factory()->create([
            'name' => 'iPhone 14 Display',
            'part_number' => 'IP14-DISP-001',
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        $this->part->models()->attach($this->model->id);
    }

    /** @test */
    public function authenticated_user_can_perform_search()
    {
        $this->actingAs($this->user);

        $response = $this->get('/search/results?q=iPhone&type=all');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('search/results')
                ->has('results.data')
                ->where('query', 'iPhone')
        );
    }

    /** @test */
    public function guest_user_can_perform_limited_searches()
    {
        $deviceId = 'test-device-' . uniqid();

        $response = $this->get("/guest/search?q=iPhone&type=all&device_id={$deviceId}");

        $response->assertStatus(200);
        $this->assertJson($response->getContent());
    }

    /** @test */
    public function guest_user_search_limit_is_enforced()
    {
        $deviceId = 'test-device-' . uniqid();
        
        // Simulate reaching the search limit
        Cache::put("guest_search_count_{$deviceId}", 3, now()->addHours(24));

        $response = $this->get("/guest/search?q=iPhone&type=all&device_id={$deviceId}");

        $response->assertStatus(429);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $data);
        $this->assertEquals('Search limit exceeded', $data['error']);
    }

    /** @test */
    public function search_button_hanging_fix_works_with_inertia()
    {
        $this->actingAs($this->user);

        $response = $this->get('/search/results?q=iPhone&type=all', [
            'X-Inertia' => 'true',
            'X-Inertia-Version' => '1.0',
        ]);

        $response->assertStatus(200);
        $response->assertHeader('X-Inertia', 'true');
    }

    /** @test */
    public function search_tracks_user_searches()
    {
        $this->actingAs($this->user);

        $this->assertDatabaseMissing('user_searches', [
            'user_id' => $this->user->id,
            'search_query' => 'iPhone',
        ]);

        $this->get('/search/results?q=iPhone&type=all');

        $this->assertDatabaseHas('user_searches', [
            'user_id' => $this->user->id,
            'search_query' => 'iPhone',
            'search_type' => 'all',
        ]);
    }

    /** @test */
    public function admin_has_unlimited_search_access()
    {
        $this->actingAs($this->admin);

        // Simulate admin having performed many searches
        $this->admin->update(['search_count' => 1000]);

        $response = $this->get('/search/results?q=iPhone&type=all');

        $response->assertStatus(200);
    }

    /** @test */
    public function search_service_handles_different_search_types()
    {
        $searchService = app(SearchService::class);

        // Test category search
        $request = new \Illuminate\Http\Request(['q' => 'Display', 'type' => 'category']);
        $results = $searchService->searchParts($request, $this->user);
        $this->assertArrayHasKey('results', $results);

        // Test model search
        $request = new \Illuminate\Http\Request(['q' => 'iPhone', 'type' => 'model']);
        $results = $searchService->searchParts($request, $this->user);
        $this->assertArrayHasKey('results', $results);

        // Test part name search
        $request = new \Illuminate\Http\Request(['q' => 'Display', 'type' => 'part_name']);
        $results = $searchService->searchParts($request, $this->user);
        $this->assertArrayHasKey('results', $results);
    }

    /** @test */
    public function guest_search_service_tracks_analytics()
    {
        $guestSearchService = app(GuestSearchService::class);
        $deviceId = 'test-device-' . uniqid();

        $request = new \Illuminate\Http\Request([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);

        $results = $guestSearchService->searchParts($request);

        $this->assertArrayHasKey('results', $results);
        
        // Check that search count is tracked
        $searchCount = Cache::get("guest_search_count_{$deviceId}", 0);
        $this->assertEquals(1, $searchCount);
    }

    /** @test */
    public function search_with_filters_works_correctly()
    {
        $this->actingAs($this->user);

        $response = $this->get('/search/results?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
        ]));

        $response->assertStatus(200);
    }

    /** @test */
    public function search_handles_empty_queries_gracefully()
    {
        $this->actingAs($this->user);

        $response = $this->get('/search/results?q=&type=all');

        $response->assertStatus(200);
    }

    /** @test */
    public function search_handles_special_characters()
    {
        $this->actingAs($this->user);

        $response = $this->get('/search/results?' . http_build_query([
            'q' => 'iPhone 14 "Pro Max"',
            'type' => 'all',
        ]));

        $response->assertStatus(200);
    }

    /** @test */
    public function search_pagination_works_correctly()
    {
        $this->actingAs($this->user);

        // Create multiple parts for pagination testing
        Part::factory()->count(25)->create([
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        $response = $this->get('/search/results?q=test&type=all&page=2');

        $response->assertStatus(200);
    }

    /** @test */
    public function search_respects_user_subscription_limits()
    {
        // Test free user limits
        $freeUser = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 19, // Near limit
        ]);

        $this->actingAs($freeUser);

        $response = $this->get('/search/results?q=iPhone&type=all');
        $response->assertStatus(200);

        // Test when limit is exceeded
        $freeUser->update(['search_count' => 20]);
        $response = $this->get('/search/results?q=iPhone&type=all');
        $response->assertRedirect(route('subscription.plans'));
    }

    /** @test */
    public function search_error_handling_works()
    {
        $this->actingAs($this->user);

        // Test with invalid search type
        $response = $this->get('/search/results?q=iPhone&type=invalid');

        $response->assertStatus(200); // Should handle gracefully
    }

    /** @test */
    public function search_caching_improves_performance()
    {
        $this->actingAs($this->user);

        // First search
        $start = microtime(true);
        $this->get('/search/results?q=iPhone&type=all');
        $firstSearchTime = microtime(true) - $start;

        // Second identical search (should be faster due to caching)
        $start = microtime(true);
        $this->get('/search/results?q=iPhone&type=all');
        $secondSearchTime = microtime(true) - $start;

        // Note: This test might be flaky in CI environments
        // Consider using cache mocking for more reliable testing
        $this->assertTrue(true); // Placeholder assertion
    }
}
