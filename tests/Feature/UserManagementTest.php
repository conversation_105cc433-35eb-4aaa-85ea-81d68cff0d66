<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PaymentRequest;
use App\Models\UserImpersonationLog;
use App\Models\UserNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;
    protected User $regularUser;
    protected User $pendingUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Admin User',
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Create regular user
        $this->regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Regular User',
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Create pending user for approval tests
        $this->pendingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Pending User',
            'subscription_plan' => 'free',
            'status' => 'pending',
            'approval_status' => 'pending',
        ]);
    }

    #[Test]
    public function admin_can_view_users_index()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/users');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Users/<USER>')
                ->has('users.data')
                ->has('stats')
        );
    }

    #[Test]
    public function admin_can_approve_pending_user()
    {
        $this->assertTrue($this->pendingUser->isPendingApproval());

        $response = $this->actingAs($this->admin)
            ->postWithCsrf("/admin/users/{$this->pendingUser->id}/approve");

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->pendingUser->refresh();
        $this->assertEquals('approved', $this->pendingUser->approval_status);
        $this->assertEquals('active', $this->pendingUser->status);
        $this->assertEquals($this->admin->id, $this->pendingUser->approved_by);
        $this->assertNotNull($this->pendingUser->approved_at);
    }

    #[Test]
    public function admin_can_suspend_user()
    {
        // Fake mail to prevent email sending issues in tests
        \Illuminate\Support\Facades\Mail::fake();

        // First approve the user
        $this->regularUser->update([
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $suspensionReason = 'Violation of terms';

        $response = $this->actingAs($this->admin)
            ->postWithCsrf("/admin/users/{$this->regularUser->id}/suspend", [
                'reason' => $suspensionReason,
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->regularUser->refresh();
        $this->assertEquals('suspended', $this->regularUser->status);
        $this->assertEquals($suspensionReason, $this->regularUser->suspension_reason);
        $this->assertEquals($this->admin->id, $this->regularUser->suspended_by);
        $this->assertNotNull($this->regularUser->suspended_at);
    }

    #[Test]
    public function admin_can_unsuspend_user()
    {
        // First suspend the user
        $this->regularUser->suspend($this->admin, 'Test suspension');

        $response = $this->actingAs($this->admin)
            ->postWithCsrf("/admin/users/{$this->regularUser->id}/unsuspend");

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->regularUser->refresh();
        $this->assertEquals('active', $this->regularUser->status);
        $this->assertNull($this->regularUser->suspension_reason);
        $this->assertNull($this->regularUser->suspended_by);
        $this->assertNull($this->regularUser->suspended_at);
    }

    #[Test]
    public function user_model_relationships_work()
    {
        // Test payment requests relationship
        $paymentRequest = PaymentRequest::factory()->create([
            'user_id' => $this->regularUser->id,
            'amount' => 99.99,
            'currency' => 'USD',
            'payment_method' => 'bank_transfer',
            'subscription_plan' => 'premium',
        ]);

        $this->assertTrue($this->regularUser->paymentRequests->contains($paymentRequest));

        // Test activity logs relationship
        $this->regularUser->logActivity('test_activity', 'Test activity description');
        $this->assertCount(1, $this->regularUser->activityLogs);

        // Test notifications relationship
        $notification = UserNotification::create([
            'user_id' => $this->regularUser->id,
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'type' => 'info',
            'sent_by' => $this->admin->id,
        ]);

        $this->assertTrue($this->regularUser->notifications->contains($notification));
    }

    #[Test]
    public function payment_request_workflow_works()
    {
        $paymentRequest = PaymentRequest::create([
            'user_id' => $this->regularUser->id,
            'amount' => 99.99,
            'currency' => 'USD',
            'payment_method' => 'bank_transfer',
            'subscription_plan' => 'premium',
            'status' => 'pending',
            'notes' => 'Test payment request',
            'requested_at' => now(),
        ]);

        $this->assertTrue($paymentRequest->isPending());

        // Test approval
        $paymentRequest->approve($this->admin, 'Approved by admin');

        $this->assertTrue($paymentRequest->isApproved());
        $this->assertEquals($this->admin->id, $paymentRequest->approved_by);
        $this->assertNotNull($paymentRequest->approved_at);
    }

    #[Test]
    public function impersonation_logging_works()
    {
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->admin->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now(),
            'ip_address' => '127.0.0.1',
            'reason' => 'Testing impersonation',
        ]);

        $this->assertTrue($impersonationLog->isActive());
        $this->assertEquals($this->admin->id, $impersonationLog->admin_user_id);
        $this->assertEquals($this->regularUser->id, $impersonationLog->target_user_id);

        // End the session
        $impersonationLog->endSession();
        $this->assertFalse($impersonationLog->isActive());
        $this->assertNotNull($impersonationLog->ended_at);
    }

    #[Test]
    public function user_status_methods_work()
    {
        // Test pending approval
        $this->assertTrue($this->pendingUser->isPendingApproval());
        $this->assertFalse($this->pendingUser->isActive());

        // Test approval
        $this->pendingUser->approve($this->admin);
        $this->assertFalse($this->pendingUser->isPendingApproval());
        $this->assertTrue($this->pendingUser->isActive());

        // Test suspension
        $this->regularUser->suspend($this->admin, 'Test suspension');
        $this->assertTrue($this->regularUser->isSuspended());
        $this->assertFalse($this->regularUser->isActive());

        // Test unsuspension
        $this->regularUser->unsuspend();
        $this->assertFalse($this->regularUser->isSuspended());
        $this->assertTrue($this->regularUser->isActive());
    }

    #[Test]
    public function activity_logging_works()
    {
        $this->regularUser->logActivity(
            'test_activity',
            'Test activity description',
            ['key' => 'value'],
            $this->admin
        );

        $activityLog = $this->regularUser->activityLogs->first();

        $this->assertEquals('test_activity', $activityLog->activity_type);
        $this->assertEquals('Test activity description', $activityLog->description);
        $this->assertEquals(['key' => 'value'], $activityLog->metadata);
        $this->assertEquals($this->admin->id, $activityLog->performed_by);
    }

    #[Test]
    public function bulk_user_approval_works()
    {
        // Create additional pending users
        $user2 = User::factory()->create(['approval_status' => 'pending']);
        $user3 = User::factory()->create(['approval_status' => 'pending']);

        $response = $this->actingAs($this->admin)
            ->postWithCsrf('/admin/users/bulk-approve', [
                'user_ids' => [$this->regularUser->id, $user2->id, $user3->id],
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check all users are approved
        $this->regularUser->refresh();
        $user2->refresh();
        $user3->refresh();

        $this->assertEquals('approved', $this->regularUser->approval_status);
        $this->assertEquals('approved', $user2->approval_status);
        $this->assertEquals('approved', $user3->approval_status);
    }

    #[Test]
    public function admin_cannot_be_suspended()
    {
        $this->actingAs($this->admin)
            ->postWithCsrf("/admin/users/{$this->admin->id}/suspend", [
                'reason' => 'Test suspension',
            ]);

        // Should not allow suspension of admin
        $this->admin->refresh();
        $this->assertEquals('active', $this->admin->status);
    }

    #[Test]
    public function non_admin_cannot_access_user_management()
    {
        $response = $this->actingAs($this->regularUser)
            ->get('/admin/users');

        // Should be redirected or forbidden
        $response->assertStatus(403);
    }

    #[Test]
    public function admin_can_view_user_detail_page()
    {
        $response = $this->actingAs($this->admin)
            ->get("/admin/users/{$this->regularUser->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Users/<USER>')
                ->has('user')
                ->where('user.id', $this->regularUser->id)
                ->where('user.name', $this->regularUser->name)
        );
    }

    #[Test]
    public function admin_can_edit_user_details()
    {
        $newData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
        ];

        $response = $this->actingAs($this->admin)
            ->putWithCsrf("/admin/users/{$this->regularUser->id}", $newData);

        $response->assertRedirect("/admin/users/{$this->regularUser->id}");
        $response->assertSessionHas('success');

        $this->regularUser->refresh();
        $this->assertEquals($newData['name'], $this->regularUser->name);
        $this->assertEquals($newData['email'], $this->regularUser->email);
        $this->assertEquals($newData['subscription_plan'], $this->regularUser->subscription_plan);
    }

    #[Test]
    public function impersonation_status_endpoint_works()
    {
        $response = $this->actingAs($this->admin)
            ->get('/impersonation/status');

        $response->assertStatus(200);
        $response->assertJson([
            'is_impersonating' => false,
        ]);
    }

    #[Test]
    public function admin_can_view_payment_requests_index()
    {
        $paymentRequest = PaymentRequest::factory()->create([
            'user_id' => $this->regularUser->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get('/admin/payment-requests');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/PaymentRequests/Index')
                ->has('paymentRequests.data')
                ->has('stats')
        );
    }

    #[Test]
    public function admin_can_view_payment_request_detail()
    {
        $paymentRequest = PaymentRequest::factory()->create([
            'user_id' => $this->regularUser->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get("/admin/payment-requests/{$paymentRequest->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/PaymentRequests/Show')
                ->has('paymentRequest')
                ->where('paymentRequest.id', $paymentRequest->id)
        );
    }

    #[Test]
    public function admin_can_approve_payment_request()
    {
        $paymentRequest = PaymentRequest::factory()->pending()->create([
            'user_id' => $this->regularUser->id,
            'subscription_plan' => 'premium',
        ]);

        $response = $this->actingAs($this->admin)
            ->postWithCsrf("/admin/payment-requests/{$paymentRequest->id}/approve", [
                'admin_notes' => 'Payment verified and approved',
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $paymentRequest->refresh();
        $this->assertEquals('processed', $paymentRequest->status);
        $this->assertEquals($this->admin->id, $paymentRequest->approved_by);
        $this->assertNotNull($paymentRequest->approved_at);
        $this->assertEquals('Payment verified and approved', $paymentRequest->admin_notes);
    }

    #[Test]
    public function admin_can_reject_payment_request()
    {
        $paymentRequest = PaymentRequest::factory()->pending()->create([
            'user_id' => $this->regularUser->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->postWithCsrf("/admin/payment-requests/{$paymentRequest->id}/reject", [
                'admin_notes' => 'Invalid payment proof',
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $paymentRequest->refresh();
        $this->assertEquals('rejected', $paymentRequest->status);
        $this->assertEquals($this->admin->id, $paymentRequest->approved_by);
        $this->assertEquals('Invalid payment proof', $paymentRequest->admin_notes);
    }
}
