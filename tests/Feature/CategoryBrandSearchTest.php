<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CategoryBrandSearchTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Category $displayCategory;
    private Category $batteryCategory;
    private Brand $appleBrand;
    private Brand $samsungBrand;
    private Part $iphone15Display;
    private Part $iphone13Display;
    private Part $samsungDisplay;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user with premium subscription
        $this->user = User::factory()->create([
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
            'search_count' => 0,
            'daily_reset' => now()->toDateString()
        ]);

        // Create test categories
        $this->displayCategory = Category::factory()->create([
            'name' => 'Display',
            'description' => 'Mobile phone displays and screens',
            'is_active' => true
        ]);

        $this->batteryCategory = Category::factory()->create([
            'name' => 'Battery',
            'description' => 'Mobile phone batteries',
            'is_active' => true
        ]);

        // Create test brands
        $this->appleBrand = Brand::factory()->create([
            'name' => 'Apple',
            'is_active' => true
        ]);

        $this->samsungBrand = Brand::factory()->create([
            'name' => 'Samsung',
            'is_active' => true
        ]);

        // Create test mobile models
        $iphone15Model = MobileModel::factory()->create([
            'name' => 'iPhone 15 Pro Max',
            'brand_id' => $this->appleBrand->id,
            'release_year' => 2023
        ]);

        $iphone13Model = MobileModel::factory()->create([
            'name' => 'iPhone 13 Pro Max',
            'brand_id' => $this->appleBrand->id,
            'release_year' => 2021
        ]);

        $galaxyModel = MobileModel::factory()->create([
            'name' => 'Galaxy S24 Ultra',
            'brand_id' => $this->samsungBrand->id,
            'release_year' => 2024
        ]);

        // Create test parts
        $this->iphone15Display = Part::factory()->create([
            'name' => 'Apple iPhone 15 Pro Max Display',
            'part_number' => 'wu051-8155',
            'manufacturer' => 'OEM',
            'category_id' => $this->displayCategory->id,
            'description' => 'High-quality Display replacement for Apple iPhone 15 Pro Max',
            'is_active' => true
        ]);

        $this->iphone13Display = Part::factory()->create([
            'name' => 'Apple iPhone 13 Pro Max Display',
            'part_number' => 'wu051-8133',
            'manufacturer' => 'OEM',
            'category_id' => $this->displayCategory->id,
            'description' => 'High-quality Display replacement for Apple iPhone 13 Pro Max',
            'is_active' => true
        ]);

        $this->samsungDisplay = Part::factory()->create([
            'name' => 'Samsung Galaxy S24 Ultra Display',
            'part_number' => 'sg024-9001',
            'manufacturer' => 'Samsung',
            'category_id' => $this->displayCategory->id,
            'description' => 'Original Samsung display for Galaxy S24 Ultra',
            'is_active' => true
        ]);

        // Associate parts with models
        $this->iphone15Display->models()->attach($iphone15Model->id);
        $this->iphone13Display->models()->attach($iphone13Model->id);
        $this->samsungDisplay->models()->attach($galaxyModel->id);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function category_search_page_loads_without_query(): void
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/category-search')
                ->has('category')
                ->has('filters')
                ->where('category.id', $this->displayCategory->id)
                ->where('category.name', 'Display')
                ->missing('results')
                ->missing('query')
        );
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function brand_search_page_loads_without_query(): void
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.brand', $this->appleBrand->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/brand-search')
                ->has('brand')
                ->has('filters')
                ->where('brand.id', $this->appleBrand->id)
                ->where('brand.name', 'Apple')
                ->missing('results')
                ->missing('query')
        );
    }

    #[Test]
    public function category_search_returns_correct_results_for_first_search(): void
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Apple iPhone 15 Pro Max Display&type=all');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/category-search')
                ->has('category')
                ->has('results')
                ->where('query', 'Apple iPhone 15 Pro Max Display')
                ->where('search_type', 'all')
                ->where('results.total', 1)
                ->where('results.data.0.name', 'Apple iPhone 15 Pro Max Display')
        );
    }

    #[Test]
    public function category_search_returns_correct_results_for_second_search(): void
    {
        // First search
        $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Apple iPhone 15 Pro Max Display&type=all');

        // Second search - this should work correctly after our fixes
        $response = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Apple iPhone 13 Pro Max Display&type=all');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/category-search')
                ->has('category')
                ->has('results')
                ->where('query', 'Apple iPhone 13 Pro Max Display')
                ->where('search_type', 'all')
                ->where('results.total', 1)
                ->where('results.data.0.name', 'Apple iPhone 13 Pro Max Display')
        );
    }

    #[Test]
    public function brand_search_returns_correct_results_for_first_search(): void
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.brand', $this->appleBrand->id) . '?q=iPhone 15&type=all');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/brand-search')
                ->has('brand')
                ->has('results')
                ->where('query', 'iPhone 15')
                ->where('search_type', 'all')
                ->where('results.total', 1)
                ->where('results.data.0.name', 'Apple iPhone 15 Pro Max Display')
        );
    }

    #[Test]
    public function brand_search_returns_correct_results_for_second_search(): void
    {
        // First search
        $this->actingAs($this->user)
            ->get(route('search.brand', $this->appleBrand->id) . '?q=iPhone 15&type=all');

        // Second search - this should work correctly after our fixes
        $response = $this->actingAs($this->user)
            ->get(route('search.brand', $this->appleBrand->id) . '?q=iPhone 13&type=all');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/brand-search')
                ->has('brand')
                ->has('results')
                ->where('query', 'iPhone 13')
                ->where('search_type', 'all')
                ->where('results.total', 1)
                ->where('results.data.0.name', 'Apple iPhone 13 Pro Max Display')
        );
    }

    #[Test]
    public function category_search_suggestions_work_correctly(): void
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.suggestions') . '?q=iPhone&category_id=' . $this->displayCategory->id);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => ['type', 'value']
        ]);

        $suggestions = $response->json();
        $this->assertNotEmpty($suggestions);
        
        // Check that suggestions contain our test parts
        $suggestionValues = collect($suggestions)->pluck('value')->toArray();
        $this->assertContains('Apple iPhone 15 Pro Max Display', $suggestionValues);
        $this->assertContains('Apple iPhone 13 Pro Max Display', $suggestionValues);
    }

    #[Test]
    public function brand_search_suggestions_work_correctly(): void
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.suggestions') . '?q=iPhone&brand_id=' . $this->appleBrand->id);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => ['type', 'value']
        ]);

        $suggestions = $response->json();
        $this->assertNotEmpty($suggestions);
        
        // Check that suggestions contain our test parts
        $suggestionValues = collect($suggestions)->pluck('value')->toArray();
        $this->assertContains('Apple iPhone 15 Pro Max Display', $suggestionValues);
        $this->assertContains('Apple iPhone 13 Pro Max Display', $suggestionValues);
    }

    #[Test]
    public function category_search_handles_no_results_correctly(): void
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=NonExistentPart&type=all');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/category-search')
                ->has('category')
                ->has('results')
                ->where('query', 'NonExistentPart')
                ->where('search_type', 'all')
                ->where('results.total', 0)
                ->where('results.data', [])
        );
    }

    #[Test]
    public function brand_search_handles_no_results_correctly(): void
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.brand', $this->appleBrand->id) . '?q=NonExistentPart&type=all');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/brand-search')
                ->has('brand')
                ->has('results')
                ->where('query', 'NonExistentPart')
                ->where('search_type', 'all')
                ->where('results.total', 0)
                ->where('results.data', [])
        );
    }

    #[Test]
    public function category_search_respects_search_type_filters(): void
    {
        // Test part_name search type
        $response = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Display&type=part_name');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/category-search')
                ->where('search_type', 'part_name')
                ->where('results.total', 3) // All three display parts should match
        );
    }

    #[Test]
    public function brand_search_respects_search_type_filters(): void
    {
        // Test part_name search type
        $response = $this->actingAs($this->user)
            ->get(route('search.brand', $this->appleBrand->id) . '?q=Display&type=part_name');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/brand-search')
                ->where('search_type', 'part_name')
                ->where('results.total', 2) // Only Apple display parts should match
        );
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function multiple_consecutive_searches_work_correctly(): void
    {
        // Simulate the exact issue reported: first search works, second doesn't
        
        // First search
        $response1 = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Apple iPhone 15 Pro Max Display&type=all');
        
        $response1->assertStatus(200);
        $response1->assertInertia(fn ($page) =>
            $page->where('results.total', 1)
                ->where('results.data.0.name', 'Apple iPhone 15 Pro Max Display')
        );

        // Second search - this was failing before our fixes
        $response2 = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Apple iPhone 13 Pro Max Display&type=all');
        
        $response2->assertStatus(200);
        $response2->assertInertia(fn ($page) =>
            $page->where('results.total', 1)
                ->where('results.data.0.name', 'Apple iPhone 13 Pro Max Display')
        );

        // Third search to ensure consistency
        $response3 = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Samsung&type=all');
        
        $response3->assertStatus(200);
        $response3->assertInertia(fn ($page) =>
            $page->where('results.total', 1)
                ->where('results.data.0.name', 'Samsung Galaxy S24 Ultra Display')
        );
    }

    #[Test]
    public function search_with_filters_works_correctly(): void
    {
        // Test category search with brand filter
        $response = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Display&type=all&brand_id=' . $this->appleBrand->id);

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/category-search')
                ->where('results.total', 2) // Only Apple display parts
                ->where('applied_filters.brand_id', (string)$this->appleBrand->id)
        );
    }

    #[Test]
    public function pagination_works_correctly(): void
    {
        // Create more parts to test pagination
        for ($i = 1; $i <= 25; $i++) {
            Part::factory()->create([
                'name' => "Test Display Part {$i}",
                'category_id' => $this->displayCategory->id,
                'is_active' => true
            ]);
        }

        $response = $this->actingAs($this->user)
            ->get(route('search.category', $this->displayCategory->id) . '?q=Display&type=all');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/category-search')
                ->where('results.per_page', 20)
                ->where('results.current_page', 1)
                ->has('results.data', 20) // Should have 20 items per page
        );
    }
}
