<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserSearch;
use App\Http\Controllers\Admin\SearchAnalyticsController;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SearchAnalyticsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Create test search data
        UserSearch::factory()->count(10)->create([
            'user_id' => $this->user->id,
            'created_at' => now()->subDays(rand(1, 30)),
        ]);
    }

    /** @test */
    public function admin_can_access_search_analytics_dashboard()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/SearchAnalytics/Index')
                ->has('analytics')
                ->has('analytics.overview')
                ->has('analytics.user_searches')
                ->has('analytics.guest_searches')
                ->has('analytics.search_trends')
                ->has('analytics.popular_searches')
                ->has('analytics.search_performance')
                ->has('analytics.real_time_stats')
        );
    }

    /** @test */
    public function non_admin_cannot_access_search_analytics()
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(403);
    }

    /** @test */
    public function search_analytics_overview_calculates_correctly()
    {
        $this->actingAs($this->admin);

        // Create specific test data
        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'search_query' => 'iPhone',
            'search_type' => 'all',
            'results_count' => 5,
            'created_at' => now(),
        ]);

        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'search_query' => 'Samsung',
            'search_type' => 'brand',
            'results_count' => 0,
            'created_at' => now(),
        ]);

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.overview.total_searches')
                ->has('analytics.overview.user_searches')
                ->has('analytics.overview.unique_searchers')
                ->has('analytics.overview.search_success_rate')
        );
    }

    /** @test */
    public function search_analytics_tracks_popular_searches()
    {
        $this->actingAs($this->admin);

        // Create multiple searches for the same query
        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'search_query' => 'iPhone Display',
            'search_type' => 'all',
            'results_count' => 3,
            'created_at' => now(),
        ]);

        UserSearch::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'search_query' => 'Samsung Battery',
            'search_type' => 'all',
            'results_count' => 2,
            'created_at' => now(),
        ]);

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.popular_searches.popular_queries')
                ->has('analytics.popular_searches.searches_by_type')
        );
    }

    /** @test */
    public function search_analytics_calculates_success_rate()
    {
        $this->actingAs($this->admin);

        // Create searches with and without results
        UserSearch::factory()->count(7)->create([
            'user_id' => $this->user->id,
            'results_count' => 5, // Successful searches
            'created_at' => now(),
        ]);

        UserSearch::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'results_count' => 0, // Failed searches
            'created_at' => now(),
        ]);

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->where('analytics.search_performance.success_rate', 70.0) // 7/10 = 70%
        );
    }

    /** @test */
    public function search_analytics_tracks_guest_searches()
    {
        $this->actingAs($this->admin);

        // Simulate guest search data in cache
        Cache::put('guest_searches_count_' . now()->format('Y-m-d'), 15, now()->addDays(30));
        Cache::put('guest_searches_count_' . now()->subDay()->format('Y-m-d'), 10, now()->addDays(30));

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.guest_searches.total_searches')
                ->has('analytics.guest_searches.unique_devices')
                ->has('analytics.guest_searches.searches_by_date')
        );
    }

    /** @test */
    public function search_analytics_real_time_data_endpoint_works()
    {
        $this->actingAs($this->admin);

        // Create recent search data
        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'created_at' => now(),
        ]);

        $response = $this->get('/admin/search-analytics/real-time?type=overview');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'searches_today',
            'searches_last_hour',
            'active_searchers_today',
            'guest_searches_today',
        ]);
    }

    /** @test */
    public function search_analytics_export_csv_works()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/admin/search-analytics/export?format=csv&days=30');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
        $response->assertHeader('Content-Disposition', 'attachment; filename="search_analytics_' . now()->format('Y-m-d') . '.csv"');
    }

    /** @test */
    public function search_analytics_export_json_works()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/admin/search-analytics/export?format=json&days=30');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'overview',
            'user_searches',
            'guest_searches',
            'search_trends',
            'popular_searches',
            'search_performance',
            'real_time_stats',
        ]);
    }

    /** @test */
    public function search_analytics_filters_by_date_range()
    {
        $this->actingAs($this->admin);

        // Create searches in different time periods
        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'created_at' => now()->subDays(5),
        ]);

        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'created_at' => now()->subDays(35), // Outside 30-day range
        ]);

        $response = $this->get('/admin/search-analytics?days=30');

        $response->assertStatus(200);
        // The analytics should only include searches from the last 30 days
    }

    /** @test */
    public function search_analytics_handles_empty_data()
    {
        $this->actingAs($this->admin);

        // Clear all search data
        UserSearch::query()->delete();

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->where('analytics.overview.total_searches', 0)
                ->where('analytics.overview.unique_searchers', 0)
        );
    }

    /** @test */
    public function search_analytics_tracks_search_types_distribution()
    {
        $this->actingAs($this->admin);

        // Create searches of different types
        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'search_type' => 'all',
            'created_at' => now(),
        ]);

        UserSearch::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'search_type' => 'category',
            'created_at' => now(),
        ]);

        UserSearch::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'search_type' => 'brand',
            'created_at' => now(),
        ]);

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.user_searches.searches_by_type')
        );
    }

    /** @test */
    public function search_analytics_identifies_top_searchers()
    {
        $this->actingAs($this->admin);

        $heavyUser = User::factory()->create();

        // Create many searches for one user
        UserSearch::factory()->count(15)->create([
            'user_id' => $heavyUser->id,
            'created_at' => now(),
        ]);

        // Create fewer searches for another user
        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'created_at' => now(),
        ]);

        $response = $this->get('/admin/search-analytics');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('analytics.user_searches.top_searchers')
        );
    }

    /** @test */
    public function search_analytics_recent_searches_endpoint_works()
    {
        $this->actingAs($this->admin);

        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'created_at' => now(),
        ]);

        $response = $this->get('/admin/search-analytics/real-time?type=recent_searches');

        $response->assertStatus(200);
        $response->assertJsonCount(5);
        $response->assertJsonStructure([
            '*' => [
                'id',
                'search_query',
                'search_type',
                'results_count',
                'created_at',
                'user' => [
                    'id',
                    'name',
                ],
            ],
        ]);
    }
}
