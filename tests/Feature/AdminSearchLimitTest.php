<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminSearchLimitTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;
    private User $regularUser;
    private SubscriptionService $subscriptionService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = $this->createAdminUser();
        $this->regularUser = User::factory()->create([
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
            'search_count' => 0,
            'daily_reset' => today(),
        ]);
        
        $this->subscriptionService = app(SubscriptionService::class);
    }

    public function test_admin_user_can_search_unlimited_times(): void
    {
        // Verify admin user can search
        $this->assertTrue($this->adminUser->canSearch());
        $this->assertTrue($this->subscriptionService->canUserSearch($this->adminUser));
        $this->assertEquals(-1, $this->adminUser->getRemainingSearches());
        $this->assertEquals(-1, $this->subscriptionService->getRemainingSearches($this->adminUser));
    }

    public function test_admin_user_bypasses_search_limit_middleware(): void
    {
        // Set admin user's search count to a high number to simulate exceeding limits
        $this->adminUser->update(['search_count' => 100]);
        
        $response = $this->actingAs($this->adminUser)
            ->get(route('search.results', ['q' => 'test']));

        // Admin should still be able to search
        $response->assertStatus(200);
    }

    public function test_regular_user_has_search_limits(): void
    {
        // Regular user should have limits
        $this->assertTrue($this->regularUser->canSearch());
        $this->assertTrue($this->subscriptionService->canUserSearch($this->regularUser));
        $this->assertEquals(20, $this->regularUser->getRemainingSearches());
        
        // Set regular user to exceed limit
        $this->regularUser->update(['search_count' => 20]);
        
        $this->assertFalse($this->regularUser->canSearch());
        $this->assertFalse($this->subscriptionService->canUserSearch($this->regularUser));
        $this->assertEquals(0, $this->regularUser->getRemainingSearches());
    }

    public function test_regular_user_blocked_when_exceeding_limit(): void
    {
        // Set regular user to exceed limit
        $this->regularUser->update(['search_count' => 20]);
        
        $response = $this->actingAs($this->regularUser)
            ->get(route('search.results', ['q' => 'test']));

        // Regular user should be blocked
        $response->assertStatus(429);
        $response->assertJson([
            'error' => 'Daily search limit exceeded',
        ]);
    }

    public function test_admin_search_count_not_incremented(): void
    {
        $initialCount = $this->adminUser->search_count;
        
        // Increment search count
        $this->adminUser->incrementSearchCount();
        
        // Admin search count should not change
        $this->assertEquals($initialCount, $this->adminUser->fresh()->search_count);
    }

    public function test_regular_user_search_count_incremented(): void
    {
        $initialCount = $this->regularUser->search_count;
        
        // Increment search count
        $this->regularUser->incrementSearchCount();
        
        // Regular user search count should increase
        $this->assertEquals($initialCount + 1, $this->regularUser->fresh()->search_count);
    }

    public function test_premium_user_has_unlimited_searches(): void
    {
        // Skip this test for now as it requires subscription setup
        $this->markTestSkipped('Premium user test requires subscription model setup');
    }

    public function test_admin_user_identification(): void
    {
        // Test that admin user is correctly identified
        $this->assertTrue($this->adminUser->isAdmin());
        $this->assertFalse($this->regularUser->isAdmin());
    }

    public function test_search_limit_reset_daily(): void
    {
        // Set regular user to have searches from yesterday
        $this->regularUser->update([
            'search_count' => 15,
            'daily_reset' => now()->subDay(),
        ]);

        // Check if user can search (should reset count)
        $canSearch = $this->regularUser->canSearch();

        $this->assertTrue($canSearch);
        $this->assertEquals(0, $this->regularUser->fresh()->search_count);
        $this->assertEquals(now()->toDateString(), $this->regularUser->fresh()->daily_reset->toDateString());
    }

    public function test_admin_search_via_api_endpoint(): void
    {
        // Skip this test as it requires actual search implementation
        $this->markTestSkipped('Search endpoint test requires full search implementation');
    }

    public function test_admin_search_via_inertia_request(): void
    {
        // Skip this test as it requires actual search implementation
        $this->markTestSkipped('Search endpoint test requires full search implementation');
    }
}
