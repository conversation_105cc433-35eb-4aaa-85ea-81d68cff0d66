<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SlugFunctionalityTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create(['subscription_plan' => 'premium']);
    }

    public function test_brand_slug_generation(): void
    {
        $brand = Brand::factory()->create(['name' => 'Test Brand']);

        $this->assertNotNull($brand->slug);
        $this->assertEquals('test-brand', $brand->slug);
    }

    public function test_category_slug_generation(): void
    {
        $category = Category::factory()->create(['name' => 'Test Category']);

        $this->assertNotNull($category->slug);
        $this->assertEquals('test-category', $category->slug);
    }

    public function test_mobile_model_slug_generation(): void
    {
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $model = MobileModel::factory()->create([
            'brand_id' => $brand->id,
            'name' => 'iPhone 15'
        ]);

        $this->assertNotNull($model->slug);
        $this->assertEquals('apple-iphone-15', $model->slug);
    }

    public function test_part_slug_generation(): void
    {
        $category = Category::factory()->create();
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'LCD Screen'
        ]);

        $this->assertNotNull($part->slug);
        $this->assertEquals('lcd-screen', $part->slug);
    }

    public function test_slug_uniqueness(): void
    {
        $category = Category::factory()->create();

        $part1 = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Test Part'
        ]);

        $part2 = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Test Part'
        ]);

        $this->assertNotEquals($part1->slug, $part2->slug);
        $this->assertEquals('test-part', $part1->slug);
        $this->assertEquals('test-part-2', $part2->slug);
    }

    public function test_route_model_binding_with_slug(): void
    {
        $category = Category::factory()->create();
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Test Part'
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('parts.show', $part->slug));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                 ->where('part.id', $part->id)
                 ->where('part.slug', $part->slug)
        );
    }

    public function test_route_model_binding_with_id_fallback(): void
    {
        $category = Category::factory()->create();
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Test Part'
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('parts.show', $part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                 ->where('part.id', $part->id)
        );
    }

    public function test_slug_generation_command(): void
    {
        // Create records by directly inserting into database to avoid slug generation
        $brandId = \DB::table('brands')->insertGetId([
            'name' => 'Test Brand',
            'slug' => null,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $categoryId = \DB::table('categories')->insertGetId([
            'name' => 'Test Category',
            'slug' => null,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Verify slugs are null
        $brand = Brand::find($brandId);
        $category = Category::find($categoryId);

        $this->assertNull($brand->slug);
        $this->assertNull($category->slug);

        // Run the slug generation command
        $this->artisan('slugs:generate')
            ->expectsOutput('Starting slug generation...')
            ->expectsOutput('Generated slugs for 1 brands.')
            ->expectsOutput('Generated slugs for 1 categories.')
            ->expectsOutput('Slug generation completed!')
            ->assertExitCode(0);

        // Check that slugs were generated
        $brand->refresh();
        $category->refresh();

        $this->assertNotNull($brand->slug);
        $this->assertNotNull($category->slug);
        $this->assertEquals('test-brand', $brand->slug);
        $this->assertEquals('test-category', $category->slug);
    }
}
