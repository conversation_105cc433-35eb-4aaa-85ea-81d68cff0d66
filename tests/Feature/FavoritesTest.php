<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Part;
use App\Models\MobileModel;
use App\Models\UserFavorite;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class FavoritesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $part;
    protected $model;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        // Create test category and brand
        $category = Category::factory()->create(['name' => 'Test Category']);
        $brand = Brand::factory()->create(['name' => 'Test Brand']);

        // Create test part
        $this->part = Part::factory()->create([
            'name' => 'Test Part',
            'category_id' => $category->id,
            'part_number' => 'TP001',
            'manufacturer' => 'Test Manufacturer'
        ]);

        // Create test mobile model
        $this->model = MobileModel::factory()->create([
            'name' => 'Test Model',
            'brand_id' => $brand->id,
            'model_number' => 'TM001'
        ]);
    }

    /** @test */
    public function authenticated_user_can_add_part_to_favorites()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), [
                'type' => 'part',
                'id' => $this->part->id
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => "'{$this->part->name}' has been added to your favorites"
            ]);

        $this->assertDatabaseHas('user_favorites', [
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id
        ]);
    }

    /** @test */
    public function authenticated_user_can_add_model_to_favorites()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), [
                'type' => 'model',
                'id' => $this->model->id
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => "'{$this->model->name}' has been added to your favorites"
            ]);

        $this->assertDatabaseHas('user_favorites', [
            'user_id' => $this->user->id,
            'favoritable_type' => MobileModel::class,
            'favoritable_id' => $this->model->id
        ]);
    }

    /** @test */
    public function unauthenticated_user_cannot_add_to_favorites()
    {
        $response = $this->postJson(route('dashboard.add-favorite'), [
            'type' => 'part',
            'id' => $this->part->id
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function user_cannot_add_same_item_to_favorites_twice()
    {
        // Add to favorites first time
        $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), [
                'type' => 'part',
                'id' => $this->part->id
            ]);

        // Try to add again
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), [
                'type' => 'part',
                'id' => $this->part->id
            ]);

        $response->assertStatus(409)
            ->assertJson([
                'message' => 'This part is already in your favorites'
            ]);

        // Should only have one record
        $this->assertEquals(1, UserFavorite::where('user_id', $this->user->id)->count());
    }

    /** @test */
    public function user_cannot_add_nonexistent_item_to_favorites()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), [
                'type' => 'part',
                'id' => 99999
            ]);

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'Part not found'
            ]);
    }

    /** @test */
    public function add_favorite_validates_required_fields()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['type', 'id']);
    }

    /** @test */
    public function add_favorite_validates_type_field()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), [
                'type' => 'invalid',
                'id' => $this->part->id
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['type']);
    }

    /** @test */
    public function user_can_remove_part_from_favorites()
    {
        // Add to favorites first
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson(route('dashboard.remove-favorite'), [
                'type' => 'part',
                'id' => $this->part->id
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => "'{$this->part->name}' has been removed from your favorites"
            ]);

        $this->assertDatabaseMissing('user_favorites', [
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id
        ]);
    }

    /** @test */
    public function user_can_remove_model_from_favorites()
    {
        // Add to favorites first
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => MobileModel::class,
            'favoritable_id' => $this->model->id
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson(route('dashboard.remove-favorite'), [
                'type' => 'model',
                'id' => $this->model->id
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => "'{$this->model->name}' has been removed from your favorites"
            ]);

        $this->assertDatabaseMissing('user_favorites', [
            'user_id' => $this->user->id,
            'favoritable_type' => MobileModel::class,
            'favoritable_id' => $this->model->id
        ]);
    }

    /** @test */
    public function user_cannot_remove_item_not_in_favorites()
    {
        $response = $this->actingAs($this->user)
            ->deleteJson(route('dashboard.remove-favorite'), [
                'type' => 'part',
                'id' => $this->part->id
            ]);

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'This part is not in your favorites'
            ]);
    }

    /** @test */
    public function user_can_view_favorites_page()
    {
        // Add some favorites
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id
        ]);

        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => MobileModel::class,
            'favoritable_id' => $this->model->id
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('dashboard.favorites'));

        $response->assertStatus(200)
            ->assertInertia(fn ($page) => $page
                ->component('dashboard/favorites')
                ->has('favorites.data', 2)
            );
    }

    /** @test */
    public function favorites_page_loads_relationships_correctly()
    {
        // Add part to favorites
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('dashboard.favorites'));

        $response->assertStatus(200)
            ->assertInertia(fn ($page) => $page
                ->component('dashboard/favorites')
                ->has('favorites.data.0.favoritable.name')
                ->has('favorites.data.0.favoritable.category')
            );
    }

    /** @test */
    public function remove_favorite_validates_required_fields()
    {
        $response = $this->actingAs($this->user)
            ->deleteJson(route('dashboard.remove-favorite'), []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['type', 'id']);
    }

    /** @test */
    public function favorites_are_paginated()
    {
        // Create 15 favorites (more than default pagination)
        for ($i = 0; $i < 15; $i++) {
            $part = Part::factory()->create([
                'category_id' => Category::factory()->create()->id
            ]);
            
            UserFavorite::create([
                'user_id' => $this->user->id,
                'favoritable_type' => Part::class,
                'favoritable_id' => $part->id
            ]);
        }

        $response = $this->actingAs($this->user)
            ->get(route('dashboard.favorites'));

        $response->assertStatus(200)
            ->assertInertia(fn ($page) => $page
                ->component('dashboard/favorites')
                ->has('favorites.data', 12) // Default pagination is 12
                ->has('favorites.links')
            );
    }
}
