import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { router } from '@inertiajs/react';
import { vi } from 'vitest';
import AdminPartsIndex from '../../resources/js/pages/admin/Parts/Index';

// Mock Inertia router
vi.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <div data-testid="head">{children}</div>,
    Link: ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href}>{children}</a>
    ),
    router: {
        visit: vi.fn(),
        get: vi.fn(),
    },
    usePage: () => ({
        props: {
            auth: {
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    name: 'Admin User',
                },
            },
        },
    }),
}));

// Mock toast
vi.mock('sonner', () => ({
    toast: {
        error: vi.fn(),
        success: vi.fn(),
    },
}));

// Mock hooks
vi.mock('../../resources/js/hooks/use-delete-confirmation', () => ({
    useDeleteConfirmation: () => ({
        showDeleteConfirmation: vi.fn(),
    }),
}));

// Mock layouts
vi.mock('../../resources/js/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

// Mock UnifiedSearchInterface
vi.mock('../../resources/js/components/unified-search-interface', () => ({
    UnifiedSearchInterface: ({ 
        searchQuery, 
        setSearchQuery, 
        onCustomSearch, 
        showFilters 
    }: any) => (
        <div data-testid="unified-search-interface">
            <input
                data-testid="search-input"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search parts..."
            />
            <button
                data-testid="search-button"
                onClick={() => onCustomSearch && onCustomSearch(searchQuery, 'all', {})}
            >
                Search
            </button>
            <div data-testid="show-filters">{showFilters ? 'true' : 'false'}</div>
        </div>
    ),
}));

const mockProps = {
    parts: {
        data: [
            {
                id: 1,
                name: 'iPhone 13 Screen',
                part_number: 'IP13-SCR-001',
                manufacturer: 'Apple',
                category: { id: 1, name: 'Display' },
                is_active: true,
                created_at: '2024-01-01T00:00:00.000000Z',
            },
            {
                id: 2,
                name: 'Samsung Galaxy Battery',
                part_number: 'SG-BAT-002',
                manufacturer: 'Samsung',
                category: { id: 2, name: 'Battery' },
                is_active: true,
                created_at: '2024-01-02T00:00:00.000000Z',
            },
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 2,
        from: 1,
        to: 2,
    },
    filters: {
        categories: [
            { id: 1, name: 'Display' },
            { id: 2, name: 'Battery' },
        ],
        manufacturers: ['Apple', 'Samsung'],
    },
    queryParams: {},
};

describe('Admin Parts Search Interface', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders without duplicate search interfaces', () => {
        render(<AdminPartsIndex {...mockProps} />);
        
        // Should only have one UnifiedSearchInterface
        const searchInterfaces = screen.getAllByTestId('unified-search-interface');
        expect(searchInterfaces).toHaveLength(1);
        
        // Should show that built-in filters are disabled
        expect(screen.getByTestId('show-filters')).toHaveTextContent('false');
    });

    it('shows advanced filters when toggle is clicked', async () => {
        const user = userEvent.setup();
        render(<AdminPartsIndex {...mockProps} />);
        
        // Advanced filters should not be visible initially
        expect(screen.queryByLabelText('Category')).not.toBeInTheDocument();
        
        // Click Advanced Filters button
        const advancedFiltersButton = screen.getByText('Advanced Filters');
        await user.click(advancedFiltersButton);
        
        // Advanced filters should now be visible
        expect(screen.getByLabelText('Category')).toBeInTheDocument();
        expect(screen.getByLabelText('Manufacturer')).toBeInTheDocument();
        expect(screen.getByLabelText('Status')).toBeInTheDocument();
    });

    it('handles search through UnifiedSearchInterface correctly', async () => {
        const user = userEvent.setup();
        render(<AdminPartsIndex {...mockProps} />);
        
        const searchInput = screen.getByTestId('search-input');
        const searchButton = screen.getByTestId('search-button');
        
        // Type search query
        await user.type(searchInput, 'iPhone');
        
        // Click search
        await user.click(searchButton);
        
        // Should call router.visit with correct parameters
        expect(router.visit).toHaveBeenCalledWith('/admin/parts', {
            data: {
                search: 'iPhone',
            },
            onStart: expect.any(Function),
            onFinish: expect.any(Function),
            onError: expect.any(Function),
            onCancel: expect.any(Function),
            preserveState: true,
            preserveScroll: false,
        });
    });

    it('handles filter application correctly', async () => {
        const user = userEvent.setup();
        render(<AdminPartsIndex {...mockProps} />);
        
        // Open advanced filters
        const advancedFiltersButton = screen.getByText('Advanced Filters');
        await user.click(advancedFiltersButton);
        
        // Select category filter
        const categorySelect = screen.getByLabelText('Category');
        await user.click(categorySelect);
        
        // Wait for dropdown to open and select an option
        await waitFor(() => {
            const displayOption = screen.getByText('Display');
            user.click(displayOption);
        });
        
        // Click Apply Filters button
        const applyButton = screen.getByText('Apply Filters');
        await user.click(applyButton);
        
        // Should call router.visit with filter parameters
        expect(router.visit).toHaveBeenCalledWith('/admin/parts', {
            data: {
                category_id: '1',
            },
            onStart: expect.any(Function),
            onFinish: expect.any(Function),
            onError: expect.any(Function),
            onCancel: expect.any(Function),
            preserveState: true,
            preserveScroll: false,
        });
    });

    it('shows clear filters button when filters are active', async () => {
        const user = userEvent.setup();
        const propsWithFilters = {
            ...mockProps,
            queryParams: { search: 'iPhone', category_id: '1' },
        };
        
        render(<AdminPartsIndex {...propsWithFilters} />);
        
        // Clear All button should be visible
        expect(screen.getByText('Clear All')).toBeInTheDocument();
        
        // Click Clear All
        const clearButton = screen.getByText('Clear All');
        await user.click(clearButton);
        
        // Should call router.get to clear filters
        expect(router.get).toHaveBeenCalledWith('/admin/parts', {}, {
            preserveState: true,
            preserveScroll: false,
        });
    });

    it('handles combined search and filters correctly', async () => {
        const user = userEvent.setup();
        render(<AdminPartsIndex {...mockProps} />);
        
        // Enter search term
        const searchInput = screen.getByTestId('search-input');
        await user.type(searchInput, 'iPhone');
        
        // Open advanced filters
        const advancedFiltersButton = screen.getByText('Advanced Filters');
        await user.click(advancedFiltersButton);
        
        // Select manufacturer filter
        const manufacturerSelect = screen.getByLabelText('Manufacturer');
        await user.click(manufacturerSelect);
        
        await waitFor(() => {
            const appleOption = screen.getByText('Apple');
            user.click(appleOption);
        });
        
        // Apply filters
        const applyButton = screen.getByText('Apply Filters');
        await user.click(applyButton);
        
        // Should combine search and filter parameters
        expect(router.visit).toHaveBeenCalledWith('/admin/parts', {
            data: {
                search: 'iPhone',
                manufacturer: 'Apple',
            },
            onStart: expect.any(Function),
            onFinish: expect.any(Function),
            onError: expect.any(Function),
            onCancel: expect.any(Function),
            preserveState: true,
            preserveScroll: false,
        });
    });
});
