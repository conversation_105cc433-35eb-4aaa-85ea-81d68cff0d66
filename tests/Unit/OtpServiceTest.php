<?php

namespace Tests\Unit;

use App\Models\User;
use App\Services\OtpService;
use App\Mail\AdminOtpCode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class OtpServiceTest extends TestCase
{
    use RefreshDatabase;

    private OtpService $otpService;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->otpService = new OtpService();
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'two_factor_enabled' => true,
        ]);
    }

    public function test_generate_and_send_otp_success(): void
    {
        Mail::fake();

        $result = $this->otpService->generateAndSendOtp($this->user, 'test_action');

        $this->assertTrue($result);
        $this->user->refresh();
        $this->assertNotNull($this->user->current_otp_code);
        $this->assertNotNull($this->user->otp_expires_at);
        $this->assertEquals(0, $this->user->otp_attempts);
        
        Mail::assertQueued(AdminOtpCode::class, function ($mail) {
            return $mail->hasTo($this->user->email);
        });
    }

    public function test_generate_and_send_otp_fails_when_locked_out(): void
    {
        Mail::fake();
        
        // Lock out the user
        Cache::put("otp_lockout:{$this->user->id}", now()->addMinutes(30), now()->addMinutes(30));

        $result = $this->otpService->generateAndSendOtp($this->user, 'test_action');

        $this->assertFalse($result);
        Mail::assertNotQueued(AdminOtpCode::class);
    }

    public function test_verify_otp_success(): void
    {
        // Generate OTP first
        $this->otpService->generateAndSendOtp($this->user, 'test_action');
        $this->user->refresh();
        $otpCode = $this->user->current_otp_code;

        $result = $this->otpService->verifyOtp($this->user, $otpCode, 'test_action');

        $this->assertTrue($result);
        $this->user->refresh();
        $this->assertNull($this->user->current_otp_code);
        $this->assertNull($this->user->otp_expires_at);
        $this->assertEquals(0, $this->user->otp_attempts);
    }

    public function test_verify_otp_fails_with_invalid_code(): void
    {
        // Generate OTP first
        $this->otpService->generateAndSendOtp($this->user, 'test_action');

        $result = $this->otpService->verifyOtp($this->user, '000000', 'test_action');

        $this->assertFalse($result);
        $this->user->refresh();
        $this->assertEquals(1, $this->user->otp_attempts);
        $this->assertNotNull($this->user->current_otp_code); // OTP should still exist
    }

    public function test_verify_otp_fails_when_expired(): void
    {
        // Generate OTP and manually expire it
        $this->otpService->generateAndSendOtp($this->user, 'test_action');
        $this->user->update(['otp_expires_at' => now()->subMinutes(1)]);
        $this->user->refresh();
        $otpCode = $this->user->current_otp_code;

        $result = $this->otpService->verifyOtp($this->user, $otpCode, 'test_action');

        $this->assertFalse($result);
        $this->user->refresh();
        $this->assertNull($this->user->current_otp_code); // Should be cleared
    }

    public function test_verify_otp_lockout_after_max_attempts(): void
    {
        // Generate OTP first
        $this->otpService->generateAndSendOtp($this->user, 'test_action');

        // Make 3 failed attempts
        for ($i = 0; $i < 3; $i++) {
            $result = $this->otpService->verifyOtp($this->user, '000000', 'test_action');
            $this->assertFalse($result);
        }

        // User should now be locked out
        $this->assertTrue($this->otpService->isUserLockedOut($this->user));
        $this->user->refresh();
        $this->assertNull($this->user->current_otp_code); // Should be cleared after lockout
    }

    public function test_verify_otp_success_on_final_attempt(): void
    {
        // Generate OTP first
        $this->otpService->generateAndSendOtp($this->user, 'test_action');
        $this->user->refresh();
        $otpCode = $this->user->current_otp_code;

        // Make 2 failed attempts
        for ($i = 0; $i < 2; $i++) {
            $result = $this->otpService->verifyOtp($this->user, '000000', 'test_action');
            $this->assertFalse($result);
        }

        // Third attempt with correct code should succeed
        $result = $this->otpService->verifyOtp($this->user, $otpCode, 'test_action');
        
        $this->assertTrue($result);
        $this->assertFalse($this->otpService->isUserLockedOut($this->user));
        $this->user->refresh();
        $this->assertNull($this->user->current_otp_code);
    }

    public function test_otp_session_management(): void
    {
        $action = 'test_action';
        
        // Initially no session
        $this->assertFalse($this->otpService->hasValidOtpSession($this->user, $action));

        // Create session
        $this->otpService->createOtpSession($this->user, $action, 30);
        $this->assertTrue($this->otpService->hasValidOtpSession($this->user, $action));

        // Clear session
        $this->otpService->clearOtpSession($this->user, $action);
        $this->assertFalse($this->otpService->hasValidOtpSession($this->user, $action));
    }

    public function test_get_otp_status(): void
    {
        // Generate OTP
        $this->otpService->generateAndSendOtp($this->user, 'test_action');
        
        $status = $this->otpService->getOtpStatus($this->user);
        
        $this->assertTrue($status['has_otp_pending']);
        $this->assertNotNull($status['otp_expires_at']);
        $this->assertEquals(3, $status['remaining_attempts']);
        $this->assertFalse($status['is_locked_out']);
        $this->assertEquals(0, $status['lockout_remaining_minutes']);
        $this->assertTrue($status['two_factor_enabled']);
    }

    public function test_lockout_time_calculation(): void
    {
        // Lock out user
        Cache::put("otp_lockout:{$this->user->id}", now()->addMinutes(15), now()->addMinutes(15));
        
        $remainingTime = $this->otpService->getRemainingLockoutTime($this->user);
        
        $this->assertGreaterThan(0, $remainingTime);
        $this->assertLessThanOrEqual(15, $remainingTime);
    }

    public function test_enable_two_factor(): void
    {
        $user = User::factory()->create(['two_factor_enabled' => false]);
        
        $this->otpService->enableTwoFactor($user);
        
        $user->refresh();
        $this->assertTrue($user->two_factor_enabled);
        $this->assertNotNull($user->two_factor_confirmed_at);
    }

    public function test_disable_two_factor(): void
    {
        // Create session first using a predefined action
        $this->otpService->createOtpSession($this->user, 'user_management', 30);

        $this->otpService->disableTwoFactor($this->user);

        $this->user->refresh();
        $this->assertFalse($this->user->two_factor_enabled);
        $this->assertNull($this->user->two_factor_confirmed_at);
        $this->assertNull($this->user->current_otp_code);
        $this->assertFalse($this->otpService->hasValidOtpSession($this->user, 'user_management'));
    }
}
