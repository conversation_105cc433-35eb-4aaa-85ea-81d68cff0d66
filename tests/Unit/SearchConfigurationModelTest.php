<?php

namespace Tests\Unit;

use App\Models\SearchConfiguration;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SearchConfigurationModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear cache before each test
        Cache::flush();
    }

    public function test_can_create_search_configuration(): void
    {
        $config = SearchConfiguration::create([
            'key' => 'test_config',
            'value' => 'test_value',
            'type' => 'string',
            'description' => 'Test configuration',
            'category' => 'test',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('search_configurations', [
            'key' => 'test_config',
            'value' => json_encode('test_value'),
            'type' => 'string',
            'description' => 'Test configuration',
            'category' => 'test',
            'is_active' => true,
        ]);
    }

    public function test_get_method_returns_configuration_value(): void
    {
        SearchConfiguration::create([
            'key' => 'test_key',
            'value' => 'test_value',
            'type' => 'string',
            'is_active' => true,
        ]);

        $value = SearchConfiguration::get('test_key');
        
        $this->assertEquals('test_value', $value);
    }

    public function test_get_method_returns_default_when_key_not_found(): void
    {
        $value = SearchConfiguration::get('non_existent_key', 'default_value');
        
        $this->assertEquals('default_value', $value);
    }

    public function test_get_method_returns_null_when_no_default_provided(): void
    {
        $value = SearchConfiguration::get('non_existent_key');
        
        $this->assertNull($value);
    }

    public function test_get_method_ignores_inactive_configurations(): void
    {
        SearchConfiguration::create([
            'key' => 'inactive_key',
            'value' => 'inactive_value',
            'type' => 'string',
            'is_active' => false,
        ]);

        $value = SearchConfiguration::get('inactive_key', 'default');
        
        $this->assertEquals('default', $value);
    }

    public function test_set_method_creates_new_configuration(): void
    {
        $config = SearchConfiguration::set('new_key', 'new_value', 'string', 'New config', 'test');

        $this->assertInstanceOf(SearchConfiguration::class, $config);
        $this->assertEquals('new_key', $config->key);
        $this->assertEquals('new_value', $config->value);
        $this->assertEquals('string', $config->type);
        $this->assertEquals('New config', $config->description);
        $this->assertEquals('test', $config->category);
        $this->assertTrue($config->is_active);
    }

    public function test_set_method_updates_existing_configuration(): void
    {
        // Create initial configuration
        SearchConfiguration::create([
            'key' => 'existing_key',
            'value' => 'old_value',
            'type' => 'string',
            'is_active' => true,
        ]);

        // Update it
        SearchConfiguration::set('existing_key', 'new_value', 'string');

        $config = SearchConfiguration::where('key', 'existing_key')->first();
        $this->assertEquals('new_value', $config->value);
    }

    public function test_set_method_clears_cache(): void
    {
        // Set initial value and cache it
        SearchConfiguration::set('cache_test', 'initial_value');
        $initialValue = SearchConfiguration::get('cache_test');
        $this->assertEquals('initial_value', $initialValue);

        // Update value
        SearchConfiguration::set('cache_test', 'updated_value');
        
        // Get value again - should return updated value (cache cleared)
        $updatedValue = SearchConfiguration::get('cache_test');
        $this->assertEquals('updated_value', $updatedValue);
    }

    public function test_get_by_category_returns_configurations_by_category(): void
    {
        SearchConfiguration::create([
            'key' => 'cat1_key1',
            'value' => 'value1',
            'category' => 'category1',
            'is_active' => true,
        ]);

        SearchConfiguration::create([
            'key' => 'cat1_key2',
            'value' => 'value2',
            'category' => 'category1',
            'is_active' => true,
        ]);

        SearchConfiguration::create([
            'key' => 'cat2_key1',
            'value' => 'value3',
            'category' => 'category2',
            'is_active' => true,
        ]);

        $category1Configs = SearchConfiguration::getByCategory('category1');
        
        $this->assertCount(2, $category1Configs);
        $this->assertEquals('value1', $category1Configs['cat1_key1']);
        $this->assertEquals('value2', $category1Configs['cat1_key2']);
        $this->assertArrayNotHasKey('cat2_key1', $category1Configs);
    }

    public function test_get_by_category_ignores_inactive_configurations(): void
    {
        SearchConfiguration::create([
            'key' => 'active_key',
            'value' => 'active_value',
            'category' => 'test_category',
            'is_active' => true,
        ]);

        SearchConfiguration::create([
            'key' => 'inactive_key',
            'value' => 'inactive_value',
            'category' => 'test_category',
            'is_active' => false,
        ]);

        $configs = SearchConfiguration::getByCategory('test_category');
        
        $this->assertCount(1, $configs);
        $this->assertEquals('active_value', $configs['active_key']);
        $this->assertArrayNotHasKey('inactive_key', $configs);
    }

    public function test_clear_cache_removes_all_configuration_cache(): void
    {
        // Create configurations and cache them
        SearchConfiguration::set('key1', 'value1');
        SearchConfiguration::set('key2', 'value2');
        
        // Access them to populate cache
        SearchConfiguration::get('key1');
        SearchConfiguration::get('key2');
        SearchConfiguration::getByCategory('general');

        // Clear cache
        SearchConfiguration::clearCache();

        // Verify cache keys are cleared by checking if fresh DB queries are made
        // We can't directly test cache clearing, but we can verify behavior
        $this->assertTrue(true); // Cache clearing doesn't throw errors
    }

    public function test_caching_works_for_get_method(): void
    {
        SearchConfiguration::create([
            'key' => 'cached_key',
            'value' => 'cached_value',
            'type' => 'string',
            'is_active' => true,
        ]);

        // First call should cache the value
        $value1 = SearchConfiguration::get('cached_key');
        
        // Manually update database without using model (bypassing cache clearing)
        \DB::table('search_configurations')
            ->where('key', 'cached_key')
            ->update(['value' => json_encode('updated_value')]);

        // Second call should return cached value, not updated value
        $value2 = SearchConfiguration::get('cached_key');
        
        $this->assertEquals('cached_value', $value1);
        $this->assertEquals('cached_value', $value2); // Should still be cached value
    }

    public function test_caching_works_for_get_by_category_method(): void
    {
        SearchConfiguration::create([
            'key' => 'category_key',
            'value' => 'category_value',
            'category' => 'test_category',
            'is_active' => true,
        ]);

        // First call should cache the values
        $configs1 = SearchConfiguration::getByCategory('test_category');
        
        // Manually update database without using model
        \DB::table('search_configurations')
            ->where('key', 'category_key')
            ->update(['value' => json_encode('updated_value')]);

        // Second call should return cached values
        $configs2 = SearchConfiguration::getByCategory('test_category');
        
        $this->assertEquals('category_value', $configs1['category_key']);
        $this->assertEquals('category_value', $configs2['category_key']); // Should still be cached
    }

    public function test_initialize_defaults_creates_all_default_configurations(): void
    {
        SearchConfiguration::initializeDefaults();

        $defaults = SearchConfiguration::getDefaults();
        
        foreach ($defaults as $key => $config) {
            $this->assertDatabaseHas('search_configurations', [
                'key' => $key,
                'value' => json_encode($config['value']),
                'type' => $config['type'],
                'description' => $config['description'],
                'category' => $config['category'],
                'is_active' => true,
            ]);
        }
    }

    public function test_initialize_defaults_does_not_duplicate_existing_configurations(): void
    {
        // Create one configuration manually
        SearchConfiguration::create([
            'key' => 'guest_search_limit',
            'value' => 5,
            'type' => 'integer',
            'description' => 'Custom description',
            'category' => 'guest_limits',
            'is_active' => true,
        ]);

        SearchConfiguration::initializeDefaults();

        // Should only have one record for this key
        $count = SearchConfiguration::where('key', 'guest_search_limit')->count();
        $this->assertEquals(1, $count);

        // Should preserve existing values
        $config = SearchConfiguration::where('key', 'guest_search_limit')->first();
        $this->assertEquals(5, $config->value);
        $this->assertEquals('Custom description', $config->description);
    }

    public function test_initialize_defaults_clears_cache(): void
    {
        // Set a configuration and cache it
        SearchConfiguration::set('guest_search_limit', 10);
        $cachedValue = SearchConfiguration::get('guest_search_limit');
        $this->assertEquals(10, $cachedValue);

        // Clear the database to simulate fresh initialization
        SearchConfiguration::truncate();

        // Initialize defaults (should create default configurations and clear cache)
        SearchConfiguration::initializeDefaults();

        // Should get default value, not cached value
        $defaultValue = SearchConfiguration::get('guest_search_limit');
        $this->assertEquals(3, $defaultValue); // Default value from getDefaults()
    }

    public function test_value_casting_works_correctly(): void
    {
        $config = SearchConfiguration::create([
            'key' => 'test_array',
            'value' => ['key1' => 'value1', 'key2' => 'value2'],
            'type' => 'array',
            'is_active' => true,
        ]);

        $this->assertIsArray($config->value);
        $this->assertEquals(['key1' => 'value1', 'key2' => 'value2'], $config->value);
    }

    public function test_is_active_casting_works_correctly(): void
    {
        $config = SearchConfiguration::create([
            'key' => 'test_boolean',
            'value' => 'test',
            'is_active' => 1, // Integer 1
        ]);

        $this->assertIsBool($config->is_active);
        $this->assertTrue($config->is_active);
    }

    public function test_get_defaults_returns_expected_structure(): void
    {
        $defaults = SearchConfiguration::getDefaults();

        $this->assertIsArray($defaults);
        $this->assertArrayHasKey('guest_search_limit', $defaults);
        $this->assertArrayHasKey('enable_partial_results', $defaults);
        $this->assertArrayHasKey('track_guest_searches', $defaults);

        // Check structure of a default configuration
        $guestLimit = $defaults['guest_search_limit'];
        $this->assertArrayHasKey('value', $guestLimit);
        $this->assertArrayHasKey('type', $guestLimit);
        $this->assertArrayHasKey('description', $guestLimit);
        $this->assertArrayHasKey('category', $guestLimit);
    }
}
