<?php

namespace Tests\Unit;

use App\Http\Middleware\CheckImpersonationExpiry;
use App\Models\User;
use App\Models\UserImpersonationLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class CheckImpersonationExpiryMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    private CheckImpersonationExpiry $middleware;
    private User $adminUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->middleware = new CheckImpersonationExpiry();
        $this->adminUser = $this->createAdminUser();
        $this->regularUser = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
    }

    public function test_allows_request_when_not_impersonating(): void
    {
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_allows_request_when_impersonation_not_expired(): void
    {
        // Set up active impersonation session
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->addMinutes(10)); // Not expired
        
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now()->subMinutes(5),
            'ip_address' => '127.0.0.1',
        ]);
        Session::put('impersonation_log_id', $impersonationLog->id);
        
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_ends_expired_impersonation_session(): void
    {
        // Set up expired impersonation session
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->subMinutes(10)); // Expired
        
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now()->subMinutes(40),
            'ip_address' => '127.0.0.1',
        ]);
        Session::put('impersonation_log_id', $impersonationLog->id);
        
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        // Should redirect to admin dashboard
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContains('admin/dashboard', $response->headers->get('Location'));
        
        // Session should be cleared
        $this->assertFalse(Session::has('impersonating_user_id'));
        $this->assertFalse(Session::has('original_admin_id'));
        $this->assertFalse(Session::has('impersonation_expires_at'));
        $this->assertFalse(Session::has('impersonation_log_id'));
        
        // Impersonation log should be ended
        $this->assertNotNull($impersonationLog->fresh()->ended_at);
    }

    public function test_handles_missing_impersonation_log(): void
    {
        // Set up impersonation session without valid log
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->subMinutes(10)); // Expired
        Session::put('impersonation_log_id', 999); // Non-existent log ID
        
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        // Should still redirect and clear session
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContains('admin/dashboard', $response->headers->get('Location'));
        
        // Session should be cleared
        $this->assertFalse(Session::has('impersonating_user_id'));
        $this->assertFalse(Session::has('original_admin_id'));
        $this->assertFalse(Session::has('impersonation_expires_at'));
        $this->assertFalse(Session::has('impersonation_log_id'));
    }

    public function test_handles_malformed_expiry_date(): void
    {
        // Set up impersonation session with invalid expiry date
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', 'invalid-date');
        
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        // Should redirect due to invalid date
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContains('admin/dashboard', $response->headers->get('Location'));
        
        // Session should be cleared
        $this->assertFalse(Session::has('impersonating_user_id'));
        $this->assertFalse(Session::has('original_admin_id'));
        $this->assertFalse(Session::has('impersonation_expires_at'));
    }

    public function test_preserves_session_flash_messages(): void
    {
        // Set up expired impersonation session
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->subMinutes(10)); // Expired
        
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now()->subMinutes(40),
            'ip_address' => '127.0.0.1',
        ]);
        Session::put('impersonation_log_id', $impersonationLog->id);
        
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        // Should have warning message in session
        $this->assertTrue(Session::has('warning'));
        $this->assertStringContains('expired', Session::get('warning'));
    }

    public function test_handles_partial_impersonation_session_data(): void
    {
        // Set up partial impersonation session (missing some data)
        Session::put('impersonating_user_id', $this->regularUser->id);
        // Missing original_admin_id and expiry date
        
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        // Should allow request to continue since session is incomplete
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_middleware_only_affects_impersonation_sessions(): void
    {
        // Regular user session without impersonation
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Regular Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Regular Success', $response->getContent());
        
        // Admin user session without impersonation
        $request = Request::create('/admin/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->adminUser);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Admin Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Admin Success', $response->getContent());
    }

    public function test_logs_impersonation_end_with_correct_data(): void
    {
        // Set up expired impersonation session
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->subMinutes(10)); // Expired
        
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now()->subMinutes(40),
            'ip_address' => '127.0.0.1',
        ]);
        Session::put('impersonation_log_id', $impersonationLog->id);
        
        $request = Request::create('/dashboard', 'GET');
        $request->setUserResolver(fn() => $this->regularUser);
        
        $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        // Check that log was properly ended
        $updatedLog = $impersonationLog->fresh();
        $this->assertNotNull($updatedLog->ended_at);
        $this->assertEquals('expired', $updatedLog->end_reason);
        $this->assertTrue($updatedLog->ended_at->greaterThan($updatedLog->started_at));
    }
}
