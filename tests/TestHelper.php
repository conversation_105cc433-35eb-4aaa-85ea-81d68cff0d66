<?php

namespace Tests;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;

class TestHelper
{
    /**
     * Create a complete test setup with related models.
     */
    public static function createCompleteSetup(): array
    {
        $category = Category::factory()->create(['name' => 'Display']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $model = MobileModel::factory()->create([
            'brand_id' => $brand->id,
            'name' => 'iPhone 15 Pro',
        ]);
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'iPhone 15 Pro OLED Screen',
        ]);
        
        $part->models()->attach($model->id, [
            'compatibility_notes' => 'Original part',
            'is_verified' => true,
        ]);

        return compact('category', 'brand', 'model', 'part');
    }

    /**
     * Create a premium user with active subscription.
     */
    public static function createPremiumUser(): User
    {
        $user = User::factory()->create(['subscription_plan' => 'premium']);
        
        $user->subscriptions()->create([
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
        ]);

        return $user;
    }

    /**
     * Create a free user with search limits.
     */
    public static function createFreeUser(int $searchCount = 0): User
    {
        return User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => $searchCount,
            'daily_reset' => today(),
        ]);
    }

    /**
     * Create multiple parts for testing search functionality.
     */
    public static function createSearchTestData(): array
    {
        $categories = [
            Category::factory()->create(['name' => 'Display']),
            Category::factory()->create(['name' => 'Battery']),
            Category::factory()->create(['name' => 'Camera']),
        ];

        $brands = [
            Brand::factory()->create(['name' => 'Apple']),
            Brand::factory()->create(['name' => 'Samsung']),
            Brand::factory()->create(['name' => 'Xiaomi']),
        ];

        $models = [];
        $parts = [];

        foreach ($brands as $brand) {
            $model = MobileModel::factory()->create([
                'brand_id' => $brand->id,
                'name' => $brand->name . ' Test Model',
            ]);
            $models[] = $model;

            foreach ($categories as $category) {
                $part = Part::factory()->create([
                    'category_id' => $category->id,
                    'name' => $brand->name . ' ' . $category->name,
                ]);
                $part->models()->attach($model->id);
                $parts[] = $part;
            }
        }

        return compact('categories', 'brands', 'models', 'parts');
    }

    /**
     * Assert that a search result contains expected data.
     */
    public static function assertSearchResult(array $result, string $expectedQuery, int $expectedCount = null): void
    {
        \PHPUnit\Framework\Assert::assertArrayHasKey('results', $result);
        \PHPUnit\Framework\Assert::assertArrayHasKey('query', $result);
        \PHPUnit\Framework\Assert::assertEquals($expectedQuery, $result['query']);
        
        if ($expectedCount !== null) {
            \PHPUnit\Framework\Assert::assertEquals($expectedCount, $result['results']->total());
        }
    }

    /**
     * Assert that a user has the expected subscription status.
     */
    public static function assertUserSubscription(User $user, string $expectedPlan, string $expectedStatus = 'active'): void
    {
        $user->refresh();
        \PHPUnit\Framework\Assert::assertEquals($expectedPlan, $user->subscription_plan);
        
        if ($expectedPlan === 'premium') {
            $subscription = $user->activeSubscription;
            \PHPUnit\Framework\Assert::assertNotNull($subscription);
            \PHPUnit\Framework\Assert::assertEquals($expectedStatus, $subscription->status);
        }
    }

    /**
     * Create test data for admin functionality.
     */
    public static function createAdminTestData(): array
    {
        $parentCategory = Category::factory()->create(['name' => 'Electronics']);
        $childCategory = Category::factory()->create([
            'name' => 'Mobile Parts',
            'parent_id' => $parentCategory->id,
        ]);

        $brand = Brand::factory()->create(['name' => 'Test Brand']);
        $model = MobileModel::factory()->create([
            'brand_id' => $brand->id,
            'name' => 'Test Model',
        ]);

        $part = Part::factory()->create([
            'category_id' => $childCategory->id,
            'name' => 'Test Part',
        ]);

        return compact('parentCategory', 'childCategory', 'brand', 'model', 'part');
    }
}
