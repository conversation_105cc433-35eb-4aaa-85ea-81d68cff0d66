import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { router } from '@inertiajs/react';
import { GlobalSearchCommand } from '@/components/global-search-command';

// Mock Inertia router
jest.mock('@inertiajs/react', () => ({
    router: {
        visit: jest.fn(),
    },
}));

describe('GlobalSearchCommand', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Clear any existing event listeners
        document.removeEventListener('keydown', jest.fn());
    });

    afterEach(() => {
        // Clean up any remaining event listeners
        const events = ['keydown'];
        events.forEach(event => {
            document.removeEventListener(event, jest.fn());
        });
    });

    it('renders search command dialog when opened', () => {
        render(<GlobalSearchCommand />);

        // Dialog should not be visible initially
        expect(screen.queryByPlaceholderText('Search for pages, features, and more...')).not.toBeInTheDocument();
    });

    it('opens dialog when Ctrl+K is pressed', async () => {
        render(<GlobalSearchCommand />);

        // Simulate Ctrl+K keypress
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByPlaceholderText('Search for pages, features, and more...')).toBeInTheDocument();
        });
    });

    it('opens dialog when Cmd+K is pressed (Mac)', async () => {
        render(<GlobalSearchCommand />);

        // Simulate Cmd+K keypress
        fireEvent.keyDown(document, {
            key: 'k',
            metaKey: true,
        });

        await waitFor(() => {
            expect(screen.getByPlaceholderText('Search for pages, features, and more...')).toBeInTheDocument();
        });
    });

    it('prevents default behavior when Ctrl+K is pressed', () => {
        render(<GlobalSearchCommand />);

        const event = new KeyboardEvent('keydown', {
            key: 'k',
            ctrlKey: true,
            bubbles: true,
            cancelable: true,
        });

        const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
        
        document.dispatchEvent(event);

        expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('displays search items correctly', async () => {
        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
            expect(screen.getByText('Search Parts')).toBeInTheDocument();
            expect(screen.getByText('Profile Settings')).toBeInTheDocument();
        });
    });

    it('filters admin-only items for non-admin users', async () => {
        render(<GlobalSearchCommand isAdmin={false} />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
            expect(screen.queryByText('Admin Dashboard')).not.toBeInTheDocument();
            expect(screen.queryByText('User Management')).not.toBeInTheDocument();
        });
    });

    it('shows admin-only items for admin users', async () => {
        render(<GlobalSearchCommand isAdmin={true} />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
            expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
            expect(screen.getByText('User Management')).toBeInTheDocument();
        });
    });

    it('navigates to selected item', async () => {
        const mockVisit = jest.fn();
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        const user = userEvent.setup();
        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });

        // Click on Dashboard item
        await user.click(screen.getByText('Dashboard'));

        expect(mockVisit).toHaveBeenCalledWith('/dashboard', expect.any(Object));
    });

    it('closes dialog after navigation', async () => {
        const mockVisit = jest.fn();
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        const user = userEvent.setup();
        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });

        // Click on Dashboard item
        await user.click(screen.getByText('Dashboard'));

        // Dialog should close
        await waitFor(() => {
            expect(screen.queryByPlaceholderText('Search for pages, features, and more...')).not.toBeInTheDocument();
        });
    });

    it('handles navigation errors gracefully', async () => {
        const mockVisit = jest.fn().mockRejectedValue(new Error('Navigation failed'));
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
        const user = userEvent.setup();

        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });

        // Click on Dashboard item
        await user.click(screen.getByText('Dashboard'));

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith('Search navigation error:', expect.any(Error));
        });

        consoleSpy.mockRestore();
    });

    it('debounces rapid selections', async () => {
        const mockVisit = jest.fn();
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        jest.useFakeTimers();
        const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });

        // Rapidly click multiple items
        await user.click(screen.getByText('Dashboard'));
        await user.click(screen.getByText('Search Parts'));

        // Only the last click should be processed after debounce
        jest.advanceTimersByTime(300);

        expect(mockVisit).toHaveBeenCalledTimes(1);

        jest.useRealTimers();
    });

    it('prevents multiple simultaneous navigations', async () => {
        const mockVisit = jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        const user = userEvent.setup();
        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });

        // Click multiple items quickly
        await user.click(screen.getByText('Dashboard'));
        await user.click(screen.getByText('Search Parts'));

        // Should only call visit once due to navigation state check
        expect(mockVisit).toHaveBeenCalledTimes(1);
    });

    it('groups items by category correctly', async () => {
        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('Core')).toBeInTheDocument();
            expect(screen.getByText('Search')).toBeInTheDocument();
            expect(screen.getByText('Settings')).toBeInTheDocument();
        });
    });

    it('displays admin badge for admin-only items', async () => {
        render(<GlobalSearchCommand isAdmin={true} />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            const adminBadges = screen.getAllByText('Admin');
            expect(adminBadges.length).toBeGreaterThan(0);
        });
    });

    it('shows keyboard shortcut tip', async () => {
        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByText('⌘K')).toBeInTheDocument();
            expect(screen.getByText('to open search anytime')).toBeInTheDocument();
        });
    });

    it('handles empty search results', async () => {
        const user = userEvent.setup();
        render(<GlobalSearchCommand />);

        // Open dialog
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByPlaceholderText('Search for pages, features, and more...')).toBeInTheDocument();
        });

        // Type a search query that won't match anything
        const searchInput = screen.getByPlaceholderText('Search for pages, features, and more...');
        await user.type(searchInput, 'nonexistentitem');

        await waitFor(() => {
            expect(screen.getByText('No results found.')).toBeInTheDocument();
        });
    });

    it('cleans up event listeners on unmount', () => {
        const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
        
        const { unmount } = render(<GlobalSearchCommand />);
        
        unmount();

        expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function), { capture: true });
    });

    it('handles singleton pattern correctly', () => {
        // Render multiple instances
        const { unmount: unmount1 } = render(<GlobalSearchCommand />);
        const { unmount: unmount2 } = render(<GlobalSearchCommand />);

        // Both should respond to keyboard events
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        // Should only have one dialog open
        const dialogs = screen.getAllByRole('dialog');
        expect(dialogs).toHaveLength(1);

        unmount1();
        unmount2();
    });

    it('toggles dialog state correctly', async () => {
        render(<GlobalSearchCommand />);

        // First Ctrl+K should open
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.getByPlaceholderText('Search for pages, features, and more...')).toBeInTheDocument();
        });

        // Second Ctrl+K should close
        fireEvent.keyDown(document, {
            key: 'k',
            ctrlKey: true,
        });

        await waitFor(() => {
            expect(screen.queryByPlaceholderText('Search for pages, features, and more...')).not.toBeInTheDocument();
        });
    });
});
