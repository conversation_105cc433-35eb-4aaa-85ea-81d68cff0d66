import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { router } from '@inertiajs/react';
import { UnifiedSearchInterface } from '@/components/unified-search-interface';

// Mock Inertia router
jest.mock('@inertiajs/react', () => ({
    router: {
        visit: jest.fn(),
    },
}));

// Mock fetch for suggestions
global.fetch = jest.fn();

describe('UnifiedSearchInterface', () => {
    const defaultProps = {
        searchQuery: '',
        setSearchQuery: jest.fn(),
        isAuthenticated: true,
        isLoading: false,
        setIsLoading: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (fetch as jest.Mock).mockClear();
    });

    it('renders search interface correctly', () => {
        render(<UnifiedSearchInterface {...defaultProps} />);

        expect(screen.getByPlaceholderText('Search for parts, models, or brands...')).toBeInTheDocument();
        expect(screen.getByRole('combobox')).toBeInTheDocument(); // Search type selector
        expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
    });

    it('handles search query input correctly', async () => {
        const setSearchQuery = jest.fn();
        const user = userEvent.setup();

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                setSearchQuery={setSearchQuery}
            />
        );

        const searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        await user.type(searchInput, 'iPhone');

        expect(setSearchQuery).toHaveBeenCalledWith('iPhone');
    });

    it('submits search for authenticated users', async () => {
        const user = userEvent.setup();
        const mockVisit = jest.fn();
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                isAuthenticated={true}
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).toHaveBeenCalledWith(
            '/search/results?q=iPhone&type=all',
            expect.any(Object)
        );
    });

    it('submits search for guest users with device ID', async () => {
        const user = userEvent.setup();
        const mockVisit = jest.fn();
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                isAuthenticated={false}
                deviceId="test-device-123"
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).toHaveBeenCalledWith(
            '/guest/search?q=iPhone&type=all&device_id=test-device-123',
            expect.any(Object)
        );
    });

    it('prevents search when query is empty', async () => {
        const user = userEvent.setup();
        const mockVisit = jest.fn();
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery=""
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).not.toHaveBeenCalled();
    });

    it('shows loading state correctly', () => {
        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                isLoading={true}
                searchQuery="iPhone"
            />
        );

        expect(screen.getByText('Searching...')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /searching/i })).toBeDisabled();
    });

    it('disables search when guest user has exceeded limit', () => {
        const searchStatus = {
            has_searched: true,
            can_search: false,
            message: 'Search limit exceeded',
        };

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                isAuthenticated={false}
                searchStatus={searchStatus}
                searchQuery="iPhone"
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        expect(searchButton).toBeDisabled();
    });

    it('shows search limit modal for guest users', async () => {
        const user = userEvent.setup();
        const searchStatus = {
            has_searched: true,
            can_search: false,
            message: 'You have reached your free search limit.',
        };

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                isAuthenticated={false}
                searchStatus={searchStatus}
                searchQuery="iPhone"
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(screen.getByText('Search Limit Reached')).toBeInTheDocument();
        expect(screen.getByText('You have reached your free search limit.')).toBeInTheDocument();
    });

    it('fetches and displays search suggestions', async () => {
        const mockSuggestions = [
            { value: 'iPhone 14', type: 'model' },
            { value: 'iPhone Display', type: 'part' },
        ];

        (fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => mockSuggestions,
        });

        const user = userEvent.setup();

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                showSuggestions={true}
            />
        );

        const searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        await user.type(searchInput, 'iPhone');

        await waitFor(() => {
            expect(fetch).toHaveBeenCalledWith('/api/search/suggestions?q=iPhone');
        });

        await waitFor(() => {
            expect(screen.getByText('iPhone 14')).toBeInTheDocument();
            expect(screen.getByText('iPhone Display')).toBeInTheDocument();
        });
    });

    it('handles suggestion selection correctly', async () => {
        const setSearchQuery = jest.fn();
        const mockSuggestions = [
            { value: 'iPhone 14', type: 'model' },
        ];

        (fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => mockSuggestions,
        });

        const user = userEvent.setup();

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                setSearchQuery={setSearchQuery}
                showSuggestions={true}
            />
        );

        const searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        await user.type(searchInput, 'iPhone');

        await waitFor(() => {
            expect(screen.getByText('iPhone 14')).toBeInTheDocument();
        });

        await user.click(screen.getByText('iPhone 14'));

        expect(setSearchQuery).toHaveBeenCalledWith('iPhone 14');
    });

    it('renders filters when enabled', () => {
        const filters = {
            categories: [
                { id: 1, name: 'Display' },
                { id: 2, name: 'Battery' },
            ],
            brands: [
                { id: 1, name: 'Apple' },
                { id: 2, name: 'Samsung' },
            ],
        };

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                showFilters={true}
                filters={filters}
            />
        );

        expect(screen.getByText('All Categories')).toBeInTheDocument();
        expect(screen.getByText('All Brands')).toBeInTheDocument();
    });

    it('includes filters in search parameters', async () => {
        const user = userEvent.setup();
        const mockVisit = jest.fn();
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        const filters = {
            categories: [{ id: 1, name: 'Display' }],
            brands: [{ id: 1, name: 'Apple' }],
        };

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                showFilters={true}
                filters={filters}
            />
        );

        // Select a category filter
        const categorySelect = screen.getByDisplayValue('All Categories');
        await user.click(categorySelect);
        await user.click(screen.getByText('Display'));

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).toHaveBeenCalledWith(
            '/search/results?q=iPhone&type=all&category_id=1',
            expect.any(Object)
        );
    });

    it('handles different search types correctly', async () => {
        const user = userEvent.setup();
        const mockVisit = jest.fn();
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
            />
        );

        // Change search type to 'model'
        const typeSelect = screen.getByDisplayValue('All');
        await user.click(typeSelect);
        await user.click(screen.getByText('Models'));

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).toHaveBeenCalledWith(
            '/search/results?q=iPhone&type=model',
            expect.any(Object)
        );
    });

    it('handles search timeout correctly', async () => {
        const setIsLoading = jest.fn();
        jest.useFakeTimers();

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                setIsLoading={setIsLoading}
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        fireEvent.click(searchButton);

        // Fast-forward time to trigger timeout
        jest.advanceTimersByTime(30000);

        expect(setIsLoading).toHaveBeenCalledWith(false);

        jest.useRealTimers();
    });

    it('cleans up timeout on unmount', () => {
        const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
        
        const { unmount } = render(
            <UnifiedSearchInterface {...defaultProps} />
        );

        unmount();

        expect(clearTimeoutSpy).toHaveBeenCalled();
    });

    it('adapts size classes correctly', () => {
        const { rerender } = render(
            <UnifiedSearchInterface
                {...defaultProps}
                size="sm"
            />
        );

        let searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        expect(searchInput).toHaveClass('h-10');

        rerender(
            <UnifiedSearchInterface
                {...defaultProps}
                size="lg"
            />
        );

        searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        expect(searchInput).toHaveClass('h-12');
    });

    it('handles navigation errors gracefully', async () => {
        const user = userEvent.setup();
        const mockVisit = jest.fn().mockRejectedValue(new Error('Navigation failed'));
        (router.visit as jest.Mock).mockImplementation(mockVisit);

        const setIsLoading = jest.fn();
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                setIsLoading={setIsLoading}
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith('Search error:', expect.any(Error));
            expect(setIsLoading).toHaveBeenCalledWith(false);
        });

        consoleSpy.mockRestore();
    });
});
