import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import CategorySearch from '../../resources/js/pages/search/category-search';
import BrandSearch from '../../resources/js/pages/search/brand-search';

// Mock the exact API responses we're seeing in the real application
const mockSuccessfulResponse = {
    data: [
        {
            id: 10,
            name: 'Samsung Galaxy S24 Ultra Display',
            slug: 'samsung-galaxy-s24-ultra-display',
            part_number: 'jk080-9901',
            manufacturer: 'Samsung',
            description: 'High-quality display for Samsung Galaxy S24 Ultra',
            images: ['image1.jpg'],
            category: { id: 1, name: 'Display' },
            models: [{ id: 1, brand: { id: 1, name: 'Samsung' }, name: 'Galaxy S24 Ultra' }],
        },
    ],
    current_page: 1,
    last_page: 1,
    per_page: 20,
    total: 1,
    from: 1,
    to: 1,
};

const mockFailedResponse = {
    data: [],
    current_page: 1,
    last_page: 1,
    per_page: 20,
    total: 0,
    from: 0,
    to: 0,
};

const mockMultiPageResponse = {
    data: [
        {
            id: 10,
            name: 'Samsung Galaxy S24 Ultra Display',
            slug: 'samsung-galaxy-s24-ultra-display',
            part_number: 'jk080-9901',
            manufacturer: 'Samsung',
            description: 'High-quality display for Samsung Galaxy S24 Ultra',
            images: ['image1.jpg'],
            category: { id: 1, name: 'Display' },
            models: [{ id: 1, brand: { id: 1, name: 'Samsung' }, name: 'Galaxy S24 Ultra' }],
        },
    ],
    current_page: 1,
    last_page: 3,
    per_page: 20,
    total: 50,
    from: 1,
    to: 20,
};

const mockCategory = {
    id: 1,
    name: 'Display',
    slug: 'display',
    description: 'Display components and screens',
};

const mockFilters = {
    categories: [],
    brands: [],
    manufacturers: [],
    release_years: [],
};

describe('Search Flow Integration', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        
        // Reset fetch mock
        global.fetch = vi.fn().mockResolvedValue({
            json: () => Promise.resolve([]),
        });
    });

    it('handles the exact search sequence that fails in production', async () => {
        const user = userEvent.setup();

        // Start with no search performed
        const { rerender } = render(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                search_type="all"
                query=""
                remaining_searches={10}
            />
        );

        // Simulate first search: jk080-9901 (this works)
        rerender(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockSuccessfulResponse}
                search_type="all"
                query="jk080-9901"
                remaining_searches={9}
            />
        );

        // Verify first search shows results
        expect(screen.getByText('1 display parts found for "jk080-9901"')).toBeInTheDocument();
        expect(screen.getByText('Samsung Galaxy S24 Ultra Display')).toBeInTheDocument();

        // Simulate second search: gf954-7572 (this fails)
        rerender(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockFailedResponse}
                search_type="all"
                query="gf954-7572"
                remaining_searches={8}
            />
        );

        // This should show no results (the bug)
        expect(screen.getByText('No display parts found')).toBeInTheDocument();

        // But let's check if the component state is correct
        const searchInput = screen.getByPlaceholderText(/search for display parts/i);
        expect(searchInput).toHaveValue('gf954-7572');

        // The search type should still be 'all'
        const searchTypeSelect = screen.getByDisplayValue('All');
        expect(searchTypeSelect).toBeInTheDocument();
    });

    it('verifies search parameters are constructed correctly', async () => {
        const user = userEvent.setup();

        render(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                search_type="all"
                query=""
                remaining_searches={10}
            />
        );

        const searchInput = screen.getByPlaceholderText(/search for display parts/i);
        const searchButton = screen.getByRole('button', { name: /^search$/i });

        // Test first search
        await user.type(searchInput, 'jk080-9901');
        await user.click(searchButton);

        // Verify the router was called with correct parameters
        expect(global.mockRouter.get).toHaveBeenCalledWith(
            '/search/category/display?q=jk080-9901&type=all',
            {},
            expect.objectContaining({
                onError: expect.any(Function),
                onFinish: expect.any(Function),
            })
        );

        // Clear and test second search
        await user.clear(searchInput);
        await user.type(searchInput, 'gf954-7572');
        await user.click(searchButton);

        // Verify the second search also uses correct parameters
        expect(global.mockRouter.get).toHaveBeenLastCalledWith(
            '/search/category/display?q=gf954-7572&type=all',
            {},
            expect.objectContaining({
                onError: expect.any(Function),
                onFinish: expect.any(Function),
            })
        );
    });

    it('tests search type consistency across multiple searches', () => {
        // Test that search type doesn't change unexpectedly
        const { rerender } = render(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                search_type="all"
                query=""
                remaining_searches={10}
            />
        );

        // First search with 'all' type
        rerender(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockSuccessfulResponse}
                search_type="all"
                query="jk080-9901"
                remaining_searches={9}
            />
        );

        let searchTypeSelect = screen.getByDisplayValue('All');
        expect(searchTypeSelect).toBeInTheDocument();

        // Second search should still have 'all' type
        rerender(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockFailedResponse}
                search_type="all"
                query="gf954-7572"
                remaining_searches={8}
            />
        );

        searchTypeSelect = screen.getByDisplayValue('All');
        expect(searchTypeSelect).toBeInTheDocument();
    });

    it('tests results data structure handling', () => {
        // Test with results that have data
        const { rerender } = render(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockSuccessfulResponse}
                search_type="all"
                query="jk080-9901"
                remaining_searches={9}
            />
        );

        // Should show results
        expect(screen.getByText('Search Results')).toBeInTheDocument();
        expect(screen.queryByText('No display parts found')).not.toBeInTheDocument();

        // Test with empty results
        rerender(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockFailedResponse}
                search_type="all"
                query="gf954-7572"
                remaining_searches={8}
            />
        );

        // Should show no results message
        expect(screen.getByText('No display parts found')).toBeInTheDocument();
    });

    it('filters out null and invalid filter values from all URL construction methods', async () => {
        // Test that our fix works for all the different ways URLs are constructed:
        // 1. Main search form
        // 2. Suggestion clicks
        // 3. Page changes
        // 4. Filter changes

        // Mock window.location.search with polluted parameters
        Object.defineProperty(window, 'location', {
            value: {
                pathname: '/search/category/display',
                search: '?q=test&type=all&brand_id=null&manufacturer=null&release_year=null',
                href: 'http://localhost/search/category/display?q=test&type=all&brand_id=null&manufacturer=null&release_year=null',
            },
            writable: true,
        });

        const { rerender } = render(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockMultiPageResponse}
                search_type="all"
                query="test"
                remaining_searches={10}
            />
        );

        // Test that page changes filter out invalid parameters
        const nextButton = screen.getByRole('button', { name: /next/i });
        await userEvent.click(nextButton);

        // Verify that the router call doesn't include null parameters
        const lastCall = global.mockRouter.get.mock.calls[global.mockRouter.get.mock.calls.length - 1];
        const url = lastCall[0];

        expect(url).not.toContain('brand_id=null');
        expect(url).not.toContain('manufacturer=null');
        expect(url).not.toContain('release_year=null');
        expect(url).toContain('page=2'); // Should still include the new page parameter
        expect(url).toContain('q=test'); // Should preserve valid parameters
        // Note: type=all is filtered out by the frontend logic as it's considered a default value
    });

    it('applies the same parameter cleaning fix to brand search', async () => {
        // This test verifies that brand search also filters out invalid parameters
        // The actual backend parameter cleaning is tested through integration

        const mockBrand = {
            id: 1,
            name: 'Apple',
            slug: 'apple'
        };

        const mockAppliedFilters = {
            category_id: null,
            manufacturer: 'null',
            release_year: undefined,
        };

        render(
            <BrandSearch
                brand={mockBrand}
                filters={mockFilters}
                results={mockSuccessfulResponse}
                applied_filters={mockAppliedFilters}
                search_type="all"
                query=""
                remaining_searches={10}
            />
        );

        const searchInput = screen.getByPlaceholderText(/search for apple parts/i);
        const searchButton = screen.getByRole('button', { name: /^search$/i });

        await userEvent.type(searchInput, 'test-query');
        await userEvent.click(searchButton);

        // Verify that null values are filtered out from brand search as well
        expect(global.mockRouter.get).toHaveBeenCalledWith(
            expect.not.stringContaining('category_id=null'),
            {},
            expect.any(Object)
        );
        expect(global.mockRouter.get).toHaveBeenCalledWith(
            expect.not.stringContaining('manufacturer=null'),
            {},
            expect.any(Object)
        );
        expect(global.mockRouter.get).toHaveBeenCalledWith(
            expect.not.stringContaining('release_year='),
            {},
            expect.any(Object)
        );
    });
});
