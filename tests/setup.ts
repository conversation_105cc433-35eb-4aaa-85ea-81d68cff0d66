import '@testing-library/jest-dom';
import { vi, beforeEach } from 'vitest';
import React from 'react';

// Mock Inertia.js
global.route = vi.fn((name: string, params?: Record<string, string | number>) => {
    const routes: Record<string, string> = {
        'search.category': '/search/category/:category',
        'search.brand': '/search/brand/:brand',
        'search.suggestions': '/search/suggestions',
        'parts.show': '/parts/:part',
    };
    
    let url = routes[name] || `/${name}`;
    
    if (params) {
        if (typeof params === 'object') {
            Object.keys(params).forEach(key => {
                url = url.replace(`:${key}`, String(params[key]));
            });
        } else {
            url = url.replace(/:[^/]+/, String(params));
        }
    }
    
    return url;
});

// Mock Inertia router
const mockRouter = {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
    visit: vi.fn(),
    reload: vi.fn(),
    remember: vi.fn(),
    restore: vi.fn(),
};

vi.mock('@inertiajs/react', () => ({
    router: mockRouter,
    usePage: vi.fn(() => ({
        props: {
            auth: {
                user: {
                    id: 1,
                    name: 'Test User',
                    email: '<EMAIL>',
                },
            },
        },
        url: '/',
        component: 'TestComponent',
    })),
    Head: ({ children }: { children: React.ReactNode }) => children,
    Link: ({ children, href, prefetch, ...props }: { children: React.ReactNode; href: string; prefetch?: boolean; [key: string]: unknown }) => {
        // Convert boolean prefetch to string to avoid React warning
        const linkProps: any = { ...props, href };
        if (prefetch !== undefined) {
            linkProps.prefetch = prefetch.toString();
        }
        return React.createElement('a', linkProps, children);
    },
}));

// Make router available globally for tests
global.mockRouter = mockRouter;

// Mock NotificationBell component to avoid API call issues in tests
vi.mock('../resources/js/components/user/NotificationBell', () => ({
    default: ({ className }: { className?: string }) => {
        return React.createElement('div', {
            className: `notification-bell-mock ${className || ''}`,
            'data-testid': 'notification-bell'
        }, 'Notifications');
    }
}));

// Mock ImpersonationBanner component to avoid API call issues in tests
vi.mock('../resources/js/components/admin/ImpersonationBanner', () => ({
    default: () => {
        return React.createElement('div', {
            className: 'impersonation-banner-mock',
            'data-testid': 'impersonation-banner'
        }, 'Impersonation Banner');
    }
}));

// Mock window.location
Object.defineProperty(window, 'location', {
    value: {
        pathname: '/',
        search: '',
        hash: '',
        href: 'http://localhost/',
    },
    writable: true,
});

// Mock fetch for API calls with proper responses
global.fetch = vi.fn((input: RequestInfo | URL) => {
    const url = typeof input === 'string' ? input : input.toString();

    // Mock notification endpoints
    if (url.includes('notifications/unread-count')) {
        return Promise.resolve({
            json: () => Promise.resolve({ count: 0 })
        } as Response);
    }
    if (url.includes('notifications/recent')) {
        return Promise.resolve({
            json: () => Promise.resolve({ notifications: [] })
        } as Response);
    }
    // Mock search suggestions
    if (url.includes('search/suggestions')) {
        return Promise.resolve({
            json: () => Promise.resolve([])
        } as Response);
    }
    // Default mock
    return Promise.resolve({
        json: () => Promise.resolve([])
    } as Response);
});

// Reset all mocks before each test
beforeEach(() => {
    vi.clearAllMocks();

    // Reset fetch mock to default behavior
    global.fetch = vi.fn((input: RequestInfo | URL) => {
        const url = typeof input === 'string' ? input : input.toString();

        if (url.includes('notifications/unread-count')) {
            return Promise.resolve({
                json: () => Promise.resolve({ count: 0 })
            } as Response);
        }
        if (url.includes('notifications/recent')) {
            return Promise.resolve({
                json: () => Promise.resolve({ notifications: [] })
            } as Response);
        }
        if (url.includes('search/suggestions')) {
            return Promise.resolve({
                json: () => Promise.resolve([])
            } as Response);
        }
        return Promise.resolve({
            json: () => Promise.resolve([])
        } as Response);
    });
});
