<?php

namespace App\Events;

use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ModelAccessed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public MobileModel $model,
        public User $user
    ) {}
}
