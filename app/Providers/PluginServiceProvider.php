<?php

namespace App\Providers;

use App\Services\PluginManager;
use Illuminate\Support\ServiceProvider;

class PluginServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the plugin manager as a singleton
        $this->app->singleton(PluginManager::class, function ($app) {
            return new PluginManager();
        });

        // Register plugin configurations
        $this->registerPluginConfigurations();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Skip plugin loading if disabled
        if (!config('plugins.enabled', true)) {
            return;
        }

        try {
            $pluginManager = $this->app->make(PluginManager::class);

            // Load plugins from configuration
            $pluginManager->loadFromConfig();

            // Auto-discover plugins if enabled
            if (config('plugins.auto_discovery', true)) {
                $discovered = $pluginManager->discoverPlugins(config('plugins.directory'));

                foreach ($discovered as $name => $config) {
                    if (!$pluginManager->hasPlugin($name)) {
                        $pluginManager->register($name, $config['service_provider'], $config);
                    }
                }
            }

            // Boot enabled plugins
            $this->bootEnabledPlugins($pluginManager);
        } catch (\Exception $e) {
            // Log plugin system errors but don't break the application
            logger()->error('Plugin system error: ' . $e->getMessage());
        }
    }

    /**
     * Register plugin configurations.
     */
    protected function registerPluginConfigurations(): void
    {
        // Plugin registration is now handled by the PluginManager
        // to avoid double registration issues
    }

    /**
     * Boot enabled plugins.
     */
    protected function bootEnabledPlugins(PluginManager $pluginManager): void
    {
        $enabledPlugins = $pluginManager->getEnabledPlugins();

        foreach ($enabledPlugins as $plugin) {
            try {
                // Check dependencies
                $missingDependencies = $pluginManager->checkDependencies($plugin['name']);

                if (!empty($missingDependencies)) {
                    logger()->warning("Plugin {$plugin['name']} has missing dependencies: " . implode(', ', $missingDependencies));
                    continue;
                }

                // Register and boot the plugin service provider
                if (isset($plugin['service_provider']) && class_exists($plugin['service_provider'])) {
                    $this->app->register($plugin['service_provider']);
                }
            } catch (\Exception $e) {
                logger()->error("Failed to boot plugin {$plugin['name']}: " . $e->getMessage());
            }
        }
    }
}
