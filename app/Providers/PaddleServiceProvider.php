<?php

namespace App\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Paddle\SDK\Client;
use Paddle\SDK\Environment;
use Paddle\SDK\Options;

class PaddleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(Client::class, function ($app) {
            $config = config('paddle');

            // Check if Paddle is properly configured with real credentials
            if (!$this->hasValidCredentials($config)) {
                if (config('app.debug')) {
                    Log::info('Paddle SDK not initialized - using placeholder credentials or missing configuration', [
                        'environment' => $config['environment'] ?? 'unknown',
                        'api_key_present' => !empty($config['api_key']),
                        'api_key_is_placeholder' => $this->isPlaceholderValue($config['api_key'] ?? ''),
                        'client_token_present' => !empty($config['client_token']),
                        'client_token_is_placeholder' => $this->isPlaceholderValue($config['client_token'] ?? ''),
                    ]);
                }
                return null;
            }

            try {
                // Determine environment
                $environment = $config['environment'] === 'production'
                    ? Environment::PRODUCTION
                    : Environment::SANDBOX;

                // Create options with environment
                $options = new Options($environment);

                // Configure logging if enabled
                if ($config['logging']['enabled'] ?? false) {
                    // You can add custom logging configuration here if needed
                }

                // Configure retry settings
                if (isset($config['retry'])) {
                    // Configure retry settings if the SDK supports it
                }

                // Configure timeout settings
                if (isset($config['timeout'])) {
                    // Configure timeout settings if the SDK supports it
                }

                $client = new Client($config['api_key'], $options);

                if (config('app.debug')) {
                    Log::info('Paddle SDK initialized successfully', [
                        'environment' => $config['environment'],
                        'api_key_length' => strlen($config['api_key']),
                    ]);
                }

                return $client;
            } catch (\Exception $e) {
                // Log the error but don't fail the application
                Log::error('Failed to initialize Paddle SDK', [
                    'error' => $e->getMessage(),
                    'environment' => $config['environment'] ?? 'unknown',
                    'api_key_length' => strlen($config['api_key'] ?? ''),
                ]);
                return null;
            }
        });

        // Register the Paddle client as an alias
        $this->app->alias(Client::class, 'paddle');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/paddle.php' => config_path('paddle.php'),
        ], 'paddle-config');

        // Validate configuration
        $this->validateConfiguration();
    }

    /**
     * Check if Paddle has valid credentials (not placeholders).
     */
    protected function hasValidCredentials(array $config): bool
    {
        // Check if API key is present and not a placeholder
        if (empty($config['api_key']) || $this->isPlaceholderValue($config['api_key'])) {
            return false;
        }

        // Check if client token is present and not a placeholder
        if (empty($config['client_token']) || $this->isPlaceholderValue($config['client_token'])) {
            return false;
        }

        return true;
    }

    /**
     * Check if a value is a placeholder (test/demo value).
     */
    protected function isPlaceholderValue(string $value): bool
    {
        // Empty values are considered placeholders
        if (empty($value)) {
            return true;
        }

        $lowerValue = strtolower($value);

        // Check for obvious placeholder patterns (but not real Paddle test tokens)
        $placeholderPatterns = [
            'placeholder',
            'demo_',
            'example_',
            'your_',
            'replace_',
            'test_key',
            'test_token',
            'test_secret',
        ];

        foreach ($placeholderPatterns as $pattern) {
            if (str_contains($lowerValue, $pattern)) {
                return true;
            }
        }

        // Check for generic test_ patterns that are too short or simple (but allow real Paddle tokens)
        if (preg_match('/^test_[a-z0-9]{1,10}$/i', $value)) {
            return true;
        }

        // Check for very short values that are likely placeholders
        if (strlen($value) < 10) {
            return true;
        }

        return false;
    }

    /**
     * Validate Paddle configuration.
     */
    protected function validateConfiguration(): void
    {
        if ($this->app->environment('production')) {
            $requiredKeys = ['api_key', 'client_token', 'webhook_secret'];

            foreach ($requiredKeys as $key) {
                $value = config("paddle.{$key}");
                if (empty($value)) {
                    throw new \InvalidArgumentException(
                        "Paddle configuration key '{$key}' is required in production environment."
                    );
                }

                if ($this->isPlaceholderValue($value)) {
                    throw new \InvalidArgumentException(
                        "Paddle configuration key '{$key}' cannot be a placeholder value in production environment."
                    );
                }
            }

            if (config('paddle.environment') !== 'production') {
                throw new \InvalidArgumentException(
                    'Paddle environment must be set to "production" in production environment.'
                );
            }
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [Client::class, 'paddle'];
    }
}
