<?php

namespace App\Http\Controllers;

use App\Services\GuestSearchService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GuestSearchController extends Controller
{
    public function __construct(
        private GuestSearchService $guestSearchService
    ) {
        //
    }

    /**
     * Perform a guest search.
     */
    public function search(Request $request)
    {
        try {
            $request->validate([
                'q' => 'required|string|min:2|max:100',
                'type' => 'sometimes|string|in:all,category,model,part_name',
                'device_id' => 'required|string|min:10|max:100',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'error' => 'Validation failed',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e;
        }

        $results = $this->guestSearchService->searchParts($request);

        if (isset($results['error'])) {
            return response()->json($results, $results['limit_reached'] ?? false ? 429 : 400);
        }

        if ($request->wantsJson() || $request->expectsJson()) {
            return response()->json($results);
        }

        return Inertia::render('search/guest-results', $results);
    }

    /**
     * Get search status for a device.
     */
    public function status(Request $request)
    {
        $request->validate([
            'device_id' => 'required|string|min:10|max:100',
        ]);

        $status = $this->guestSearchService->getSearchStatus($request->device_id);

        return response()->json($status);
    }

    /**
     * Get available filters for guest users.
     */
    public function filters()
    {
        $filters = $this->guestSearchService->getAvailableFilters();

        return response()->json($filters);
    }
}
