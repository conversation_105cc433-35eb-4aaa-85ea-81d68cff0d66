<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\CoinbaseCommerceService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;
use Inertia\Response;

class CoinbaseCommerceConfigController extends Controller
{
    public function __construct(
        private CoinbaseCommerceService $coinbaseCommerceService
    ) {}

    /**
     * Show the Coinbase Commerce configuration form.
     */
    public function configure(): Response
    {
        $config = [
            'is_enabled' => config('coinbase_commerce.api_key') ? true : false,
            'api_key' => config('coinbase_commerce.api_key', ''),
            'webhook_secret' => config('coinbase_commerce.webhook_secret', ''),
            'debug_mode' => config('coinbase_commerce.debug', false),
            'auto_activate_subscriptions' => config('coinbase_commerce.auto_activate_subscriptions', true),
            'supported_currencies' => array_keys(config('coinbase_commerce.supported_currencies', [])),
            'webhook_url' => url('/webhooks/coinbase-commerce'),
        ];

        $isConfigured = $this->coinbaseCommerceService->isConfigured();

        return Inertia::render('admin/payment-gateways/coinbase/Configure', [
            'config' => $config,
            'isConfigured' => $isConfigured,
            'supportedCurrencies' => config('coinbase_commerce.supported_currencies', []),
        ]);
    }

    /**
     * Store the Coinbase Commerce configuration.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'is_enabled' => 'required|boolean',
            'api_key' => 'required|string|max:255',
            'webhook_secret' => 'nullable|string|max:255',
            'debug_mode' => 'boolean',
            'auto_activate_subscriptions' => 'boolean',
            'supported_currencies' => 'required|array|min:1',
            'supported_currencies.*' => 'string|in:BTC,ETH,LTC,BCH,USDC,DAI',
        ]);

        try {
            // Update environment file
            $this->updateEnvironmentFile([
                'COINBASE_COMMERCE_API_KEY' => $request->api_key,
                'COINBASE_COMMERCE_WEBHOOK_SECRET' => $request->webhook_secret ?? '',
                'COINBASE_COMMERCE_DEBUG' => $request->debug_mode ? 'true' : 'false',
                'COINBASE_COMMERCE_AUTO_ACTIVATE' => $request->auto_activate_subscriptions ? 'true' : 'false',
            ]);

            // Test the configuration if enabled
            if ($request->is_enabled) {
                $testResult = $this->testConfigurationWithCredentials($request->api_key);

                if ($testResult['success']) {
                    Log::info('Coinbase Commerce configuration updated successfully', [
                        'admin_user_id' => $request->user()->id,
                        'api_key_length' => strlen($request->api_key),
                        'supported_currencies' => $request->supported_currencies,
                    ]);

                    return redirect()->back()->with('success', 'Coinbase Commerce configuration saved and tested successfully!');
                } else {
                    return redirect()->back()->with('warning',
                        'Configuration saved but connection test failed: ' . $testResult['message']
                    );
                }
            } else {
                Log::info('Coinbase Commerce configuration updated (disabled)', [
                    'admin_user_id' => $request->user()->id,
                ]);

                return redirect()->back()->with('success', 'Coinbase Commerce configuration saved successfully!');
            }

        } catch (\Exception $e) {
            Log::error('Failed to update Coinbase Commerce configuration', [
                'error' => $e->getMessage(),
                'admin_user_id' => $request->user()->id,
            ]);

            return redirect()->back()->with('error', 'Failed to save configuration: ' . $e->getMessage());
        }
    }

    /**
     * Test Coinbase Commerce connection.
     */
    public function test(Request $request): JsonResponse
    {
        $request->validate([
            'api_key' => 'required|string',
        ]);

        try {
            $result = $this->testConfigurationWithCredentials($request->api_key);
            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Exception testing Coinbase Commerce connection', [
                'error' => $e->getMessage(),
                'admin_user_id' => $request->user()->id ?? null,
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Connection test failed',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Test configuration with provided credentials.
     */
    private function testConfigurationWithCredentials(string $apiKey): array
    {
        try {
            // Create a test charge to verify API connection
            $testData = [
                'name' => 'Connection Test',
                'description' => 'Test charge to verify API connection',
                'pricing_type' => 'fixed_price',
                'local_price' => [
                    'amount' => '1.00',
                    'currency' => 'USD'
                ],
                'metadata' => [
                    'test' => true,
                    'timestamp' => now()->toISOString()
                ]
            ];

            $response = Http::withHeaders([
                'X-CC-Api-Key' => $apiKey,
                'Content-Type' => 'application/json',
                'X-CC-Version' => '2018-03-22'
            ])->post(config('coinbase_commerce.base_url') . '/charges', $testData);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info('Coinbase Commerce connection test successful', [
                    'api_key_length' => strlen($apiKey),
                    'response_status' => $response->status(),
                    'charge_id' => $responseData['data']['id'] ?? null
                ]);

                return [
                    'success' => true,
                    'message' => 'Connection successful! API key is valid.',
                    'charge_id' => $responseData['data']['id'] ?? null
                ];
            } else {
                $errorData = $response->json();

                Log::warning('Coinbase Commerce connection test failed', [
                    'api_key_length' => strlen($apiKey),
                    'response_status' => $response->status(),
                    'error_response' => $errorData
                ]);

                return [
                    'success' => false,
                    'message' => 'API connection failed: ' . ($errorData['error']['message'] ?? 'Unknown error'),
                    'details' => $errorData
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during Coinbase Commerce connection test', [
                'api_key_length' => strlen($apiKey),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update environment file with new values.
     */
    private function updateEnvironmentFile(array $values): void
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        foreach ($values as $key => $value) {
            $pattern = "/^{$key}=.*$/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envFile, $envContent);

        // Clear config cache to reload new values
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    }
}
