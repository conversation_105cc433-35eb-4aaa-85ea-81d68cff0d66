<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class BrandController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Brand::withCount('models');

        // Apply search
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('country', 'LIKE', "%{$search}%")
                  ->orWhere('website', 'LIKE', "%{$search}%");
            });
        }

        // Apply country filter
        if ($country = $request->get('country')) {
            $query->where('country', $country);
        }

        // Apply status filter
        if ($status = $request->get('status')) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        if (in_array($sortBy, ['name', 'country', 'created_at', 'updated_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('name', 'asc');
        }

        $brands = $query->paginate(15)->withQueryString();

        // Get filter options
        $countries = Brand::select('country')
            ->whereNotNull('country')
            ->distinct()
            ->orderBy('country')
            ->pluck('country');

        return Inertia::render('admin/Brands/Index', [
            'brands' => $brands,
            'filters' => [
                'countries' => $countries,
            ],
            'queryParams' => $request->only(['search', 'country', 'status', 'sort_by', 'sort_order', 'view']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('admin/Brands/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:brands',
            'logo_url' => 'nullable|url',
            'country' => 'nullable|string|max:255',
            'website' => 'nullable|url',
            'is_active' => 'boolean',
        ]);

        Brand::create($validated);

        return redirect()->route('admin.brands.index')
            ->with('success', 'Brand created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Brand $brand)
    {
        $brand->load('models');

        return Inertia::render('admin/Brands/Show', [
            'brand' => $brand,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Brand $brand)
    {
        return Inertia::render('admin/Brands/Edit', [
            'brand' => $brand,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Brand $brand)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('brands')->ignore($brand->id)],
            'logo_url' => 'nullable|url',
            'country' => 'nullable|string|max:255',
            'website' => 'nullable|url',
            'is_active' => 'boolean',
        ]);

        $brand->update($validated);

        return redirect()->route('admin.brands.index')
            ->with('success', 'Brand updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Brand $brand)
    {
        // Check if brand has models
        if ($brand->models()->count() > 0) {
            return back()->with('error', 'Cannot delete brand with associated models.');
        }

        $brand->delete();

        return redirect()->route('admin.brands.index')
            ->with('success', 'Brand deleted successfully.');
    }

    /**
     * Export brands to CSV.
     */
    public function export(Request $request)
    {
        $query = Brand::withCount('models');

        // Apply the same filters as the index method
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('country', 'LIKE', "%{$search}%")
                  ->orWhere('website', 'LIKE', "%{$search}%");
            });
        }

        // Apply country filter
        if ($country = $request->get('country')) {
            $query->where('country', $country);
        }

        // Apply status filter
        if ($status = $request->get('status')) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Handle selected IDs for bulk export
        if ($request->has('ids')) {
            $ids = $request->get('ids');
            if (is_array($ids) && !empty($ids)) {
                $query->whereIn('id', $ids);
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        if (in_array($sortBy, ['name', 'country', 'created_at', 'updated_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('name', 'asc');
        }

        $brands = $query->get();

        // Prepare CSV data
        $csvData = [];
        $csvData[] = ['Brand Name', 'Country', 'Website', 'Logo URL', 'Status', 'Models Count', 'Created Date'];

        foreach ($brands as $brand) {
            $csvData[] = [
                $brand->name ?? '',
                $brand->country ?? '',
                $brand->website ?? '',
                $brand->logo_url ?? '',
                $brand->is_active ? 'Active' : 'Inactive',
                $brand->models_count ?? 0,
                $brand->created_at ? $brand->created_at->format('Y-m-d H:i:s') : '',
            ];
        }

        $filename = 'brands_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');

            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($csvData as $row) {
                fputcsv($file, $row, ',', '"', '\\');
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    /**
     * Download CSV template for brands import.
     */
    public function downloadTemplate()
    {
        $csvData = [];
        $csvData[] = ['name', 'country', 'website', 'logo_url'];
        $csvData[] = ['# IMPORT TEMPLATE: Use this template to import brands'];
        $csvData[] = ['# name: Required - the brand name (must be unique)'];
        $csvData[] = ['# country: Optional - country where the brand is based'];
        $csvData[] = ['# website: Optional - brand website URL (must be valid URL if provided)'];
        $csvData[] = ['# logo_url: Optional - brand logo URL (must be valid URL if provided)'];
        $csvData[] = ['# NOTE: All imported brands will be set as Active by default'];
        $csvData[] = ['Apple', 'United States', 'https://www.apple.com', 'https://example.com/apple-logo.png'];
        $csvData[] = ['Samsung', 'South Korea', 'https://www.samsung.com', 'https://example.com/samsung-logo.png'];
        $csvData[] = ['Google', 'United States', 'https://www.google.com', ''];

        $filename = 'brands_import_template.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');

            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($csvData as $row) {
                fputcsv($file, $row, ',', '"', '\\');
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }
}
