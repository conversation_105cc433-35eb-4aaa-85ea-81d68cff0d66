<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\PaymentApproved;
use App\Models\PaymentRequest;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use Inertia\Response;

class PaymentRequestController extends Controller
{
    /**
     * Display a listing of payment requests.
     */
    public function index(Request $request): Response
    {
        $query = PaymentRequest::query()
            ->with(['user', 'approvedBy']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        if ($request->filled('subscription_plan')) {
            $query->where('subscription_plan', $request->get('subscription_plan'));
        }

        if ($request->filled('date_from')) {
            $query->where('requested_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('requested_at', '<=', $request->get('date_to'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'requested_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $paymentRequests = $query->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total_requests' => PaymentRequest::count(),
            'pending_requests' => PaymentRequest::where('status', 'pending')->count(),
            'approved_requests' => PaymentRequest::where('status', 'approved')->count(),
            'rejected_requests' => PaymentRequest::where('status', 'rejected')->count(),
            'total_amount_pending' => PaymentRequest::where('status', 'pending')->sum('amount'),
        ];

        return Inertia::render('admin/PaymentRequests/Index', [
            'paymentRequests' => $paymentRequests,
            'stats' => $stats,
            'filters' => $request->only([
                'search', 'status', 'payment_method', 'subscription_plan',
                'date_from', 'date_to', 'sort_by', 'sort_order'
            ]),
        ]);
    }

    /**
     * Display the specified payment request.
     */
    public function show(PaymentRequest $paymentRequest): Response
    {
        $paymentRequest->load(['user', 'approvedBy']);

        return Inertia::render('admin/PaymentRequests/Show', [
            'paymentRequest' => $paymentRequest,
        ]);
    }

    /**
     * Approve a payment request.
     */
    public function approve(Request $request, PaymentRequest $paymentRequest): RedirectResponse
    {
        $validated = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if (!$paymentRequest->isPending()) {
            return redirect()->back()
                ->with('error', 'Payment request is not pending.');
        }

        $admin = $request->user();
        $paymentRequest->approve($admin, $validated['admin_notes'] ?? null);

        // Create or update subscription
        $this->processSubscription($paymentRequest);

        // Send email notification
        try {
            Mail::to($paymentRequest->user->email)->send(new PaymentApproved($paymentRequest, $admin));
        } catch (\Exception $e) {
            // Log email error but don't fail the approval
            Log::error('Failed to send payment approval email', [
                'payment_request_id' => $paymentRequest->id,
                'user_id' => $paymentRequest->user_id,
                'error' => $e->getMessage()
            ]);
        }

        // Log the activity
        $paymentRequest->user->logActivity(
            'payment_approved',
            'Payment request approved by admin',
            [
                'payment_request_id' => $paymentRequest->id,
                'amount' => $paymentRequest->amount,
                'subscription_plan' => $paymentRequest->subscription_plan,
                'email_sent' => true,
            ],
            $admin
        );

        return redirect()->back()
            ->with('success', 'Payment request approved successfully and notification email sent.');
    }

    /**
     * Reject a payment request.
     */
    public function reject(Request $request, PaymentRequest $paymentRequest): RedirectResponse
    {
        $validated = $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ]);

        if (!$paymentRequest->isPending()) {
            return redirect()->back()
                ->with('error', 'Payment request is not pending.');
        }

        $paymentRequest->reject($request->user(), $validated['admin_notes']);

        // Log the activity
        $paymentRequest->user->logActivity(
            'payment_rejected',
            'Payment request rejected by admin',
            [
                'payment_request_id' => $paymentRequest->id,
                'reason' => $validated['admin_notes'],
            ],
            $request->user()
        );

        return redirect()->back()
            ->with('success', 'Payment request rejected.');
    }

    /**
     * Bulk approve payment requests.
     */
    public function bulkApprove(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'payment_request_ids' => 'required|array',
            'payment_request_ids.*' => 'exists:payment_requests,id',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $paymentRequests = PaymentRequest::whereIn('id', $validated['payment_request_ids'])
            ->where('status', 'pending')
            ->get();

        $admin = $request->user();
        $approvedCount = 0;
        $emailErrors = 0;

        foreach ($paymentRequests as $paymentRequest) {
            $paymentRequest->approve($admin, $validated['admin_notes'] ?? null);
            $this->processSubscription($paymentRequest);

            // Send email notification
            try {
                Mail::to($paymentRequest->user->email)->send(new PaymentApproved($paymentRequest, $admin));
            } catch (\Exception $e) {
                $emailErrors++;
                Log::error('Failed to send payment approval email (bulk)', [
                    'payment_request_id' => $paymentRequest->id,
                    'user_id' => $paymentRequest->user_id,
                    'error' => $e->getMessage()
                ]);
            }

            $paymentRequest->user->logActivity(
                'payment_approved',
                'Payment request approved by admin (bulk action)',
                [
                    'payment_request_id' => $paymentRequest->id,
                    'amount' => $paymentRequest->amount,
                    'subscription_plan' => $paymentRequest->subscription_plan,
                    'email_sent' => true,
                ],
                $admin
            );
            $approvedCount++;
        }

        $message = "Successfully approved {$approvedCount} payment requests.";
        if ($emailErrors > 0) {
            $message .= " However, {$emailErrors} email notifications failed to send.";
        }

        return redirect()->back()
            ->with('success', $message);
    }

    /**
     * Process subscription after payment approval.
     */
    private function processSubscription(PaymentRequest $paymentRequest): void
    {
        $user = $paymentRequest->user;
        
        // Update user subscription plan
        $user->update(['subscription_plan' => $paymentRequest->subscription_plan]);

        // Create or update subscription record
        if ($paymentRequest->subscription_plan === 'premium') {
            // Get the premium pricing plan
            $pricingPlan = \App\Models\PricingPlan::where('name', 'premium')->where('is_active', true)->first();

            if ($pricingPlan) {
                $subscription = $user->subscriptions()->where('status', 'active')->first();

                if ($subscription) {
                    // Extend existing subscription
                    $subscription->update([
                        'current_period_end' => $subscription->current_period_end->addYear(),
                        'pricing_plan_id' => $pricingPlan->id, // Update to reference pricing plan
                    ]);
                } else {
                    // Create new subscription
                    Subscription::create([
                        'user_id' => $user->id,
                        'plan_name' => 'premium',
                        'pricing_plan_id' => $pricingPlan->id,
                        'status' => 'active',
                        'current_period_start' => now(),
                        'current_period_end' => now()->addYear(),
                        'stripe_subscription_id' => null, // Manual payment
                    ]);
                }
            }
        }

        // Mark payment request as processed
        $paymentRequest->markAsProcessed();
    }
}
