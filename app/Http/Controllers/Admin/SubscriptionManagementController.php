<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\PricingPlan;
use App\Models\User;
use App\Services\AdminSubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class SubscriptionManagementController extends Controller
{
    public function __construct(
        private AdminSubscriptionService $adminSubscriptionService
    ) {}

    /**
     * Display a listing of subscriptions.
     */
    public function index(Request $request): Response
    {
        $filters = array_merge([
            'status' => '',
            'plan' => '',
            'search' => '',
            'sort_by' => 'created_at',
            'sort_direction' => 'desc',
        ], $request->only(['status', 'plan', 'search', 'sort_by', 'sort_direction']));

        $subscriptionsPaginator = $this->adminSubscriptionService->getSubscriptions($filters, 15);
        $stats = $this->adminSubscriptionService->getSubscriptionStats();

        // Transform paginator to match frontend expectations
        $subscriptions = [
            'data' => $subscriptionsPaginator->items(),
            'links' => [
                'first' => $subscriptionsPaginator->url(1),
                'last' => $subscriptionsPaginator->url($subscriptionsPaginator->lastPage()),
                'prev' => $subscriptionsPaginator->previousPageUrl(),
                'next' => $subscriptionsPaginator->nextPageUrl(),
            ],
            'meta' => [
                'current_page' => $subscriptionsPaginator->currentPage(),
                'from' => $subscriptionsPaginator->firstItem(),
                'last_page' => $subscriptionsPaginator->lastPage(),
                'path' => $subscriptionsPaginator->path(),
                'per_page' => $subscriptionsPaginator->perPage(),
                'to' => $subscriptionsPaginator->lastItem(),
                'total' => $subscriptionsPaginator->total(),
            ],
        ];

        // Get filter options
        $statusOptions = [
            'active' => 'Active',
            'cancelled' => 'Cancelled',
            'expired' => 'Expired',
        ];

        $planOptions = PricingPlan::active()->pluck('display_name', 'name')->toArray();

        // Format counts for frontend compatibility
        $counts = [
            'total' => $stats['total_subscriptions'] ?? 0,
            'active' => $stats['active_subscriptions'] ?? 0,
            'cancelled' => $stats['cancelled_subscriptions'] ?? 0,
            'expired' => $stats['expired_subscriptions'] ?? 0,
        ];

        return Inertia::render('admin/subscriptions/Index', [
            'subscriptions' => $subscriptions,
            'stats' => $stats,
            'counts' => $counts,
            'filters' => $filters,
            'statusOptions' => $statusOptions,
            'planOptions' => $planOptions,
        ]);
    }

    /**
     * Show the form for creating a new subscription.
     */
    public function create(): Response
    {
        $users = User::select('id', 'name', 'email', 'subscription_plan')
            ->orderBy('name')
            ->get();

        $pricingPlans = PricingPlan::active()->ordered()->get();

        return Inertia::render('admin/subscriptions/Create', [
            'users' => $users,
            'pricingPlans' => $pricingPlans,
        ]);
    }

    /**
     * Store a newly created subscription.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'pricing_plan_id' => 'required|exists:pricing_plans,id',
            'status' => 'required|in:active,cancelled,expired',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'stripe_subscription_id' => 'nullable|string',
        ]);

        $user = User::findOrFail($validated['user_id']);
        $pricingPlan = PricingPlan::findOrFail($validated['pricing_plan_id']);

        $options = [
            'status' => $validated['status'],
            'start_date' => $validated['start_date'] ? \Carbon\Carbon::parse($validated['start_date']) : null,
            'end_date' => $validated['end_date'] ? \Carbon\Carbon::parse($validated['end_date']) : null,
            'stripe_subscription_id' => $validated['stripe_subscription_id'],
        ];

        $this->adminSubscriptionService->createSubscription($user, $pricingPlan, $options);

        return redirect()->route('admin.subscriptions.index')
            ->with('success', 'Subscription created successfully.');
    }

    /**
     * Display the specified subscription.
     */
    public function show(Subscription $subscription): Response
    {
        $subscription->load(['user', 'pricingPlan']);

        return Inertia::render('admin/subscriptions/Show', [
            'subscription' => $subscription,
        ]);
    }

    /**
     * Show the form for editing the specified subscription.
     */
    public function edit(Subscription $subscription): Response
    {
        $subscription->load(['user', 'pricingPlan']);
        $pricingPlans = PricingPlan::active()->ordered()->get();

        return Inertia::render('admin/subscriptions/Edit', [
            'subscription' => $subscription,
            'pricingPlans' => $pricingPlans,
        ]);
    }

    /**
     * Update the specified subscription.
     */
    public function update(Request $request, Subscription $subscription): RedirectResponse
    {
        $validated = $request->validate([
            'pricing_plan_id' => 'sometimes|exists:pricing_plans,id',
            'status' => 'sometimes|in:active,cancelled,expired',
            'current_period_start' => 'sometimes|date',
            'current_period_end' => 'sometimes|date|after:current_period_start',
            'stripe_subscription_id' => 'nullable|string',
        ]);

        $this->adminSubscriptionService->updateSubscription($subscription, $validated);

        return redirect()->route('admin.subscriptions.index')
            ->with('success', 'Subscription updated successfully.');
    }

    /**
     * Remove the specified subscription.
     */
    public function destroy(Subscription $subscription): RedirectResponse
    {
        $subscription->delete();

        return redirect()->route('admin.subscriptions.index')
            ->with('success', 'Subscription deleted successfully.');
    }

    /**
     * Cancel a subscription.
     */
    public function cancel(Request $request, Subscription $subscription): RedirectResponse
    {
        $immediately = $request->boolean('immediately', false);

        $this->adminSubscriptionService->cancelSubscription($subscription, $immediately);

        $message = $immediately
            ? 'Subscription cancelled immediately.'
            : 'Subscription will be cancelled at the end of the current period.';

        return redirect()->route('admin.subscriptions.index')
            ->with('success', $message);
    }

    /**
     * Extend a subscription.
     */
    public function extend(Request $request, Subscription $subscription): RedirectResponse
    {
        $validated = $request->validate([
            'months' => 'required|integer|min:1|max:12',
        ]);

        $this->adminSubscriptionService->extendSubscription($subscription, $validated['months']);

        return redirect()->route('admin.subscriptions.index')
            ->with('success', "Subscription extended by {$validated['months']} month(s).");
    }

    /**
     * Get subscriptions expiring soon.
     */
    public function expiringSoon(): Response
    {
        $expiringSoon = $this->adminSubscriptionService->getExpiringSoon(7);

        return Inertia::render('admin/subscriptions/ExpiringSoon', [
            'subscriptions' => $expiringSoon,
        ]);
    }

    /**
     * Bulk update subscriptions.
     */
    public function bulkUpdate(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'subscription_ids' => 'required|array',
            'subscription_ids.*' => 'exists:subscriptions,id',
            'action' => 'required|in:cancel,extend,change_plan',
            'months' => 'required_if:action,extend|integer|min:1|max:12',
            'pricing_plan_id' => 'required_if:action,change_plan|exists:pricing_plans,id',
        ]);

        $updated = 0;

        foreach ($validated['subscription_ids'] as $subscriptionId) {
            $subscription = Subscription::find($subscriptionId);
            if (!$subscription) continue;

            switch ($validated['action']) {
                case 'cancel':
                    $this->adminSubscriptionService->cancelSubscription($subscription, true);
                    $updated++;
                    break;

                case 'extend':
                    $this->adminSubscriptionService->extendSubscription($subscription, $validated['months']);
                    $updated++;
                    break;

                case 'change_plan':
                    $this->adminSubscriptionService->updateSubscription($subscription, [
                        'pricing_plan_id' => $validated['pricing_plan_id']
                    ]);
                    $updated++;
                    break;
            }
        }

        return redirect()->route('admin.subscriptions.index')
            ->with('success', "Successfully updated {$updated} subscription(s).");
    }
}
