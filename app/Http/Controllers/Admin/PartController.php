<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Exception;

class PartController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Part::with('category')
            ->withCount('models');

        // Apply search
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('part_number', 'LIKE', "%{$search}%")
                  ->orWhere('manufacturer', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%")
                  ->orWhereHas('category', function ($categoryQuery) use ($search) {
                      $categoryQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Apply category filter
        if ($categoryId = $request->get('category_id')) {
            $query->where('category_id', $categoryId);
        }

        // Apply manufacturer filter
        if ($manufacturer = $request->get('manufacturer')) {
            $query->where('manufacturer', 'LIKE', "%{$manufacturer}%");
        }

        // Apply status filter
        if ($request->has('status')) {
            $status = $request->get('status');
            if ($status !== 'all') {
                $query->where('is_active', $status === 'active');
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        $allowedSorts = ['name', 'part_number', 'manufacturer', 'created_at', 'models_count'];
        if (in_array($sortBy, $allowedSorts)) {
            if ($sortBy === 'models_count') {
                $query->orderBy('models_count', $sortOrder);
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }
        } else {
            $query->orderBy('name', 'asc');
        }

        $parts = $query->paginate(15)->withQueryString();

        // Get filter options
        $categories = Category::active()->orderBy('name')->get(['id', 'name']);
        $manufacturers = Part::select('manufacturer')
            ->whereNotNull('manufacturer')
            ->distinct()
            ->orderBy('manufacturer')
            ->pluck('manufacturer');

        return Inertia::render('admin/Parts/Index', [
            'parts' => $parts,
            'filters' => [
                'categories' => $categories,
                'manufacturers' => $manufacturers,
            ],
            'queryParams' => $request->only(['search', 'category_id', 'manufacturer', 'status', 'sort_by', 'sort_order', 'view']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::active()->orderBy('name')->get();

        return Inertia::render('admin/Parts/Create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'name' => 'required|string|max:255',
            'part_number' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'specifications' => 'nullable|array',
            'images' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        Part::create($validated);

        return redirect()->route('admin.parts.index')
            ->with('success', 'Part created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Part $part)
    {
        $part->load('category', 'models.brand');

        return Inertia::render('admin/Parts/Show', [
            'part' => $part,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Part $part)
    {
        $categories = Category::active()->orderBy('name')->get();

        return Inertia::render('admin/Parts/Edit', [
            'part' => $part,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Part $part)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'name' => 'required|string|max:255',
            'part_number' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'specifications' => 'nullable|array',
            'images' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $part->update($validated);

        return redirect()->route('admin.parts.index')
            ->with('success', 'Part updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Part $part)
    {
        $part->delete();

        return redirect()->route('admin.parts.index')
            ->with('success', 'Part deleted successfully.');
    }

    /**
     * Manage part compatibility with models.
     */
    public function compatibility(Part $part)
    {
        $part->load('models.brand');
        $availableModels = MobileModel::with('brand')
            ->whereNotIn('id', $part->models->pluck('id'))
            ->active()
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/Parts/Compatibility', [
            'part' => $part,
            'availableModels' => $availableModels,
        ]);
    }

    /**
     * Edit part compatibility with models in table view.
     */
    public function editCompatibility(Part $part)
    {
        $part->load(['models.brand', 'category']);

        // Get all models with their compatibility status for this part
        $allModels = MobileModel::with('brand')
            ->active()
            ->orderBy('name')
            ->get()
            ->map(function ($model) use ($part) {
                $compatibility = $part->models->firstWhere('id', $model->id);
                return [
                    'id' => $model->id,
                    'name' => $model->name,
                    'model_number' => $model->model_number,
                    'release_year' => $model->release_year,
                    'brand' => $model->brand,
                    'is_compatible' => $compatibility !== null,
                    'compatibility_notes' => $compatibility?->pivot?->compatibility_notes,
                    'is_verified' => $compatibility?->pivot?->is_verified ?? false,
                ];
            });

        return Inertia::render('admin/Parts/EditCompatibility', [
            'part' => $part,
            'allModels' => $allModels,
        ]);
    }

    /**
     * Add model compatibility.
     */
    public function addCompatibility(Request $request, Part $part)
    {
        $validated = $request->validate([
            'model_id' => 'required|exists:models,id',
            'compatibility_notes' => 'nullable|string',
            'is_verified' => 'boolean',
        ]);

        $part->models()->attach($validated['model_id'], [
            'compatibility_notes' => $validated['compatibility_notes'] ?? null,
            'is_verified' => $validated['is_verified'] ?? false,
        ]);

        return back()->with('success', 'Model compatibility added successfully.');
    }

    /**
     * Update model compatibility.
     */
    public function updateCompatibility(Request $request, Part $part, MobileModel $model)
    {
        $validated = $request->validate([
            'compatibility_notes' => 'nullable|string',
            'is_verified' => 'boolean',
        ]);

        $part->models()->updateExistingPivot($model->id, [
            'compatibility_notes' => $validated['compatibility_notes'] ?? null,
            'is_verified' => $validated['is_verified'] ?? false,
        ]);

        return back()->with('success', 'Model compatibility updated successfully.');
    }

    /**
     * Remove model compatibility.
     */
    public function removeCompatibility(Part $part, MobileModel $model)
    {
        $part->models()->detach($model->id);

        return back()->with('success', 'Model compatibility removed successfully.');
    }

    /**
     * Add bulk model compatibility.
     */
    public function addBulkCompatibility(Request $request, Part $part)
    {
        $validated = $request->validate([
            'model_ids' => 'required|array|min:1',
            'model_ids.*' => 'required|exists:models,id',
            'compatibility_notes' => 'nullable|string',
            'is_verified' => 'boolean',
        ]);

        // Get existing model IDs to avoid duplicates
        $existingModelIds = $part->models()->pluck('models.id')->toArray();

        // Filter out already existing models
        $newModelIds = array_diff($validated['model_ids'], $existingModelIds);

        if (empty($newModelIds)) {
            return back()->withErrors(['model_ids' => 'All selected models are already compatible with this part.']);
        }

        $attachData = [];
        foreach ($newModelIds as $modelId) {
            $attachData[$modelId] = [
                'compatibility_notes' => $validated['compatibility_notes'] ?? null,
                'is_verified' => $validated['is_verified'] ?? false,
            ];
        }

        $part->models()->attach($attachData);

        $count = count($newModelIds);
        $skipped = count($validated['model_ids']) - $count;

        $message = "Added compatibility for {$count} models successfully.";
        if ($skipped > 0) {
            $message .= " ({$skipped} models were already compatible and skipped.)";
        }

        return back()->with('success', $message);
    }

    /**
     * Export parts to CSV.
     */
    public function export(Request $request)
    {
        $request->validate([
            'ids' => 'nullable|array',
            'ids.*' => 'exists:parts,id',
            'search' => 'nullable|string',
            'category_id' => 'nullable|exists:categories,id',
            'manufacturer' => 'nullable|string',
            'status' => 'nullable|in:active,inactive',
        ]);

        $query = Part::with(['category', 'models.brand']);

        // If specific IDs are provided, filter by them
        if ($request->has('ids') && !empty($request->ids)) {
            $query->whereIn('id', $request->ids);
        } else {
            // Apply the same filters as the index method when exporting all

            // Apply search
            if ($search = $request->get('search')) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('part_number', 'LIKE', "%{$search}%")
                      ->orWhere('manufacturer', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%")
                      ->orWhereHas('category', function ($categoryQuery) use ($search) {
                          $categoryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Apply category filter
            if ($categoryId = $request->get('category_id')) {
                $query->where('category_id', $categoryId);
            }

            // Apply manufacturer filter
            if ($manufacturer = $request->get('manufacturer')) {
                $query->where('manufacturer', 'LIKE', "%{$manufacturer}%");
            }

            // Apply status filter
            if ($status = $request->get('status')) {
                if ($status === 'active') {
                    $query->where('is_active', true);
                } elseif ($status === 'inactive') {
                    $query->where('is_active', false);
                }
            }
        }

        $parts = $query->get();

        $csvData = [];
        $csvData[] = ['Brand', 'Models', 'Part Name', 'Part Number', 'Description', 'Manufacturer', 'Category', 'Status', 'Created'];

        foreach ($parts as $part) {
            // Get all brands and models for this part
            $brands = $part->models->pluck('brand.name')->unique()->implode(', ');
            $models = $part->models->map(function ($model) {
                return $model->brand->name . ' ' . $model->name;
            })->implode(', ');

            $csvData[] = [
                $brands ?: '',
                $models ?: '',
                $part->name,
                $part->part_number ?: '',
                $part->description ?: '',
                $part->manufacturer ?: '',
                $part->category->name,
                $part->is_active ? 'Active' : 'Inactive',
                $part->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $filename = 'parts_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Export single part to CSV with compatibility format.
     */
    public function exportSingle(Part $part)
    {
        $part->load(['category', 'models.brand']);

        $csvData = [];
        $csvData[] = ['Brand', 'Model', 'Model Number', 'Part Name', 'Part Number', 'Description', 'Manufacturer', 'Category', 'Compatible', 'Verified', 'Notes'];
        $csvData[] = ['# COMPATIBILITY EXPORT: Current compatibility data for ' . $part->name];
        $csvData[] = ['# Compatible: true/false - whether the model is compatible with this part'];
        $csvData[] = ['# Verified: true/false - whether the compatibility is verified'];
        $csvData[] = ['# Notes: Compatibility notes'];
        $csvData[] = ['# You can edit Compatible, Verified, and Notes columns and re-import this file'];

        // Add each compatible model as a separate row
        foreach ($part->models as $model) {
            $csvData[] = [
                $model->brand->name,
                $model->name,
                $model->model_number ?: '',
                $part->name,
                $part->part_number ?: '',
                $part->description ?: '',
                $part->manufacturer ?: '',
                $part->category->name,
                'true', // Compatible (since it's in the compatibility list)
                $model->pivot->is_verified ? 'true' : 'false',
                $model->pivot->compatibility_notes ?: '',
            ];
        }

        // If no compatible models, add a sample row for guidance
        if ($part->models->isEmpty()) {
            $csvData[] = ['# No compatible models found. Add rows below with model information and set Compatible to true/false'];
            $csvData[] = ['Apple', 'iPhone 13', 'A2482', $part->name, $part->part_number ?: '', $part->description ?: '', $part->manufacturer ?: '', $part->category->name, 'true', 'false', 'Add your compatibility notes here'];
        }

        $filename = 'part_compatibility_export_' . $part->id . '_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Download CSV template for parts import.
     */
    public function downloadTemplate()
    {
        $csvData = [];
        $csvData[] = ['Brand', 'Models', 'Part Name', 'Part Number', 'Description', 'Manufacturer', 'Category', 'Status', 'Created'];
        $csvData[] = ['# DUPLICATE DETECTION: Parts are considered duplicates if they have the same Part Number + Category, or Name + Category + Manufacturer'];
        $csvData[] = ['# IMPORT OPTIONS: Choose Skip (ignore duplicates), Update (overwrite existing), or Error (report as errors)'];
        $csvData[] = ['Apple', 'iPhone 12, iPhone 12 Pro', 'Battery', 'A2471', 'Lithium-ion battery for iPhone 12 series', 'Apple', 'Battery', 'Active', ''];
        $csvData[] = ['Samsung', 'Galaxy S21', 'Display', 'SM-G991B-DISPLAY', 'AMOLED display assembly', 'Samsung', 'Display', 'Active', ''];
        $csvData[] = ['Xiaomi', 'Redmi Note 9', 'Camera Module', 'IMX582', '48MP main camera sensor', 'Sony', 'Camera', 'Active', ''];

        $filename = 'parts_import_template.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Download CSV template for part compatibility import.
     */
    public function downloadCompatibilityTemplate(Part $part)
    {
        $part->load(['category']);

        $csvData = [];
        $csvData[] = ['Brand', 'Model', 'Model Number', 'Part Name', 'Part Number', 'Description', 'Manufacturer', 'Category', 'Compatible', 'Verified', 'Notes'];
        $csvData[] = ['# COMPATIBILITY IMPORT: Update compatibility for ' . $part->name];
        $csvData[] = ['# Compatible: true/false or 1/0 - whether the model is compatible with this part'];
        $csvData[] = ['# Verified: true/false or 1/0 - whether the compatibility is verified'];
        $csvData[] = ['# Notes: Optional compatibility notes'];
        $csvData[] = ['# Part information is included for reference. Only compatibility data will be updated.'];
        $csvData[] = ['Apple', 'iPhone 13', 'A2482', $part->name, $part->part_number ?: '', $part->description ?: '', $part->manufacturer ?: '', $part->category->name, 'true', 'true', 'Fully tested and verified'];
        $csvData[] = ['Samsung', 'Galaxy S21', 'SM-G991B', $part->name, $part->part_number ?: '', $part->description ?: '', $part->manufacturer ?: '', $part->category->name, 'true', 'false', 'Compatible but needs verification'];
        $csvData[] = ['Xiaomi', 'Redmi Note 9', 'M2003J15SC', $part->name, $part->part_number ?: '', $part->description ?: '', $part->manufacturer ?: '', $part->category->name, 'false', 'false', 'Not compatible'];

        $filename = 'compatibility_import_template_' . $part->id . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Import compatibility data for a specific part from CSV.
     */
    public function importCompatibility(Request $request, Part $part)
    {
        \Log::info('Compatibility import started', ['part_id' => $part->id, 'part_name' => $part->name]);

        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
        ]);

        $file = $request->file('file');
        \Log::info('File received', ['filename' => $file->getClientOriginalName(), 'size' => $file->getSize()]);

        // Read and parse CSV file with error handling
        try {
            $csvData = array_map(function($line) {
                return str_getcsv($line, ',', '"', '\\');
            }, file($file->path()));
            if (empty($csvData)) {
                \Log::error('CSV file is empty');
                return back()->withErrors(['file' => 'The CSV file appears to be empty.']);
            }
            $header = array_shift($csvData);
            if (empty($header)) {
                \Log::error('CSV file has no header');
                return back()->withErrors(['file' => 'The CSV file does not contain a valid header row.']);
            }
            \Log::info('CSV parsed successfully', ['header' => $header, 'rows_count' => count($csvData)]);

            // Validate CSV format by checking required headers
            $requiredHeaders = ['Brand', 'Model', 'Compatible'];
            $missingHeaders = array_diff($requiredHeaders, $header);
            if (!empty($missingHeaders)) {
                \Log::error('Invalid CSV format - missing headers', ['missing' => $missingHeaders, 'provided' => $header]);
                return back()->withErrors(['file' => 'Invalid CSV format. Missing required columns: ' . implode(', ', $missingHeaders) . '. Please use the template with columns: Brand, Model, Model Number, Part Name, Part Number, Description, Manufacturer, Category, Compatible, Verified, Notes']);
            }
        } catch (Exception $e) {
            \Log::error('Error reading CSV file', ['error' => $e->getMessage()]);
            return back()->withErrors(['file' => 'Error reading CSV file: ' . $e->getMessage()]);
        }

        $imported = 0;
        $updated = 0;
        $removed = 0;
        $errors = [];
        $warnings = [];
        $prefixRemovals = [];

        \Log::info('Starting transaction for compatibility import');

        DB::transaction(function () use ($csvData, $header, $part, &$imported, &$updated, &$removed, &$errors, &$warnings, &$prefixRemovals) {
            \Log::info('Inside transaction', ['rows_to_process' => count($csvData)]);

            foreach ($csvData as $index => $row) {
                \Log::info('Processing row', ['index' => $index, 'row' => $row]);

                // Skip empty rows or comment rows (starting with #)
                if (empty($row) || (isset($row[0]) && strpos(trim($row[0]), '#') === 0)) {
                    \Log::info('Skipping row', ['reason' => 'empty or comment', 'index' => $index]);
                    continue;
                }

                // Ensure row has the same number of columns as header
                $headerCount = count($header);
                $rowCount = count($row);

                if ($rowCount < $headerCount) {
                    // Pad with empty strings
                    $row = array_pad($row, $headerCount, '');
                } elseif ($rowCount > $headerCount) {
                    // Trim extra columns
                    $row = array_slice($row, 0, $headerCount);
                }

                $data = array_combine($header, $row);

                // Map CSV headers to expected format
                $mappedData = [
                    'brand' => $data['Brand'] ?? $data['brand'] ?? null,
                    'model' => $data['Model'] ?? $data['model'] ?? $data['model_name'] ?? null,
                    'model_number' => $data['Model Number'] ?? $data['model_number'] ?? null,
                    'compatible' => $data['Compatible'] ?? $data['compatible'] ?? null,
                    'verified' => $data['Verified'] ?? $data['verified'] ?? $data['is_verified'] ?? null,
                    'notes' => $data['Notes'] ?? $data['notes'] ?? $data['compatibility_notes'] ?? null,
                ];

                $validator = Validator::make($mappedData, [
                    'brand' => 'required|string',
                    'model' => 'required|string',
                    'model_number' => 'nullable|string',
                    'compatible' => 'required|string',
                    'verified' => 'nullable|string',
                    'notes' => 'nullable|string',
                ]);

                if ($validator->fails()) {
                    $errors[] = "Row " . ($index + 2) . ": " . implode(', ', $validator->errors()->all());
                    continue;
                }

                // Add data quality warnings
                if (!empty($mappedData['model']) && stripos($mappedData['model'], $mappedData['brand']) === 0) {
                    $warnings[] = "Row " . ($index + 2) . ": Model name '{$mappedData['model']}' appears to contain brand prefix. This will be automatically cleaned for matching.";
                }

                // Find the brand
                $brand = Brand::where('name', $mappedData['brand'])->first();
                if (!$brand) {
                    $errors[] = "Row " . ($index + 2) . ": Brand '{$mappedData['brand']}' not found";
                    continue;
                }

                // Find the model - try exact match first, then try without brand prefix
                $modelQuery = MobileModel::where('brand_id', $brand->id)
                    ->where('name', $mappedData['model']);

                if (!empty($mappedData['model_number'])) {
                    $modelQuery->where('model_number', $mappedData['model_number']);
                }

                $model = $modelQuery->first();

                // If not found, try intelligent brand prefix removal
                if (!$model) {
                    $modelNameWithoutBrand = null;
                    $prefixRemovalMethod = null;

                    // Method 1: Exact brand prefix with space (most common case)
                    if (stripos($mappedData['model'], $mappedData['brand'] . ' ') === 0) {
                        $modelNameWithoutBrand = trim(substr($mappedData['model'], strlen($mappedData['brand'] . ' ')));
                        $prefixRemovalMethod = 'exact_with_space';
                    }
                    // Method 2: Exact brand prefix without space (fallback)
                    elseif (stripos($mappedData['model'], $mappedData['brand']) === 0 &&
                            strlen($mappedData['model']) > strlen($mappedData['brand'])) {
                        $modelNameWithoutBrand = trim(substr($mappedData['model'], strlen($mappedData['brand'])));
                        $prefixRemovalMethod = 'exact_without_space';
                    }

                    if ($modelNameWithoutBrand && !empty($modelNameWithoutBrand)) {
                        $modelQuery = MobileModel::where('brand_id', $brand->id)
                            ->where('name', $modelNameWithoutBrand);

                        if (!empty($mappedData['model_number'])) {
                            $modelQuery->where('model_number', $mappedData['model_number']);
                        }

                        $model = $modelQuery->first();

                        if ($model) {
                            // Track successful prefix removal for user feedback
                            $prefixRemovals[] = [
                                'row' => $index + 2,
                                'original' => $mappedData['model'],
                                'cleaned' => $modelNameWithoutBrand,
                                'method' => $prefixRemovalMethod,
                                'brand' => $mappedData['brand']
                            ];

                            \Log::info('Model found after removing brand prefix', [
                                'row' => $index + 2,
                                'original_model_name' => $mappedData['model'],
                                'cleaned_model_name' => $modelNameWithoutBrand,
                                'found_model_id' => $model->id,
                                'removal_method' => $prefixRemovalMethod
                            ]);
                        } else {
                            // Log failed prefix removal attempt
                            \Log::info('Brand prefix removal attempted but model still not found', [
                                'row' => $index + 2,
                                'original_model_name' => $mappedData['model'],
                                'cleaned_model_name' => $modelNameWithoutBrand,
                                'brand' => $mappedData['brand'],
                                'removal_method' => $prefixRemovalMethod
                            ]);
                        }
                    }
                }

                if (!$model) {
                    $errors[] = "Row " . ($index + 2) . ": Model '{$mappedData['brand']} {$mappedData['model']}' not found";
                    continue;
                }

                // Parse compatibility status
                $isCompatible = in_array(strtolower(trim($mappedData['compatible'])), ['true', '1', 'yes', 'compatible']);
                $isVerified = in_array(strtolower(trim($mappedData['verified'] ?? 'false')), ['true', '1', 'yes', 'verified']);

                \Log::info('Compatibility status parsed', [
                    'model_id' => $model->id,
                    'model_name' => $model->name,
                    'is_compatible' => $isCompatible,
                    'is_verified' => $isVerified,
                    'raw_compatible' => $mappedData['compatible'],
                    'raw_verified' => $mappedData['verified'] ?? 'false'
                ]);

                // Check if compatibility already exists
                $existingCompatibility = $part->models()->where('models.id', $model->id)->exists();
                \Log::info('Existing compatibility check', ['model_id' => $model->id, 'exists' => $existingCompatibility]);

                if ($isCompatible) {
                    if ($existingCompatibility) {
                        // Update existing compatibility
                        \Log::info('Updating existing compatibility', ['model_id' => $model->id]);
                        $part->models()->updateExistingPivot($model->id, [
                            'compatibility_notes' => $mappedData['notes'],
                            'is_verified' => $isVerified,
                        ]);
                        $updated++;
                        \Log::info('Updated compatibility', ['model_id' => $model->id, 'updated_count' => $updated]);
                    } else {
                        // Add new compatibility
                        \Log::info('Adding new compatibility', ['model_id' => $model->id]);
                        $part->models()->attach($model->id, [
                            'compatibility_notes' => $mappedData['notes'],
                            'is_verified' => $isVerified,
                        ]);
                        $imported++;
                        \Log::info('Added compatibility', ['model_id' => $model->id, 'imported_count' => $imported]);
                    }
                } else {
                    if ($existingCompatibility) {
                        // Remove compatibility
                        \Log::info('Removing compatibility', ['model_id' => $model->id]);
                        $part->models()->detach($model->id);
                        $removed++;
                        \Log::info('Removed compatibility', ['model_id' => $model->id, 'removed_count' => $removed]);
                    }
                    // If not compatible and doesn't exist, do nothing
                }
            }
        });

        // Build comprehensive success message
        $messageParts = [];
        if ($imported > 0) $messageParts[] = "Added: {$imported}";
        if ($updated > 0) $messageParts[] = "Updated: {$updated}";
        if ($removed > 0) $messageParts[] = "Removed: {$removed}";

        \Log::info('Import completed', [
            'imported' => $imported,
            'updated' => $updated,
            'removed' => $removed,
            'errors_count' => count($errors),
            'prefix_removals_count' => count($prefixRemovals),
        ]);

        // Build comprehensive success message with prefix removal feedback
        $message = '';
        $hasOperations = !empty($messageParts);
        $hasErrors = !empty($errors);
        $hasPrefixRemovals = !empty($prefixRemovals);

        if ($hasErrors && !$hasOperations) {
            // Only errors, no successful operations
            return back()->withErrors(['file' => 'Import failed. ' . implode(' ', $errors)]);
        }

        if ($hasOperations) {
            $message = "Compatibility import completed successfully. " . implode(', ', $messageParts) . " compatibility records.";
        } else {
            $message = "No compatibility changes were made. Please check your CSV file format.";
        }

        // Add prefix removal feedback
        if ($hasPrefixRemovals) {
            $prefixMessage = " Note: Brand prefixes were automatically removed from " . count($prefixRemovals) . " model name(s) for better matching:";
            foreach ($prefixRemovals as $removal) {
                $prefixMessage .= " Row {$removal['row']}: '{$removal['original']}' → '{$removal['cleaned']}'.";
            }
            $message .= $prefixMessage;
        }

        // Add errors as warnings if there were successful operations
        if ($hasErrors && $hasOperations) {
            $message .= " However, there were some issues: " . implode(' ', $errors);
        }

        return back()->with('success', $message);
    }
}
