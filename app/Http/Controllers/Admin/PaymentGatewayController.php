<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\CoinbaseCommerceService;
use Inertia\Inertia;

class PaymentGatewayController extends Controller
{
    private CoinbaseCommerceService $coinbaseCommerceService;

    public function __construct(CoinbaseCommerceService $coinbaseCommerceService)
    {
        $this->coinbaseCommerceService = $coinbaseCommerceService;
    }

    /**
     * Display a listing of payment gateways.
     */
    public function index()
    {
        // Check Coinbase Commerce configuration status
        $coinbaseStatus = $this->coinbaseCommerceService->isConfigured() ? 'configured' : 'not_configured';
        $paymentGateways = [
            [
                'id' => 'paddle',
                'name' => 'Paddle',
                'description' => 'Complete payment solution with global tax compliance',
                'status' => 'configured',
                'logo' => '/images/paddle-logo.png',
                'features' => ['Subscription Billing', 'Global Tax Compliance', 'Fraud Protection', 'Analytics'],
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK'],
                'configuration_url' => route('admin.payment-gateways.paddle.configure'),
                'documentation_url' => 'https://developer.paddle.com/getting-started'
            ],
            [
                'id' => 'shurjopay',
                'name' => 'ShurjoPay',
                'description' => 'Leading payment gateway for Bangladesh with local banking support',
                'status' => 'configured',
                'logo' => '/images/shurjopay-logo.png',
                'features' => ['Mobile Banking', 'Internet Banking', 'Card Payments', 'Local Support'],
                'supported_currencies' => ['BDT', 'USD', 'EUR', 'GBP'],
                'configuration_url' => route('admin.payment-gateways.shurjopay.configure'),
                'documentation_url' => 'https://shurjopay.com.bd/developers'
            ],
            [
                'id' => 'stripe',
                'name' => 'Stripe',
                'description' => 'Online payment processing for internet businesses',
                'status' => 'not_configured',
                'logo' => '/images/stripe-logo.png',
                'features' => ['Credit Cards', 'Digital Wallets', 'Bank Transfers', 'Buy Now Pay Later'],
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK', 'PLN', 'CZK'],
                'configuration_url' => '#', // Will be implemented later
                'documentation_url' => 'https://stripe.com/docs'
            ],
            [
                'id' => 'paypal',
                'name' => 'PayPal',
                'description' => 'Global digital payment platform',
                'status' => 'not_configured',
                'logo' => '/images/paypal-logo.png',
                'features' => ['PayPal Checkout', 'Credit Cards', 'PayPal Credit', 'Venmo'],
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'ILS'],
                'configuration_url' => '#', // Will be implemented later
                'documentation_url' => 'https://developer.paypal.com/docs'
            ],
            [
                'id' => 'coinbase',
                'name' => 'Coinbase Commerce',
                'description' => 'Next-generation crypto payments with Onchain Payment Protocol - automatic USDC settlement, hundreds of currencies, instant confirmation',
                'status' => $coinbaseStatus,
                'logo' => '/images/coinbase-logo.png',
                'features' => [
                    'Onchain Payment Protocol',
                    'Auto USDC Settlement',
                    'Hundreds of Currencies',
                    'Instant Confirmation',
                    'Base & Polygon Networks',
                    'Volatility Protection',
                    'Enterprise Security',
                    'Real-time Updates'
                ],
                'supported_currencies' => ['BTC', 'ETH', 'LTC', 'BCH', 'USDC', 'DAI', '+100s more'],
                'configuration_url' => route('admin.payment-gateways.coinbase.configure'),
                'documentation_url' => 'https://docs.cdp.coinbase.com/commerce/introduction/welcome'
            ],
            [
                'id' => 'razorpay',
                'name' => 'Razorpay',
                'description' => 'Complete payment solution for India',
                'status' => 'not_configured',
                'logo' => '/images/razorpay-logo.png',
                'features' => ['UPI', 'Credit Cards', 'Debit Cards', 'Net Banking', 'Wallets'],
                'supported_currencies' => ['INR', 'USD', 'EUR', 'GBP', 'SGD', 'AED'],
                'configuration_url' => '#', // Will be implemented later
                'documentation_url' => 'https://razorpay.com/docs'
            ],
            [
                'id' => 'square',
                'name' => 'Square',
                'description' => 'Payment processing and business solutions',
                'status' => 'not_configured',
                'logo' => '/images/square-logo.png',
                'features' => ['Credit Cards', 'Digital Wallets', 'Gift Cards', 'Invoicing'],
                'supported_currencies' => ['USD', 'CAD', 'GBP', 'AUD', 'JPY'],
                'configuration_url' => '#', // Will be implemented later
                'documentation_url' => 'https://developer.squareup.com/docs'
            ]
        ];

        return Inertia::render('admin/payment-gateways/Index', [
            'paymentGateways' => $paymentGateways
        ]);
    }
}
