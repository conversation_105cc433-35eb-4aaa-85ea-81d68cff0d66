<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class BulkImportController extends Controller
{
    public function index()
    {
        return Inertia::render('admin/BulkImport/Index');
    }

    public function importBrands(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
            'duplicate_action' => 'nullable|in:skip,update,error',
        ]);

        $file = $request->file('file');
        $duplicateAction = $request->input('duplicate_action', 'skip'); // Default to skip duplicates

        // Development debugging
        if (app()->environment('local')) {
            \Log::info('🚀 BRANDS IMPORT STARTED', [
                'file_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'duplicate_action' => $duplicateAction
            ]);
        }

        // Read and parse CSV file with error handling
        try {
            $csvData = array_map(function($line) {
                return str_getcsv($line, ',', '"', '\\');
            }, file($file->path()));
            if (empty($csvData)) {
                return back()->withErrors(['file' => 'The CSV file appears to be empty.']);
            }
            $header = array_shift($csvData);
            if (empty($header)) {
                return back()->withErrors(['file' => 'The CSV file does not contain a valid header row.']);
            }

            // Clean and normalize headers more thoroughly
            $header = array_map(function($h) {
                // Remove BOM, quotes, and various whitespace characters
                $cleaned = trim($h, " \t\n\r\0\x0B\xEF\xBB\xBF\"'");
                // Remove any remaining invisible characters
                $cleaned = preg_replace('/[\x00-\x1F\x7F]/', '', $cleaned);
                return $cleaned;
            }, $header);

            if (app()->environment('local')) {
                // Get raw headers for debugging
                $fileLines = file($file->path());
                $rawHeaderLine = str_getcsv($fileLines[0], ',', '"', '\\');

                \Log::info('📋 CSV HEADERS DETECTED', [
                    'raw_headers' => $rawHeaderLine,
                    'cleaned_headers' => $header,
                    'header_count' => count($header)
                ]);
            }

        } catch (Exception $e) {
            return back()->withErrors(['file' => 'Error reading CSV file: ' . $e->getMessage()]);
        }

        $imported = 0;
        $updated = 0;
        $skipped = 0;
        $errors = [];

        // Create column mapping for flexible header names
        $columnMapping = $this->createBrandColumnMapping($header);

        if (app()->environment('local')) {
            \Log::info('🗂️ COLUMN MAPPING', [
                'mapping' => $columnMapping,
                'available_headers' => $header,
                'normalized_headers' => array_map(function($h) {
                    return strtolower(trim($h));
                }, $header)
            ]);
        }

        // Validate that required columns are present
        if (!isset($columnMapping['name'])) {
            $availableHeaders = implode('", "', $header);
            $expectedHeaders = 'Brand Name, name, brand, or brandname';

            if (app()->environment('local')) {
                \Log::error('❌ BRAND IMPORT - Required column not found', [
                    'available_headers' => $header,
                    'column_mapping' => $columnMapping,
                    'expected_variations' => ['brand name', 'name', 'brand', 'brandname']
                ]);
            }

            return back()->withErrors(['file' => "Required brand name column not found. Available columns: \"{$availableHeaders}\". Expected one of: {$expectedHeaders}."]);
        }

        DB::transaction(function () use ($csvData, $header, $columnMapping, $duplicateAction, &$imported, &$updated, &$skipped, &$errors) {
            foreach ($csvData as $index => $row) {
                // Skip empty rows or comment rows (starting with #)
                if (empty($row) || (isset($row[0]) && strpos(trim($row[0]), '#') === 0)) {
                    continue;
                }

                // Skip rows where all values are empty (just commas)
                if (empty(array_filter($row, function($value) {
                    return !empty(trim($value));
                }))) {
                    if (app()->environment('local')) {
                        \Log::info("⏭️ SKIPPED EMPTY ROW " . ($index + 2));
                    }
                    continue;
                }

                // Ensure row has the same number of columns as header
                $headerCount = count($header);
                $rowCount = count($row);

                if ($rowCount < $headerCount) {
                    // Pad with empty strings
                    $row = array_pad($row, $headerCount, '');
                } elseif ($rowCount > $headerCount) {
                    // Trim extra columns
                    $row = array_slice($row, 0, $headerCount);
                }

                $rawData = array_combine($header, $row);

                // Map columns to expected field names
                $mappedData = [];
                foreach ($columnMapping as $field => $csvColumn) {
                    $mappedData[$field] = isset($rawData[$csvColumn]) ? trim($rawData[$csvColumn]) : null;
                }

                if (app()->environment('local')) {
                    \Log::info("📝 PROCESSING ROW " . ($index + 2), [
                        'raw_data' => $rawData,
                        'mapped_data' => $mappedData
                    ]);
                }

                // Handle empty name field
                if (empty($mappedData['name'])) {
                    $errors[] = "Row " . ($index + 2) . ": Brand name is required";
                    continue;
                }

                // Check if brand already exists
                $existingBrand = Brand::where('name', $mappedData['name'])->first();

                if ($existingBrand) {
                    if ($duplicateAction === 'skip') {
                        $skipped++;
                        if (app()->environment('local')) {
                            \Log::info("⏭️ SKIPPED EXISTING BRAND", ['name' => $mappedData['name']]);
                        }
                        continue;
                    } elseif ($duplicateAction === 'update') {
                        // Update existing brand
                        $updateData = array_filter([
                            'country' => $mappedData['country'] ?: null,
                            'website' => $mappedData['website'] ?: null,
                            'logo_url' => $mappedData['logo_url'] ?: null,
                        ]);

                        if (!empty($updateData)) {
                            $existingBrand->update($updateData);
                            $updated++;
                            if (app()->environment('local')) {
                                \Log::info("🔄 UPDATED EXISTING BRAND", ['name' => $mappedData['name'], 'data' => $updateData]);
                            }
                        } else {
                            $skipped++;
                        }
                        continue;
                    } else { // error
                        $errors[] = "Row " . ($index + 2) . ": Brand '{$mappedData['name']}' already exists";
                        continue;
                    }
                }

                // Validate new brand data
                $validator = Validator::make($mappedData, [
                    'name' => 'required|string|max:255',
                    'country' => 'nullable|string|max:255',
                    'website' => 'nullable|url',
                    'logo_url' => 'nullable|url',
                ]);

                if ($validator->fails()) {
                    $errors[] = "Row " . ($index + 2) . ": " . implode(', ', $validator->errors()->all());
                    continue;
                }

                // Create new brand
                $brand = Brand::create([
                    'name' => $mappedData['name'],
                    'country' => $mappedData['country'] ?: null,
                    'website' => $mappedData['website'] ?: null,
                    'logo_url' => $mappedData['logo_url'] ?: null,
                    'is_active' => true,
                ]);

                $imported++;

                if (app()->environment('local')) {
                    \Log::info("✅ CREATED NEW BRAND", ['id' => $brand->id, 'name' => $brand->name]);
                }
            }
        });

        // Build result message
        $messageParts = [];
        if ($imported > 0) {
            $messageParts[] = "Imported {$imported} new brands";
        }
        if ($updated > 0) {
            $messageParts[] = "Updated {$updated} existing brands";
        }
        if ($skipped > 0) {
            $messageParts[] = "Skipped {$skipped} duplicate brands";
        }

        $message = !empty($messageParts) ? implode(', ', $messageParts) . ' successfully.' : 'No brands were processed.';

        if (app()->environment('local')) {
            \Log::info('📊 IMPORT SUMMARY', [
                'imported' => $imported,
                'updated' => $updated,
                'skipped' => $skipped,
                'errors' => count($errors),
                'message' => $message
            ]);
        }

        // Return error if nothing was processed and there are errors
        if ($imported === 0 && $updated === 0 && !empty($errors)) {
            $errorSample = array_slice($errors, 0, 3);
            return back()->withErrors(['file' => 'Import failed. ' . implode(' ', $errorSample) . (count($errors) > 3 ? ' (and ' . (count($errors) - 3) . ' more errors)' : '')])
                        ->with('import_errors', $errors);
        }

        // Return error if nothing was processed at all
        if ($imported === 0 && $updated === 0 && $skipped === 0) {
            return back()->withErrors(['file' => 'No valid data found in the CSV file. Please check the format and try again.']);
        }

        if (!empty($errors)) {
            $message .= " However, there were some issues with " . count($errors) . " rows.";
            return back()->with('success', $message)
                        ->with('import_errors', $errors);
        }

        return back()->with('success', $message);
    }

    public function importCategories(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
        ]);

        $file = $request->file('file');

        // Read and parse CSV file with error handling
        try {
            $csvData = array_map(function($line) {
                return str_getcsv($line, ',', '"', '\\');
            }, file($file->path()));
            if (empty($csvData)) {
                return back()->withErrors(['file' => 'The CSV file appears to be empty.']);
            }
            $header = array_shift($csvData);
            if (empty($header)) {
                return back()->withErrors(['file' => 'The CSV file does not contain a valid header row.']);
            }
        } catch (\Exception $e) {
            return back()->withErrors(['file' => 'Error reading CSV file: ' . $e->getMessage()]);
        }

        $imported = 0;
        $errors = [];

        // First pass: collect all categories to import and validate
        $categoriesToImport = [];
        foreach ($csvData as $index => $row) {
            // Skip empty rows or comment rows (starting with #)
            if (empty($row) || (isset($row[0]) && strpos(trim($row[0]), '#') === 0)) {
                continue;
            }

            // Ensure row has the same number of columns as header
            $headerCount = count($header);
            $rowCount = count($row);

            if ($rowCount < $headerCount) {
                // Pad with empty strings
                $row = array_pad($row, $headerCount, '');
            } elseif ($rowCount > $headerCount) {
                // Trim extra columns
                $row = array_slice($row, 0, $headerCount);
            }

            $data = array_combine($header, $row);

            $validator = Validator::make($data, [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'parent_category' => 'nullable|string|max:255',
                'sort_order' => 'required|integer|min:0',
                'is_active' => 'required|string|in:Active,Inactive,active,inactive',
            ]);

            if ($validator->fails()) {
                $errors[] = "Row " . ($index + 2) . ": " . implode(', ', $validator->errors()->all());
                continue;
            }

            // Check for duplicate names in the import file
            $existingInImport = collect($categoriesToImport)->firstWhere('name', $data['name']);
            if ($existingInImport) {
                $errors[] = "Row " . ($index + 2) . ": Duplicate category name '{$data['name']}' found in import file";
                continue;
            }

            // Check if category already exists in database
            $existingCategory = Category::where('name', $data['name'])->first();
            if ($existingCategory) {
                $errors[] = "Row " . ($index + 2) . ": Category '{$data['name']}' already exists";
                continue;
            }

            $categoriesToImport[] = [
                'row_number' => $index + 2,
                'name' => $data['name'],
                'description' => $data['description'] ?: null,
                'parent_category' => $data['parent_category'] ?: null,
                'sort_order' => (int) $data['sort_order'],
                'is_active' => in_array(strtolower($data['is_active']), ['active', '1', 'true']),
            ];
        }

        // If there are validation errors, return early
        if (!empty($errors)) {
            return back()->withErrors(['file' => 'Import failed due to validation errors'])
                        ->with('import_errors', $errors);
        }

        // Second pass: import categories in correct order (parents before children)
        DB::transaction(function () use ($categoriesToImport, &$imported, &$errors) {
            // Sort categories: root categories first, then children
            $rootCategories = collect($categoriesToImport)->where('parent_category', null);
            $childCategories = collect($categoriesToImport)->where('parent_category', '!=', null);

            // Import root categories first
            foreach ($rootCategories as $categoryData) {
                try {
                    Category::create([
                        'name' => $categoryData['name'],
                        'description' => $categoryData['description'],
                        'parent_id' => null,
                        'sort_order' => $categoryData['sort_order'],
                        'is_active' => $categoryData['is_active'],
                    ]);
                    $imported++;
                } catch (\Exception $e) {
                    $errors[] = "Row " . $categoryData['row_number'] . ": Failed to create category - " . $e->getMessage();
                }
            }

            // Import child categories
            foreach ($childCategories as $categoryData) {
                try {
                    // Find parent category
                    $parentCategory = Category::where('name', $categoryData['parent_category'])->first();
                    if (!$parentCategory) {
                        $errors[] = "Row " . $categoryData['row_number'] . ": Parent category '{$categoryData['parent_category']}' not found";
                        continue;
                    }

                    Category::create([
                        'name' => $categoryData['name'],
                        'description' => $categoryData['description'],
                        'parent_id' => $parentCategory->id,
                        'sort_order' => $categoryData['sort_order'],
                        'is_active' => $categoryData['is_active'],
                    ]);
                    $imported++;
                } catch (\Exception $e) {
                    $errors[] = "Row " . $categoryData['row_number'] . ": Failed to create category - " . $e->getMessage();
                }
            }
        });

        $message = "Imported {$imported} categories successfully.";

        if (!empty($errors)) {
            $message .= " However, there were some issues:";
            return back()->with('success', $message)
                        ->with('import_errors', $errors);
        }

        return back()->with('success', $message);
    }

    public function importModels(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
        ]);

        $file = $request->file('file');

        // Read and parse CSV file with error handling
        try {
            $csvData = array_map(function($line) {
                return str_getcsv($line, ',', '"', '\\');
            }, file($file->path()));

            if (empty($csvData)) {
                return back()->withErrors(['file' => 'The CSV file appears to be empty.']);
            }
            $header = array_shift($csvData);
            if (empty($header)) {
                return back()->withErrors(['file' => 'The CSV file does not contain a valid header row.']);
            }

            // Remove BOM from header if present
            if (!empty($header[0])) {
                $header[0] = preg_replace('/^\xEF\xBB\xBF/', '', $header[0]);
            }
        } catch (Exception $e) {
            return back()->withErrors(['file' => 'Error reading CSV file: ' . $e->getMessage()]);
        }

        $imported = 0;
        $errors = [];

        DB::transaction(function () use ($csvData, $header, &$imported, &$errors) {
            foreach ($csvData as $index => $row) {
                // Skip empty rows or comment rows (starting with #)
                if (empty($row) || (isset($row[0]) && strpos(trim($row[0]), '#') === 0)) {
                    continue;
                }

                // Ensure row has the same number of columns as header
                $headerCount = count($header);
                $rowCount = count($row);

                if ($rowCount < $headerCount) {
                    // Pad with empty strings
                    $row = array_pad($row, $headerCount, '');
                } elseif ($rowCount > $headerCount) {
                    // Trim extra columns
                    $row = array_slice($row, 0, $headerCount);
                }

                $data = array_combine($header, $row);

                // Map CSV headers to expected field names (support both formats)
                $mappedData = [
                    'brand_name' => $data['Brand Name'] ?? $data['Brand'] ?? $data['brand_name'] ?? '',
                    'name' => $data['Model Name'] ?? $data['Model'] ?? $data['name'] ?? '',
                    'model_number' => $data['Model Number'] ?? $data['model_number'] ?? '',
                    'release_year' => $data['Release Year'] ?? $data['release_year'] ?? '',
                    'specifications' => $data['Specifications'] ?? $data['specifications'] ?? '',
                    'status' => $data['Status'] ?? $data['status'] ?? 'Active',
                ];

                // If brand_name is still empty, it might be because the first column had BOM
                if (empty($mappedData['brand_name']) && isset($data[array_keys($data)[0]])) {
                    $firstColumnValue = $data[array_keys($data)[0]];
                    if (!empty($firstColumnValue)) {
                        $mappedData['brand_name'] = $firstColumnValue;
                    }
                }

                $validator = Validator::make($mappedData, [
                    'brand_name' => 'required|string|exists:brands,name',
                    'name' => 'required|string|max:255',
                    'model_number' => 'nullable|string|max:255',
                    'release_year' => 'nullable|integer|min:2000|max:' . (date('Y') + 1),
                    'specifications' => 'nullable|string',
                    'status' => 'nullable|string|in:Active,Inactive,active,inactive',
                ]);

                if ($validator->fails()) {
                    $errors[] = "Row " . ($index + 2) . ": " . implode(', ', $validator->errors()->all());
                    continue;
                }

                $brand = Brand::where('name', $mappedData['brand_name'])->first();

                // Parse specifications if provided
                $specifications = null;
                $specsValue = trim($mappedData['specifications'] ?? '');

                if (!empty($specsValue)) {
                    // Try to parse as JSON first (for old format)
                    $decodedSpecs = json_decode($specsValue, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $specifications = $decodedSpecs;
                    } else {
                        // Parse the new format: "key: value; key: value"
                        $specArray = [];
                        $pairs = explode(';', $specsValue);
                        foreach ($pairs as $pair) {
                            $pair = trim($pair);
                            if (strpos($pair, ':') !== false) {
                                list($key, $value) = explode(':', $pair, 2);
                                $key = trim($key);
                                $value = trim($value);
                                if (!empty($key) && !empty($value)) {
                                    $specArray[$key] = $value;
                                }
                            }
                        }
                        if (!empty($specArray)) {
                            $specifications = $specArray;
                        }
                    }
                }

                // Determine if model is active
                $isActive = in_array(strtolower($mappedData['status']), ['active', '1', 'true']) ? true : false;

                try {
                    MobileModel::create([
                        'brand_id' => $brand->id,
                        'name' => $mappedData['name'],
                        'model_number' => $mappedData['model_number'] ?: null,
                        'release_year' => $mappedData['release_year'] ?: null,
                        'specifications' => $specifications,
                        'is_active' => $isActive,
                    ]);

                    $imported++;
                } catch (Exception $e) {
                    $errors[] = "Row " . ($index + 2) . ": Failed to create model - " . $e->getMessage();
                }
            }
        });

        $message = "Imported {$imported} models successfully.";

        if (!empty($errors)) {
            $message .= " However, there were some issues:";
            return back()->with('success', $message)
                        ->with('import_errors', $errors);
        }

        return back()->with('success', $message);
    }

    public function importParts(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
            'duplicate_action' => 'nullable|in:skip,update,error',
        ]);

        $file = $request->file('file');

        // Read and parse CSV file with error handling
        try {
            $csvData = array_map(function($line) {
                return str_getcsv($line, ',', '"', '\\');
            }, file($file->path()));
            if (empty($csvData)) {
                return back()->withErrors(['file' => 'The CSV file appears to be empty.']);
            }
            $header = array_shift($csvData);
            if (empty($header)) {
                return back()->withErrors(['file' => 'The CSV file does not contain a valid header row.']);
            }
        } catch (Exception $e) {
            return back()->withErrors(['file' => 'Error reading CSV file: ' . $e->getMessage()]);
        }

        $imported = 0;
        $updated = 0;
        $skipped = 0;
        $errors = [];
        $duplicateAction = $request->get('duplicate_action', 'skip'); // Default to skip

        DB::transaction(function () use ($csvData, $header, $duplicateAction, &$imported, &$updated, &$skipped, &$errors) {
            foreach ($csvData as $index => $row) {
                // Skip empty rows or comment rows (starting with #)
                if (empty($row) || (isset($row[0]) && strpos(trim($row[0]), '#') === 0)) {
                    continue;
                }

                // Ensure row has the same number of columns as header
                // Pad with empty strings if row is shorter, or trim if longer
                $headerCount = count($header);
                $rowCount = count($row);

                if ($rowCount < $headerCount) {
                    // Pad with empty strings
                    $row = array_pad($row, $headerCount, '');
                } elseif ($rowCount > $headerCount) {
                    // Trim extra columns
                    $row = array_slice($row, 0, $headerCount);
                }

                $data = array_combine($header, $row);

                // Map CSV headers to expected format
                $mappedData = [
                    'category' => $data['Category'] ?? $data['category_name'] ?? null,
                    'part_name' => $data['Part Name'] ?? $data['name'] ?? null,
                    'part_number' => $data['Part Number'] ?? $data['part_number'] ?? null,
                    'manufacturer' => $data['Manufacturer'] ?? $data['manufacturer'] ?? null,
                    'description' => $data['Description'] ?? $data['description'] ?? null,
                    'status' => $data['Status'] ?? 'Active',
                    'brand' => $data['Brand'] ?? null,
                    'models' => $data['Models'] ?? null,
                ];

                $validator = Validator::make($mappedData, [
                    'category' => 'required|string',
                    'part_name' => 'required|string|max:255',
                    'part_number' => 'nullable|string|max:255',
                    'manufacturer' => 'nullable|string|max:255',
                    'description' => 'nullable|string',
                    'status' => 'nullable|string|in:Active,Inactive,active,inactive',
                    'brand' => 'nullable|string',
                    'models' => 'nullable|string',
                ]);

                if ($validator->fails()) {
                    $errors[] = "Row " . ($index + 2) . ": " . implode(', ', $validator->errors()->all());
                    continue;
                }

                // Find or create category
                $category = Category::where('name', $mappedData['category'])->first();
                if (!$category) {
                    $errors[] = "Row " . ($index + 2) . ": Category '{$mappedData['category']}' not found";
                    continue;
                }

                // Check for duplicate parts
                $existingPart = $this->findDuplicatePart($mappedData, $category->id);



                if ($existingPart) {
                    // Handle duplicate based on action
                    switch ($duplicateAction) {
                        case 'error':
                            $errors[] = "Row " . ($index + 2) . ": Duplicate part found - '{$mappedData['part_name']}'" .
                                       ($mappedData['part_number'] ? " (Part #: {$mappedData['part_number']})" : "");
                            continue 2; // Continue to next row

                        case 'skip':
                            $skipped++;
                            continue 2; // Continue to next row

                        case 'update':
                            // Update existing part
                            $existingPart->update([
                                'name' => $mappedData['part_name'],
                                'part_number' => $mappedData['part_number'],
                                'manufacturer' => $mappedData['manufacturer'],
                                'description' => $mappedData['description'],
                                'is_active' => in_array(strtolower($mappedData['status']), ['active', '1', 'true']) ? true : false,
                            ]);
                            $part = $existingPart;
                            $updated++;
                            break;
                    }
                } else {
                    // Create new part
                    $part = Part::create([
                        'category_id' => $category->id,
                        'name' => $mappedData['part_name'],
                        'part_number' => $mappedData['part_number'],
                        'manufacturer' => $mappedData['manufacturer'],
                        'description' => $mappedData['description'],
                        'is_active' => in_array(strtolower($mappedData['status']), ['active', '1', 'true']) ? true : false,
                    ]);
                    $imported++;
                }

                // Handle model compatibility if models are specified
                if (!empty($mappedData['models'])) {

                    $this->syncModelsToPartFromCsv($part, $mappedData['models'], $mappedData['brand'], $errors, $index, $existingPart !== null);
                }
            }
        });

        // Build comprehensive success message
        $messageParts = [];
        if ($imported > 0) $messageParts[] = "Created: {$imported}";
        if ($updated > 0) $messageParts[] = "Updated: {$updated}";
        if ($skipped > 0) $messageParts[] = "Skipped: {$skipped}";

        $message = "Import completed successfully. " . implode(', ', $messageParts) . " parts.";

        if (!empty($errors)) {
            $message .= " However, there were some issues:";
            return back()->with('success', $message)
                        ->with('import_errors', $errors);
        }

        return back()->with('success', $message);
    }

    /**
     * Find duplicate part based on multiple strategies.
     */
    private function findDuplicatePart(array $mappedData, int $categoryId): ?Part
    {
        // Strategy 1: If part_number exists, check by part_number + category
        if (!empty($mappedData['part_number'])) {
            $part = Part::where('category_id', $categoryId)
                       ->where('part_number', $mappedData['part_number'])
                       ->first();
            if ($part) {
                return $part;
            }
        }

        // Strategy 2: Check by name + category + manufacturer (if manufacturer exists)
        if (!empty($mappedData['manufacturer'])) {
            $part = Part::where('category_id', $categoryId)
                       ->where('name', $mappedData['part_name'])
                       ->where('manufacturer', $mappedData['manufacturer'])
                       ->first();
            if ($part) {
                return $part;
            }
        }

        // Strategy 3: Fallback - check by name + category only
        $part = Part::where('category_id', $categoryId)
                   ->where('name', $mappedData['part_name'])
                   ->first();

        return $part;
    }

    /**
     * Synchronize models to part based on CSV data.
     * For updates, this replaces all existing model relationships.
     * For new parts, this adds the model relationships.
     */
    private function syncModelsToPartFromCsv(Part $part, string $modelsString, ?string $brandString, array &$errors, int $index, bool $isUpdate = false)
    {
        // Parse models string (comma-separated)
        $modelNames = array_map('trim', explode(',', $modelsString));
        $foundModelIds = [];

        // Parse brands string (comma-separated) if provided
        $brandNames = [];
        if ($brandString) {
            $brandNames = array_map('trim', explode(',', $brandString));
        }



        foreach ($modelNames as $modelName) {
            if (empty($modelName)) continue; // Skip empty model names

            // Try to find the model by name
            $model = null;

            // Strategy 1: If brands are specified, try to find model within those brands
            if (!empty($brandNames)) {
                foreach ($brandNames as $brandName) {
                    $brand = Brand::where('name', $brandName)->first();
                    if ($brand) {
                        $model = MobileModel::where('brand_id', $brand->id)
                            ->where('name', $modelName)
                            ->first();
                        if ($model) break; // Found it, stop searching
                    }
                }
            }

            // Strategy 2: If not found with brands, try to find by full name (Brand Model format)
            if (!$model && strpos($modelName, ' ') !== false) {
                $parts = explode(' ', $modelName, 2);
                $brandName = $parts[0];
                $modelNamePart = $parts[1];

                $brand = Brand::where('name', $brandName)->first();
                if ($brand) {
                    $model = MobileModel::where('brand_id', $brand->id)
                        ->where('name', $modelNamePart)
                        ->first();
                }
            }

            // Strategy 3: If still not found, try global search
            if (!$model) {
                $model = MobileModel::where('name', $modelName)->first();
            }

            if ($model) {
                $foundModelIds[] = $model->id;
            } else {
                $errors[] = "Row " . ($index + 2) . ": Model '{$modelName}' not found";
            }
        }

        // For updates, sync (replace) all model relationships
        // For new parts, just attach the models
        if ($isUpdate) {
            // Sync will remove existing relationships and add new ones
            $syncData = [];
            foreach ($foundModelIds as $modelId) {
                $syncData[$modelId] = [
                    'compatibility_notes' => null,
                    'is_verified' => false,
                ];
            }

            $part->models()->sync($syncData);
        } else {
            // For new parts, just attach the models (avoid duplicates)
            foreach ($foundModelIds as $modelId) {
                if (!$part->models()->where('model_id', $modelId)->exists()) {
                    $part->models()->attach($modelId, [
                        'compatibility_notes' => null,
                        'is_verified' => false,
                    ]);
                }
            }
        }
    }

    /**
     * Create flexible column mapping for brand import
     */
    private function createBrandColumnMapping(array $headers): array
    {
        $mapping = [];

        // Normalize headers for comparison - more thorough cleaning
        $normalizedHeaders = array_map(function($header) {
            $normalized = strtolower(trim($header));
            // Remove extra spaces and replace multiple spaces with single space
            $normalized = preg_replace('/\s+/', ' ', $normalized);
            // Remove any remaining quotes or special characters
            $normalized = trim($normalized, '"\'');
            return $normalized;
        }, $headers);

        if (app()->environment('local')) {
            \Log::info('🔍 HEADER NORMALIZATION DEBUG', [
                'original_headers' => $headers,
                'normalized_headers' => $normalizedHeaders
            ]);
        }

        // Map name field (required) - expanded variations
        $nameVariations = [
            'brand name', 'name', 'brand', 'brandname', 'brand_name',
            'manufacturer', 'company', 'make', 'vendor'
        ];
        foreach ($nameVariations as $variation) {
            $index = array_search($variation, $normalizedHeaders);
            if ($index !== false) {
                $mapping['name'] = $headers[$index];
                if (app()->environment('local')) {
                    \Log::info("✅ FOUND NAME COLUMN: '{$variation}' -> '{$headers[$index]}'");
                }
                break;
            }
        }

        // Map country field - expanded variations
        $countryVariations = [
            'country', 'origin', 'location', 'nation', 'region',
            'country of origin', 'headquarters', 'hq'
        ];
        foreach ($countryVariations as $variation) {
            $index = array_search($variation, $normalizedHeaders);
            if ($index !== false) {
                $mapping['country'] = $headers[$index];
                break;
            }
        }

        // Map website field - expanded variations
        $websiteVariations = [
            'website', 'url', 'web', 'site', 'homepage', 'web site',
            'web url', 'company website', 'official website'
        ];
        foreach ($websiteVariations as $variation) {
            $index = array_search($variation, $normalizedHeaders);
            if ($index !== false) {
                $mapping['website'] = $headers[$index];
                break;
            }
        }

        // Map logo_url field - expanded variations
        $logoVariations = [
            'logo url', 'logo_url', 'logourl', 'logo', 'image', 'icon',
            'logo image', 'brand logo', 'company logo', 'logo link'
        ];
        foreach ($logoVariations as $variation) {
            $index = array_search($variation, $normalizedHeaders);
            if ($index !== false) {
                $mapping['logo_url'] = $headers[$index];
                break;
            }
        }

        if (app()->environment('local')) {
            \Log::info('🗂️ FINAL COLUMN MAPPING', [
                'mapping' => $mapping,
                'missing_required' => !isset($mapping['name']) ? 'name column not found' : 'all required columns found'
            ]);
        }

        return $mapping;
    }
}
