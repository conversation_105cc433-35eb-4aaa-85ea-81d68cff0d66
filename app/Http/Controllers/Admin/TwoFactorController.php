<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\OtpService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;
use Inertia\Response;

class TwoFactorController extends Controller
{
    public function __construct(
        private OtpService $otpService
    ) {
        //
    }

    /**
     * Display two-factor authentication settings.
     */
    public function index(): Response
    {
        /** @var User $user */
        $user = Auth::user();
        $otpStatus = $this->otpService->getOtpStatus($user);
        $globalSettings = $this->getGlobalTwoFactorSettings();

        return Inertia::render('admin/TwoFactor/Index', [
            'user_2fa_status' => $otpStatus,
            'global_settings' => $globalSettings,
            'recent_verifications' => $this->getRecentVerifications($user),
            'active_sessions' => $this->getActiveSessions($user),
        ]);
    }

    /**
     * Enable two-factor authentication for user.
     */
    public function enable(Request $request): RedirectResponse
    {
        $user = $request->user();

        if ($user->two_factor_enabled) {
            return redirect()->back()
                ->with('error', 'Two-factor authentication is already enabled.');
        }

        $this->otpService->enableTwoFactor($user);

        return redirect()->back()
            ->with('success', 'Two-factor authentication has been enabled for your account.');
    }

    /**
     * Disable two-factor authentication for user.
     */
    public function disable(Request $request): RedirectResponse
    {
        $request->validate([
            'confirmation' => 'required|string|in:DISABLE_2FA',
        ]);

        $user = $request->user();

        if (!$user->two_factor_enabled) {
            return redirect()->back()
                ->with('error', 'Two-factor authentication is not enabled.');
        }

        $this->otpService->disableTwoFactor($user);

        return redirect()->back()
            ->with('success', 'Two-factor authentication has been disabled for your account.');
    }

    /**
     * Send test OTP code.
     */
    public function sendTestOtp(Request $request)
    {
        $user = $request->user();

        if (!$user->two_factor_enabled) {
            return response()->json([
                'error' => 'Two-factor authentication is not enabled'
            ], 400);
        }

        $sent = $this->otpService->generateAndSendOtp($user, 'test_verification');

        if ($sent) {
            return response()->json([
                'message' => 'Test verification code sent to your email',
                'otp_status' => $this->otpService->getOtpStatus($user),
            ]);
        }

        return response()->json([
            'error' => 'Failed to send test verification code'
        ], 500);
    }

    /**
     * Verify test OTP code.
     */
    public function verifyTestOtp(Request $request)
    {
        $validated = $request->validate([
            'otp_code' => 'required|string|size:6',
        ]);

        $user = $request->user();
        $verified = $this->otpService->verifyOtp($user, $validated['otp_code'], 'test_verification');

        if ($verified) {
            return response()->json([
                'message' => 'Test verification successful',
                'verified' => true,
            ]);
        }

        return response()->json([
            'error' => 'Invalid verification code',
            'verified' => false,
            'otp_status' => $this->otpService->getOtpStatus($user),
        ], 422);
    }

    /**
     * Toggle global two-factor authentication requirement.
     */
    public function toggleGlobal(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'enabled' => 'required|boolean',
        ]);

        Cache::put('admin_two_factor_enabled', $validated['enabled'], now()->addDays(30));

        $status = $validated['enabled'] ? 'enabled' : 'disabled';
        
        // Log the change
        $request->user()->logActivity(
            'admin_two_factor_toggled',
            "Global admin two-factor authentication {$status}",
            [
                'enabled' => $validated['enabled'],
                'changed_by' => $request->user()->email,
            ]
        );

        return redirect()->back()
            ->with('success', "Global two-factor authentication has been {$status}.");
    }

    /**
     * Clear OTP sessions for user.
     */
    public function clearSessions(Request $request): RedirectResponse
    {
        $user = $request->user();
        $actions = ['user_management', 'payment_approval', 'bulk_operations', 'impersonation', 'system_config'];
        
        foreach ($actions as $action) {
            $this->otpService->clearOtpSession($user, $action);
        }

        $user->logActivity(
            'otp_sessions_cleared',
            'All OTP verification sessions cleared',
            [
                'cleared_by' => $user->email,
                'ip_address' => $request->ip(),
            ]
        );

        return redirect()->back()
            ->with('success', 'All verification sessions have been cleared.');
    }

    /**
     * Get OTP verification status for API.
     */
    public function getStatus(Request $request)
    {
        $user = $request->user();
        $action = $request->get('action', 'default');

        return response()->json([
            'otp_status' => $this->otpService->getOtpStatus($user),
            'has_valid_session' => $this->otpService->hasValidOtpSession($user, $action),
            'global_enabled' => Cache::get('admin_two_factor_enabled', true),
        ]);
    }

    /**
     * Get global two-factor settings.
     */
    private function getGlobalTwoFactorSettings(): array
    {
        return [
            'enabled' => Cache::get('admin_two_factor_enabled', true),
            'otp_expiry_minutes' => OtpService::OTP_EXPIRY_MINUTES,
            'max_attempts' => OtpService::MAX_OTP_ATTEMPTS,
            'lockout_duration_minutes' => OtpService::LOCKOUT_DURATION_MINUTES,
        ];
    }

    /**
     * Get recent OTP verifications for user.
     */
    private function getRecentVerifications($user): array
    {
        return $user->activityLogs()
            ->whereIn('activity_type', ['otp_verification_success', 'otp_verification_failed'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->id,
                    'type' => $log->activity_type,
                    'action' => $log->metadata['action'] ?? 'unknown',
                    'success' => $log->activity_type === 'otp_verification_success',
                    'ip_address' => $log->ip_address,
                    'created_at' => $log->created_at,
                ];
            })
            ->toArray();
    }

    /**
     * Get active OTP sessions for user.
     */
    private function getActiveSessions($user): array
    {
        $actions = ['user_management', 'payment_approval', 'bulk_operations', 'impersonation', 'system_config'];
        $sessions = [];

        foreach ($actions as $action) {
            $cacheKey = "otp_verified:{$user->id}:{$action}";
            $sessionData = Cache::get($cacheKey);
            
            if ($sessionData) {
                $sessions[] = [
                    'action' => $action,
                    'verified_at' => $sessionData['verified_at'] ?? null,
                    'expires_at' => $sessionData['expires_at'] ?? null,
                ];
            }
        }

        return $sessions;
    }
}
