<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PricingPlan;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class PricingPlanController extends Controller
{
    /**
     * Display a listing of pricing plans.
     */
    public function index(): Response
    {
        $pricingPlans = PricingPlan::withCount('subscriptions')
            ->ordered()
            ->get();

        return Inertia::render('admin/pricing-plans/Index', [
            'pricingPlans' => $pricingPlans,
        ]);
    }

    /**
     * Show the form for creating a new pricing plan.
     */
    public function create(): Response
    {
        return Inertia::render('admin/pricing-plans/Create');
    }

    /**
     * Store a newly created pricing plan.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:pricing_plans,name',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'interval' => 'required|in:month,year',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'search_limit' => 'required|integer|min:-1',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'is_popular' => 'boolean',
            'sort_order' => 'required|integer|min:0',
            'metadata' => 'nullable|array',
            'paddle_price_id_monthly' => 'nullable|string',
            'paddle_price_id_yearly' => 'nullable|string',
            'paddle_product_id' => 'nullable|string',
            'online_payment_enabled' => 'boolean',
            'offline_payment_enabled' => 'boolean',
        ]);

        // Ensure only one default plan
        if ($validated['is_default'] ?? false) {
            PricingPlan::where('is_default', true)->update(['is_default' => false]);
        }

        PricingPlan::create($validated);

        return redirect()->route('admin.pricing-plans.index')
            ->with('success', 'Pricing plan created successfully.');
    }

    /**
     * Display the specified pricing plan.
     */
    public function show(PricingPlan $pricingPlan): Response
    {
        $pricingPlan->load(['subscriptions.user']);

        return Inertia::render('admin/pricing-plans/Show', [
            'pricingPlan' => $pricingPlan,
        ]);
    }

    /**
     * Show the form for editing the specified pricing plan.
     */
    public function edit(PricingPlan $pricingPlan): Response
    {
        return Inertia::render('admin/pricing-plans/Edit', [
            'pricingPlan' => $pricingPlan,
        ]);
    }

    /**
     * Update the specified pricing plan.
     */
    public function update(Request $request, PricingPlan $pricingPlan): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:pricing_plans,name,' . $pricingPlan->id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'interval' => 'required|in:month,year',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'search_limit' => 'required|integer|min:-1',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'is_popular' => 'boolean',
            'sort_order' => 'required|integer|min:0',
            'metadata' => 'nullable|array',
            'paddle_price_id_monthly' => 'nullable|string',
            'paddle_price_id_yearly' => 'nullable|string',
            'paddle_product_id' => 'nullable|string',
            'online_payment_enabled' => 'boolean',
            'offline_payment_enabled' => 'boolean',
        ]);

        // Ensure only one default plan
        if ($validated['is_default'] ?? false) {
            PricingPlan::where('is_default', true)
                ->where('id', '!=', $pricingPlan->id)
                ->update(['is_default' => false]);
        }

        $pricingPlan->update($validated);

        return redirect()->route('admin.pricing-plans.index')
            ->with('success', 'Pricing plan updated successfully.');
    }

    /**
     * Remove the specified pricing plan.
     */
    public function destroy(PricingPlan $pricingPlan): RedirectResponse
    {
        // Check if plan has active subscriptions
        $activeSubscriptions = $pricingPlan->subscriptions()->where('status', 'active')->count();

        if ($activeSubscriptions > 0) {
            return redirect()->route('admin.pricing-plans.index')
                ->with('error', 'Cannot delete pricing plan with active subscriptions.');
        }

        $pricingPlan->delete();

        return redirect()->route('admin.pricing-plans.index')
            ->with('success', 'Pricing plan deleted successfully.');
    }

    /**
     * Toggle the active status of a pricing plan.
     */
    public function toggleActive(PricingPlan $pricingPlan): RedirectResponse
    {
        $pricingPlan->update(['is_active' => !$pricingPlan->is_active]);

        $status = $pricingPlan->is_active ? 'activated' : 'deactivated';

        return redirect()->route('admin.pricing-plans.index')
            ->with('success', "Pricing plan {$status} successfully.");
    }

    /**
     * Duplicate a pricing plan.
     */
    public function duplicate(PricingPlan $pricingPlan): RedirectResponse
    {
        $newPlan = $pricingPlan->replicate();
        $newPlan->name = $pricingPlan->name . '_copy';
        $newPlan->display_name = $pricingPlan->display_name . ' (Copy)';
        $newPlan->is_default = false;
        $newPlan->is_active = false;
        $newPlan->save();

        return redirect()->route('admin.pricing-plans.edit', $newPlan)
            ->with('success', 'Pricing plan duplicated successfully. Please review and update the details.');
    }
}
