<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

class PaddleConfigController extends Controller
{
    /**
     * Show the Paddle configuration form.
     */
    public function configure()
    {
        // Load current configuration from config and env
        $paddleConfig = [
            'id' => 1,
            'is_enabled' => config('paddle.enabled', false),
            'environment' => config('paddle.environment', 'sandbox'),
            'api_key' => config('paddle.api_key', ''),
            'client_token' => config('paddle.client_token', ''),
            'webhook_secret' => config('paddle.webhook_secret', ''),
            'vendor_id' => config('paddle.vendor_id', ''),
            'default_currency' => config('paddle.default_currency', 'USD'),
            'supported_currencies' => config('paddle.supported_currencies', ['USD', 'EUR', 'GBP']),
            'webhook_url' => url('/webhooks/paddle'),
            'logging_enabled' => config('paddle.logging.enabled', true),
            'debug_mode' => config('paddle.debug_mode', false),
        ];

        $webhookUrl = url('/webhooks/paddle');

        // Check if configuration is complete
        $isConfigured = !empty($paddleConfig['api_key']) &&
                       !empty($paddleConfig['client_token']) &&
                       !empty($paddleConfig['webhook_secret']);

        return Inertia::render('admin/payment-gateways/paddle/Configure', [
            'paddleConfig' => $paddleConfig,
            'webhookUrl' => $webhookUrl,
            'isConfigured' => $isConfigured
        ]);
    }

    /**
     * Store the Paddle configuration.
     */
    public function store(Request $request)
    {
        $request->validate([
            'is_enabled' => 'required|boolean',
            'environment' => 'required|in:sandbox,production',
            'api_key' => 'required|string|max:255',
            'client_token' => 'required|string|max:255',
            'webhook_secret' => 'nullable|string|max:255',
            'vendor_id' => 'nullable|string|max:255',
            'default_currency' => 'required|string|in:USD,EUR,GBP',
            'supported_currencies' => 'required|array',
            'supported_currencies.*' => 'string|in:USD,EUR,GBP,CAD,AUD,JPY,CHF,SEK,NOK,DKK',
            'logging_enabled' => 'required|boolean',
            'debug_mode' => 'required|boolean',
        ]);

        try {
            // Log the configuration update
            Log::info('Paddle configuration update started', [
                'environment' => $request->environment,
                'is_enabled' => $request->is_enabled,
                'has_api_key' => !empty($request->api_key),
                'has_client_token' => !empty($request->client_token),
                'debug_mode' => $request->debug_mode,
            ]);

            // In a production environment, you would save these to a secure configuration store
            // For now, we'll update the runtime config and log the values for manual .env update

            config([
                'paddle.enabled' => $request->is_enabled,
                'paddle.environment' => $request->environment,
                'paddle.api_key' => $request->api_key,
                'paddle.client_token' => $request->client_token,
                'paddle.webhook_secret' => $request->webhook_secret,
                'paddle.vendor_id' => $request->vendor_id,
                'paddle.default_currency' => $request->default_currency,
                'paddle.supported_currencies' => $request->supported_currencies,
                'paddle.logging.enabled' => $request->logging_enabled,
                'paddle.debug_mode' => $request->debug_mode,
            ]);

            // Log configuration for manual .env update
            Log::info('Paddle configuration to be updated in .env:', [
                'PADDLE_ENABLED' => $request->is_enabled ? 'true' : 'false',
                'PADDLE_ENVIRONMENT' => $request->environment,
                'PADDLE_API_KEY' => $request->api_key,
                'PADDLE_CLIENT_TOKEN' => $request->client_token,
                'PADDLE_WEBHOOK_SECRET' => $request->webhook_secret,
                'PADDLE_VENDOR_ID' => $request->vendor_id,
                'PADDLE_DEFAULT_CURRENCY' => $request->default_currency,
                'PADDLE_LOGGING_ENABLED' => $request->logging_enabled ? 'true' : 'false',
                'PADDLE_DEBUG_MODE' => $request->debug_mode ? 'true' : 'false',
            ]);

            return redirect()
                ->route('admin.payment-gateways.paddle.configure')
                ->with('success', 'Paddle configuration saved successfully! Please update your .env file with the logged values.');

        } catch (\Exception $e) {
            Log::error('Failed to save Paddle configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()
                ->route('admin.payment-gateways.paddle.configure')
                ->with('error', 'Failed to save configuration: ' . $e->getMessage());
        }
    }

    /**
     * Test the Paddle API connection.
     */
    public function test(Request $request)
    {
        $request->validate([
            'api_key' => 'required|string',
            'client_token' => 'required|string',
            'environment' => 'required|in:sandbox,production',
        ]);

        try {
            Log::info('Testing Paddle API connection', [
                'environment' => $request->environment,
                'has_api_key' => !empty($request->api_key),
                'has_client_token' => !empty($request->client_token),
            ]);

            // Determine the API base URL based on environment
            $baseUrl = $request->environment === 'production'
                ? 'https://api.paddle.com'
                : 'https://sandbox-api.paddle.com';

            // Test API connection by fetching event types (recommended by Paddle for auth testing)
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $request->api_key,
                'Content-Type' => 'application/json',
            ])->get($baseUrl . '/event-types');

            if ($response->successful()) {
                $data = $response->json();

                Log::info('Paddle API connection test successful', [
                    'status' => $response->status(),
                    'event_types_count' => count($data['data'] ?? [])
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Connection successful! API credentials are valid.',
                    'details' => [
                        'event_types_count' => count($data['data'] ?? []),
                        'environment' => $request->environment,
                        'status' => 'connected',
                        'api_url' => $baseUrl
                    ]
                ]);
            } else {
                Log::warning('Paddle API connection test failed', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);

                $errorData = $response->json();
                $errorMessage = 'Connection failed. Please check your API credentials.';

                // Extract specific error message from Paddle API response
                if (isset($errorData['error']['detail'])) {
                    $errorMessage = $errorData['error']['detail'];
                } elseif (isset($errorData['error']['message'])) {
                    $errorMessage = $errorData['error']['message'];
                } elseif ($response->status() === 404) {
                    $errorMessage = 'API endpoint not found. Please check the API URL.';
                } elseif ($response->status() === 401 || $response->status() === 403) {
                    $errorMessage = 'Invalid API credentials. Please check your API key.';
                }

                return response()->json([
                    'success' => false,
                    'message' => $errorMessage,
                    'details' => [
                        'status' => $response->status(),
                        'error' => $errorMessage,
                        'api_url' => $baseUrl . '/event-types',
                        'raw_response' => $response->body()
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Paddle API connection test error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ]);
        }
    }
}
