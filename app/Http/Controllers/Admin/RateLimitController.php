<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Middleware\AdminRateLimit;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Inertia\Inertia;
use Inertia\Response;

class RateLimitController extends Controller
{
    /**
     * Display rate limiting dashboard.
     */
    public function index(): Response
    {
        $isEnabled = Cache::get('admin_rate_limiting_enabled', true);
        $configs = Cache::get('admin_rate_limit_configs', $this->getDefaultConfigs());
        
        // Get current rate limit status for the authenticated user
        $user = auth()->user();
        $userStatus = [];
        
        if ($user) {
            foreach (array_keys($configs) as $action) {
                $userStatus[$action] = AdminRateLimit::getRateLimitStatus(
                    $user->id, 
                    $action, 
                    request()->ip()
                );
            }
        }

        return Inertia::render('admin/RateLimit/Index', [
            'is_enabled' => $isEnabled,
            'configs' => $configs,
            'user_status' => $userStatus,
            'statistics' => $this->getRateLimitStatistics(),
        ]);
    }

    /**
     * Toggle rate limiting on/off.
     */
    public function toggle(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'enabled' => 'required|boolean',
        ]);

        Cache::put('admin_rate_limiting_enabled', $validated['enabled'], now()->addDays(30));

        $status = $validated['enabled'] ? 'enabled' : 'disabled';
        
        // Log the change
        $request->user()->logActivity(
            'admin_rate_limiting_toggled',
            "Admin rate limiting {$status}",
            [
                'enabled' => $validated['enabled'],
                'changed_by' => $request->user()->email,
            ]
        );

        return redirect()->back()
            ->with('success', "Rate limiting has been {$status}.");
    }

    /**
     * Update rate limit configurations.
     */
    public function updateConfigs(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'configs' => 'required|array',
            'configs.*.max_attempts' => 'required|integer|min:1|max:1000',
            'configs.*.decay_seconds' => 'required|integer|min:60|max:3600',
            'configs.*.description' => 'required|string|max:255',
        ]);

        // Validate that all required actions are present
        $requiredActions = array_keys($this->getDefaultConfigs());
        $providedActions = array_keys($validated['configs']);
        
        if (array_diff($requiredActions, $providedActions)) {
            return redirect()->back()
                ->with('error', 'All rate limit configurations must be provided.');
        }

        Cache::put('admin_rate_limit_configs', $validated['configs'], now()->addDays(30));

        // Log the change
        $request->user()->logActivity(
            'admin_rate_limit_configs_updated',
            'Admin rate limit configurations updated',
            [
                'configs' => $validated['configs'],
                'changed_by' => $request->user()->email,
            ]
        );

        return redirect()->back()
            ->with('success', 'Rate limit configurations updated successfully.');
    }

    /**
     * Reset configurations to defaults.
     */
    public function resetConfigs(Request $request): RedirectResponse
    {
        $defaultConfigs = $this->getDefaultConfigs();
        Cache::put('admin_rate_limit_configs', $defaultConfigs, now()->addDays(30));

        // Log the change
        $request->user()->logActivity(
            'admin_rate_limit_configs_reset',
            'Admin rate limit configurations reset to defaults',
            [
                'reset_by' => $request->user()->email,
            ]
        );

        return redirect()->back()
            ->with('success', 'Rate limit configurations reset to defaults.');
    }

    /**
     * Clear rate limits for a specific user.
     */
    public function clearUserLimits(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        AdminRateLimit::clearUserRateLimits($validated['user_id']);

        $user = \App\Models\User::find($validated['user_id']);
        
        // Log the action
        $request->user()->logActivity(
            'admin_rate_limits_cleared',
            "Rate limits cleared for user: {$user->name}",
            [
                'target_user_id' => $validated['user_id'],
                'target_user_email' => $user->email,
                'cleared_by' => $request->user()->email,
            ]
        );

        return redirect()->back()
            ->with('success', "Rate limits cleared for user: {$user->name}");
    }

    /**
     * Get rate limit statistics.
     */
    public function getStatistics()
    {
        $stats = $this->getRateLimitStatistics();
        return response()->json($stats);
    }

    /**
     * Get current rate limit status for a user.
     */
    public function getUserStatus(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $configs = Cache::get('admin_rate_limit_configs', $this->getDefaultConfigs());
        $userStatus = [];
        
        foreach (array_keys($configs) as $action) {
            $userStatus[$action] = AdminRateLimit::getRateLimitStatus(
                $validated['user_id'], 
                $action, 
                $request->ip()
            );
        }

        return response()->json($userStatus);
    }

    /**
     * Get default rate limit configurations.
     */
    private function getDefaultConfigs(): array
    {
        return [
            'default' => [
                'max_attempts' => 60,
                'decay_seconds' => 60,
                'description' => 'General admin actions'
            ],
            'user_management' => [
                'max_attempts' => 30,
                'decay_seconds' => 60,
                'description' => 'User approval, suspension, etc.'
            ],
            'payment_approval' => [
                'max_attempts' => 20,
                'decay_seconds' => 60,
                'description' => 'Payment request approvals'
            ],
            'bulk_operations' => [
                'max_attempts' => 10,
                'decay_seconds' => 300,
                'description' => 'Bulk approve, bulk import, etc.'
            ],
            'impersonation' => [
                'max_attempts' => 5,
                'decay_seconds' => 300,
                'description' => 'User impersonation actions'
            ],
            'data_export' => [
                'max_attempts' => 10,
                'decay_seconds' => 300,
                'description' => 'Data export operations'
            ],
            'email_config' => [
                'max_attempts' => 10,
                'decay_seconds' => 60,
                'description' => 'Email configuration changes'
            ],
            'system_config' => [
                'max_attempts' => 15,
                'decay_seconds' => 300,
                'description' => 'System configuration changes'
            ]
        ];
    }

    /**
     * Get rate limit statistics.
     */
    private function getRateLimitStatistics(): array
    {
        // Get recent rate limit violations from activity logs
        $recentViolations = \App\Models\UserActivityLog::where('activity_type', 'admin_rate_limit_exceeded')
            ->where('created_at', '>=', now()->subDays(7))
            ->with('user:id,name,email')
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        $violationsByAction = $recentViolations->groupBy(function ($log) {
            return $log->metadata['action'] ?? 'unknown';
        })->map->count();

        $violationsByUser = $recentViolations->groupBy('user_id')
            ->map(function ($violations) {
                return [
                    'user' => $violations->first()->user,
                    'count' => $violations->count(),
                    'last_violation' => $violations->first()->created_at,
                ];
            })
            ->sortByDesc('count')
            ->take(10)
            ->values();

        return [
            'total_violations_7_days' => $recentViolations->count(),
            'violations_by_action' => $violationsByAction,
            'top_violating_users' => $violationsByUser,
            'recent_violations' => $recentViolations->take(10)->map(function ($log) {
                return [
                    'id' => $log->id,
                    'user' => $log->user,
                    'action' => $log->metadata['action'] ?? 'unknown',
                    'ip_address' => $log->ip_address,
                    'created_at' => $log->created_at,
                ];
            }),
        ];
    }
}
