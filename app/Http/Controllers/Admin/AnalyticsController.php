<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AnalyticsController extends Controller
{
    public function __construct(
        private AnalyticsService $analyticsService
    ) {
        //
    }

    /**
     * Display the analytics dashboard.
     */
    public function index(Request $request): Response
    {
        $days = $request->get('days', 30);
        $analytics = $this->analyticsService->getUserEngagementAnalytics($days);
        
        return Inertia::render('admin/Analytics/Index', [
            'analytics' => $analytics,
            'days' => $days,
        ]);
    }

    /**
     * Display user behavior analytics.
     */
    public function userBehavior(Request $request): Response
    {
        $userId = $request->get('user_id');
        $days = $request->get('days', 30);
        
        $userBehavior = null;
        if ($userId) {
            $userBehavior = $this->analyticsService->getUserBehaviorPatterns($userId, $days);
        }
        
        // Get list of users for dropdown
        $users = \App\Models\User::select('id', 'name', 'email')
            ->orderBy('name')
            ->get();
        
        return Inertia::render('admin/Analytics/UserBehavior', [
            'userBehavior' => $userBehavior,
            'users' => $users,
            'selectedUserId' => $userId,
            'days' => $days,
        ]);
    }

    /**
     * Display system performance metrics.
     */
    public function systemPerformance(Request $request): Response
    {
        $days = $request->get('days', 7);
        $metrics = $this->analyticsService->getSystemPerformanceMetrics($days);
        
        return Inertia::render('admin/Analytics/SystemPerformance', [
            'metrics' => $metrics,
            'days' => $days,
        ]);
    }

    /**
     * Export analytics data as CSV.
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'engagement');
        $days = $request->get('days', 30);
        
        switch ($type) {
            case 'engagement':
                return $this->exportEngagementData($days);
            case 'user_behavior':
                return $this->exportUserBehaviorData($days);
            case 'system_performance':
                return $this->exportSystemPerformanceData($days);
            default:
                return response()->json(['error' => 'Invalid export type'], 400);
        }
    }

    /**
     * Export engagement analytics data.
     */
    private function exportEngagementData(int $days)
    {
        $analytics = $this->analyticsService->getUserEngagementAnalytics($days);
        
        $csvData = [];
        $csvData[] = ['Metric', 'Value'];
        
        // Overview data
        foreach ($analytics['overview'] as $key => $value) {
            $csvData[] = [ucwords(str_replace('_', ' ', $key)), $value];
        }
        
        // Feature usage data
        $csvData[] = ['', '']; // Empty row
        $csvData[] = ['Feature Usage', ''];
        $csvData[] = ['Feature', 'Usage Count', 'Unique Users'];
        
        foreach ($analytics['feature_usage']['feature_usage'] as $feature) {
            $csvData[] = [
                ucwords(str_replace('_', ' ', $feature['activity_type'])),
                $feature['usage_count'],
                $feature['unique_users']
            ];
        }
        
        return $this->generateCsvResponse($csvData, "engagement_analytics_{$days}days.csv");
    }

    /**
     * Export user behavior data.
     */
    private function exportUserBehaviorData(int $days)
    {
        // Get all users with activity in the period
        $users = \App\Models\User::whereHas('activityLogs', function ($query) use ($days) {
            $query->where('created_at', '>=', now()->subDays($days));
        })->with(['activityLogs' => function ($query) use ($days) {
            $query->where('created_at', '>=', now()->subDays($days));
        }])->get();
        
        $csvData = [];
        $csvData[] = ['User ID', 'Name', 'Email', 'Total Activities', 'Login Count', 'Search Count', 'Last Activity'];
        
        foreach ($users as $user) {
            $activities = $user->activityLogs;
            $loginCount = $activities->where('activity_type', 'login')->count();
            $searchCount = $activities->where('activity_type', 'search')->count();
            $lastActivity = $activities->sortByDesc('created_at')->first();
            
            $csvData[] = [
                $user->id,
                $user->name,
                $user->email,
                $activities->count(),
                $loginCount,
                $searchCount,
                $lastActivity ? $lastActivity->created_at->format('Y-m-d H:i:s') : 'Never'
            ];
        }
        
        return $this->generateCsvResponse($csvData, "user_behavior_{$days}days.csv");
    }

    /**
     * Export system performance data.
     */
    private function exportSystemPerformanceData(int $days)
    {
        $metrics = $this->analyticsService->getSystemPerformanceMetrics($days);
        
        $csvData = [];
        $csvData[] = ['Metric', 'Value'];
        $csvData[] = ['Total Activities', $metrics['total_activities']];
        $csvData[] = ['Error Rate (%)', $metrics['error_rate']];
        $csvData[] = ['Avg Daily Activities', $metrics['avg_daily_activities']];
        
        $csvData[] = ['', '']; // Empty row
        $csvData[] = ['Peak Usage Hours', ''];
        $csvData[] = ['Hour', 'Activity Count'];
        
        foreach ($metrics['peak_usage_hours'] as $hour) {
            $csvData[] = [$hour['hour'] . ':00', $hour['activity_count']];
        }
        
        return $this->generateCsvResponse($csvData, "system_performance_{$days}days.csv");
    }

    /**
     * Generate CSV response.
     */
    private function generateCsvResponse(array $data, string $filename)
    {
        $output = fopen('php://temp', 'r+');
        
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return response($csv)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    /**
     * Get real-time analytics data for dashboard widgets.
     */
    public function realTimeData(Request $request)
    {
        $type = $request->get('type', 'overview');
        
        switch ($type) {
            case 'overview':
                return response()->json([
                    'active_users_today' => \App\Models\User::where('last_login_at', '>=', now()->startOfDay())->count(),
                    'searches_today' => \App\Models\UserActivityLog::where('activity_type', 'search')
                        ->where('created_at', '>=', now()->startOfDay())->count(),
                    'new_users_today' => \App\Models\User::where('created_at', '>=', now()->startOfDay())->count(),
                    'payments_today' => \App\Models\PaymentRequest::where('status', 'approved')
                        ->where('approved_at', '>=', now()->startOfDay())->count(),
                ]);
                
            case 'hourly_activity':
                $hourlyData = \App\Models\UserActivityLog::selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
                    ->where('created_at', '>=', now()->startOfDay())
                    ->groupBy('hour')
                    ->orderBy('hour')
                    ->get();
                    
                return response()->json($hourlyData);
                
            default:
                return response()->json(['error' => 'Invalid data type'], 400);
        }
    }
}
