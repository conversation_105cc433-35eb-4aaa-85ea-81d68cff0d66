<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserNotification;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class UserNotificationController extends Controller
{
    /**
     * Display a listing of notifications.
     */
    public function index(Request $request): Response
    {
        $query = UserNotification::query()
            ->with(['user', 'sentBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        if ($request->filled('read_status')) {
            if ($request->get('read_status') === 'read') {
                $query->whereNotNull('read_at');
            } elseif ($request->get('read_status') === 'unread') {
                $query->whereNull('read_at');
            }
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->get('date_to'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $notifications = $query->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total_notifications' => UserNotification::count(),
            'unread_notifications' => UserNotification::whereNull('read_at')->count(),
            'notifications_today' => UserNotification::whereDate('created_at', today())->count(),
            'notifications_this_week' => UserNotification::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
        ];

        // Get users for filter dropdown
        $users = User::select('id', 'name', 'email')->orderBy('name')->get();

        return Inertia::render('admin/Notifications/Index', [
            'notifications' => $notifications,
            'stats' => $stats,
            'users' => $users,
            'filters' => $request->only([
                'user_id', 'type', 'read_status', 'date_from', 'date_to', 
                'sort_by', 'sort_order'
            ]),
        ]);
    }

    /**
     * Show the form for creating a new notification.
     */
    public function create(): Response
    {
        $users = User::select('id', 'name', 'email')->orderBy('name')->get();
        
        return Inertia::render('admin/Notifications/Create', [
            'users' => $users,
        ]);
    }

    /**
     * Store a newly created notification.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'type' => 'required|in:info,warning,success,error,announcement',
        ]);

        $admin = $request->user();
        $createdCount = 0;

        foreach ($validated['user_ids'] as $userId) {
            // Temporary fix: map 'announcement' to 'info' until migration is run
            $notificationType = $validated['type'] === 'announcement' ? 'info' : $validated['type'];

            UserNotification::create([
                'user_id' => $userId,
                'title' => $validated['title'],
                'message' => $validated['message'],
                'type' => $notificationType,
                'sent_by' => $admin->id,
            ]);
            $createdCount++;
        }

        // Log the activity
        $admin->logActivity(
            'notifications_sent',
            'Admin sent notifications to users',
            [
                'notification_count' => $createdCount,
                'user_count' => count($validated['user_ids']),
                'title' => $validated['title'],
                'type' => $validated['type'],
            ],
            $admin
        );

        return redirect()->route('admin.notifications.index')
            ->with('success', "Successfully sent notification to {$createdCount} users.");
    }

    /**
     * Display the specified notification.
     */
    public function show(UserNotification $notification): Response
    {
        $notification->load(['user', 'sentBy']);

        return Inertia::render('admin/Notifications/Show', [
            'notification' => $notification,
        ]);
    }

    /**
     * Remove the specified notification.
     */
    public function destroy(UserNotification $notification): RedirectResponse
    {
        $notification->delete();

        return redirect()->route('admin.notifications.index')
            ->with('success', 'Notification deleted successfully.');
    }

    /**
     * Send bulk notifications.
     */
    public function bulkSend(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'recipient_type' => 'required|in:all,active,premium,pending',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'type' => 'required|in:info,warning,success,error,announcement',
        ]);

        $admin = $request->user();
        
        // Build user query based on recipient type
        $userQuery = User::query();
        
        switch ($validated['recipient_type']) {
            case 'active':
                $userQuery->where('status', 'active')->where('approval_status', 'approved');
                break;
            case 'premium':
                $userQuery->where('subscription_plan', 'premium');
                break;
            case 'pending':
                $userQuery->where('approval_status', 'pending');
                break;
            case 'all':
                // No additional filters
                break;
        }

        $users = $userQuery->get();
        $createdCount = 0;

        foreach ($users as $user) {
            // Temporary fix: map 'announcement' to 'info' until migration is run
            $notificationType = $validated['type'] === 'announcement' ? 'info' : $validated['type'];

            UserNotification::create([
                'user_id' => $user->id,
                'title' => $validated['title'],
                'message' => $validated['message'],
                'type' => $notificationType,
                'sent_by' => $admin->id,
            ]);
            $createdCount++;
        }

        // Log the activity
        $admin->logActivity(
            'bulk_notifications_sent',
            'Admin sent bulk notifications',
            [
                'notification_count' => $createdCount,
                'recipient_type' => $validated['recipient_type'],
                'title' => $validated['title'],
                'type' => $validated['type'],
            ],
            $admin
        );

        return redirect()->route('admin.notifications.index')
            ->with('success', "Successfully sent notification to {$createdCount} users.");
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(UserNotification $notification): RedirectResponse
    {
        $notification->markAsRead();

        return redirect()->back()
            ->with('success', 'Notification marked as read.');
    }

    /**
     * Mark notification as unread.
     */
    public function markAsUnread(UserNotification $notification): RedirectResponse
    {
        $notification->markAsUnread();

        return redirect()->back()
            ->with('success', 'Notification marked as unread.');
    }
}
