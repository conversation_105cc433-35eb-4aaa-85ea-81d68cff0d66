<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class UserActivityController extends Controller
{
    /**
     * Display a listing of user activity logs.
     */
    public function index(Request $request): Response
    {
        $query = UserActivityLog::query()
            ->with(['user', 'performedBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        if ($request->filled('activity_type')) {
            $query->where('activity_type', $request->get('activity_type'));
        }

        if ($request->filled('performed_by')) {
            $query->where('performed_by', $request->get('performed_by'));
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->get('date_to'));
        }

        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $activities = $query->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total_activities' => UserActivityLog::count(),
            'activities_today' => UserActivityLog::whereDate('created_at', today())->count(),
            'activities_this_week' => UserActivityLog::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'unique_users_today' => UserActivityLog::whereDate('created_at', today())
                ->distinct('user_id')->count('user_id'),
        ];

        // Get activity types for filter
        $activityTypes = UserActivityLog::select('activity_type')
            ->distinct()
            ->orderBy('activity_type')
            ->pluck('activity_type');

        // Get users for filter dropdown
        $users = User::select('id', 'name', 'email')->orderBy('name')->get();

        return Inertia::render('admin/Activities/Index', [
            'activities' => $activities,
            'stats' => $stats,
            'activityTypes' => $activityTypes,
            'users' => $users,
            'filters' => $request->only([
                'user_id', 'activity_type', 'performed_by', 'date_from', 'date_to',
                'search', 'sort_by', 'sort_order'
            ]),
        ]);
    }

    /**
     * Display the specified activity log.
     */
    public function show(UserActivityLog $activity): Response
    {
        $activity->load(['user', 'performedBy']);

        return Inertia::render('admin/Activities/Show', [
            'activity' => $activity,
        ]);
    }

    /**
     * Export activity logs.
     */
    public function export(Request $request)
    {
        $query = UserActivityLog::query()
            ->with(['user', 'performedBy']);

        // Apply same filters as index
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        if ($request->filled('activity_type')) {
            $query->where('activity_type', $request->get('activity_type'));
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->get('date_to'));
        }

        $activities = $query->orderBy('created_at', 'desc')->get();

        $csvData = [];
        $csvData[] = ['Date', 'User', 'Email', 'Activity Type', 'Description', 'IP Address', 'Performed By'];

        foreach ($activities as $activity) {
            $csvData[] = [
                $activity->created_at->format('Y-m-d H:i:s'),
                $activity->user->name ?? 'N/A',
                $activity->user->email ?? 'N/A',
                $activity->activity_type,
                $activity->description,
                $activity->ip_address ?? 'N/A',
                $activity->performedBy->name ?? 'System',
            ];
        }

        $filename = 'user_activities_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
