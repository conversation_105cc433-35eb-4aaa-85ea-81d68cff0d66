<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Category::with('parent', 'children')
            ->withCount('parts');

        // Apply search
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%")
                  ->orWhereHas('parent', function ($parentQuery) use ($search) {
                      $parentQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Apply parent filter
        if ($request->has('parent_id')) {
            $parentId = $request->get('parent_id');
            if ($parentId === 'root') {
                $query->whereNull('parent_id');
            } elseif ($parentId === 'child') {
                $query->whereNotNull('parent_id');
            } elseif ($parentId !== 'all') {
                $query->where('parent_id', $parentId);
            }
        }

        // Apply status filter
        if ($request->has('status')) {
            $status = $request->get('status');
            if ($status !== 'all') {
                $query->where('is_active', $status === 'active');
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');

        $allowedSorts = ['name', 'sort_order', 'created_at', 'parts_count'];
        if (in_array($sortBy, $allowedSorts)) {
            if ($sortBy === 'parts_count') {
                $query->orderBy('parts_count', $sortOrder);
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Add secondary sort for consistency
            if ($sortBy !== 'name') {
                $query->orderBy('name', 'asc');
            }
        } else {
            $query->orderBy('sort_order', 'asc')->orderBy('name', 'asc');
        }

        $categories = $query->paginate(15)->withQueryString();

        // Get filter options
        $parentCategories = Category::whereNull('parent_id')
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('admin/Categories/Index', [
            'categories' => $categories,
            'filters' => [
                'parentCategories' => $parentCategories,
            ],
            'queryParams' => $request->only(['search', 'parent_id', 'status', 'sort_by', 'sort_order', 'view']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $parentCategories = Category::whereNull('parent_id')
            ->active()
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/Categories/Create', [
            'parentCategories' => $parentCategories,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'sort_order' => 'integer|min:0',
            'is_active' => 'boolean',
        ]);

        Category::create($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        $category->load('parent', 'children', 'parts');

        return Inertia::render('admin/Categories/Show', [
            'category' => $category,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        $parentCategories = Category::whereNull('parent_id')
            ->where('id', '!=', $category->id)
            ->active()
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/Categories/Edit', [
            'category' => $category,
            'parentCategories' => $parentCategories,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('categories')->ignore($category->id)],
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'sort_order' => 'integer|min:0',
            'is_active' => 'boolean',
        ]);

        $category->update($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        // Check if category has parts or children
        if ($category->parts()->count() > 0) {
            return back()->with('error', 'Cannot delete category with associated parts.');
        }

        if ($category->children()->count() > 0) {
            return back()->with('error', 'Cannot delete category with subcategories.');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Export categories to CSV.
     */
    public function export(Request $request)
    {
        $query = Category::with('parent', 'children')
            ->withCount('parts');

        // Apply the same filters as the index method
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%")
                  ->orWhereHas('parent', function ($parentQuery) use ($search) {
                      $parentQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Apply parent filter
        if ($parentId = $request->get('parent_id')) {
            if ($parentId === 'root') {
                $query->whereNull('parent_id');
            } elseif ($parentId === 'child') {
                $query->whereNotNull('parent_id');
            } elseif (is_numeric($parentId)) {
                $query->where('parent_id', $parentId);
            }
        }

        // Apply status filter
        if ($status = $request->get('status')) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Handle selected IDs for bulk export
        if ($request->has('ids')) {
            $ids = $request->get('ids');
            if (is_array($ids) && !empty($ids)) {
                $query->whereIn('id', $ids);
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');

        $allowedSorts = ['name', 'sort_order', 'created_at', 'parts_count'];
        if (in_array($sortBy, $allowedSorts)) {
            if ($sortBy === 'parts_count') {
                $query->orderBy('parts_count', $sortOrder);
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Add secondary sort for consistency
            if ($sortBy !== 'name') {
                $query->orderBy('name', 'asc');
            }
        } else {
            $query->orderBy('sort_order', 'asc')->orderBy('name', 'asc');
        }

        $categories = $query->get();

        // Prepare CSV data
        $csvData = [];
        $csvData[] = ['Category Name', 'Description', 'Parent Category', 'Sort Order', 'Status', 'Parts Count', 'Subcategories Count', 'Created Date'];

        foreach ($categories as $category) {
            $csvData[] = [
                $category->name ?? '',
                $category->description ?? '',
                $category->parent ? $category->parent->name : '',
                $category->sort_order ?? 0,
                $category->is_active ? 'Active' : 'Inactive',
                $category->parts_count ?? 0,
                $category->children->count() ?? 0,
                $category->created_at ? $category->created_at->format('Y-m-d H:i:s') : '',
            ];
        }

        $filename = 'categories_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');

            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($csvData as $row) {
                fputcsv($file, $row, ',', '"', '\\');
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    /**
     * Download CSV template for categories import.
     */
    public function downloadTemplate()
    {
        $csvData = [];
        $csvData[] = ['name', 'description', 'parent_category', 'sort_order', 'is_active'];
        $csvData[] = ['# IMPORT TEMPLATE: Use this template to import categories'];
        $csvData[] = ['# name: Required - the category name (must be unique)'];
        $csvData[] = ['# description: Optional - category description'];
        $csvData[] = ['# parent_category: Optional - name of parent category (leave empty for root categories)'];
        $csvData[] = ['# sort_order: Required - numeric sort order (0-999)'];
        $csvData[] = ['# is_active: Required - Active or Inactive'];
        $csvData[] = ['# NOTE: Parent categories must be imported before child categories'];
        $csvData[] = ['# NOTE: If parent_category is specified, it must already exist'];
        $csvData[] = ['Display', 'Mobile phone display components', '', '10', 'Active'];
        $csvData[] = ['Battery', 'Mobile phone battery components', '', '20', 'Active'];
        $csvData[] = ['Camera', 'Mobile phone camera components', '', '30', 'Active'];
        $csvData[] = ['LCD Display', 'LCD display screens', 'Display', '11', 'Active'];
        $csvData[] = ['OLED Display', 'OLED display screens', 'Display', '12', 'Active'];
        $csvData[] = ['Front Camera', 'Front-facing camera modules', 'Camera', '31', 'Active'];
        $csvData[] = ['Rear Camera', 'Rear-facing camera modules', 'Camera', '32', 'Active'];

        $filename = 'categories_import_template.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');

            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($csvData as $row) {
                fputcsv($file, $row, ',', '"', '\\');
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }
}
