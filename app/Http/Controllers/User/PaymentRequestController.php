<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\PaymentRequest;
use App\Models\PricingPlan;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class PaymentRequestController extends Controller
{
    /**
     * Display a listing of the user's payment requests.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        
        // Get filter parameters
        $status = $request->get('status', 'all');
        $perPage = $request->get('per_page', 10);
        
        // Build query
        $query = $user->paymentRequests()
            ->with('approvedBy:id,name,email')
            ->latest();
        
        // Apply status filter
        if ($status !== 'all') {
            $query->where('status', $status);
        }
        
        $paymentRequests = $query->paginate($perPage);

        // Format the payment requests data to match the expected structure
        $paymentRequestsData = [
            'data' => $paymentRequests->items(),
            'meta' => [
                'total' => $paymentRequests->total(),
                'per_page' => $paymentRequests->perPage(),
                'current_page' => $paymentRequests->currentPage(),
                'last_page' => $paymentRequests->lastPage(),
                'from' => $paymentRequests->firstItem(),
                'to' => $paymentRequests->lastItem(),
            ],
            'links' => $paymentRequests->linkCollection()->toArray(),
        ];

        // Get statistics
        $stats = [
            'total' => $user->paymentRequests()->count(),
            'pending' => $user->paymentRequests()->pending()->count(),
            'approved' => $user->paymentRequests()->approved()->count(),
            'rejected' => $user->paymentRequests()->where('status', 'rejected')->count(),
        ];

        return Inertia::render('user/payment-requests/Index', [
            'payment_requests' => $paymentRequestsData,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'per_page' => $perPage,
            ],
            'statuses' => [
                'all' => 'All Requests',
                'pending' => 'Pending',
                'approved' => 'Approved',
                'rejected' => 'Rejected',
                'processed' => 'Processed',
            ],
        ]);
    }

    /**
     * Show the form for creating a new payment request.
     */
    public function create(): Response
    {
        $subscriptionService = new SubscriptionService();

        // Get all active pricing plans that support offline payments
        $allPlans = $subscriptionService->getPlans();
        $offlinePaymentPlans = [];

        foreach ($allPlans as $key => $plan) {
            // Check if the plan supports offline payments by querying the database
            $pricingPlan = PricingPlan::where('name', $key)->where('is_active', true)->first();
            if ($pricingPlan && $pricingPlan->hasOfflinePaymentEnabled()) {
                $offlinePaymentPlans[$key] = $plan;
            }
        }

        // If no plans found in database, fallback to service default plans
        if (empty($offlinePaymentPlans)) {
            $offlinePaymentPlans = $allPlans;
        }

        return Inertia::render('user/payment-requests/Create', [
            'payment_methods' => [
                'bank_transfer' => 'Bank Transfer',
                'mobile_money' => 'Mobile Money',
                'cash_deposit' => 'Cash Deposit',
                'other' => 'Other',
            ],
            'subscription_plans' => $offlinePaymentPlans,
            'currencies' => [
                'USD' => 'US Dollar',
                'EUR' => 'Euro',
                'GBP' => 'British Pound',
                'KES' => 'Kenyan Shilling',
                'UGX' => 'Ugandan Shilling',
                'TZS' => 'Tanzanian Shilling',
            ],
        ]);
    }

    /**
     * Store a newly created payment request.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = $request->user();

        // Get valid subscription plan names for validation
        $subscriptionService = new SubscriptionService();
        $allPlans = $subscriptionService->getPlans();
        $validPlanNames = [];

        foreach ($allPlans as $key => $plan) {
            $pricingPlan = PricingPlan::where('name', $key)->where('is_active', true)->first();
            if ($pricingPlan && $pricingPlan->hasOfflinePaymentEnabled()) {
                $validPlanNames[] = $key;
            }
        }

        // Fallback to all plan names if none found
        if (empty($validPlanNames)) {
            $validPlanNames = array_keys($allPlans);
        }

        $validated = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string|in:USD,EUR,GBP,KES,UGX,TZS',
            'payment_method' => 'required|string|in:bank_transfer,mobile_money,cash_deposit,other',
            'subscription_plan' => ['required', 'string', Rule::in($validPlanNames)],
            'notes' => 'nullable|string|max:1000',
            'proof_of_payment' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
        ]);
        
        // Handle file upload
        $proofOfPaymentPath = null;
        if ($request->hasFile('proof_of_payment')) {
            $proofOfPaymentPath = $request->file('proof_of_payment')->store(
                'payment-proofs/' . $user->id,
                'private'
            );
        }
        
        $paymentRequest = PaymentRequest::create([
            'user_id' => $user->id,
            'amount' => $validated['amount'],
            'currency' => $validated['currency'],
            'payment_method' => $validated['payment_method'],
            'subscription_plan' => $validated['subscription_plan'],
            'notes' => $validated['notes'],
            'proof_of_payment' => $proofOfPaymentPath,
            'status' => 'pending',
            'requested_at' => now(),
        ]);
        
        // Log the activity
        $user->logActivity(
            'payment_request_submitted',
            'User submitted a payment request',
            [
                'payment_request_id' => $paymentRequest->id,
                'amount' => $validated['amount'],
                'currency' => $validated['currency'],
                'payment_method' => $validated['payment_method'],
                'subscription_plan' => $validated['subscription_plan'],
            ]
        );
        
        return redirect()->route('payment-requests.show', $paymentRequest)
            ->with('success', 'Payment request submitted successfully. We will review it shortly.');
    }

    /**
     * Display the specified payment request.
     */
    public function show(Request $request, PaymentRequest $paymentRequest): Response
    {
        $user = $request->user();
        
        // Ensure user can only view their own payment requests
        if ($paymentRequest->user_id !== $user->id) {
            abort(403, 'Unauthorized access to payment request.');
        }
        
        $paymentRequest->load('approvedBy:id,name,email');
        
        return Inertia::render('user/payment-requests/Show', [
            'payment_request' => $paymentRequest,
            'can_download_proof' => $paymentRequest->proof_of_payment && Storage::disk('private')->exists($paymentRequest->proof_of_payment),
        ]);
    }

    /**
     * Download proof of payment file.
     */
    public function downloadProof(Request $request, PaymentRequest $paymentRequest)
    {
        $user = $request->user();
        
        // Ensure user can only download their own payment proof
        if ($paymentRequest->user_id !== $user->id) {
            abort(403, 'Unauthorized access to payment proof.');
        }
        
        if (!$paymentRequest->proof_of_payment || !Storage::disk('private')->exists($paymentRequest->proof_of_payment)) {
            abort(404, 'Proof of payment file not found.');
        }
        
        $fileName = 'payment-proof-' . $paymentRequest->id . '.' . pathinfo($paymentRequest->proof_of_payment, PATHINFO_EXTENSION);
        
        return response()->download(
            Storage::disk('private')->path($paymentRequest->proof_of_payment),
            $fileName
        );
    }

    /**
     * Get payment request summary for dashboard.
     */
    public function getSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $summary = [
            'recent_requests' => $user->paymentRequests()
                ->with('approvedBy:id,name')
                ->latest()
                ->take(3)
                ->get(),
            'pending_count' => $user->paymentRequests()->pending()->count(),
            'approved_count' => $user->paymentRequests()->approved()->count(),
            'total_amount_approved' => $user->paymentRequests()
                ->approved()
                ->sum('amount'),
        ];
        
        return response()->json($summary);
    }
}
