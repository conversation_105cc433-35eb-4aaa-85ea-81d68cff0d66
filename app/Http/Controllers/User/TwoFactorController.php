<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\OtpService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class TwoFactorController extends Controller
{
    public function __construct(
        private OtpService $otpService
    ) {
        //
    }

    /**
     * Display user two-factor authentication settings.
     */
    public function index(): Response
    {
        /** @var User $user */
        $user = Auth::user();
        $otpStatus = $this->otpService->getOtpStatus($user);

        return Inertia::render('user/two-factor/Index', [
            'user_2fa_status' => $otpStatus,
            'two_factor_enabled' => $user->two_factor_enabled,
            'two_factor_confirmed_at' => $user->two_factor_confirmed_at,
            'recent_verifications' => $this->getRecentVerifications($user),
        ]);
    }

    /**
     * Enable two-factor authentication for user.
     */
    public function enable(Request $request): JsonResponse
    {
        $user = $request->user();

        if ($user->two_factor_enabled) {
            return response()->json([
                'error' => 'Two-factor authentication is already enabled.',
            ], 422);
        }

        // Enable 2FA for the user
        $user->update([
            'two_factor_enabled' => true,
            'two_factor_confirmed_at' => now(),
        ]);

        // Log the activity
        $user->logActivity(
            'two_factor_enabled',
            'User enabled two-factor authentication',
            [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]
        );

        return response()->json([
            'message' => 'Two-factor authentication has been enabled successfully.',
            'two_factor_enabled' => true,
        ]);
    }

    /**
     * Disable two-factor authentication for user.
     */
    public function disable(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user->two_factor_enabled) {
            return response()->json([
                'error' => 'Two-factor authentication is not enabled.',
            ], 422);
        }

        // Use the OtpService to disable 2FA properly
        $this->otpService->disableTwoFactor($user);

        // Log the activity
        $user->logActivity(
            'two_factor_disabled',
            'User disabled two-factor authentication',
            [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]
        );

        return response()->json([
            'message' => 'Two-factor authentication has been disabled.',
            'two_factor_enabled' => false,
        ]);
    }

    /**
     * Send test OTP to user.
     */
    public function sendTestOtp(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user->two_factor_enabled) {
            return response()->json([
                'error' => 'Two-factor authentication must be enabled to send test codes.',
            ], 422);
        }

        $sent = $this->otpService->generateAndSendOtp($user, 'test_verification');

        if ($sent) {
            return response()->json([
                'message' => 'Test verification code sent to your email.',
                'otp_status' => $this->otpService->getOtpStatus($user),
            ]);
        }

        return response()->json([
            'error' => 'Failed to send verification code. Please try again.',
            'otp_status' => $this->otpService->getOtpStatus($user),
        ], 500);
    }

    /**
     * Verify test OTP code.
     */
    public function verifyTestOtp(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'otp_code' => 'required|string|size:6',
        ]);

        $user = $request->user();
        $verified = $this->otpService->verifyOtp($user, $validated['otp_code'], 'test_verification');

        if ($verified) {
            return response()->json([
                'message' => 'Test verification successful! Your 2FA is working correctly.',
                'verified' => true,
            ]);
        }

        return response()->json([
            'error' => 'Invalid verification code. Please check the code and try again.',
            'verified' => false,
            'otp_status' => $this->otpService->getOtpStatus($user),
        ], 422);
    }

    /**
     * Get OTP verification status for user.
     */
    public function getStatus(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'two_factor_enabled' => $user->two_factor_enabled,
            'two_factor_confirmed_at' => $user->two_factor_confirmed_at,
            'otp_status' => $this->otpService->getOtpStatus($user),
        ]);
    }

    /**
     * Get recent verification attempts for the user.
     */
    private function getRecentVerifications(User $user): array
    {
        return $user->activityLogs()
            ->whereIn('activity_type', ['otp_verified', 'otp_failed', 'two_factor_enabled', 'two_factor_disabled'])
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->id,
                    'type' => $log->activity_type,
                    'description' => $log->description,
                    'success' => in_array($log->activity_type, ['otp_verified', 'two_factor_enabled']),
                    'ip_address' => $log->ip_address,
                    'created_at' => $log->created_at->toISOString(),
                ];
            })
            ->toArray();
    }
}
