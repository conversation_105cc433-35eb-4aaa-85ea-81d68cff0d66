<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\UserActivityLog;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ActivityController extends Controller
{
    /**
     * Display a listing of the user's activity logs.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        
        // Get filter parameters
        $activityType = $request->get('activity_type', 'all');
        $dateRange = $request->get('date_range', '30d');
        $perPage = $request->get('per_page', 20);
        
        // Build query
        $query = $user->activityLogs()
            ->with('performedBy:id,name,email')
            ->latest();
        
        // Apply activity type filter
        if ($activityType !== 'all') {
            $query->where('activity_type', $activityType);
        }
        
        // Apply date range filter
        switch ($dateRange) {
            case '7d':
                $query->where('created_at', '>=', now()->subDays(7));
                break;
            case '30d':
                $query->where('created_at', '>=', now()->subDays(30));
                break;
            case '90d':
                $query->where('created_at', '>=', now()->subDays(90));
                break;
            case '1y':
                $query->where('created_at', '>=', now()->subYear());
                break;
            case 'all':
                // No date filter
                break;
        }
        
        $activities = $query->paginate($perPage);

        // Ensure we have a proper structure even if no data
        if ($activities->isEmpty()) {
            // Create some sample activity for testing if none exists
            $user->logActivity(
                'page_view',
                'User viewed activity log page',
                ['page' => 'activity_index']
            );

            // Re-fetch after creating sample data
            $activities = $query->paginate($perPage);
        }

        // Format the activities data to match the expected structure
        $activitiesData = [
            'data' => $activities->items(),
            'meta' => [
                'total' => $activities->total(),
                'per_page' => $activities->perPage(),
                'current_page' => $activities->currentPage(),
                'last_page' => $activities->lastPage(),
                'from' => $activities->firstItem(),
                'to' => $activities->lastItem(),
            ],
            'links' => $activities->linkCollection()->toArray(),
        ];
        
        // Get statistics
        $stats = [
            'total_activities' => $user->activityLogs()->count(),
            'recent_activities' => $user->activityLogs()->recent(7)->count(),
            'login_count' => $user->activityLogs()->where('activity_type', 'login')->count(),
            'search_count' => $user->activityLogs()->where('activity_type', 'search')->count(),
        ];
        
        // Get activity type counts for the current date range
        $activityTypeCounts = $user->activityLogs()
            ->when($dateRange !== 'all', function ($query) use ($dateRange) {
                switch ($dateRange) {
                    case '7d':
                        return $query->where('created_at', '>=', now()->subDays(7));
                    case '30d':
                        return $query->where('created_at', '>=', now()->subDays(30));
                    case '90d':
                        return $query->where('created_at', '>=', now()->subDays(90));
                    case '1y':
                        return $query->where('created_at', '>=', now()->subYear());
                }
            })
            ->selectRaw('activity_type, COUNT(*) as count')
            ->groupBy('activity_type')
            ->pluck('count', 'activity_type')
            ->toArray();
        
        return Inertia::render('user/activity/Index', [
            'activities' => $activitiesData,
            'stats' => $stats,
            'activity_type_counts' => $activityTypeCounts,
            'filters' => [
                'activity_type' => $activityType,
                'date_range' => $dateRange,
                'per_page' => $perPage,
            ],
            'activity_types' => [
                'all' => 'All Activities',
                'login' => 'Login',
                'logout' => 'Logout',
                'search' => 'Search',
                'view_part' => 'View Part',
                'view_category' => 'View Category',
                'view_brand' => 'View Brand',
                'view_model' => 'View Model',
                'subscription_change' => 'Subscription Change',
                'profile_update' => 'Profile Update',
                'password_change' => 'Password Change',
                'email_verification' => 'Email Verification',
            ],
            'date_ranges' => [
                '7d' => 'Last 7 days',
                '30d' => 'Last 30 days',
                '90d' => 'Last 90 days',
                '1y' => 'Last year',
                'all' => 'All time',
            ],
        ]);
    }

    /**
     * Display the specified activity log.
     */
    public function show(Request $request, UserActivityLog $userActivityLog): Response
    {
        $user = $request->user();
        
        // Ensure user can only view their own activity logs
        if ($userActivityLog->user_id !== $user->id) {
            abort(403, 'Unauthorized access to activity log.');
        }

        $userActivityLog->load('performedBy:id,name,email');

        return Inertia::render('user/activity/Show', [
            'activity' => $userActivityLog,
        ]);
    }

    /**
     * Get activity summary for dashboard.
     */
    public function getSummary(Request $request)
    {
        $user = $request->user();
        
        $summary = [
            'recent_activities' => $user->activityLogs()
                ->with('performedBy:id,name')
                ->latest()
                ->take(5)
                ->get(),
            'today_count' => $user->activityLogs()
                ->whereDate('created_at', today())
                ->count(),
            'week_count' => $user->activityLogs()
                ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->count(),
        ];
        
        return response()->json($summary);
    }
}
