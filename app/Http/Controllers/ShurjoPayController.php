<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\ShurjoPayTransaction;
use App\Services\CsrfTokenService;
use App\Services\ShurjoPayService;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class ShurjoPayController extends Controller
{
    public function __construct(
        private ShurjoPayService $shurjoPayService,
        private SubscriptionService $subscriptionService,
        private CsrfTokenService $csrfTokenService
    ) {}

    /**
     * Create a checkout session for ShurjoPay.
     */
    public function createCheckout(Request $request): JsonResponse
    {
        try {
            // Enhanced CSRF and session debugging
            if (config('app.debug')) {
                $csrfValidation = $this->csrfTokenService->validateToken($request);
                $this->csrfTokenService->logValidationAttempt($request, $csrfValidation);

                Log::info('ShurjoPay checkout request received', [
                    'user_id' => $request->user()?->id,
                    'request_data' => $request->all(),
                    'csrf_validation' => $csrfValidation,
                    'session_debug' => $this->csrfTokenService->getSessionDebugInfo($request),
                ]);
            }

            $request->validate([
                'plan_id' => 'required|exists:pricing_plans,id',
                'billing_cycle' => 'required|in:month,year',
            ]);

            $user = $request->user();
            $plan = PricingPlan::findOrFail($request->plan_id);

            // Check if user already has an active subscription
            if ($user->activeSubscription) {
                return response()->json([
                    'error' => 'You already have an active subscription.'
                ], 400);
            }

            // Check if plan supports ShurjoPay
            if (!$plan->hasShurjoPayIntegration()) {
                return response()->json([
                    'error' => 'This plan does not support ShurjoPay payments.'
                ], 400);
            }

            // Calculate amount based on billing cycle
            $amount = $request->billing_cycle === 'year'
                ? $plan->price * 12
                : $plan->price;

            // Create payment with ShurjoPay
            $paymentData = $this->shurjoPayService->createPayment(
                $user,
                $plan,
                $amount,
                config('shurjopay.currency', 'BDT')
            );

            if (!$paymentData) {
                return response()->json([
                    'error' => 'Failed to create payment session. Please try again.'
                ], 500);
            }

            // Store transaction in database
            $transaction = ShurjoPayTransaction::create([
                'shurjopay_order_id' => $paymentData['sp_order_id'],
                'merchant_order_id' => $paymentData['merchant_order_id'] ?? 'MANUAL_' . time(),
                'user_id' => $user->id,
                'pricing_plan_id' => $plan->id,
                'status' => 'pending',
                'currency' => config('shurjopay.currency', 'BDT'),
                'amount' => $amount,
                'customer_details' => [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone,
                ],
                'shurjopay_data' => $paymentData,
                'checkout_url' => $paymentData['checkout_url'],
            ]);

            // Log the checkout creation
            Log::info('ShurjoPay checkout created', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'transaction_id' => $transaction->id,
                'amount' => $amount,
                'billing_cycle' => $request->billing_cycle,
            ]);

            return response()->json([
                'success' => true,
                'checkout_url' => $paymentData['checkout_url'],
                'transaction_id' => $transaction->id,
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Invalid request data.',
                'details' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('ShurjoPay checkout creation failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'An unexpected error occurred. Please try again.'
            ], 500);
        }
    }

    /**
     * Handle successful payment callback.
     */
    public function handleSuccess(Request $request): RedirectResponse
    {
        try {
            $orderId = $request->get('order_id');

            if (!$orderId) {
                Log::warning('ShurjoPay success callback missing order_id');
                return redirect()->route('subscription.plans')
                    ->with('error', 'Invalid payment response.');
            }

            // Verify payment with ShurjoPay
            $verificationData = $this->shurjoPayService->verifyPayment($orderId);

            if (!$verificationData) {
                Log::error('ShurjoPay payment verification failed', ['order_id' => $orderId]);
                return redirect()->route('subscription.plans')
                    ->with('error', 'Payment verification failed.');
            }

            // Find the transaction
            $transaction = ShurjoPayTransaction::where('shurjopay_order_id', $orderId)->first();

            if (!$transaction) {
                Log::error('ShurjoPay transaction not found', ['order_id' => $orderId]);
                return redirect()->route('subscription.plans')
                    ->with('error', 'Transaction not found.');
            }

            // Process the payment
            $this->processPaymentResult($transaction, $verificationData);

            if ($this->shurjoPayService->isPaymentSuccessful($verificationData)) {
                return redirect()->route('subscription.success')
                    ->with('success', 'Payment completed successfully!');
            } else {
                $message = $this->shurjoPayService->getPaymentStatusMessage($verificationData);
                return redirect()->route('subscription.plans')
                    ->with('error', "Payment failed: {$message}");
            }

        } catch (\Exception $e) {
            Log::error('ShurjoPay success callback error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return redirect()->route('subscription.plans')
                ->with('error', 'An error occurred while processing your payment.');
        }
    }

    /**
     * Handle cancelled payment callback.
     */
    public function handleCancel(Request $request): RedirectResponse
    {
        try {
            $orderId = $request->get('order_id');

            if ($orderId) {
                // Find and update transaction
                $transaction = ShurjoPayTransaction::where('shurjopay_order_id', $orderId)->first();
                if ($transaction) {
                    $transaction->update([
                        'status' => 'cancelled',
                        'sp_code' => '1002',
                        'sp_message' => 'Payment cancelled by customer',
                    ]);

                    Log::info('ShurjoPay payment cancelled', [
                        'transaction_id' => $transaction->id,
                        'order_id' => $orderId,
                        'user_id' => $transaction->user_id,
                    ]);
                }
            }

            return redirect()->route('subscription.plans')
                ->with('info', 'Payment was cancelled.');

        } catch (\Exception $e) {
            Log::error('ShurjoPay cancel callback error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return redirect()->route('subscription.plans')
                ->with('error', 'An error occurred.');
        }
    }

    /**
     * Handle IPN (Instant Payment Notification) webhook.
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        try {
            $data = $request->all();

            Log::info('ShurjoPay IPN received', ['data' => $data]);

            // Process the webhook
            $success = $this->shurjoPayService->processWebhook($data);

            if ($success) {
                return response()->json(['status' => 'success']);
            }

            return response()->json(['status' => 'failed'], 400);

        } catch (\Exception $e) {
            Log::error('ShurjoPay webhook processing error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Process payment result and update subscription.
     */
    private function processPaymentResult(ShurjoPayTransaction $transaction, array $verificationData): void
    {
        DB::transaction(function () use ($transaction, $verificationData) {
            // Update transaction with verification data
            $transaction->update([
                'status' => $this->shurjoPayService->isPaymentSuccessful($verificationData) ? 'completed' : 'failed',
                'sp_code' => $verificationData['sp_code'],
                'sp_message' => $verificationData['sp_message'] ?? $verificationData['sp_massage'] ?? null,
                'method' => $verificationData['method'] ?? null,
                'bank_trx_id' => $verificationData['bank_trx_id'] ?? null,
                'invoice_no' => $verificationData['invoice_no'] ?? null,
                'bank_status' => $verificationData['bank_status'] ?? null,
                'payable_amount' => $verificationData['payable_amount'] ?? null,
                'received_amount' => $verificationData['recived_amount'] ?? null, // Note: ShurjoPay has typo in API
                'discount_amount' => $verificationData['discount_amount'] ?? null,
                'disc_percent' => $verificationData['disc_percent'] ?? null,
                'shurjopay_data' => $verificationData,
                'shurjopay_created_at' => isset($verificationData['date_time'])
                    ? \Carbon\Carbon::parse($verificationData['date_time'])
                    : null,
            ]);

            // If payment is successful, create/update subscription
            if ($this->shurjoPayService->isPaymentSuccessful($verificationData)) {
                $this->createSubscription($transaction, $verificationData);
            }
        });
    }

    /**
     * Create subscription after successful payment.
     */
    private function createSubscription(ShurjoPayTransaction $transaction, array $verificationData): void
    {
        $user = $transaction->user;
        $plan = $transaction->pricingPlan;

        if (!$plan) {
            Log::error('Cannot create subscription: pricing plan not found', [
                'transaction_id' => $transaction->id
            ]);
            return;
        }

        // Create subscription
        $subscription = Subscription::create([
            'user_id' => $user->id,
            'plan_name' => $plan->name,
            'pricing_plan_id' => $plan->id,
            'status' => 'active',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(), // ShurjoPay typically handles one-time payments
            'shurjopay_subscription_id' => $transaction->shurjopay_order_id,
            'payment_gateway' => 'shurjopay',
        ]);

        // Update transaction with subscription ID
        $transaction->update(['subscription_id' => $subscription->id]);

        // Log subscription creation
        Log::info('Subscription created via ShurjoPay', [
            'user_id' => $user->id,
            'subscription_id' => $subscription->id,
            'transaction_id' => $transaction->id,
            'plan_id' => $plan->id,
        ]);

        // Log user activity
        $user->logActivity(
            'subscription_created',
            'Subscription created via ShurjoPay payment',
            [
                'subscription_id' => $subscription->id,
                'plan_name' => $plan->name,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'payment_gateway' => 'shurjopay',
            ]
        );
    }

    /**
     * Show payment status page.
     */
    public function showStatus(Request $request, string $transactionId): Response
    {
        $transaction = ShurjoPayTransaction::with(['user', 'pricingPlan'])
            ->findOrFail($transactionId);

        // Ensure user can only view their own transactions
        if ($transaction->user_id !== $request->user()->id) {
            abort(403);
        }

        return Inertia::render('subscription/shurjopay-status', [
            'transaction' => $transaction,
            'status_message' => $this->shurjoPayService->getPaymentStatusMessage($transaction->shurjopay_data ?? []),
        ]);
    }
}
