<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\CoinbaseCommerceTransaction;
use App\Services\CoinbaseCommerceService;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class CoinbaseCommerceController extends Controller
{
    public function __construct(
        private CoinbaseCommerceService $coinbaseCommerceService,
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Create a charge for subscription payment.
     */
    public function createCharge(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'pricing_plan_id' => 'required|exists:pricing_plans,id',
                'billing_cycle' => 'required|in:month,year',
            ]);

            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            $plan = PricingPlan::findOrFail($validated['pricing_plan_id']);

            // Check if crypto payments are enabled for this plan
            if (!$plan->crypto_payment_enabled) {
                return response()->json(['error' => 'Crypto payments not enabled for this plan'], 400);
            }

            Log::info('Creating Coinbase Commerce charge', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'billing_cycle' => $validated['billing_cycle']
            ]);

            // Check if Coinbase Commerce is configured
            if (!$this->coinbaseCommerceService->isConfigured()) {
                Log::warning('Coinbase Commerce not configured', [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'environment' => config('app.env')
                ]);

                $errorMessage = config('app.env') === 'local'
                    ? 'Coinbase Commerce is not configured. Please set COINBASE_COMMERCE_API_KEY in your .env file.'
                    : 'Cryptocurrency payments are temporarily unavailable. Please try another payment method.';

                return response()->json(['error' => $errorMessage], 503);
            }

            $result = $this->coinbaseCommerceService->createCharge($user, $plan, [
                'billing_cycle' => $validated['billing_cycle']
            ]);

            if (!$result) {
                Log::error('Failed to create Coinbase Commerce charge', [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id
                ]);

                // Check recent logs for specific error messages
                $recentLogs = file_get_contents(storage_path('logs/laravel.log'));
                $errorMessage = 'Failed to create payment charge';

                if (strpos($recentLogs, 'merchant has no settlement') !== false) {
                    $errorMessage = config('app.env') === 'local'
                        ? 'Coinbase Commerce merchant setup incomplete. Please complete your business information at commerce.coinbase.com → Settings → Business Information.'
                        : 'Cryptocurrency payment setup is incomplete. Please contact support or use another payment method.';
                }

                return response()->json(['error' => $errorMessage], 500);
            }

            return response()->json([
                'success' => true,
                'charge_id' => $result['charge_id'],
                'hosted_url' => $result['hosted_url'],
                'transaction_id' => $result['transaction']->id,
                'message' => 'Payment charge created successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json(['error' => 'Validation failed', 'details' => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error('Exception creating Coinbase Commerce charge', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Get charge status.
     */
    public function getChargeStatus(string $chargeId): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            // Find the transaction
            $transaction = CoinbaseCommerceTransaction::where('coinbase_charge_id', $chargeId)
                ->where('user_id', $user->id)
                ->first();

            if (!$transaction) {
                return response()->json(['error' => 'Transaction not found'], 404);
            }

            // Get latest charge data from Coinbase Commerce
            $chargeData = $this->coinbaseCommerceService->getCharge($chargeId);

            if ($chargeData) {
                // Update transaction with latest data
                $transaction->update([
                    'status' => $chargeData['status'] ?? $transaction->status,
                    'timeline' => $chargeData['timeline'] ?? $transaction->timeline,
                    'coinbase_data' => $chargeData,
                    'coinbase_updated_at' => isset($chargeData['updated_at']) ?
                        \Carbon\Carbon::parse($chargeData['updated_at']) : now(),
                ]);
            }

            return response()->json([
                'success' => true,
                'transaction' => [
                    'id' => $transaction->id,
                    'charge_id' => $transaction->coinbase_charge_id,
                    'status' => $transaction->status,
                    'amount' => $transaction->formatted_amount,
                    'crypto_amount' => $transaction->formatted_crypto_amount,
                    'currency' => $transaction->currency,
                    'hosted_url' => $transaction->hosted_url,
                    'expires_at' => $transaction->expires_at?->toISOString(),
                    'confirmed_at' => $transaction->confirmed_at?->toISOString(),
                    'is_valid' => $transaction->isValid(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Exception getting charge status', [
                'charge_id' => $chargeId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Handle webhook from Coinbase Commerce.
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            Log::info('Received Coinbase Commerce webhook', [
                'headers' => $request->headers->all(),
                'payload_length' => strlen($request->getContent())
            ]);

            $success = $this->coinbaseCommerceService->processWebhook($request);

            if ($success) {
                Log::info('Coinbase Commerce webhook processed successfully');
                return response()->json(['success' => true]);
            } else {
                Log::error('Failed to process Coinbase Commerce webhook');
                return response()->json(['error' => 'Failed to process webhook'], 400);
            }

        } catch (\Exception $e) {
            Log::error('Exception processing Coinbase Commerce webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Get user's Coinbase Commerce transactions.
     */
    public function getUserTransactions(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            $transactions = CoinbaseCommerceTransaction::where('user_id', $user->id)
                ->with(['pricingPlan', 'subscription'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            return response()->json([
                'success' => true,
                'transactions' => $transactions->items(),
                'pagination' => [
                    'current_page' => $transactions->currentPage(),
                    'last_page' => $transactions->lastPage(),
                    'per_page' => $transactions->perPage(),
                    'total' => $transactions->total(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Exception getting user transactions', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Test Coinbase Commerce connection.
     */
    public function testConnection(): JsonResponse
    {
        try {
            $config = $this->coinbaseCommerceService->getConfiguration();

            if (!$config['is_configured']) {
                return response()->json([
                    'success' => false,
                    'error' => 'Coinbase Commerce not configured'
                ]);
            }

            // Try to make a simple API call to test the connection
            // We'll create a test charge with minimal data
            $testData = [
                'name' => 'Connection Test',
                'description' => 'Test charge to verify API connection',
                'pricing_type' => 'fixed_price',
                'local_price' => [
                    'amount' => '1.00',
                    'currency' => 'USD'
                ]
            ];

            $response = \Illuminate\Support\Facades\Http::withHeaders([
                'X-CC-Api-Key' => config('coinbase_commerce.api_key'),
                'Content-Type' => 'application/json',
                'X-CC-Version' => '2018-03-22'
            ])->post(config('coinbase_commerce.base_url') . '/charges', $testData);

            if ($response->successful()) {
                // Delete the test charge if possible
                $chargeData = $response->json()['data'] ?? null;
                if ($chargeData && isset($chargeData['id'])) {
                    // Note: Coinbase Commerce doesn't allow deleting charges via API
                    // The test charge will remain but won't affect anything
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Connection successful',
                    'config' => $config
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'API connection failed',
                    'details' => $response->json()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception testing Coinbase Commerce connection', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'error' => 'Connection test failed',
                'details' => $e->getMessage()
            ]);
        }
    }
}
