<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class CheckImpersonationExpiry
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if currently impersonating
        if (Session::has('impersonating_user_id')) {
            $expiresAt = Session::get('impersonation_expires_at');
            
            // Check if session has expired
            if ($expiresAt && now()->isAfter($expiresAt)) {
                // End impersonation session
                $this->endImpersonationSession();
                
                // Redirect to admin dashboard with message
                return redirect()->route('admin.dashboard')
                    ->with('warning', 'Impersonation session has expired and has been automatically ended.');
            }
        }

        return $next($request);
    }

    /**
     * End the impersonation session.
     */
    private function endImpersonationSession(): void
    {
        $originalAdminId = Session::get('original_admin_id');
        $impersonationLogId = Session::get('impersonation_log_id');

        // End the impersonation log
        if ($impersonationLogId) {
            $impersonationLog = \App\Models\UserImpersonationLog::find($impersonationLogId);
            if ($impersonationLog) {
                $impersonationLog->endSession();
            }
        }

        // Clear impersonation session data
        Session::forget([
            'impersonating_user_id',
            'original_admin_id',
            'impersonation_log_id',
            'impersonation_expires_at'
        ]);

        // Login as original admin
        if ($originalAdminId) {
            $admin = \App\Models\User::find($originalAdminId);
            if ($admin) {
                Auth::login($admin);
            }
        }
    }
}
