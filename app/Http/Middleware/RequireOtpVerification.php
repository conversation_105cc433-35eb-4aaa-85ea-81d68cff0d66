<?php

namespace App\Http\Middleware;

use App\Services\OtpService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class RequireOtpVerification
{
    public function __construct(
        private OtpService $otpService
    ) {
        //
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $action = 'default'): Response
    {
        $user = $request->user();

        // Skip if user is not authenticated
        if (!$user) {
            return $next($request);
        }

        // Skip if user is not admin
        if (!$user->isAdmin()) {
            return $next($request);
        }

        // Check if 2FA is globally enabled for admin actions
        if (!$this->isTwoFactorEnabled()) {
            return $next($request);
        }

        // Skip if user doesn't have 2FA enabled
        if (!$user->two_factor_enabled) {
            return $next($request);
        }

        // Check if user has valid OTP session for this action
        if ($this->otpService->hasValidOtpSession($user, $action)) {
            return $next($request);
        }

        // Check if this is an OTP verification request
        if ($request->has('otp_code')) {
            return $this->handleOtpVerification($request, $next, $action);
        }

        // Require OTP verification
        return $this->requireOtpVerification($request, $action);
    }

    /**
     * Handle OTP verification.
     */
    private function handleOtpVerification(Request $request, Closure $next, string $action): Response
    {
        $user = $request->user();
        $otpCode = $request->input('otp_code');

        // Validate OTP code format
        if (!preg_match('/^\d{6}$/', $otpCode)) {
            return response()->json([
                'error' => 'Invalid OTP format',
                'message' => 'OTP code must be 6 digits',
                'requires_otp' => true,
                'action' => $action,
            ], 422);
        }

        // Check if user is locked out
        if ($this->otpService->isUserLockedOut($user)) {
            $remainingMinutes = $this->otpService->getRemainingLockoutTime($user);
            return response()->json([
                'error' => 'Account temporarily locked',
                'message' => "Too many failed attempts. Try again in {$remainingMinutes} minutes.",
                'lockout_remaining_minutes' => $remainingMinutes,
                'requires_otp' => true,
                'action' => $action,
            ], 429);
        }

        // Verify OTP
        if ($this->otpService->verifyOtp($user, $otpCode, $action)) {
            // Create OTP session (valid for 30 minutes)
            $this->otpService->createOtpSession($user, $action, 30);
            
            // Remove OTP code from request to prevent it from being processed by the main action
            $request->request->remove('otp_code');
            
            return $next($request);
        }

        // OTP verification failed
        $otpStatus = $this->otpService->getOtpStatus($user);
        
        return response()->json([
            'error' => 'Invalid verification code',
            'message' => 'The verification code you entered is incorrect.',
            'remaining_attempts' => $otpStatus['remaining_attempts'],
            'requires_otp' => true,
            'action' => $action,
        ], 422);
    }

    /**
     * Require OTP verification.
     */
    private function requireOtpVerification(Request $request, string $action): Response
    {
        $user = $request->user();
        $otpStatus = $this->otpService->getOtpStatus($user);

        // Check if user is locked out
        if ($otpStatus['is_locked_out']) {
            return response()->json([
                'error' => 'Account temporarily locked',
                'message' => "Too many failed attempts. Try again in {$otpStatus['lockout_remaining_minutes']} minutes.",
                'lockout_remaining_minutes' => $otpStatus['lockout_remaining_minutes'],
                'requires_otp' => true,
                'action' => $action,
            ], 429);
        }

        // Generate and send new OTP if none exists or expired
        if (!$otpStatus['has_otp_pending']) {
            $otpSent = $this->otpService->generateAndSendOtp($user, $action);
            
            if (!$otpSent) {
                return response()->json([
                    'error' => 'Failed to send verification code',
                    'message' => 'Unable to send verification code. Please try again.',
                    'requires_otp' => true,
                    'action' => $action,
                ], 500);
            }
        }

        // Return OTP requirement response
        return response()->json([
            'error' => 'Two-factor authentication required',
            'message' => 'Please check your email for a verification code.',
            'requires_otp' => true,
            'action' => $action,
            'otp_status' => $otpStatus,
        ], 403);
    }

    /**
     * Check if two-factor authentication is globally enabled.
     */
    private function isTwoFactorEnabled(): bool
    {
        return Cache::get('admin_two_factor_enabled', true);
    }
}
