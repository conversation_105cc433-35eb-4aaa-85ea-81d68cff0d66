<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class AdminRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $action = 'default'): Response
    {
        // Check if rate limiting is enabled
        if (!$this->isRateLimitingEnabled()) {
            return $next($request);
        }

        $user = $request->user();
        if (!$user) {
            return $next($request);
        }

        // Get rate limit configuration for the action
        $config = $this->getRateLimitConfig($action);
        
        // Create rate limiter key
        $key = $this->getRateLimiterKey($user->id, $action, $request);
        
        // Check rate limit
        $executed = RateLimiter::attempt(
            $key,
            $config['max_attempts'],
            function () use ($next, $request, $user, $action) {
                // Log the admin action
                $this->logAdminAction($user, $action, $request);
                return $next($request);
            },
            $config['decay_seconds']
        );

        if (!$executed) {
            // Rate limit exceeded
            $this->logRateLimitExceeded($user, $action, $request);
            
            return response()->json([
                'message' => 'Too many requests. Please slow down.',
                'retry_after' => RateLimiter::availableIn($key),
                'action' => $action,
                'max_attempts' => $config['max_attempts'],
                'window_seconds' => $config['decay_seconds']
            ], 429);
        }

        return $executed;
    }

    /**
     * Check if rate limiting is enabled.
     */
    private function isRateLimitingEnabled(): bool
    {
        return Cache::get('admin_rate_limiting_enabled', true);
    }

    /**
     * Get rate limit configuration for an action.
     */
    private function getRateLimitConfig(string $action): array
    {
        $configs = Cache::get('admin_rate_limit_configs', $this->getDefaultConfigs());
        
        return $configs[$action] ?? $configs['default'];
    }

    /**
     * Get default rate limit configurations.
     */
    private function getDefaultConfigs(): array
    {
        return [
            'default' => [
                'max_attempts' => 60,
                'decay_seconds' => 60,
                'description' => 'General admin actions'
            ],
            'user_management' => [
                'max_attempts' => 30,
                'decay_seconds' => 60,
                'description' => 'User approval, suspension, etc.'
            ],
            'payment_approval' => [
                'max_attempts' => 20,
                'decay_seconds' => 60,
                'description' => 'Payment request approvals'
            ],
            'bulk_operations' => [
                'max_attempts' => 10,
                'decay_seconds' => 300, // 5 minutes
                'description' => 'Bulk approve, bulk import, etc.'
            ],
            'impersonation' => [
                'max_attempts' => 5,
                'decay_seconds' => 300, // 5 minutes
                'description' => 'User impersonation actions'
            ],
            'data_export' => [
                'max_attempts' => 10,
                'decay_seconds' => 300, // 5 minutes
                'description' => 'Data export operations'
            ],
            'email_config' => [
                'max_attempts' => 10,
                'decay_seconds' => 60,
                'description' => 'Email configuration changes'
            ],
            'system_config' => [
                'max_attempts' => 15,
                'decay_seconds' => 300, // 5 minutes
                'description' => 'System configuration changes'
            ]
        ];
    }

    /**
     * Generate rate limiter key.
     */
    private function getRateLimiterKey(int $userId, string $action, Request $request): string
    {
        $ip = $request->ip();
        return "admin_rate_limit:{$userId}:{$action}:{$ip}";
    }

    /**
     * Log admin action.
     */
    private function logAdminAction(object $user, string $action, Request $request): void
    {
        try {
            $user->logActivity(
                'admin_action_rate_limited',
                "Admin action performed: {$action}",
                [
                    'action' => $action,
                    'route' => $request->route()?->getName(),
                    'method' => $request->method(),
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'rate_limit_applied' => true,
                ]
            );
        } catch (\Exception $e) {
            Log::error('Failed to log admin action', [
                'user_id' => $user->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Log rate limit exceeded.
     */
    private function logRateLimitExceeded(object $user, string $action, Request $request): void
    {
        Log::warning('Admin rate limit exceeded', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'action' => $action,
            'route' => $request->route()?->getName(),
            'method' => $request->method(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString()
        ]);

        // Also log to user activity
        try {
            $user->logActivity(
                'admin_rate_limit_exceeded',
                "Rate limit exceeded for admin action: {$action}",
                [
                    'action' => $action,
                    'route' => $request->route()?->getName(),
                    'method' => $request->method(),
                    'ip_address' => $request->ip(),
                    'severity' => 'warning',
                ]
            );
        } catch (\Exception $e) {
            Log::error('Failed to log rate limit exceeded', [
                'user_id' => $user->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get current rate limit status for a user and action.
     */
    public static function getRateLimitStatus(int $userId, string $action, string $ip): array
    {
        $middleware = new self();
        $config = $middleware->getRateLimitConfig($action);
        $key = $middleware->getRateLimiterKey($userId, $action, request());
        
        $remaining = RateLimiter::remaining($key, $config['max_attempts']);
        $retryAfter = RateLimiter::availableIn($key);
        
        return [
            'action' => $action,
            'max_attempts' => $config['max_attempts'],
            'remaining_attempts' => $remaining,
            'window_seconds' => $config['decay_seconds'],
            'retry_after_seconds' => $retryAfter,
            'is_limited' => $remaining <= 0,
            'enabled' => $middleware->isRateLimitingEnabled()
        ];
    }

    /**
     * Clear rate limits for a user.
     */
    public static function clearUserRateLimits(int $userId): void
    {
        $middleware = new self();
        $configs = $middleware->getDefaultConfigs();
        
        foreach (array_keys($configs) as $action) {
            $key = $middleware->getRateLimiterKey($userId, $action, request());
            RateLimiter::clear($key);
        }
    }
}
