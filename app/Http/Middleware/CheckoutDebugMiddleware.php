<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckoutDebugMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only log in debug mode
        if (!config('app.debug')) {
            return $next($request);
        }

        $startTime = microtime(true);

        // Log incoming request details
        $this->logRequest($request);

        $response = $next($request);

        // Log response details
        $this->logResponse($request, $response, $startTime);

        return $response;
    }

    /**
     * Log incoming request details
     */
    private function logRequest(Request $request): void
    {
        Log::info('Checkout Request Debug', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'route' => $request->route()?->getName(),
            'user_id' => $request->user()?->id,
            'user_email' => $request->user()?->email,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'headers' => [
                'content_type' => $request->header('Content-Type'),
                'accept' => $request->header('Accept'),
                'x_requested_with' => $request->header('X-Requested-With'),
                'csrf_token_present' => $request->header('X-CSRF-TOKEN') ? 'yes' : 'no',
                'csrf_token_length' => $request->header('X-CSRF-TOKEN') ? strlen($request->header('X-CSRF-TOKEN')) : 0,
                'referer' => $request->header('Referer'),
                'origin' => $request->header('Origin'),
            ],
            'session' => [
                'id' => $request->session()->getId(),
                'token_present' => $request->session()->token() ? 'yes' : 'no',
                'token_length' => $request->session()->token() ? strlen($request->session()->token()) : 0,
                'csrf_match' => $request->session()->token() === $request->header('X-CSRF-TOKEN') ? 'yes' : 'no',
            ],
            'request_data' => $request->all(),
        ]);
    }

    /**
     * Log response details
     */
    private function logResponse(Request $request, Response $response, float $startTime): void
    {
        $duration = round((microtime(true) - $startTime) * 1000, 2);

        Log::info('Checkout Response Debug', [
            'status' => $response->getStatusCode(),
            'duration_ms' => $duration,
            'content_type' => $response->headers->get('Content-Type'),
            'content_length' => $response->headers->get('Content-Length'),
            'route' => $request->route()?->getName(),
            'user_id' => $request->user()?->id,
            'response_preview' => $this->getResponsePreview($response),
        ]);

        // Log errors in detail
        if ($response->getStatusCode() >= 400) {
            Log::error('Checkout Error Response', [
                'status' => $response->getStatusCode(),
                'route' => $request->route()?->getName(),
                'user_id' => $request->user()?->id,
                'request_data' => $request->all(),
                'response_content' => $this->getResponseContent($response),
            ]);
        }
    }

    /**
     * Get a preview of the response content
     */
    private function getResponsePreview(Response $response): string
    {
        $content = $response->getContent();
        if (!$content) {
            return 'empty';
        }

        // For JSON responses, try to decode and show structure
        if (str_contains($response->headers->get('Content-Type', ''), 'application/json')) {
            $decoded = json_decode($content, true);
            if ($decoded !== null) {
                return 'JSON with keys: ' . implode(', ', array_keys($decoded));
            }
        }

        // For other responses, show first 100 characters
        return substr($content, 0, 100) . (strlen($content) > 100 ? '...' : '');
    }

    /**
     * Get full response content for error logging
     */
    private function getResponseContent(Response $response): string
    {
        $content = $response->getContent();
        if (!$content) {
            return 'empty';
        }

        // Limit content length for logging
        return strlen($content) > 1000 ? substr($content, 0, 1000) . '...' : $content;
    }
}
