<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ContentProtection
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Add security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Prevent caching of sensitive content
        if ($this->isSensitiveRoute($request)) {
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate, private');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
        }

        // Add Content Security Policy
        $csp = $this->getContentSecurityPolicy();
        $response->headers->set('Content-Security-Policy', $csp);

        return $response;
    }

    /**
     * Check if the current route contains sensitive content.
     */
    private function isSensitiveRoute(Request $request): bool
    {
        $sensitiveRoutes = [
            'parts.show',
            'search.results',
            'admin.*',
            'subscription.*',
        ];

        $currentRoute = $request->route()?->getName();

        foreach ($sensitiveRoutes as $pattern) {
            if (fnmatch($pattern, $currentRoute)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get Content Security Policy header value.
     */
    private function getContentSecurityPolicy(): string
    {
        $policies = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.paddle.com https://js.paddle.com", // Needed for React/Vite and Paddle
            "style-src 'self' 'unsafe-inline' https://cdn.paddle.com", // Needed for Tailwind CSS and Paddle
            "img-src 'self' data: https:",
            "font-src 'self' data: https://cdn.paddle.com",
            "connect-src 'self' https://api.paddle.com https://sandbox-api.paddle.com https://checkout.paddle.com https://sandbox-checkout.paddle.com https://sandbox-checkout-service.paddle.com",
            "media-src 'self'",
            "object-src 'none'",
            "frame-src 'self' https://buy.paddle.com https://sandbox-buy.paddle.com https://checkout.paddle.com https://sandbox-checkout.paddle.com", // Allow Paddle checkout frames
            "base-uri 'self'",
            "form-action 'self' https://buy.paddle.com https://sandbox-buy.paddle.com",
        ];

        return implode('; ', $policies);
    }
}
