<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ShurjoPayDebugMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only log in debug mode and for development environments
        if (!config('app.debug') || !config('shurjopay.debug')) {
            return $next($request);
        }

        $startTime = microtime(true);

        // Log incoming request
        $this->logRequest($request);

        $response = $next($request);

        // Log response
        $this->logResponse($request, $response, $startTime);

        return $response;
    }

    /**
     * Log incoming request details.
     */
    protected function logRequest(Request $request): void
    {
        $logData = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'headers' => $this->sanitizeHeaders($request->headers->all()),
        ];

        // Add request body for POST requests (sanitized)
        if ($request->isMethod('POST')) {
            $logData['body'] = $this->sanitizeRequestBody($request->all());
        }

        Log::channel('shurjopay')->debug('ShurjoPay Request', $logData);
    }

    /**
     * Log response details.
     */
    protected function logResponse(Request $request, Response $response, float $startTime): void
    {
        $duration = round((microtime(true) - $startTime) * 1000, 2);

        $logData = [
            'status_code' => $response->getStatusCode(),
            'duration_ms' => $duration,
            'content_type' => $response->headers->get('Content-Type'),
        ];

        // Add response content for debugging (limited size)
        if (config('shurjopay.debug') && $response->getStatusCode() >= 400) {
            $content = $response->getContent();
            if (strlen($content) > 1000) {
                $content = substr($content, 0, 1000) . '... (truncated)';
            }
            $logData['response_content'] = $content;
        }

        $level = $response->getStatusCode() >= 400 ? 'warning' : 'debug';
        Log::channel('shurjopay')->{$level}('ShurjoPay Response', $logData);
    }

    /**
     * Sanitize headers to remove sensitive information.
     */
    protected function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = ['authorization', 'x-csrf-token', 'cookie'];

        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['***REDACTED***'];
            }
        }

        return $headers;
    }

    /**
     * Sanitize request body to remove sensitive information.
     */
    protected function sanitizeRequestBody(array $data): array
    {
        $sensitiveFields = ['password', 'token', 'secret', 'key'];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***REDACTED***';
            }
        }

        return $data;
    }
}
