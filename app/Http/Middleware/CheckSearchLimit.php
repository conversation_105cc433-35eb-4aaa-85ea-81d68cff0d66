<?php

namespace App\Http\Middleware;

use App\Services\SubscriptionService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSearchLimit
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $isInertiaRequest = $request->header('X-Inertia');

        // Allow guest users to be handled by GuestSearchController
        if (!$user) {
            if ($isInertiaRequest) {
                // For Inertia requests, redirect to home with error message
                return redirect()->route('home')->with('error', 'Please sign in to search or use the guest search on the home page.');
            }

            return response()->json([
                'error' => 'Authentication required',
                'message' => 'Please sign in to search or use the guest search on the home page.',
                'login_url' => route('login'),
                'home_url' => route('home'),
            ], 401);
        }

        // Admin users have unlimited search access - bypass all limits
        if ($user->isAdmin()) {
            return $next($request);
        }

        if (!$this->subscriptionService->canUserSearch($user)) {
            if ($isInertiaRequest) {
                // For Inertia requests, redirect to subscription plans with error message
                return redirect()->route('subscription.plans')->with([
                    'error' => 'Daily search limit exceeded',
                    'message' => 'You have reached your daily search limit. Upgrade to Premium for unlimited searches.',
                ]);
            }

            return response()->json([
                'error' => 'Daily search limit exceeded',
                'message' => 'You have reached your daily search limit. Upgrade to Premium for unlimited searches.',
                'remaining_searches' => 0,
                'upgrade_url' => route('subscription.plans'),
            ], 429);
        }

        return $next($request);
    }
}
