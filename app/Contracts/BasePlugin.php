<?php

namespace App\Contracts;

use Illuminate\Support\ServiceProvider;

abstract class BasePlugin extends ServiceProvider
{
    /**
     * Plugin name.
     */
    abstract public function getName(): string;

    /**
     * Plugin version.
     */
    abstract public function getVersion(): string;

    /**
     * Plugin description.
     */
    abstract public function getDescription(): string;

    /**
     * Plugin author.
     */
    abstract public function getAuthor(): string;

    /**
     * Plugin dependencies.
     */
    public function getDependencies(): array
    {
        return [];
    }

    /**
     * Register plugin services.
     */
    public function register(): void
    {
        // Override in plugin implementation
    }

    /**
     * Boot plugin services.
     */
    public function boot(): void
    {
        // Override in plugin implementation
    }

    /**
     * Install plugin.
     */
    public function install(): void
    {
        // Override in plugin implementation
    }

    /**
     * Uninstall plugin.
     */
    public function uninstall(): void
    {
        // Override in plugin implementation
    }

    /**
     * Enable plugin.
     */
    public function enable(): void
    {
        // Override in plugin implementation
    }

    /**
     * Disable plugin.
     */
    public function disable(): void
    {
        // Override in plugin implementation
    }

    /**
     * Get plugin configuration.
     */
    public function getConfig(): array
    {
        return [
            'name' => $this->getName(),
            'version' => $this->getVersion(),
            'description' => $this->getDescription(),
            'author' => $this->getAuthor(),
            'dependencies' => $this->getDependencies(),
        ];
    }

    /**
     * Check if plugin is compatible with current system.
     */
    public function isCompatible(): bool
    {
        return true; // Override in plugin implementation
    }

    /**
     * Get plugin routes.
     */
    public function getRoutes(): array
    {
        return []; // Override in plugin implementation
    }

    /**
     * Get plugin middleware.
     */
    public function getMiddleware(): array
    {
        return []; // Override in plugin implementation
    }

    /**
     * Get plugin views.
     */
    public function getViews(): array
    {
        return []; // Override in plugin implementation
    }

    /**
     * Get plugin assets.
     */
    public function getAssets(): array
    {
        return []; // Override in plugin implementation
    }

    /**
     * Get plugin migrations.
     */
    public function getMigrations(): array
    {
        return []; // Override in plugin implementation
    }

    /**
     * Get plugin seeders.
     */
    public function getSeeders(): array
    {
        return []; // Override in plugin implementation
    }
}
