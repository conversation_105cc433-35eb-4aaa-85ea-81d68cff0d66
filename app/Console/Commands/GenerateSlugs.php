<?php

namespace App\Console\Commands;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use Illuminate\Console\Command;

class GenerateSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'slugs:generate {--model=* : Specific models to generate slugs for (brands, categories, models, parts)} {--force : Force regenerate existing slugs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate slugs for existing records in the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $models = $this->option('model');
        $force = $this->option('force');

        // If no specific models provided, generate for all
        if (empty($models)) {
            $models = ['brands', 'categories', 'models', 'parts'];
        }

        $this->info('Starting slug generation...');

        foreach ($models as $model) {
            switch ($model) {
                case 'brands':
                    $this->generateBrandSlugs($force);
                    break;
                case 'categories':
                    $this->generateCategorySlugs($force);
                    break;
                case 'models':
                    $this->generateModelSlugs($force);
                    break;
                case 'parts':
                    $this->generatePartSlugs($force);
                    break;
                default:
                    $this->error("Unknown model: {$model}");
            }
        }

        $this->info('Slug generation completed!');
    }

    /**
     * Generate slugs for brands.
     */
    private function generateBrandSlugs(bool $force = false): void
    {
        $query = Brand::query();

        if (!$force) {
            $query->whereNull('slug');
        }

        $brands = $query->get();
        $count = 0;

        foreach ($brands as $brand) {
            if ($force || empty($brand->slug)) {
                $brand->slug = null; // Reset to trigger regeneration
                $brand->save();
                $count++;
            }
        }

        $this->info("Generated slugs for {$count} brands.");
    }

    /**
     * Generate slugs for categories.
     */
    private function generateCategorySlugs(bool $force = false): void
    {
        $query = Category::query();

        if (!$force) {
            $query->whereNull('slug');
        }

        $categories = $query->get();
        $count = 0;

        foreach ($categories as $category) {
            if ($force || empty($category->slug)) {
                $category->slug = null; // Reset to trigger regeneration
                $category->save();
                $count++;
            }
        }

        $this->info("Generated slugs for {$count} categories.");
    }

    /**
     * Generate slugs for mobile models.
     */
    private function generateModelSlugs(bool $force = false): void
    {
        $query = MobileModel::with('brand');

        if (!$force) {
            $query->whereNull('slug');
        }

        $models = $query->get();
        $count = 0;

        foreach ($models as $model) {
            if ($force || empty($model->slug)) {
                $model->slug = null; // Reset to trigger regeneration
                $model->save();
                $count++;
            }
        }

        $this->info("Generated slugs for {$count} mobile models.");
    }

    /**
     * Generate slugs for parts.
     */
    private function generatePartSlugs(bool $force = false): void
    {
        $query = Part::query();

        if (!$force) {
            $query->whereNull('slug');
        }

        $parts = $query->get();
        $count = 0;

        foreach ($parts as $part) {
            if ($force || empty($part->slug)) {
                $part->slug = null; // Reset to trigger regeneration
                $part->save();
                $count++;
            }
        }

        $this->info("Generated slugs for {$count} parts.");
    }
}
