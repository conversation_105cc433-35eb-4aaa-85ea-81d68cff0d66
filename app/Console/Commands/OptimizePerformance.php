<?php

namespace App\Console\Commands;

use App\Services\CacheService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class OptimizePerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:optimize-performance {--warm-cache : Warm up application caches}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize application performance by clearing caches, optimizing database, and warming up caches';

    private CacheService $cacheService;

    public function __construct(CacheService $cacheService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting performance optimization...');

        // Clear all caches
        $this->clearCaches();

        // Optimize database
        $this->optimizeDatabase();

        // Optimize Laravel
        $this->optimizeLaravel();

        // Warm up caches if requested
        if ($this->option('warm-cache')) {
            $this->warmUpCaches();
        }

        // Show performance stats
        $this->showPerformanceStats();

        $this->info('✅ Performance optimization completed!');
    }

    private function clearCaches(): void
    {
        $this->info('🧹 Clearing caches...');

        // Clear Laravel caches
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');
        Artisan::call('event:clear');

        // Clear application-specific caches
        $this->cacheService->clearSearchCaches();
        $this->cacheService->clearAdminCaches();

        $this->line('   ✓ All caches cleared');
    }

    private function optimizeDatabase(): void
    {
        $this->info('🗄️ Optimizing database...');

        // Analyze and optimize tables
        $tables = DB::select('SHOW TABLES');
        $tableColumn = 'Tables_in_' . config('database.connections.mysql.database');

        foreach ($tables as $table) {
            $tableName = $table->$tableColumn;

            try {
                DB::statement("ANALYZE TABLE `{$tableName}`");
                DB::statement("OPTIMIZE TABLE `{$tableName}`");
                $this->line("   ✓ Optimized table: {$tableName}");
            } catch (\Exception $e) {
                $this->warn("   ⚠️ Could not optimize table: {$tableName}");
            }
        }

        // Update table statistics
        DB::statement('ANALYZE TABLE parts, categories, brands, mobile_models, users');

        $this->line('   ✓ Database optimization completed');
    }

    private function optimizeLaravel(): void
    {
        $this->info('⚡ Optimizing Laravel...');

        // Cache configurations
        Artisan::call('config:cache');
        $this->line('   ✓ Configuration cached');

        // Cache routes
        Artisan::call('route:cache');
        $this->line('   ✓ Routes cached');

        // Cache views
        Artisan::call('view:cache');
        $this->line('   ✓ Views cached');

        // Cache events
        Artisan::call('event:cache');
        $this->line('   ✓ Events cached');

        // Optimize autoloader
        if (app()->environment('production')) {
            exec('composer dump-autoload --optimize --no-dev');
            $this->line('   ✓ Autoloader optimized');
        }
    }

    private function warmUpCaches(): void
    {
        $this->info('🔥 Warming up caches...');

        // Warm up application caches
        $this->cacheService->warmUpCaches();
        $this->line('   ✓ Application caches warmed up');

        // Warm up search-related data
        $this->warmUpSearchData();
        $this->line('   ✓ Search data warmed up');

        // Warm up admin data
        $this->warmUpAdminData();
        $this->line('   ✓ Admin data warmed up');
    }

    private function warmUpSearchData(): void
    {
        // Pre-load popular categories
        DB::table('categories')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Pre-load popular brands
        DB::table('brands')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        // Pre-load recent parts
        DB::table('parts')
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->limit(100)
            ->get();
    }

    private function warmUpAdminData(): void
    {
        // Pre-load admin statistics
        $this->cacheService->cacheAdminStats();

        // Pre-load category and brand counts
        $this->cacheService->cacheCategoriesWithCount();
        $this->cacheService->cacheBrandsWithCount();
    }

    private function showPerformanceStats(): void
    {
        $this->info('📊 Performance Statistics:');

        // Database stats
        $dbStats = $this->getDatabaseStats();
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Parts', number_format($dbStats['total_parts'])],
                ['Total Categories', number_format($dbStats['total_categories'])],
                ['Total Brands', number_format($dbStats['total_brands'])],
                ['Total Models', number_format($dbStats['total_models'])],
                ['Total Users', number_format($dbStats['total_users'])],
                ['Database Size', $dbStats['database_size']],
            ]
        );

        // Cache stats
        $cacheStats = $this->cacheService->getCacheStats();
        $this->line('');
        $this->line("Cache Driver: {$cacheStats['cache_driver']}");
        $this->line("Cached Items: {$cacheStats['cached_items']}");
        $this->line("Cache Size: {$cacheStats['cache_size']}");
    }

    private function getDatabaseStats(): array
    {
        return [
            'total_parts' => DB::table('parts')->count(),
            'total_categories' => DB::table('categories')->count(),
            'total_brands' => DB::table('brands')->count(),
            'total_models' => DB::table('mobile_models')->count(),
            'total_users' => DB::table('users')->count(),
            'database_size' => $this->getDatabaseSize(),
        ];
    }

    private function getDatabaseSize(): string
    {
        try {
            $result = DB::select("
                SELECT
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = ?
            ", [config('database.connections.mysql.database')]);

            return ($result[0]->size_mb ?? 0) . ' MB';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
}
