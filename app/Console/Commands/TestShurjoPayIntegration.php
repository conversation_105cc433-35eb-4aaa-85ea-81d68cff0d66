<?php

namespace App\Console\Commands;

use App\Services\ShurjoPayService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestShurjoPayIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shurjopay:test {--detailed : Show detailed output}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test ShurjoPay integration and configuration';

    /**
     * Execute the console command.
     */
    public function handle(ShurjoPayService $shurjoPayService)
    {
        $this->info('🚀 Testing ShurjoPay Integration...');
        $this->newLine();

        // Test 1: Configuration Check
        $this->info('📋 Checking Configuration...');
        $configValid = $this->testConfiguration();

        if (!$configValid) {
            $this->error('❌ Configuration test failed. Please check your ShurjoPay settings.');
            return 1;
        }

        $this->info('✅ Configuration is valid');
        $this->newLine();

        // Test 2: Authentication Test
        $this->info('🔐 Testing Authentication...');
        $authResult = $this->testAuthentication($shurjoPayService);

        if (!$authResult) {
            $this->error('❌ Authentication test failed. Please check your credentials.');
            return 1;
        }

        $this->info('✅ Authentication successful');
        $this->newLine();

        // Test 3: API Connectivity
        $this->info('🌐 Testing API Connectivity...');
        $connectivityResult = $this->testConnectivity();

        if (!$connectivityResult) {
            $this->error('❌ API connectivity test failed.');
            return 1;
        }

        $this->info('✅ API connectivity successful');
        $this->newLine();

        // Test 4: Environment Check
        $this->info('🔧 Checking Environment...');
        $this->checkEnvironment();
        $this->newLine();

        $this->info('🎉 All tests passed! ShurjoPay integration is ready.');

        return 0;
    }

    /**
     * Test configuration validity.
     */
    protected function testConfiguration(): bool
    {
        $requiredConfigs = [
            'shurjopay.username' => 'Username',
            'shurjopay.password' => 'Password',
            'shurjopay.prefix' => 'Prefix',
            'shurjopay.environment' => 'Environment',
        ];

        $allValid = true;

        foreach ($requiredConfigs as $key => $name) {
            $value = config($key);

            if (empty($value)) {
                $this->error("   ❌ {$name} is not configured");
                $allValid = false;
            } else {
                if ($this->option('detailed')) {
                    $displayValue = in_array($key, ['shurjopay.password']) ? '***configured***' : $value;
                    $this->line("   ✅ {$name}: {$displayValue}");
                }
            }
        }

        // Check environment validity
        $environment = config('shurjopay.environment');
        if (!in_array($environment, ['sandbox', 'production'])) {
            $this->error("   ❌ Invalid environment: {$environment}");
            $allValid = false;
        }

        return $allValid;
    }

    /**
     * Test authentication with ShurjoPay.
     */
    protected function testAuthentication(ShurjoPayService $shurjoPayService): bool
    {
        try {
            $authData = $shurjoPayService->authenticate();

            if ($authData && isset($authData['token'])) {
                if ($this->option('detailed')) {
                    $this->line("   ✅ Token received: " . substr($authData['token'], 0, 20) . '...');
                    $this->line("   ✅ Store ID: " . ($authData['store_id'] ?? 'N/A'));
                    $this->line("   ✅ Token Type: " . ($authData['token_type'] ?? 'N/A'));
                }
                return true;
            }

            $this->error('   ❌ Authentication failed: No token received');
            return false;

        } catch (\Exception $e) {
            $this->error("   ❌ Authentication error: " . $e->getMessage());
            if ($this->option('detailed')) {
                $this->line("   Debug: " . $e->getTraceAsString());
            }
            return false;
        }
    }

    /**
     * Test API connectivity.
     */
    protected function testConnectivity(): bool
    {
        $apiUrl = config('shurjopay.api_url');

        try {
            $response = \Illuminate\Support\Facades\Http::timeout(10)->get($apiUrl);

            if ($response->successful() || $response->status() === 404) {
                // 404 is acceptable as it means the server is reachable
                if ($this->option('detailed')) {
                    $this->line("   ✅ API URL reachable: {$apiUrl}");
                    $this->line("   ✅ Response status: " . $response->status());
                }
                return true;
            }

            $this->error("   ❌ API not reachable. Status: " . $response->status());
            return false;

        } catch (\Exception $e) {
            $this->error("   ❌ Connectivity error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check environment settings.
     */
    protected function checkEnvironment(): void
    {
        $environment = config('shurjopay.environment');
        $debug = config('shurjopay.debug');
        $logging = config('shurjopay.logging.enabled');

        $this->line("   🔧 Environment: {$environment}");
        $this->line("   🔧 Debug Mode: " . ($debug ? 'Enabled' : 'Disabled'));
        $this->line("   🔧 Logging: " . ($logging ? 'Enabled' : 'Disabled'));

        if ($environment === 'production' && $debug) {
            $this->warn("   ⚠️  Warning: Debug mode is enabled in production environment");
        }

        if ($environment === 'sandbox') {
            $this->warn("   ⚠️  Note: Using sandbox environment - switch to production for live payments");
        }

        // Check SSL verification
        $sslVerify = config('shurjopay.ssl_verify');
        if (!$sslVerify) {
            $this->warn("   ⚠️  Warning: SSL verification is disabled");
        }

        // Check log directory
        $logLocation = config('shurjopay.logging.location');
        if ($logging && !is_dir(dirname($logLocation))) {
            $this->warn("   ⚠️  Warning: Log directory does not exist: " . dirname($logLocation));
        }
    }
}
