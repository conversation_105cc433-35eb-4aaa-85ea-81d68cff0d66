<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\PaymentRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;

class CheckUserManagementStatus extends Command
{
    protected $signature = 'user-management:status';
    protected $description = 'Check the status of the User Management System';

    public function handle()
    {
        $this->info('🔍 User Management System Status Check');
        $this->info('=====================================');
        $this->newLine();

        // Check database tables
        $this->info('📊 Database Tables:');
        $tables = [
            'users' => 'Users table',
            'payment_requests' => 'Payment Requests table',
            'user_activity_logs' => 'Activity Logs table',
            'user_searches' => 'User Searches table',
            'user_impersonation_logs' => 'Impersonation Logs table',
        ];

        foreach ($tables as $table => $description) {
            if (Schema::hasTable($table)) {
                $this->info("   ✅ {$description}");
            } else {
                $this->error("   ❌ {$description} - Missing");
            }
        }

        $this->newLine();

        // Check data
        if (Schema::hasTable('users')) {
            $this->info('👥 User Statistics:');
            $totalUsers = User::count();
            $activeUsers = User::where('status', 'active')->count();
            $pendingUsers = User::where('approval_status', 'pending')->count();
            $adminUsers = User::where('email', 'LIKE', '%@mobileparts.com')->count();

            $this->info("   Total Users: {$totalUsers}");
            $this->info("   Active Users: {$activeUsers}");
            $this->info("   Pending Approval: {$pendingUsers}");
            $this->info("   Admin Users: {$adminUsers}");
        }

        $this->newLine();

        if (Schema::hasTable('payment_requests')) {
            $this->info('💳 Payment Request Statistics:');
            $totalPayments = PaymentRequest::count();
            $pendingPayments = PaymentRequest::where('status', 'pending')->count();
            $approvedPayments = PaymentRequest::where('status', 'approved')->count();

            $this->info("   Total Requests: {$totalPayments}");
            $this->info("   Pending: {$pendingPayments}");
            $this->info("   Approved: {$approvedPayments}");
        }

        $this->newLine();

        // Check admin user
        $this->info('🔑 Admin Access:');
        if (Schema::hasTable('users')) {
            $adminUser = User::where('email', '<EMAIL>')->first();
            if ($adminUser) {
                $this->info("   ✅ Admin user exists: {$adminUser->email}");
                $this->info("   Status: {$adminUser->status}");
                $this->info("   Approval: {$adminUser->approval_status}");
            } else {
                $this->error('   ❌ Admin user not found');
                $this->warn('   Run: php setup-user-management.php to create admin user');
            }
        }

        $this->newLine();

        // Check routes
        $this->info('🌐 Available Routes:');
        $routes = [
            '/admin/dashboard' => 'Admin Dashboard',
            '/admin/users' => 'User Management',
            '/admin/payment-requests' => 'Payment Requests',
        ];

        foreach ($routes as $route => $description) {
            $this->info("   🔗 {$route} - {$description}");
        }

        $this->newLine();

        // Overall status
        $allTablesExist = true;
        foreach (array_keys($tables) as $table) {
            if (!Schema::hasTable($table)) {
                $allTablesExist = false;
                break;
            }
        }

        $adminExists = Schema::hasTable('users') && User::where('email', '<EMAIL>')->exists();

        if ($allTablesExist && $adminExists) {
            $this->info('🎉 System Status: READY');
            $this->info('   All components are properly configured!');
        } else {
            $this->error('⚠️  System Status: NEEDS SETUP');
            if (!$allTablesExist) {
                $this->warn('   Run: php artisan migrate');
            }
            if (!$adminExists) {
                $this->warn('   Run: php setup-user-management.php');
            }
        }

        return 0;
    }
}
