<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\ShurjoPayTransaction;
use App\Services\ShurjoPayService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TestShurjoPayPaymentFlow extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shurjopay:test-payment {--user-id= : User ID to test with} {--plan-id= : Plan ID to test with} {--amount=100 : Test amount}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test complete ShurjoPay payment flow with real API calls';

    /**
     * Execute the console command.
     */
    public function handle(ShurjoPayService $shurjoPayService)
    {
        $this->info('🧪 Testing ShurjoPay Payment Flow...');
        $this->newLine();

        // Step 1: Get or create test user
        $user = $this->getTestUser();
        if (!$user) {
            $this->error('❌ Failed to get test user');
            return 1;
        }

        $this->info("✅ Test User: {$user->name} (ID: {$user->id})");

        // Step 2: Get or create test plan
        $plan = $this->getTestPlan();
        if (!$plan) {
            $this->error('❌ Failed to get test plan');
            return 1;
        }

        $this->info("✅ Test Plan: {$plan->name} (ID: {$plan->id})");
        $this->newLine();

        // Step 3: Test Authentication
        $this->info('🔐 Testing Authentication...');
        $authResult = $shurjoPayService->authenticate();
        if (!$authResult) {
            $this->error('❌ Authentication failed');
            return 1;
        }
        $this->info('✅ Authentication successful');
        $this->newLine();

        // Step 4: Test Payment Creation
        $this->info('💳 Testing Payment Creation...');
        $amount = (float)$this->option('amount');

        try {
            $paymentResult = $shurjoPayService->createPayment($user, $plan, $amount, 'BDT');

            if (!$paymentResult) {
                $this->error('❌ Payment creation failed');
                return 1;
            }

            $this->info('✅ Payment created successfully');
            $this->table(['Field', 'Value'], [
                ['SP Order ID', $paymentResult['sp_order_id'] ?? 'N/A'],
                ['Checkout URL', $paymentResult['checkout_url'] ?? 'N/A'],
                ['Amount', $amount . ' BDT'],
                ['Currency', 'BDT'],
            ]);

            // Step 5: Save transaction to database
            $this->info('💾 Saving transaction to database...');
            $transaction = $this->saveTestTransaction($user, $plan, $paymentResult, $amount);

            if ($transaction) {
                $this->info("✅ Transaction saved (ID: {$transaction->id})");
            } else {
                $this->warn('⚠️  Failed to save transaction to database');
            }

            $this->newLine();

            // Step 6: Display checkout information
            $this->displayCheckoutInfo($paymentResult);

            // Step 7: Test payment verification (if order ID is available)
            if (isset($paymentResult['sp_order_id'])) {
                $this->newLine();
                $this->info('🔍 Testing Payment Verification...');

                $verificationResult = $shurjoPayService->verifyPayment($paymentResult['sp_order_id']);

                if ($verificationResult) {
                    $this->info('✅ Payment verification successful');
                    $this->table(['Field', 'Value'], [
                        ['SP Code', $verificationResult['sp_code'] ?? 'N/A'],
                        ['SP Message', $verificationResult['sp_message'] ?? 'N/A'],
                        ['Transaction Status', $verificationResult['transaction_status'] ?? 'N/A'],
                        ['Method', $verificationResult['method'] ?? 'N/A'],
                    ]);
                } else {
                    $this->warn('⚠️  Payment verification returned no data (expected for new payment)');
                }
            }

            $this->newLine();
            $this->info('🎉 Payment flow test completed successfully!');
            $this->newLine();

            // Display next steps
            $this->displayNextSteps($paymentResult);

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Payment flow test failed: ' . $e->getMessage());
            if ($this->output->isVerbose()) {
                $this->line($e->getTraceAsString());
            }
            return 1;
        }
    }

    /**
     * Get or create a test user.
     */
    protected function getTestUser(): ?User
    {
        $userId = $this->option('user-id');

        if ($userId) {
            return User::find($userId);
        }

        // Find or create a test user
        return User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'ShurjoPay Test User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'phone' => '01712345678',
                'address' => 'House 123, Road 456, Dhanmondi',
                'city' => 'Dhaka',
                'state' => 'Dhaka',
                'country' => 'BD',
                'postal_code' => '1205',
            ]
        );
    }

    /**
     * Get or create a test plan.
     */
    protected function getTestPlan(): ?PricingPlan
    {
        $planId = $this->option('plan-id');

        if ($planId) {
            return PricingPlan::find($planId);
        }

        // Find or create a test plan
        return PricingPlan::firstOrCreate(
            ['name' => 'shurjopay_test_plan'],
            [
                'display_name' => 'ShurjoPay Test Plan',
                'description' => 'Test plan for ShurjoPay integration',
                'price' => 100.00,
                'currency' => 'USD',
                'interval' => 'month',
                'features' => ['Test Feature 1', 'Test Feature 2'],
                'search_limit' => 1000,
                'is_active' => true,
                'online_payment_enabled' => true,
                'offline_payment_enabled' => false,
            ]
        );
    }

    /**
     * Save test transaction to database.
     */
    protected function saveTestTransaction(User $user, PricingPlan $plan, array $paymentResult, float $amount): ?ShurjoPayTransaction
    {
        try {
            return ShurjoPayTransaction::create([
                'shurjopay_order_id' => $paymentResult['sp_order_id'] ?? 'test_' . uniqid(),
                'merchant_order_id' => 'TEST_' . time() . '_' . uniqid(),
                'user_id' => $user->id,
                'pricing_plan_id' => $plan->id,
                'status' => 'pending',
                'currency' => 'BDT',
                'amount' => $amount,
                'customer_details' => [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone,
                ],
                'shurjopay_data' => $paymentResult,
                'checkout_url' => $paymentResult['checkout_url'] ?? null,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to save test transaction', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'plan_id' => $plan->id,
            ]);
            return null;
        }
    }

    /**
     * Display checkout information.
     */
    protected function displayCheckoutInfo(array $paymentResult): void
    {
        $this->info('🛒 Checkout Information:');
        $this->line('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

        if (isset($paymentResult['checkout_url'])) {
            $this->line("🔗 Checkout URL: {$paymentResult['checkout_url']}");
            $this->newLine();
            $this->line("📱 To complete the payment:");
            $this->line("   1. Open the checkout URL in your browser");
            $this->line("   2. Select your preferred payment method");
            $this->line("   3. Complete the payment process");
            $this->line("   4. You'll be redirected back to the application");
        } else {
            $this->warn('⚠️  No checkout URL received');
        }
    }

    /**
     * Display next steps.
     */
    protected function displayNextSteps(array $paymentResult): void
    {
        $this->info('📋 Next Steps:');
        $this->line('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        $this->line('1. 🌐 Visit the merchant panel: https://sandbox.admin.shurjopayment.com');
        $this->line('   Username: <EMAIL>');
        $this->line('   Password: R1451212r@');
        $this->newLine();
        $this->line('2. 💳 Test the checkout URL in your browser');
        $this->line('3. 📊 Monitor logs: tail -f storage/logs/shurjopay.log');
        $this->line('4. 🔍 Check transaction status in the database');
        $this->newLine();
        $this->line('🧪 Test Payment Methods:');
        $this->line('   • Mobile Banking (bKash, Nagad, Rocket)');
        $this->line('   • Internet Banking');
        $this->line('   • Card Payments');
        $this->newLine();
        $this->line('📱 For mobile banking test, use sandbox test numbers provided by ShurjoPay');
    }
}
