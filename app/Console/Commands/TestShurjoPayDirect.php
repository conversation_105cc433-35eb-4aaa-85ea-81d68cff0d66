<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestShurjoPayDirect extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shurjopay:test-direct';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test ShurjoPay API directly without plugin';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing ShurjoPay API Directly...');
        $this->newLine();

        // Step 1: Authentication
        $this->info('🔐 Step 1: Authentication');
        $authResult = $this->authenticate();

        if (!$authResult) {
            $this->error('❌ Authentication failed');
            return 1;
        }

        $this->info('✅ Authentication successful');
        $this->table(['Field', 'Value'], [
            ['Token (first 20 chars)', substr($authResult['token'], 0, 20) . '...'],
            ['Store ID', $authResult['store_id']],
            ['Token Type', $authResult['token_type']],
            ['Expires In', $authResult['expires_in'] . ' seconds'],
        ]);
        $this->newLine();

        // Step 2: Create Payment
        $this->info('💳 Step 2: Create Payment');
        $paymentResult = $this->createPayment($authResult);

        if (!$paymentResult) {
            $this->error('❌ Payment creation failed');
            return 1;
        }

        $this->info('✅ Payment created successfully');
        $this->table(['Field', 'Value'], [
            ['SP Order ID', $paymentResult['sp_order_id'] ?? 'N/A'],
            ['Checkout URL', $paymentResult['checkout_url'] ?? 'N/A'],
            ['Amount', $paymentResult['amount'] ?? 'N/A'],
            ['Currency', $paymentResult['currency'] ?? 'N/A'],
        ]);
        $this->newLine();

        // Step 3: Display checkout info
        $this->displayCheckoutInfo($paymentResult);

        return 0;
    }

    /**
     * Authenticate with ShurjoPay API.
     */
    protected function authenticate(): ?array
    {
        try {
            $response = Http::timeout(30)->post('https://sandbox.shurjopayment.com/api/get_token', [
                'username' => config('shurjopay.username'),
                'password' => config('shurjopay.password'),
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            $this->error('Authentication failed: ' . $response->status() . ' - ' . $response->body());
            return null;

        } catch (\Exception $e) {
            $this->error('Authentication error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create payment with ShurjoPay API.
     */
    protected function createPayment(array $authData): ?array
    {
        try {
            $orderId = 'TEST_' . time() . '_' . uniqid();

            $paymentData = [
                'token' => $authData['token'],
                'store_id' => $authData['store_id'],
                'prefix' => config('shurjopay.prefix'),
                'currency' => 'BDT',
                'return_url' => config('shurjopay.success_url'),
                'cancel_url' => config('shurjopay.cancel_url'),
                'amount' => 500,
                'order_id' => $orderId,
                'discsount_amount' => 0,
                'disc_percent' => 0,
                'client_ip' => '127.0.0.1',
                'customer_name' => 'Test Customer',
                'customer_phone' => '01712345678',
                'customer_email' => '<EMAIL>',
                'customer_address' => 'Test Address, Dhaka',
                'customer_city' => 'Dhaka',
                'customer_state' => 'Dhaka',
                'customer_postcode' => '1000',
                'customer_country' => 'BD',
                'value1' => 'test_value_1',
                'value2' => 'test_value_2',
                'value3' => 'test_value_3',
                'value4' => 'test_value_4',
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $authData['token'],
                'Content-Type' => 'application/json',
            ])->timeout(30)->post('https://sandbox.shurjopayment.com/api/secret-pay', $paymentData);

            if ($response->successful()) {
                return $response->json();
            }

            $this->error('Payment creation failed: ' . $response->status() . ' - ' . $response->body());
            return null;

        } catch (\Exception $e) {
            $this->error('Payment creation error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Display checkout information.
     */
    protected function displayCheckoutInfo(array $paymentResult): void
    {
        $this->info('🛒 Checkout Information:');
        $this->line('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

        if (isset($paymentResult['checkout_url'])) {
            $this->line("🔗 Checkout URL: {$paymentResult['checkout_url']}");
            $this->newLine();
            $this->line("📱 To complete the payment:");
            $this->line("   1. Open the checkout URL in your browser");
            $this->line("   2. Select your preferred payment method");
            $this->line("   3. Complete the payment process");
            $this->line("   4. You'll be redirected back to the application");
            $this->newLine();
            $this->line("🌐 Merchant Panel: https://sandbox.admin.shurjopayment.com");
            $this->line("   Username: <EMAIL>");
            $this->line("   Password: R1451212r@");
        } else {
            $this->warn('⚠️  No checkout URL received');
        }
    }
}
