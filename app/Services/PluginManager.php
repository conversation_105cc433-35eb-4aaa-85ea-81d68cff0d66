<?php

namespace App\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;

class PluginManager
{
    protected Collection $plugins;
    protected Collection $hooks;

    public function __construct()
    {
        $this->plugins = collect();
        $this->hooks = collect();
    }

    /**
     * Register a plugin.
     */
    public function register(string $name, string $serviceProvider, array $config = []): void
    {
        $this->plugins->put($name, [
            'name' => $name,
            'service_provider' => $serviceProvider,
            'config' => $config,
            'enabled' => $config['enabled'] ?? true,
            'version' => $config['version'] ?? '1.0.0',
            'description' => $config['description'] ?? '',
            'author' => $config['author'] ?? '',
        ]);
    }

    /**
     * Get all registered plugins.
     */
    public function getPlugins(): Collection
    {
        return $this->plugins;
    }

    /**
     * Get enabled plugins.
     */
    public function getEnabledPlugins(): Collection
    {
        return $this->plugins->where('enabled', true);
    }

    /**
     * Check if a plugin is registered.
     */
    public function hasPlugin(string $name): bool
    {
        return $this->plugins->has($name);
    }

    /**
     * Get a specific plugin.
     */
    public function getPlugin(string $name): ?array
    {
        return $this->plugins->get($name);
    }

    /**
     * Enable a plugin.
     */
    public function enablePlugin(string $name): bool
    {
        if (!$this->hasPlugin($name)) {
            return false;
        }

        $plugin = $this->plugins->get($name);
        $plugin['enabled'] = true;
        $this->plugins->put($name, $plugin);

        return true;
    }

    /**
     * Disable a plugin.
     */
    public function disablePlugin(string $name): bool
    {
        if (!$this->hasPlugin($name)) {
            return false;
        }

        $plugin = $this->plugins->get($name);
        $plugin['enabled'] = false;
        $this->plugins->put($name, $plugin);

        return true;
    }

    /**
     * Register a hook.
     */
    public function addHook(string $name, callable $callback, int $priority = 10): void
    {
        if (!$this->hooks->has($name)) {
            $this->hooks->put($name, collect());
        }

        $this->hooks->get($name)->push([
            'callback' => $callback,
            'priority' => $priority,
        ]);

        // Sort by priority
        $this->hooks->put($name, $this->hooks->get($name)->sortBy('priority'));
    }

    /**
     * Execute hooks for a given name.
     */
    public function executeHook(string $name, ...$args): mixed
    {
        if (!$this->hooks->has($name)) {
            return $args[0] ?? null;
        }

        $result = $args[0] ?? null;

        foreach ($this->hooks->get($name) as $hook) {
            $result = call_user_func($hook['callback'], $result, ...$args);
        }

        return $result;
    }

    /**
     * Get all hooks for a given name.
     */
    public function getHooks(string $name): Collection
    {
        return $this->hooks->get($name, collect());
    }

    /**
     * Load plugins from configuration.
     */
    public function loadFromConfig(): void
    {
        $plugins = config('plugins.registered', []);

        foreach ($plugins as $name => $config) {
            if (is_array($config) && isset($config['service_provider'])) {
                $this->register($name, $config['service_provider'], $config);
            }
        }
    }

    /**
     * Discover plugins from a directory.
     */
    public function discoverPlugins(string $directory): Collection
    {
        $discovered = collect();

        if (!File::exists($directory)) {
            return $discovered;
        }

        $pluginDirs = File::directories($directory);

        foreach ($pluginDirs as $pluginDir) {
            $configFile = $pluginDir . '/plugin.json';
            
            if (File::exists($configFile)) {
                $config = json_decode(File::get($configFile), true);
                
                if ($config && isset($config['name'], $config['service_provider'])) {
                    $discovered->put($config['name'], $config);
                }
            }
        }

        return $discovered;
    }

    /**
     * Install a plugin.
     */
    public function installPlugin(string $name, array $config): bool
    {
        try {
            // Register the plugin
            $this->register($name, $config['service_provider'], $config);

            // Run installation hooks if available
            if (isset($config['install_hook'])) {
                call_user_func($config['install_hook']);
            }

            return true;
        } catch (\Exception $e) {
            // Log error
            logger()->error("Failed to install plugin {$name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Uninstall a plugin.
     */
    public function uninstallPlugin(string $name): bool
    {
        if (!$this->hasPlugin($name)) {
            return false;
        }

        try {
            $plugin = $this->getPlugin($name);

            // Run uninstallation hooks if available
            if (isset($plugin['config']['uninstall_hook'])) {
                call_user_func($plugin['config']['uninstall_hook']);
            }

            // Remove from registered plugins
            $this->plugins->forget($name);

            return true;
        } catch (\Exception $e) {
            // Log error
            logger()->error("Failed to uninstall plugin {$name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get plugin statistics.
     */
    public function getStats(): array
    {
        return [
            'total_plugins' => $this->plugins->count(),
            'enabled_plugins' => $this->getEnabledPlugins()->count(),
            'disabled_plugins' => $this->plugins->where('enabled', false)->count(),
            'total_hooks' => $this->hooks->sum(fn($hooks) => $hooks->count()),
        ];
    }

    /**
     * Check plugin dependencies.
     */
    public function checkDependencies(string $name): array
    {
        $plugin = $this->getPlugin($name);
        $missing = [];

        if (!$plugin || !isset($plugin['config']['dependencies'])) {
            return $missing;
        }

        foreach ($plugin['config']['dependencies'] as $dependency) {
            if (!$this->hasPlugin($dependency) || !$this->getPlugin($dependency)['enabled']) {
                $missing[] = $dependency;
            }
        }

        return $missing;
    }
}
