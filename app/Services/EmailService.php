<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use SendGrid\Mail\Mail as SendGridMail;
use SendGrid;

class EmailService
{
    /**
     * Send email using the configured provider.
     */
    public function send($mailable, $to, array $options = []): bool
    {
        try {
            $provider = config('mail.default');

            Log::info('Attempting to send email', [
                'provider' => $provider,
                'to' => $to,
                'mailable' => get_class($mailable)
            ]);

            switch ($provider) {
                case 'sendgrid':
                    return $this->sendViaSendGrid($mailable, $to, $options);
                case 'smtp':
                default:
                    return $this->sendViaSmtp($mailable, $to, $options);
            }
        } catch (\Exception $e) {
            Log::error('Email sending failed', [
                'to' => $to,
                'mailable' => get_class($mailable),
                'provider' => config('mail.default'),
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'config' => [
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'encryption' => config('mail.mailers.smtp.encryption'),
                    'username' => config('mail.mailers.smtp.username') ? 'configured' : 'not configured',
                    'password' => config('mail.mailers.smtp.password') ? 'configured' : 'not configured',
                ]
            ]);

            return false;
        }
    }

    /**
     * Send email via SMTP.
     */
    private function sendViaSmtp($mailable, $to, array $options = []): bool
    {
        try {
            Mail::to($to)->send($mailable);

            Log::info('Email sent via SMTP successfully', [
                'to' => $to,
                'mailable' => get_class($mailable),
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port')
            ]);

            return true;
        } catch (\Symfony\Component\Mailer\Exception\TransportException $e) {
            Log::error('SMTP transport error', [
                'to' => $to,
                'error' => $e->getMessage(),
                'error_type' => 'transport',
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port'),
                'encryption' => config('mail.mailers.smtp.encryption')
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('SMTP email sending failed', [
                'to' => $to,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Test SMTP connection using Symfony Mailer.
     */
    private function testSmtpConnection(): void
    {
        // In Symfony Mailer, we test connection by attempting to send a test message
        // The actual connection test is handled internally by Symfony Mailer
        $mailer = app('mailer');

        // Get the transport for the current mailer
        $transport = $mailer->getSymfonyTransport();

        // Test the connection by pinging the transport
        if (method_exists($transport, 'ping')) {
            $transport->ping();
        }
    }

    /**
     * Send email via SendGrid.
     */
    private function sendViaSendGrid($mailable, $to, array $options = []): bool
    {
        try {
            $sendgrid = new SendGrid(config('services.sendgrid.api_key'));
            
            // Build the email
            $email = new SendGridMail();
            $email->setFrom(config('mail.from.address'), config('mail.from.name'));
            $email->setSubject($mailable->envelope()->subject);
            $email->addTo($to);
            
            // Get the rendered content
            $content = $this->renderMailable($mailable);
            $email->addContent("text/html", $content['html']);
            
            if (isset($content['text'])) {
                $email->addContent("text/plain", $content['text']);
            }
            
            // Add custom headers if provided
            if (isset($options['headers'])) {
                foreach ($options['headers'] as $key => $value) {
                    $email->addHeader($key, $value);
                }
            }
            
            // Send the email
            $response = $sendgrid->send($email);
            
            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                Log::info('Email sent via SendGrid', [
                    'to' => $to,
                    'mailable' => get_class($mailable),
                    'status_code' => $response->statusCode()
                ]);
                
                return true;
            } else {
                Log::error('SendGrid email sending failed', [
                    'to' => $to,
                    'status_code' => $response->statusCode(),
                    'body' => $response->body()
                ]);
                
                return false;
            }
        } catch (\Exception $e) {
            Log::error('SendGrid email sending failed', [
                'to' => $to,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Render mailable content.
     */
    private function renderMailable($mailable): array
    {
        $content = $mailable->content();
        $data = $content->with ?? [];
        
        $html = view($content->html, $data)->render();
        $text = null;
        
        if ($content->text) {
            $text = view($content->text, $data)->render();
        }
        
        return [
            'html' => $html,
            'text' => $text
        ];
    }

    /**
     * Test email configuration.
     */
    public function testConfiguration(?string $provider = null): array
    {
        $provider = $provider ?? config('mail.default');
        
        try {
            switch ($provider) {
                case 'sendgrid':
                    return $this->testSendGridConfiguration();
                case 'smtp':
                default:
                    return $this->testSmtpConfiguration();
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Configuration test failed: ' . $e->getMessage(),
                'provider' => $provider
            ];
        }
    }

    /**
     * Test SMTP configuration.
     */
    private function testSmtpConfiguration(): array
    {
        try {
            // Validate configuration first
            $host = config('mail.mailers.smtp.host');
            $port = config('mail.mailers.smtp.port');
            $username = config('mail.mailers.smtp.username');
            $password = config('mail.mailers.smtp.password');
            $encryption = config('mail.mailers.smtp.encryption');

            if (empty($host) || empty($port) || empty($username) || empty($password)) {
                return [
                    'success' => false,
                    'message' => 'SMTP configuration is incomplete. Please check host, port, username, and password.',
                    'provider' => 'smtp',
                    'details' => [
                        'host' => $host ?: 'missing',
                        'port' => $port ?: 'missing',
                        'username' => $username ? 'configured' : 'missing',
                        'password' => $password ? 'configured' : 'missing',
                        'encryption' => $encryption ?: 'none'
                    ]
                ];
            }

            // Test connection by attempting to send a test email to a non-existent address
            // This will validate SMTP connection without actually delivering an email
            try {
                // Use Laravel's Mail facade to test the connection
                Mail::raw('SMTP Connection Test', function ($message) {
                    $message->to('<EMAIL>')
                           ->subject('SMTP Connection Test');
                });

                // If we get here without an exception, the SMTP connection is working
                return [
                    'success' => true,
                    'message' => 'SMTP configuration is valid and connection successful',
                    'provider' => 'smtp',
                    'details' => [
                        'host' => $host,
                        'port' => $port,
                        'encryption' => $encryption,
                        'username' => $username,
                        'connection_test' => 'passed'
                    ]
                ];

            } catch (\Symfony\Component\Mailer\Exception\TransportException $e) {
                // Check if the error is about the invalid email domain (which means SMTP connection worked)
                if (strpos($e->getMessage(), 'nonexistent-domain-for-testing.invalid') !== false ||
                    strpos($e->getMessage(), 'No MX record') !== false ||
                    strpos($e->getMessage(), 'Domain not found') !== false) {
                    // SMTP connection worked, just the test domain doesn't exist (which is expected)
                    return [
                        'success' => true,
                        'message' => 'SMTP configuration is valid and connection successful',
                        'provider' => 'smtp',
                        'details' => [
                            'host' => $host,
                            'port' => $port,
                            'encryption' => $encryption,
                            'username' => $username,
                            'connection_test' => 'passed'
                        ]
                    ];
                } else {
                    // Real SMTP connection error
                    return [
                        'success' => false,
                        'message' => 'SMTP connection failed: ' . $e->getMessage(),
                        'provider' => 'smtp',
                        'error_type' => 'transport',
                        'details' => [
                            'host' => $host,
                            'port' => $port,
                            'encryption' => $encryption,
                            'error_details' => $e->getMessage()
                        ]
                    ];
                }
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'SMTP configuration test failed: ' . $e->getMessage(),
                'provider' => 'smtp',
                'error_type' => get_class($e),
                'details' => [
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'encryption' => config('mail.mailers.smtp.encryption'),
                    'error_details' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * Test SendGrid configuration.
     */
    private function testSendGridConfiguration(): array
    {
        try {
            $apiKey = config('services.sendgrid.api_key');
            
            if (!$apiKey) {
                throw new \Exception('SendGrid API key not configured');
            }
            
            $sendgrid = new SendGrid($apiKey);
            
            // Test API key validity by making a simple API call
            $response = $sendgrid->client->user()->get();
            
            if ($response->statusCode() === 200) {
                return [
                    'success' => true,
                    'message' => 'SendGrid configuration is valid',
                    'provider' => 'sendgrid',
                    'api_key_prefix' => substr($apiKey, 0, 10) . '...'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'SendGrid API key is invalid',
                    'provider' => 'sendgrid',
                    'status_code' => $response->statusCode()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'SendGrid configuration test failed: ' . $e->getMessage(),
                'provider' => 'sendgrid'
            ];
        }
    }

    /**
     * Get email statistics.
     */
    public function getEmailStatistics(int $days = 30): array
    {
        // This would typically integrate with your email provider's API
        // For now, we'll return mock data structure
        
        return [
            'total_sent' => 0,
            'total_delivered' => 0,
            'total_bounced' => 0,
            'total_opened' => 0,
            'total_clicked' => 0,
            'delivery_rate' => 0,
            'open_rate' => 0,
            'click_rate' => 0,
            'bounce_rate' => 0,
            'provider' => config('mail.default'),
            'period_days' => $days
        ];
    }

    /**
     * Validate email address.
     */
    public function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Get email provider status.
     */
    public function getProviderStatus(): array
    {
        $provider = config('mail.default');
        
        return [
            'provider' => $provider,
            'configured' => $this->isProviderConfigured($provider),
            'status' => $this->getProviderHealthStatus($provider)
        ];
    }

    /**
     * Check if email provider is properly configured.
     */
    private function isProviderConfigured(string $provider): bool
    {
        switch ($provider) {
            case 'sendgrid':
                return !empty(config('services.sendgrid.api_key'));
            case 'smtp':
                return !empty(config('mail.mailers.smtp.host')) && 
                       !empty(config('mail.mailers.smtp.port'));
            default:
                return false;
        }
    }

    /**
     * Get provider health status.
     */
    private function getProviderHealthStatus(string $provider): string
    {
        $test = $this->testConfiguration($provider);
        return $test['success'] ? 'healthy' : 'unhealthy';
    }
}
