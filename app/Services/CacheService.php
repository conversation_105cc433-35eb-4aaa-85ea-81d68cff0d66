<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CacheService
{
    /**
     * Cache duration constants (in seconds).
     */
    const SHORT_CACHE = 300;    // 5 minutes
    const MEDIUM_CACHE = 1800;  // 30 minutes
    const LONG_CACHE = 3600;    // 1 hour
    const DAILY_CACHE = 86400;  // 24 hours

    /**
     * Cache search results.
     */
    public function cacheSearchResults(string $query, array $filters, $results): void
    {
        $key = $this->generateSearchCacheKey($query, $filters);
        Cache::put($key, $results, self::MEDIUM_CACHE);
    }

    /**
     * Get cached search results.
     */
    public function getCachedSearchResults(string $query, array $filters)
    {
        $key = $this->generateSearchCacheKey($query, $filters);
        return Cache::get($key);
    }

    /**
     * Cache search suggestions.
     */
    public function cacheSuggestions(string $query, array $suggestions): void
    {
        $key = "search_suggestions:" . md5($query);
        Cache::put($key, $suggestions, self::LONG_CACHE);
    }

    /**
     * Get cached suggestions.
     */
    public function getCachedSuggestions(string $query): ?array
    {
        $key = "search_suggestions:" . md5($query);
        return Cache::get($key);
    }

    /**
     * Cache popular searches.
     */
    public function cachePopularSearches(): array
    {
        return Cache::remember('popular_searches', self::DAILY_CACHE, function () {
            return DB::table('user_searches')
                ->select('search_query', DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', now()->subDays(7))
                ->groupBy('search_query')
                ->orderByDesc('count')
                ->limit(10)
                ->pluck('search_query')
                ->toArray();
        });
    }

    /**
     * Cache categories with parts count.
     */
    public function cacheCategoriesWithCount(): array
    {
        return Cache::remember('categories_with_count', self::LONG_CACHE, function () {
            return DB::table('categories')
                ->leftJoin('parts', 'categories.id', '=', 'parts.category_id')
                ->select('categories.*', DB::raw('COUNT(parts.id) as parts_count'))
                ->where('categories.is_active', true)
                ->groupBy('categories.id')
                ->orderBy('categories.sort_order')
                ->get()
                ->toArray();
        });
    }

    /**
     * Cache brands with models count.
     */
    public function cacheBrandsWithCount(): array
    {
        return Cache::remember('brands_with_count', self::LONG_CACHE, function () {
            return DB::table('brands')
                ->leftJoin('mobile_models', 'brands.id', '=', 'mobile_models.brand_id')
                ->select('brands.*', DB::raw('COUNT(mobile_models.id) as models_count'))
                ->where('brands.is_active', true)
                ->groupBy('brands.id')
                ->orderBy('brands.name')
                ->get()
                ->toArray();
        });
    }

    /**
     * Cache admin dashboard statistics.
     */
    public function cacheAdminStats(): array
    {
        return Cache::remember('admin_stats', self::SHORT_CACHE, function () {
            return [
                'total_users' => DB::table('users')->count(),
                'premium_users' => DB::table('users')->where('subscription_plan', 'premium')->count(),
                'total_parts' => DB::table('parts')->where('is_active', true)->count(),
                'total_searches_today' => DB::table('user_searches')
                    ->whereDate('created_at', today())
                    ->count(),
                'total_searches_week' => DB::table('user_searches')
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count(),
                'active_subscriptions' => DB::table('subscriptions')
                    ->where('status', 'active')
                    ->count(),
                'recent_registrations' => DB::table('users')
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count(),
            ];
        });
    }

    /**
     * Cache user's search statistics.
     */
    public function cacheUserStats(int $userId): array
    {
        $key = "user_stats:{$userId}";
        
        return Cache::remember($key, self::MEDIUM_CACHE, function () use ($userId) {
            return [
                'total_searches' => DB::table('user_searches')
                    ->where('user_id', $userId)
                    ->count(),
                'searches_today' => DB::table('user_searches')
                    ->where('user_id', $userId)
                    ->whereDate('created_at', today())
                    ->count(),
                'searches_this_week' => DB::table('user_searches')
                    ->where('user_id', $userId)
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count(),
                'favorite_categories' => DB::table('user_searches')
                    ->join('parts', function ($join) {
                        $join->on(DB::raw("JSON_UNQUOTE(JSON_EXTRACT(user_searches.results_data, '$.part_ids[0]'))"), '=', 'parts.id');
                    })
                    ->join('categories', 'parts.category_id', '=', 'categories.id')
                    ->where('user_searches.user_id', $userId)
                    ->select('categories.name', DB::raw('COUNT(*) as count'))
                    ->groupBy('categories.id', 'categories.name')
                    ->orderByDesc('count')
                    ->limit(5)
                    ->get()
                    ->toArray(),
            ];
        });
    }

    /**
     * Cache part details with related data.
     */
    public function cachePartDetails(int $partId): array
    {
        $key = "part_details:{$partId}";
        
        return Cache::remember($key, self::LONG_CACHE, function () use ($partId) {
            return DB::table('parts')
                ->leftJoin('categories', 'parts.category_id', '=', 'categories.id')
                ->leftJoin('part_model', 'parts.id', '=', 'part_model.part_id')
                ->leftJoin('mobile_models', 'part_model.model_id', '=', 'mobile_models.id')
                ->leftJoin('brands', 'mobile_models.brand_id', '=', 'brands.id')
                ->where('parts.id', $partId)
                ->select(
                    'parts.*',
                    'categories.name as category_name',
                    DB::raw('GROUP_CONCAT(DISTINCT brands.name) as compatible_brands'),
                    DB::raw('GROUP_CONCAT(DISTINCT mobile_models.name) as compatible_models')
                )
                ->groupBy('parts.id')
                ->first();
        });
    }

    /**
     * Clear all search-related caches.
     */
    public function clearSearchCaches(): void
    {
        Cache::forget('popular_searches');
        Cache::forget('categories_with_count');
        Cache::forget('brands_with_count');
        
        // Clear search result caches (pattern-based)
        $this->clearCacheByPattern('search_results:*');
        $this->clearCacheByPattern('search_suggestions:*');
    }

    /**
     * Clear user-specific caches.
     */
    public function clearUserCaches(int $userId): void
    {
        Cache::forget("user_stats:{$userId}");
    }

    /**
     * Clear admin caches.
     */
    public function clearAdminCaches(): void
    {
        Cache::forget('admin_stats');
        $this->clearSearchCaches();
    }

    /**
     * Warm up essential caches.
     */
    public function warmUpCaches(): void
    {
        // Warm up popular searches
        $this->cachePopularSearches();
        
        // Warm up categories and brands
        $this->cacheCategoriesWithCount();
        $this->cacheBrandsWithCount();
        
        // Warm up admin stats
        $this->cacheAdminStats();
    }

    /**
     * Generate cache key for search results.
     */
    private function generateSearchCacheKey(string $query, array $filters): string
    {
        $filterString = http_build_query($filters);
        return "search_results:" . md5($query . $filterString);
    }

    /**
     * Clear cache by pattern (Redis only).
     */
    private function clearCacheByPattern(string $pattern): void
    {
        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            $redis = Cache::getStore()->getRedis();
            $keys = $redis->keys(Cache::getStore()->getPrefix() . $pattern);
            
            if (!empty($keys)) {
                $redis->del($keys);
            }
        }
    }

    /**
     * Get cache statistics.
     */
    public function getCacheStats(): array
    {
        $stats = [
            'cache_driver' => config('cache.default'),
            'cached_items' => 0,
            'cache_size' => 0,
        ];

        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            $redis = Cache::getStore()->getRedis();
            $info = $redis->info('memory');
            
            $stats['cache_size'] = $info['used_memory_human'] ?? 'Unknown';
            $stats['cached_items'] = $redis->dbsize();
        }

        return $stats;
    }
}
