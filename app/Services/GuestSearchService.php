<?php

namespace App\Services;

use App\Models\Part;
use App\Models\SearchConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class GuestSearchService
{
    /**
     * Perform a search for guest users with limitations.
     */
    public function searchParts(Request $request): array
    {
        $query = $request->get('q', '');
        $searchType = $request->get('type', 'all');
        $deviceId = $request->get('device_id', '');

        // Validate device ID
        if (empty($deviceId)) {
            return [
                'error' => 'Device identification required',
                'message' => 'Please enable JavaScript to use the search feature.',
            ];
        }

        // Get configurable limits
        $searchLimit = SearchConfiguration::get('guest_search_limit', 3);
        $resetHours = SearchConfiguration::get('guest_search_reset_hours', 24);
        $trackSearches = SearchConfiguration::get('track_guest_searches', true);

        // Check current search count for device
        $cacheKey = "guest_search_count_{$deviceId}";
        $searchCount = Cache::get($cacheKey, 0);

        if ($searchCount >= $searchLimit) {
            return [
                'error' => 'Search limit exceeded',
                'message' => "You have used all {$searchLimit} of your free searches. Please sign up to continue searching our mobile parts database.",
                'signup_url' => route('register'),
                'login_url' => route('login'),
                'limit_reached' => true,
                'searches_used' => $searchCount,
                'search_limit' => $searchLimit,
                'reset_hours' => $resetHours,
            ];
        }

        // Perform the search
        $results = $this->performSearch($query, $searchType);

        // Increment search count and set expiration
        $newCount = $searchCount + 1;
        Cache::put($cacheKey, $newCount, now()->addHours($resetHours));

        // Track guest search if enabled
        if ($trackSearches) {
            $this->trackGuestSearch($deviceId, $query, $searchType, $results->total());
        }

        $remainingSearches = max(0, $searchLimit - $newCount);
        $isLastSearch = $remainingSearches === 0;

        // Get partial results configuration
        $enablePartialResults = SearchConfiguration::get('enable_partial_results', true);
        $maxVisibleResults = SearchConfiguration::get('guest_max_visible_results', 5);
        $blurIntensity = SearchConfiguration::get('blur_intensity', 'medium');
        $showSignupCta = SearchConfiguration::get('show_signup_cta', true);

        // Apply partial results logic if enabled, this is the last search, and we have enough results
        $partialResults = null;
        $shouldApplyPartialResults = $enablePartialResults && $isLastSearch && $results->total() > $maxVisibleResults;
        if ($shouldApplyPartialResults) {
            $partialResults = $this->applyPartialResults($results, $maxVisibleResults);
        }

        return [
            'results' => $partialResults ?? $results,
            'query' => $query,
            'search_type' => $searchType,
            'total' => $results->total(),
            'per_page' => $results->perPage(),
            'current_page' => $results->currentPage(),
            'last_page' => $results->lastPage(),
            'searches_used' => $newCount,
            'search_limit' => $searchLimit,
            'remaining_searches' => $remainingSearches,
            'is_last_search' => $isLastSearch,
            'reset_hours' => $resetHours,
            'partial_results_enabled' => $shouldApplyPartialResults,
            'max_visible_results' => $maxVisibleResults,
            'blur_intensity' => $blurIntensity,
            'show_signup_cta' => $showSignupCta,
            'message' => $isLastSearch
                ? 'This was your last free search. Sign up to get unlimited access to our mobile parts database.'
                : "You have {$remainingSearches} free searches remaining. Sign up for unlimited access.",
            'signup_url' => route('register'),
        ];
    }

    /**
     * Check if a device has already used their free search.
     */
    public function hasDeviceSearched(string $deviceId): bool
    {
        $searchLimit = SearchConfiguration::get('guest_search_limit', 3);
        $cacheKey = "guest_search_count_{$deviceId}";
        $searchCount = Cache::get($cacheKey, 0);

        return $searchCount >= $searchLimit;
    }

    /**
     * Get search status for a device.
     */
    public function getSearchStatus(string $deviceId): array
    {
        $searchLimit = SearchConfiguration::get('guest_search_limit', 3);
        $resetHours = SearchConfiguration::get('guest_search_reset_hours', 24);

        $cacheKey = "guest_search_count_{$deviceId}";
        $searchCount = Cache::get($cacheKey, 0);

        $remainingSearches = max(0, $searchLimit - $searchCount);
        $hasSearched = $searchCount > 0;
        $canSearch = $remainingSearches > 0;

        return [
            'has_searched' => $hasSearched,
            'can_search' => $canSearch,
            'searches_used' => $searchCount,
            'search_limit' => $searchLimit,
            'remaining_searches' => $remainingSearches,
            'searches_remaining' => $remainingSearches, // Keep for backward compatibility
            'reset_hours' => $resetHours,
            'message' => $canSearch
                ? "You have {$remainingSearches} free searches remaining."
                : "You have used all {$searchLimit} free searches. Sign up for unlimited access.",
        ];
    }

    /**
     * Perform the actual search query.
     */
    private function performSearch(string $query, string $searchType)
    {
        $partsQuery = Part::query()
            ->with(['category', 'models.brand'])
            ->where('is_active', true);

        // Apply search based on type
        switch ($searchType) {
            case 'category':
                $partsQuery->whereHas('category', function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%");
                });
                break;
            case 'model':
                $partsQuery->whereHas('models', function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhereHas('brand', function ($brandQuery) use ($query) {
                          $brandQuery->where('name', 'like', "%{$query}%");
                      });
                });
                break;
            case 'part_name':
                $partsQuery->where('name', 'like', "%{$query}%")
                          ->orWhere('part_number', 'like', "%{$query}%");
                break;
            default:
                // Search all fields
                $partsQuery->where(function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('part_number', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhereHas('category', function ($categoryQuery) use ($query) {
                          $categoryQuery->where('name', 'like', "%{$query}%");
                      })
                      ->orWhereHas('models', function ($modelQuery) use ($query) {
                          $modelQuery->where('name', 'like', "%{$query}%")
                                    ->orWhereHas('brand', function ($brandQuery) use ($query) {
                                        $brandQuery->where('name', 'like', "%{$query}%");
                                    });
                      });
                });
        }

        // Get configurable results per page for guests
        $resultsPerPage = SearchConfiguration::get('guest_results_per_page', 10);

        // Limit results for guest users
        return $partsQuery->paginate($resultsPerPage);
    }

    /**
     * Get available filters for guest users (limited set).
     */
    public function getAvailableFilters(): array
    {
        return [
            'categories' => \App\Models\Category::where('is_active', true)
                ->orderBy('name')
                ->limit(10)
                ->get(['id', 'name']),
            'brands' => \App\Models\Brand::where('is_active', true)
                ->orderBy('name')
                ->limit(10)
                ->get(['id', 'name']),
        ];
    }

    /**
     * Apply partial results with blur effect for guest users.
     */
    private function applyPartialResults($results, int $maxVisible)
    {
        $data = $results->toArray();
        $allResults = $data['data'];

        // Split results into visible and blurred
        $visibleResults = array_slice($allResults, 0, $maxVisible);
        $blurredResults = array_slice($allResults, $maxVisible);

        // Mark blurred results
        foreach ($blurredResults as &$result) {
            $result['is_blurred'] = true;
            // Partially obscure sensitive data for blurred results
            $result['description'] = $this->obscureText($result['description'] ?? '');
            if (isset($result['part_number'])) {
                $result['part_number'] = $this->obscurePartNumber($result['part_number']);
            }
        }

        // Combine visible and blurred results
        $data['data'] = array_merge($visibleResults, $blurredResults);
        $data['visible_count'] = count($visibleResults);
        $data['blurred_count'] = count($blurredResults);

        // Create a new paginator-like object
        return (object) $data;
    }

    /**
     * Obscure text for blurred results.
     */
    private function obscureText(string $text): string
    {
        if (strlen($text) <= 20) {
            return substr($text, 0, 10) . '...';
        }

        $words = explode(' ', $text);
        $visibleWords = array_slice($words, 0, 3);

        return implode(' ', $visibleWords) . '...';
    }

    /**
     * Obscure part number for blurred results.
     */
    private function obscurePartNumber(string $partNumber): string
    {
        if (strlen($partNumber) <= 4) {
            return substr($partNumber, 0, 2) . '***';
        }

        return substr($partNumber, 0, 3) . '***' . substr($partNumber, -2);
    }

    /**
     * Track guest search for analytics.
     */
    private function trackGuestSearch(string $deviceId, string $query, string $searchType, int $resultsCount): void
    {
        try {
            // Store guest search data in cache for analytics
            $searchData = [
                'device_id' => $deviceId,
                'query' => $query,
                'search_type' => $searchType,
                'results_count' => $resultsCount,
                'timestamp' => now()->toISOString(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ];

            // Store in cache with a longer expiration for analytics
            $analyticsKey = "guest_search_analytics_" . now()->format('Y-m-d') . "_{$deviceId}_" . uniqid();
            Cache::put($analyticsKey, $searchData, now()->addDays(30));

            // Also increment daily guest search counter
            $dailyCountKey = "guest_searches_count_" . now()->format('Y-m-d');
            Cache::increment($dailyCountKey, 1);
            Cache::put($dailyCountKey, Cache::get($dailyCountKey, 1), now()->addDays(30));
        } catch (\Exception $e) {
            // Silently fail - don't break search functionality for tracking issues
            \Log::warning('Failed to track guest search', [
                'device_id' => $deviceId,
                'query' => $query,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
