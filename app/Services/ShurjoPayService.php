<?php

namespace App\Services;

use App\Models\User;
use App\Models\PricingPlan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use ShurjopayPlugin\Shurjopay;
use ShurjopayPlugin\ShurjopayConfig;
use ShurjopayPlugin\PaymentRequest;
use Exception;

class ShurjoPayService
{
    protected ?Shurjopay $shurjopay;
    protected array $config;
    protected bool $debug;

    public function __construct()
    {
        $this->config = config('shurjopay');
        $this->debug = $this->config['debug'] ?? false;
        
        try {
            // Initialize ShurjoPay with configuration
            $shurjopayConfig = new ShurjopayConfig();
            $shurjopayConfig->username = $this->config['username'];
            $shurjopayConfig->password = $this->config['password'];
            $shurjopayConfig->order_prefix = $this->config['prefix'];
            $shurjopayConfig->callback_url = $this->config['success_url'];
            $shurjopayConfig->api_endpoint = $this->getApiUrl();
            $shurjopayConfig->log_path = $this->config['logging']['location'];
            $shurjopayConfig->ssl_verifypeer = $this->config['ssl_verify'];

            $this->shurjopay = new Shurjopay($shurjopayConfig);
            
            if ($this->debug) {
                Log::info('ShurjoPay service initialized', [
                    'environment' => $this->config['environment'],
                    'api_url' => $this->getApiUrl(),
                ]);
            }
        } catch (Exception $e) {
            Log::error('Failed to initialize ShurjoPay service', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->shurjopay = null;
        }
    }

    /**
     * Get the appropriate API URL based on environment.
     */
    protected function getApiUrl(): string
    {
        $environment = $this->config['environment'];
        return $this->config['urls'][$environment] ?? $this->config['api_url'];
    }

    /**
     * Authenticate with ShurjoPay and get access token.
     */
    public function authenticate(): ?array
    {
        try {
            // Use direct API call for authentication to get proper response
            $response = Http::timeout($this->config['timeout']['request'])
                ->connectTimeout($this->config['timeout']['connect'])
                ->post($this->getApiUrl() . '/api/get_token', [
                    'username' => $this->config['username'],
                    'password' => $this->config['password'],
                ]);

            if ($response->successful()) {
                $data = $response->json();

                if ($this->debug) {
                    Log::info('ShurjoPay authentication successful', [
                        'store_id' => $data['store_id'] ?? null,
                        'token_type' => $data['token_type'] ?? null,
                        'expires_in' => $data['expires_in'] ?? null,
                    ]);
                }

                return $data;
            }

            Log::error('ShurjoPay authentication failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('ShurjoPay authentication error', [
                'error' => $e->getMessage(),
                'trace' => $this->debug ? $e->getTraceAsString() : null
            ]);
            return null;
        }
    }

    /**
     * Create a payment request with ShurjoPay.
     */
    public function createPayment(User $user, PricingPlan $plan, float $amount, string $currency = 'BDT'): ?array
    {
        try {
            if (!$this->shurjopay) {
                Log::error('ShurjoPay service not initialized');
                return null;
            }

            // First authenticate to get token
            $authToken = $this->shurjopay->authenticate();
            if (!$authToken) {
                Log::error('Cannot create payment: authentication failed');
                return null;
            }

            // Create payment request
            $paymentRequest = new PaymentRequest();
            $paymentRequest->currency = $currency;
            $paymentRequest->amount = $amount;
            $paymentRequest->discountAmount = 0;
            $paymentRequest->discPercent = 0;
            $paymentRequest->customerName = $user->name;
            $paymentRequest->customerEmail = $user->email;
            $paymentRequest->customerPhone = $this->formatPhoneNumber($user->phone ?? '01712345678');
            $paymentRequest->customerAddress = $user->address ?? 'Test Address';
            $paymentRequest->customerCity = $user->city ?? 'Dhaka';
            $paymentRequest->customerState = $user->state ?? 'Dhaka';
            $paymentRequest->customerPostcode = $user->postal_code ?? '1000';
            $paymentRequest->customerCountry = $user->country ?? 'BD';

            // Optional values for tracking
            $paymentRequest->value1 = json_encode([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
            ]);
            $paymentRequest->value2 = 'subscription_payment';
            $paymentRequest->value3 = $plan->name;
            $paymentRequest->value4 = (string)$plan->id;

            // Use direct API call instead of plugin's makePayment method
            $paymentResponse = $this->createPaymentDirect($authToken, $paymentRequest, $amount, $currency);

            if ($paymentResponse && is_array($paymentResponse)) {
                if ($this->debug) {
                    Log::info('ShurjoPay payment created successfully', [
                        'user_id' => $user->id,
                        'sp_order_id' => $paymentResponse['sp_order_id'] ?? null,
                        'amount' => $amount,
                        'currency' => $currency,
                    ]);
                }

                return $paymentResponse;
            }

            Log::error('ShurjoPay payment creation failed', [
                'user_id' => $user->id,
                'response' => $paymentResponse
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('ShurjoPay payment creation error', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $this->debug ? $e->getTraceAsString() : null
            ]);
            return null;
        }
    }

    /**
     * Verify a payment with ShurjoPay.
     */
    public function verifyPayment(string $orderId): ?array
    {
        try {
            if (!$this->shurjopay) {
                Log::error('ShurjoPay service not initialized');
                return null;
            }

            // First authenticate to get token
            $authToken = $this->shurjopay->authenticate();
            if (!$authToken) {
                Log::error('Cannot verify payment: authentication failed');
                return null;
            }

            $verificationResult = $this->shurjopay->verifyPayment($orderId);

            if ($verificationResult) {
                // Convert object to array if needed
                $data = is_object($verificationResult) ? (array)$verificationResult : $verificationResult;

                if ($this->debug) {
                    Log::info('ShurjoPay payment verification successful', [
                        'order_id' => $orderId,
                        'sp_code' => $data['sp_code'] ?? null,
                        'transaction_status' => $data['transaction_status'] ?? null,
                    ]);
                }

                return $data;
            }

            Log::error('ShurjoPay payment verification failed', [
                'order_id' => $orderId,
                'result' => $verificationResult
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('ShurjoPay payment verification error', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $this->debug ? $e->getTraceAsString() : null
            ]);
            return null;
        }
    }

    /**
     * Generate a unique order ID.
     */
    protected function generateOrderId(): string
    {
        return $this->config['prefix'] . '_' . time() . '_' . uniqid();
    }

    /**
     * Format phone number for ShurjoPay validation.
     * ShurjoPay expects exactly 11 digits (e.g., 01712345678)
     */
    protected function formatPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // If phone starts with 88, remove country code
        if (str_starts_with($phone, '88')) {
            $phone = substr($phone, 2);
        }

        // If phone starts with 01, it's already in correct format
        if (str_starts_with($phone, '01') && strlen($phone) == 11) {
            return $phone;
        }

        // If phone starts with 1, add 0
        if (str_starts_with($phone, '1') && strlen($phone) == 10) {
            return '0' . $phone;
        }

        // Default fallback for invalid numbers (valid BD mobile number)
        if (strlen($phone) != 11 || !str_starts_with($phone, '01')) {
            return '01712345678';
        }

        return $phone;
    }

    /**
     * Create payment using direct API call (bypassing plugin's makePayment method).
     */
    protected function createPaymentDirect(string $authToken, PaymentRequest $paymentRequest, float $amount, string $currency): ?array
    {
        try {
            // Get store ID from authentication
            $authData = $this->authenticate();
            if (!$authData) {
                return null;
            }

            $orderId = $this->generateOrderId();

            $paymentData = [
                'token' => $authToken,
                'store_id' => (int)$authData['store_id'],
                'prefix' => $this->config['prefix'],
                'currency' => $currency,
                'return_url' => $this->config['success_url'],
                'cancel_url' => $this->config['cancel_url'],
                'amount' => $amount,
                'order_id' => $orderId,
                'discsount_amount' => $paymentRequest->discountAmount ?? 0,
                'disc_percent' => $paymentRequest->discPercent ?? 0,
                'client_ip' => request()->ip() ?? '127.0.0.1',
                'customer_name' => $paymentRequest->customerName,
                'customer_phone' => $paymentRequest->customerPhone,
                'customer_email' => $paymentRequest->customerEmail,
                'customer_address' => $paymentRequest->customerAddress,
                'customer_city' => $paymentRequest->customerCity,
                'customer_state' => $paymentRequest->customerState,
                'customer_postcode' => $paymentRequest->customerPostcode,
                'customer_country' => $paymentRequest->customerCountry,
                'value1' => $paymentRequest->value1,
                'value2' => $paymentRequest->value2,
                'value3' => $paymentRequest->value3,
                'value4' => $paymentRequest->value4,
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $authToken,
                'Content-Type' => 'application/json',
            ])->timeout($this->config['timeout']['request'])
              ->connectTimeout($this->config['timeout']['connect'])
              ->post($this->getApiUrl() . '/api/secret-pay', $paymentData);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('ShurjoPay direct payment creation failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return null;

        } catch (Exception $e) {
            Log::error('ShurjoPay direct payment creation error', [
                'error' => $e->getMessage(),
                'trace' => $this->debug ? $e->getTraceAsString() : null
            ]);
            return null;
        }
    }

    /**
     * Check if payment was successful based on ShurjoPay response codes.
     */
    public function isPaymentSuccessful(array $paymentData): bool
    {
        $spCode = $paymentData['sp_code'] ?? null;
        return $spCode === '1000' || $spCode === 1000;
    }

    /**
     * Check if payment was cancelled.
     */
    public function isPaymentCancelled(array $paymentData): bool
    {
        $spCode = $paymentData['sp_code'] ?? null;
        return $spCode === '1002' || $spCode === 1002;
    }

    /**
     * Check if payment was declined.
     */
    public function isPaymentDeclined(array $paymentData): bool
    {
        $spCode = $paymentData['sp_code'] ?? null;
        return $spCode === '1001' || $spCode === 1001;
    }

    /**
     * Get payment status message.
     */
    public function getPaymentStatusMessage(array $paymentData): string
    {
        $spCode = $paymentData['sp_code'] ?? null;
        $message = $paymentData['sp_message'] ?? 'Unknown status';
        
        return match($spCode) {
            '1000', 1000 => 'Payment successful',
            '1001', 1001 => 'Payment declined by bank',
            '1002', 1002 => 'Payment cancelled by customer',
            default => $message
        };
    }

    /**
     * Process webhook/IPN data.
     */
    public function processWebhook(array $data): bool
    {
        try {
            if ($this->debug) {
                Log::info('Processing ShurjoPay webhook', [
                    'data' => $data
                ]);
            }

            // Verify the payment to ensure webhook data is valid
            $orderId = $data['order_id'] ?? null;
            if (!$orderId) {
                Log::error('ShurjoPay webhook missing order_id');
                return false;
            }

            $verificationData = $this->verifyPayment($orderId);
            if (!$verificationData) {
                Log::error('ShurjoPay webhook verification failed', ['order_id' => $orderId]);
                return false;
            }

            // Process the payment based on verification result
            if ($this->isPaymentSuccessful($verificationData)) {
                Log::info('ShurjoPay webhook: payment successful', [
                    'order_id' => $orderId,
                    'amount' => $verificationData['amount'] ?? null
                ]);
                return true;
            }

            Log::warning('ShurjoPay webhook: payment not successful', [
                'order_id' => $orderId,
                'status' => $this->getPaymentStatusMessage($verificationData)
            ]);
            
            return false;
        } catch (Exception $e) {
            Log::error('ShurjoPay webhook processing error', [
                'error' => $e->getMessage(),
                'trace' => $this->debug ? $e->getTraceAsString() : null
            ]);
            return false;
        }
    }
}
