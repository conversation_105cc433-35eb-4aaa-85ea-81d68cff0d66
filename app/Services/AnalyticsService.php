<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserActivityLog;
use App\Models\PaymentRequest;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsService
{
    /**
     * Get comprehensive user engagement analytics.
     */
    public function getUserEngagementAnalytics(int $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        return [
            'overview' => $this->getEngagementOverview($startDate),
            'daily_activity' => $this->getDailyActivityStats($startDate),
            'feature_usage' => $this->getFeatureUsageStats($startDate),
            'user_segments' => $this->getUserSegmentAnalytics($startDate),
            'search_analytics' => $this->getSearchAnalytics($startDate),
            'subscription_analytics' => $this->getSubscriptionAnalytics($startDate),
        ];
    }

    /**
     * Get engagement overview statistics.
     */
    private function getEngagementOverview(Carbon $startDate): array
    {
        $totalUsers = User::count();
        $activeUsers = User::where('last_login_at', '>=', $startDate)->count();
        $newUsers = User::where('created_at', '>=', $startDate)->count();
        
        $totalSessions = UserActivityLog::where('activity_type', 'login')
            ->where('created_at', '>=', $startDate)
            ->count();
            
        $avgSessionsPerUser = $activeUsers > 0 ? round($totalSessions / $activeUsers, 2) : 0;
        
        return [
            'total_users' => $totalUsers,
            'active_users' => $activeUsers,
            'new_users' => $newUsers,
            'user_retention_rate' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 2) : 0,
            'total_sessions' => $totalSessions,
            'avg_sessions_per_user' => $avgSessionsPerUser,
        ];
    }

    /**
     * Get daily activity statistics.
     */
    private function getDailyActivityStats(Carbon $startDate): array
    {
        $dailyLogins = UserActivityLog::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(DISTINCT user_id) as unique_users'),
                DB::raw('COUNT(*) as total_logins')
            )
            ->where('activity_type', 'login')
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();

        $dailySearches = UserActivityLog::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(DISTINCT user_id) as unique_users'),
                DB::raw('COUNT(*) as total_searches')
            )
            ->where('activity_type', 'search')
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();

        return [
            'daily_logins' => $dailyLogins,
            'daily_searches' => $dailySearches,
        ];
    }

    /**
     * Get feature usage statistics.
     */
    private function getFeatureUsageStats(Carbon $startDate): array
    {
        $featureUsage = UserActivityLog::select(
                'activity_type',
                DB::raw('COUNT(*) as usage_count'),
                DB::raw('COUNT(DISTINCT user_id) as unique_users')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('activity_type')
            ->orderByDesc('usage_count')
            ->get()
            ->toArray();

        // Get most popular search types
        $searchTypes = UserActivityLog::select(
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.search_type')) as search_type"),
                DB::raw('COUNT(*) as usage_count')
            )
            ->where('activity_type', 'search')
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('metadata')
            ->groupBy('search_type')
            ->orderByDesc('usage_count')
            ->get()
            ->toArray();

        return [
            'feature_usage' => $featureUsage,
            'search_types' => $searchTypes,
        ];
    }

    /**
     * Get user segment analytics.
     */
    private function getUserSegmentAnalytics(Carbon $startDate): array
    {
        // Segment by subscription plan
        $subscriptionSegments = User::select(
                'subscription_plan',
                DB::raw('COUNT(*) as user_count'),
                DB::raw('COUNT(CASE WHEN last_login_at >= ? THEN 1 END) as active_users')
            )
            ->setBindings([$startDate])
            ->groupBy('subscription_plan')
            ->get()
            ->toArray();

        // Segment by activity level
        $activitySegments = DB::select("
            SELECT 
                CASE 
                    WHEN login_count >= 50 THEN 'high_activity'
                    WHEN login_count >= 10 THEN 'medium_activity'
                    WHEN login_count >= 1 THEN 'low_activity'
                    ELSE 'inactive'
                END as activity_level,
                COUNT(*) as user_count,
                AVG(login_count) as avg_logins
            FROM users 
            GROUP BY activity_level
            ORDER BY avg_logins DESC
        ");

        return [
            'subscription_segments' => $subscriptionSegments,
            'activity_segments' => $activitySegments,
        ];
    }

    /**
     * Get search analytics.
     */
    private function getSearchAnalytics(Carbon $startDate): array
    {
        $totalSearches = UserActivityLog::where('activity_type', 'search')
            ->where('created_at', '>=', $startDate)
            ->count();

        // Calculate average searches per user using a subquery approach
        $activeUsersCount = User::where('last_login_at', '>=', $startDate)->count();
        $avgSearchesPerUser = $activeUsersCount > 0 ? round($totalSearches / $activeUsersCount, 2) : 0;

        // Most searched terms
        $popularSearches = UserActivityLog::select(
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.search_query')) as search_query"),
                DB::raw('COUNT(*) as search_count')
            )
            ->where('activity_type', 'search')
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('metadata')
            ->groupBy('search_query')
            ->orderByDesc('search_count')
            ->limit(20)
            ->get()
            ->toArray();

        // Search success rate (searches with results)
        $searchesWithResults = UserActivityLog::where('activity_type', 'search')
            ->where('created_at', '>=', $startDate)
            ->whereRaw("JSON_EXTRACT(metadata, '$.results_count') > 0")
            ->count();

        $searchSuccessRate = $totalSearches > 0 ? round(($searchesWithResults / $totalSearches) * 100, 2) : 0;

        return [
            'total_searches' => $totalSearches,
            'avg_searches_per_user' => $avgSearchesPerUser,
            'popular_searches' => $popularSearches,
            'search_success_rate' => $searchSuccessRate,
        ];
    }

    /**
     * Get subscription analytics.
     */
    private function getSubscriptionAnalytics(Carbon $startDate): array
    {
        $totalRevenue = PaymentRequest::where('status', 'approved')
            ->where('approved_at', '>=', $startDate)
            ->sum('amount');

        $subscriptionConversions = PaymentRequest::where('status', 'approved')
            ->where('approved_at', '>=', $startDate)
            ->count();

        $pendingPayments = PaymentRequest::where('status', 'pending')->count();

        // Revenue by subscription plan
        $revenueByPlan = PaymentRequest::select(
                'subscription_plan',
                DB::raw('COUNT(*) as payment_count'),
                DB::raw('SUM(amount) as total_revenue')
            )
            ->where('status', 'approved')
            ->where('approved_at', '>=', $startDate)
            ->groupBy('subscription_plan')
            ->get()
            ->toArray();

        return [
            'total_revenue' => $totalRevenue,
            'subscription_conversions' => $subscriptionConversions,
            'pending_payments' => $pendingPayments,
            'revenue_by_plan' => $revenueByPlan,
        ];
    }

    /**
     * Get user behavior patterns.
     */
    public function getUserBehaviorPatterns(int $userId, int $days = 30): array
    {
        $startDate = now()->subDays($days);
        $user = User::findOrFail($userId);

        $activities = UserActivityLog::where('user_id', $userId)
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'desc')
            ->get();

        $activityByType = $activities->groupBy('activity_type')
            ->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'last_activity' => $group->first()->created_at,
                ];
            });

        $dailyActivity = $activities->groupBy(function ($activity) {
                return $activity->created_at->format('Y-m-d');
            })
            ->map(function ($group) {
                return $group->count();
            });

        return [
            'user' => $user,
            'total_activities' => $activities->count(),
            'activity_by_type' => $activityByType,
            'daily_activity' => $dailyActivity,
            'most_active_day' => $dailyActivity->keys()->first(),
            'avg_daily_activities' => round($activities->count() / $days, 2),
        ];
    }

    /**
     * Get system performance metrics.
     */
    public function getSystemPerformanceMetrics(int $days = 7): array
    {
        $startDate = now()->subDays($days);

        // Error rate from activity logs
        $totalActivities = UserActivityLog::where('created_at', '>=', $startDate)->count();
        $errorActivities = UserActivityLog::where('created_at', '>=', $startDate)
            ->where('activity_type', 'error')
            ->count();

        $errorRate = $totalActivities > 0 ? round(($errorActivities / $totalActivities) * 100, 2) : 0;

        // Peak usage hours
        $hourlyUsage = UserActivityLog::select(
                DB::raw('HOUR(created_at) as hour'),
                DB::raw('COUNT(*) as activity_count')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('hour')
            ->orderByDesc('activity_count')
            ->get()
            ->toArray();

        return [
            'total_activities' => $totalActivities,
            'error_rate' => $errorRate,
            'peak_usage_hours' => $hourlyUsage,
            'avg_daily_activities' => round($totalActivities / $days, 2),
        ];
    }
}
