<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class SecurityService
{
    /**
     * Check if IP is blocked.
     */
    public function isIpBlocked(string $ip): bool
    {
        return Cache::has('blocked_ip:' . $ip);
    }

    /**
     * Block an IP address.
     */
    public function blockIp(string $ip, int $minutes = 60): void
    {
        Cache::put('blocked_ip:' . $ip, true, now()->addMinutes($minutes));
        
        Log::warning('IP address blocked', [
            'ip' => $ip,
            'duration_minutes' => $minutes,
            'timestamp' => now(),
        ]);
    }

    /**
     * Unblock an IP address.
     */
    public function unblockIp(string $ip): void
    {
        Cache::forget('blocked_ip:' . $ip);
        
        Log::info('IP address unblocked', [
            'ip' => $ip,
            'timestamp' => now(),
        ]);
    }

    /**
     * Check for suspicious login attempts.
     */
    public function checkSuspiciousLogin(Request $request, string $email): bool
    {
        $key = 'login_attempts:' . $request->ip() . ':' . $email;
        $attempts = Cache::get($key, 0);

        if ($attempts >= 5) {
            $this->logSuspiciousActivity('excessive_login_attempts', $request, [
                'email' => $email,
                'attempts' => $attempts,
            ]);
            
            // Block IP after too many failed attempts
            if ($attempts >= 10) {
                $this->blockIp($request->ip(), 30);
            }
            
            return true;
        }

        return false;
    }

    /**
     * Record failed login attempt.
     */
    public function recordFailedLogin(Request $request, string $email): void
    {
        $key = 'login_attempts:' . $request->ip() . ':' . $email;
        $attempts = Cache::get($key, 0) + 1;
        
        Cache::put($key, $attempts, now()->addMinutes(15));
        
        if ($attempts >= 3) {
            $this->logSuspiciousActivity('failed_login_attempts', $request, [
                'email' => $email,
                'attempts' => $attempts,
            ]);
        }
    }

    /**
     * Clear failed login attempts.
     */
    public function clearFailedLogins(Request $request, string $email): void
    {
        $key = 'login_attempts:' . $request->ip() . ':' . $email;
        Cache::forget($key);
    }

    /**
     * Check for bot-like behavior.
     */
    public function detectBotBehavior(Request $request): bool
    {
        $userAgent = $request->userAgent();
        
        // Common bot user agents
        $botPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/java/i',
        ];

        foreach ($botPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                $this->logSuspiciousActivity('bot_detected', $request, [
                    'user_agent' => $userAgent,
                ]);
                return true;
            }
        }

        // Check for missing common headers
        $requiredHeaders = ['accept', 'accept-language', 'accept-encoding'];
        foreach ($requiredHeaders as $header) {
            if (!$request->hasHeader($header)) {
                $this->logSuspiciousActivity('missing_headers', $request, [
                    'missing_header' => $header,
                ]);
                return true;
            }
        }

        return false;
    }

    /**
     * Validate request integrity.
     */
    public function validateRequestIntegrity(Request $request): bool
    {
        // Check for SQL injection patterns
        $sqlPatterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/insert\s+into/i',
            '/delete\s+from/i',
            '/update\s+set/i',
            '/script\s*>/i',
            '/<\s*script/i',
        ];

        $input = json_encode($request->all());
        
        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                $this->logSuspiciousActivity('sql_injection_attempt', $request, [
                    'pattern_matched' => $pattern,
                    'input' => $input,
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Generate secure token.
     */
    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Hash sensitive data.
     */
    public function hashSensitiveData(string $data): string
    {
        return Hash::make($data);
    }

    /**
     * Verify hashed data.
     */
    public function verifySensitiveData(string $data, string $hash): bool
    {
        return Hash::check($data, $hash);
    }

    /**
     * Encrypt sensitive data.
     */
    public function encryptSensitiveData(string $data): string
    {
        return encrypt($data);
    }

    /**
     * Decrypt sensitive data.
     */
    public function decryptSensitiveData(string $encryptedData): string
    {
        return decrypt($encryptedData);
    }

    /**
     * Log suspicious activity.
     */
    public function logSuspiciousActivity(string $type, Request $request, array $additional = []): void
    {
        Log::warning('Suspicious activity detected', array_merge([
            'type' => $type,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => $request->user()?->id,
            'timestamp' => now(),
        ], $additional));
    }

    /**
     * Check if user has permission to access content.
     */
    public function checkContentAccess(User $user, string $contentType): bool
    {
        // Premium content requires premium subscription
        if ($contentType === 'premium' && !$user->isPremium()) {
            return false;
        }

        // Check if user is within search limits
        if ($contentType === 'search' && !$user->canSearch()) {
            return false;
        }

        return true;
    }

    /**
     * Generate content watermark.
     */
    public function generateWatermark(User $user): array
    {
        return [
            'user_id' => $user->id,
            'timestamp' => now()->timestamp,
            'hash' => hash('sha256', $user->id . $user->email . now()->timestamp),
        ];
    }

    /**
     * Validate content watermark.
     */
    public function validateWatermark(array $watermark, User $user): bool
    {
        $expectedHash = hash('sha256', $user->id . $user->email . $watermark['timestamp']);
        
        return hash_equals($expectedHash, $watermark['hash']) && 
               $watermark['user_id'] == $user->id;
    }

    /**
     * Get security headers.
     */
    public function getSecurityHeaders(): array
    {
        return [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'camera=(), microphone=(), geolocation=()',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
        ];
    }

    /**
     * Check for CSRF token validity.
     */
    public function validateCsrfToken(Request $request): bool
    {
        return $request->session()->token() === $request->input('_token');
    }
}
