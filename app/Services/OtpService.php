<?php

namespace App\Services;

use App\Models\User;
use App\Mail\AdminOtpCode;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class OtpService
{
    /**
     * <PERSON>TP expiry time in minutes.
     */
    const OTP_EXPIRY_MINUTES = 10;

    /**
     * Maximum OTP attempts before lockout.
     */
    const MAX_OTP_ATTEMPTS = 3;

    /**
     * Lockout duration in minutes after max attempts.
     */
    const LOCKOUT_DURATION_MINUTES = 30;

    /**
     * Generate and send OTP code to admin user.
     */
    public function generateAndSendOtp(User $user, string $action): bool
    {
        try {
            // Check if user is locked out
            if ($this->isUserLockedOut($user)) {
                return false;
            }

            // Generate 6-digit OTP code
            $otpCode = $this->generateOtpCode();
            
            // Store OTP in database
            $user->update([
                'current_otp_code' => $otpCode,
                'otp_expires_at' => now()->addMinutes(self::OTP_EXPIRY_MINUTES),
                'otp_attempts' => 0,
            ]);

            // Send OTP via email
            Mail::to($user->email)->send(new AdminOtpCode($user, $otpCode, $action));

            // Log OTP generation
            $user->logActivity(
                'otp_generated',
                "OTP code generated for admin action: {$action}",
                [
                    'action' => $action,
                    'expires_at' => now()->addMinutes(self::OTP_EXPIRY_MINUTES)->toISOString(),
                    'ip_address' => request()->ip(),
                ]
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to generate and send OTP', [
                'user_id' => $user->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Verify OTP code.
     */
    public function verifyOtp(User $user, string $otpCode, string $action): bool
    {
        try {
            // Check if user is locked out
            if ($this->isUserLockedOut($user)) {
                return false;
            }

            // Check if OTP exists and hasn't expired
            if (!$user->current_otp_code || !$user->otp_expires_at) {
                return false;
            }

            if (now()->isAfter($user->otp_expires_at)) {
                $this->clearOtp($user);
                return false;
            }

            // Verify OTP code first
            if ($user->current_otp_code !== $otpCode) {
                // Increment attempt count only for failed attempts
                $user->increment('otp_attempts');
                $user->update(['otp_last_attempt_at' => now()]);

                // Check if max attempts reached after failed attempt
                if ($user->otp_attempts >= self::MAX_OTP_ATTEMPTS) {
                    $this->lockoutUser($user);
                    $this->logFailedVerification($user, $action, 'max_attempts_reached');
                    return false;
                }

                $this->logFailedVerification($user, $action, 'invalid_code');
                return false;
            }

            // OTP is valid - clear it and log success
            $this->clearOtp($user);
            $this->logSuccessfulVerification($user, $action);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to verify OTP', [
                'user_id' => $user->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if user has valid OTP session for action.
     */
    public function hasValidOtpSession(User $user, string $action): bool
    {
        $cacheKey = "otp_verified:{$user->id}:{$action}";
        return Cache::has($cacheKey);
    }

    /**
     * Create OTP session after successful verification.
     */
    public function createOtpSession(User $user, string $action, int $durationMinutes = 30): void
    {
        $cacheKey = "otp_verified:{$user->id}:{$action}";
        Cache::put($cacheKey, [
            'verified_at' => now()->toISOString(),
            'expires_at' => now()->addMinutes($durationMinutes)->toISOString(),
            'action' => $action,
        ], now()->addMinutes($durationMinutes));
    }

    /**
     * Clear OTP session.
     */
    public function clearOtpSession(User $user, string $action): void
    {
        $cacheKey = "otp_verified:{$user->id}:{$action}";
        Cache::forget($cacheKey);
    }

    /**
     * Check if user is currently locked out.
     */
    public function isUserLockedOut(User $user): bool
    {
        $lockoutKey = "otp_lockout:{$user->id}";
        return Cache::has($lockoutKey);
    }

    /**
     * Get remaining lockout time in minutes.
     */
    public function getRemainingLockoutTime(User $user): int
    {
        $lockoutKey = "otp_lockout:{$user->id}";
        $lockoutExpiry = Cache::get($lockoutKey);
        
        if (!$lockoutExpiry) {
            return 0;
        }

        return max(0, now()->diffInMinutes($lockoutExpiry, false));
    }

    /**
     * Get OTP status for user.
     */
    public function getOtpStatus(User $user): array
    {
        return [
            'has_otp_pending' => $user->current_otp_code && $user->otp_expires_at && now()->isBefore($user->otp_expires_at),
            'otp_expires_at' => $user->otp_expires_at,
            'remaining_attempts' => max(0, self::MAX_OTP_ATTEMPTS - $user->otp_attempts),
            'is_locked_out' => $this->isUserLockedOut($user),
            'lockout_remaining_minutes' => $this->getRemainingLockoutTime($user),
            'two_factor_enabled' => $user->two_factor_enabled,
        ];
    }

    /**
     * Enable 2FA for user.
     */
    public function enableTwoFactor(User $user): void
    {
        $user->update([
            'two_factor_enabled' => true,
            'two_factor_confirmed_at' => now(),
        ]);

        $user->logActivity(
            'two_factor_enabled',
            'Two-factor authentication enabled',
            [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]
        );
    }

    /**
     * Disable 2FA for user.
     */
    public function disableTwoFactor(User $user): void
    {
        $user->update([
            'two_factor_enabled' => false,
            'two_factor_confirmed_at' => null,
            'current_otp_code' => null,
            'otp_expires_at' => null,
            'otp_attempts' => 0,
            'otp_last_attempt_at' => null,
        ]);

        // Clear any existing OTP sessions
        $actions = ['user_management', 'payment_approval', 'bulk_operations', 'impersonation', 'system_config'];
        foreach ($actions as $action) {
            $this->clearOtpSession($user, $action);
        }

        $user->logActivity(
            'two_factor_disabled',
            'Two-factor authentication disabled',
            [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]
        );
    }

    /**
     * Generate 6-digit OTP code.
     */
    private function generateOtpCode(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Clear OTP from user record.
     */
    private function clearOtp(User $user): void
    {
        $user->update([
            'current_otp_code' => null,
            'otp_expires_at' => null,
            'otp_attempts' => 0,
            'otp_last_attempt_at' => null,
        ]);
    }

    /**
     * Lock out user after max attempts.
     */
    private function lockoutUser(User $user): void
    {
        $lockoutKey = "otp_lockout:{$user->id}";
        Cache::put($lockoutKey, now()->addMinutes(self::LOCKOUT_DURATION_MINUTES), now()->addMinutes(self::LOCKOUT_DURATION_MINUTES));
        
        $this->clearOtp($user);
    }

    /**
     * Log failed OTP verification.
     */
    private function logFailedVerification(User $user, string $action, string $reason): void
    {
        $user->logActivity(
            'otp_verification_failed',
            "OTP verification failed for admin action: {$action}",
            [
                'action' => $action,
                'reason' => $reason,
                'attempts' => $user->otp_attempts,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]
        );
    }

    /**
     * Log successful OTP verification.
     */
    private function logSuccessfulVerification(User $user, string $action): void
    {
        $user->logActivity(
            'otp_verification_success',
            "OTP verification successful for admin action: {$action}",
            [
                'action' => $action,
                'attempts' => $user->otp_attempts,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]
        );
    }
}
