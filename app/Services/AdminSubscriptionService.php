<?php

namespace App\Services;

use App\Models\User;
use App\Models\Subscription;
use App\Models\PricingPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class AdminSubscriptionService
{
    /**
     * Get paginated subscriptions with filters.
     */
    public function getSubscriptions(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Subscription::with(['user', 'pricingPlan']);

        // Apply filters
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['plan'])) {
            $query->where('plan_name', $filters['plan']);
        }

        if (!empty($filters['search'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('email', 'like', '%' . $filters['search'] . '%');
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get subscription statistics.
     */
    public function getSubscriptionStats(): array
    {
        $totalSubscriptions = Subscription::count();
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        $expiredSubscriptions = Subscription::where('status', 'expired')->count();
        $cancelledSubscriptions = Subscription::where('status', 'cancelled')->count();

        // Plan distribution
        $planDistribution = Subscription::select('plan_name', DB::raw('count(*) as count'))
            ->groupBy('plan_name')
            ->get()
            ->pluck('count', 'plan_name')
            ->toArray();

        // Monthly revenue (active subscriptions only)
        $monthlyRevenue = Subscription::join('pricing_plans', 'subscriptions.pricing_plan_id', '=', 'pricing_plans.id')
            ->where('subscriptions.status', 'active')
            ->sum('pricing_plans.price');

        // Recent subscriptions (last 30 days)
        $recentSubscriptions = Subscription::where('created_at', '>=', now()->subDays(30))->count();

        // Expiring soon (next 7 days)
        $expiringSoon = Subscription::where('status', 'active')
            ->where('current_period_end', '<=', now()->addDays(7))
            ->count();

        return [
            'total_subscriptions' => $totalSubscriptions,
            'active_subscriptions' => $activeSubscriptions,
            'expired_subscriptions' => $expiredSubscriptions,
            'cancelled_subscriptions' => $cancelledSubscriptions,
            'plan_distribution' => $planDistribution,
            'monthly_revenue' => $monthlyRevenue,
            'recent_subscriptions' => $recentSubscriptions,
            'expiring_soon' => $expiringSoon,
        ];
    }

    /**
     * Create a subscription for a user.
     */
    public function createSubscription(User $user, PricingPlan $plan, array $options = []): Subscription
    {
        return DB::transaction(function () use ($user, $plan, $options) {
            // Cancel any existing active subscriptions
            $user->subscriptions()->where('status', 'active')->update(['status' => 'cancelled']);

            // Calculate period dates
            $startDate = $options['start_date'] ?? now();
            $endDate = $options['end_date'] ?? $this->calculateEndDate($startDate, $plan->interval);

            // Create new subscription
            $subscription = $user->subscriptions()->create([
                'plan_name' => $plan->name,
                'pricing_plan_id' => $plan->id,
                'status' => $options['status'] ?? 'active',
                'current_period_start' => $startDate,
                'current_period_end' => $endDate,
                'stripe_subscription_id' => $options['stripe_subscription_id'] ?? null,
            ]);

            // Update user's subscription plan
            $user->update(['subscription_plan' => $plan->name]);

            return $subscription;
        });
    }

    /**
     * Update a subscription.
     */
    public function updateSubscription(Subscription $subscription, array $data): Subscription
    {
        return DB::transaction(function () use ($subscription, $data) {
            // If changing plan, update pricing plan relationship
            if (isset($data['pricing_plan_id'])) {
                $pricingPlan = PricingPlan::findOrFail($data['pricing_plan_id']);
                $data['plan_name'] = $pricingPlan->name;
                
                // Update user's subscription plan
                $subscription->user->update(['subscription_plan' => $pricingPlan->name]);
            }

            $subscription->update($data);
            return $subscription->fresh(['user', 'pricingPlan']);
        });
    }

    /**
     * Cancel a subscription.
     */
    public function cancelSubscription(Subscription $subscription, bool $immediately = false): Subscription
    {
        return DB::transaction(function () use ($subscription, $immediately) {
            if ($immediately) {
                $subscription->update([
                    'status' => 'cancelled',
                    'current_period_end' => now(),
                ]);
                
                // Downgrade user to free plan
                $freePlan = PricingPlan::where('name', 'free')->first();
                if ($freePlan) {
                    $subscription->user->update(['subscription_plan' => 'free']);
                }
            } else {
                // Cancel at period end
                $subscription->update(['status' => 'cancelled']);
            }

            return $subscription->fresh(['user', 'pricingPlan']);
        });
    }

    /**
     * Extend a subscription.
     */
    public function extendSubscription(Subscription $subscription, int $months = 1): Subscription
    {
        $newEndDate = $subscription->current_period_end->addMonths($months);
        
        $subscription->update([
            'current_period_end' => $newEndDate,
            'status' => 'active', // Reactivate if cancelled
        ]);

        return $subscription->fresh(['user', 'pricingPlan']);
    }

    /**
     * Get subscriptions expiring soon.
     */
    public function getExpiringSoon(int $days = 7): \Illuminate\Database\Eloquent\Collection
    {
        return Subscription::with(['user', 'pricingPlan'])
            ->where('status', 'active')
            ->where('current_period_end', '<=', now()->addDays($days))
            ->where('current_period_end', '>', now())
            ->orderBy('current_period_end')
            ->get();
    }

    /**
     * Calculate end date based on interval.
     */
    private function calculateEndDate(Carbon $startDate, string $interval): Carbon
    {
        return match ($interval) {
            'month' => $startDate->copy()->addMonth(),
            'year' => $startDate->copy()->addYear(),
            default => $startDate->copy()->addMonth(),
        };
    }

    /**
     * Bulk update subscriptions.
     */
    public function bulkUpdateSubscriptions(array $subscriptionIds, array $data): int
    {
        return DB::transaction(function () use ($subscriptionIds, $data) {
            $updated = 0;
            
            foreach ($subscriptionIds as $id) {
                $subscription = Subscription::find($id);
                if ($subscription) {
                    $this->updateSubscription($subscription, $data);
                    $updated++;
                }
            }
            
            return $updated;
        });
    }
}
