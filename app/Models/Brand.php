<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Brand extends Model
{
    use HasFactory, Sluggable;

    protected $fillable = [
        'name',
        'slug',
        'logo_url',
        'country',
        'website',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Return the sluggable configuration array for this model.
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name'
            ]
        ];
    }

    /**
     * Get the models for this brand.
     */
    public function models(): HasMany
    {
        return $this->hasMany(MobileModel::class);
    }

    /**
     * Scope to get only active brands.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the public URL for this brand.
     */
    public function getPublicUrl(): string
    {
        return route('brands.show', $this->slug ?: $this->id);
    }

    /**
     * Get the admin URL for this brand.
     */
    public function getAdminUrl(): string
    {
        return route('admin.brands.show', $this->id);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Retrieve the model for a bound value.
     * This allows resolving by both slug and ID for backward compatibility.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        // Try to find by slug first
        $model = $this->where('slug', $value)->first();

        // If not found by slug and value is numeric, try by ID
        if (!$model && is_numeric($value)) {
            $model = $this->where('id', $value)->first();
        }

        return $model;
    }
}
