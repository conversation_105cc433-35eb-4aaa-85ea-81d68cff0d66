<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShurjoPayTransaction extends Model
{
    use HasFactory;

    protected $table = 'shurjopay_transactions';

    protected $fillable = [
        'shurjopay_order_id',
        'merchant_order_id',
        'user_id',
        'subscription_id',
        'pricing_plan_id',
        'status',
        'currency',
        'amount',
        'payable_amount',
        'discount_amount',
        'received_amount',
        'disc_percent',
        'sp_code',
        'sp_message',
        'method',
        'bank_trx_id',
        'invoice_no',
        'bank_status',
        'customer_details',
        'shurjopay_data',
        'checkout_url',
        'shurjopay_created_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payable_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'received_amount' => 'decimal:2',
        'customer_details' => 'array',
        'shurjopay_data' => 'array',
        'shurjopay_created_at' => 'datetime',
    ];

    /**
     * Get the user that owns this transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription associated with this transaction.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the pricing plan associated with this transaction.
     */
    public function pricingPlan(): BelongsTo
    {
        return $this->belongsTo(PricingPlan::class);
    }

    /**
     * Scope to get only successful transactions.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('sp_code', '1000');
    }

    /**
     * Scope to get only failed transactions.
     */
    public function scopeFailed($query)
    {
        return $query->where('sp_code', '1001');
    }

    /**
     * Scope to get only cancelled transactions.
     */
    public function scopeCancelled($query)
    {
        return $query->where('sp_code', '1002');
    }

    /**
     * Check if transaction is successful.
     */
    public function isSuccessful(): bool
    {
        return $this->sp_code === '1000';
    }

    /**
     * Check if transaction is failed.
     */
    public function isFailed(): bool
    {
        return $this->sp_code === '1001';
    }

    /**
     * Check if transaction is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->sp_code === '1002';
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->sp_code) {
            '1000' => 'green',
            '1001' => 'red',
            '1002' => 'yellow',
            default => 'gray'
        };
    }
}
