<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CoinbaseCommerceTransaction extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'coinbase_commerce_transactions';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'coinbase_charge_id',
        'merchant_order_id',
        'user_id',
        'subscription_id',
        'pricing_plan_id',
        'status',
        'currency',
        'amount',
        'crypto_amount',
        'received_amount',
        'addresses',
        'timeline',
        'metadata',
        'coinbase_data',
        'hosted_url',
        'expires_at',
        'confirmed_at',
        'coinbase_created_at',
        'coinbase_updated_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'amount' => 'decimal:8',
        'crypto_amount' => 'decimal:18',
        'received_amount' => 'decimal:18',
        'addresses' => 'array',
        'timeline' => 'array',
        'metadata' => 'array',
        'coinbase_data' => 'array',
        'expires_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'coinbase_created_at' => 'datetime',
        'coinbase_updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns this transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription associated with this transaction.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the pricing plan associated with this transaction.
     */
    public function pricingPlan(): BelongsTo
    {
        return $this->belongsTo(PricingPlan::class);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get completed transactions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get pending transactions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get expired transactions.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    /**
     * Check if the transaction is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the transaction is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the transaction has failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the transaction has expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired';
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get formatted crypto amount.
     */
    public function getFormattedCryptoAmountAttribute(): string
    {
        if (!$this->crypto_amount) {
            return 'N/A';
        }

        return number_format($this->crypto_amount, 8) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get formatted received amount.
     */
    public function getFormattedReceivedAmountAttribute(): string
    {
        if (!$this->received_amount) {
            return 'N/A';
        }

        return number_format($this->received_amount, 8) . ' ' . strtoupper($this->currency);
    }

    /**
     * Check if the transaction is still valid (not expired).
     */
    public function isValid(): bool
    {
        return $this->expires_at && $this->expires_at->isFuture();
    }

    /**
     * Get the payment URL for this transaction.
     */
    public function getPaymentUrl(): ?string
    {
        return $this->hosted_url;
    }

    /**
     * Get the current status from timeline (last entry).
     */
    public function getCurrentTimelineStatus(): ?string
    {
        if (!$this->timeline || !is_array($this->timeline)) {
            return null;
        }

        $lastEntry = end($this->timeline);
        return $lastEntry['status'] ?? null;
    }

    /**
     * Get the full timeline with formatted timestamps.
     */
    public function getFormattedTimeline(): array
    {
        if (!$this->timeline || !is_array($this->timeline)) {
            return [];
        }

        return array_map(function ($entry) {
            return [
                'status' => $entry['status'] ?? 'unknown',
                'time' => isset($entry['time']) ?
                    \Carbon\Carbon::parse($entry['time'])->format('Y-m-d H:i:s') : null,
                'context' => $entry['context'] ?? null,
            ];
        }, $this->timeline);
    }

    /**
     * Check if the charge has reached a specific timeline status.
     */
    public function hasReachedTimelineStatus(string $status): bool
    {
        if (!$this->timeline || !is_array($this->timeline)) {
            return false;
        }

        foreach ($this->timeline as $entry) {
            if (($entry['status'] ?? '') === $status) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get payment details from the latest timeline entry.
     */
    public function getPaymentDetails(): array
    {
        $currentStatus = $this->getCurrentTimelineStatus();

        return [
            'current_status' => $currentStatus,
            'is_pending' => $currentStatus === 'PENDING',
            'is_completed' => $currentStatus === 'COMPLETED',
            'timeline_count' => is_array($this->timeline) ? count($this->timeline) : 0,
            'formatted_timeline' => $this->getFormattedTimeline(),
        ];
    }
}
