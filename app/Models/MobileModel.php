<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class MobileModel extends Model
{
    use HasFactory, Sluggable;

    protected $table = 'models';

    protected $fillable = [
        'brand_id',
        'name',
        'slug',
        'model_number',
        'release_year',
        'specifications',
        'images',
        'is_active',
    ];

    protected $casts = [
        'specifications' => 'array',
        'images' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Return the sluggable configuration array for this model.
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => ['brand.name', 'name'],
                'separator' => '-'
            ]
        ];
    }

    /**
     * Get the brand that owns this model.
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the parts compatible with this model.
     */
    public function parts(): BelongsToMany
    {
        return $this->belongsToMany(Part::class, 'model_parts', 'model_id', 'part_id')
            ->withPivot('compatibility_notes', 'is_verified')
            ->withTimestamps();
    }

    /**
     * Get the user favorites for this model.
     */
    public function favorites(): MorphMany
    {
        return $this->morphMany(UserFavorite::class, 'favoritable');
    }

    /**
     * Scope to get only active models.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the public URL for this model.
     */
    public function getPublicUrl(): string
    {
        return route('models.show', $this->slug ?: $this->id);
    }

    /**
     * Get the admin URL for this model.
     */
    public function getAdminUrl(): string
    {
        return route('admin.models.show', $this->id);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Retrieve the model for a bound value.
     * This allows resolving by both slug and ID for backward compatibility.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        // Try to find by slug first
        $model = $this->where('slug', $value)->first();

        // If not found by slug and value is numeric, try by ID
        if (!$model && is_numeric($value)) {
            $model = $this->where('id', $value)->first();
        }

        return $model;
    }
}
