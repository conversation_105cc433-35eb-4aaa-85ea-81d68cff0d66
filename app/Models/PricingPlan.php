<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PricingPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'price',
        'currency',
        'interval',
        'features',
        'search_limit',
        'is_active',
        'is_default',
        'is_popular',
        'sort_order',
        'metadata',
        'paddle_price_id_monthly',
        'paddle_price_id_yearly',
        'paddle_product_id',
        'shurjopay_price_id_monthly',
        'shurjopay_price_id_yearly',
        'shurjopay_product_id',
        'online_payment_enabled',
        'offline_payment_enabled',
        'coinbase_commerce_price_id_monthly',
        'coinbase_commerce_price_id_yearly',
        'coinbase_commerce_product_id',
        'crypto_payment_enabled',
    ];

    protected $casts = [
        'features' => 'array',
        'metadata' => 'array',
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'is_popular' => 'boolean',
        'online_payment_enabled' => 'boolean',
        'offline_payment_enabled' => 'boolean',
        'crypto_payment_enabled' => 'boolean',
    ];

    /**
     * Get the subscriptions for this pricing plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Scope to get only active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get plans ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Get the default pricing plan.
     */
    public static function getDefault(): ?self
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * Check if this plan has unlimited searches.
     */
    public function hasUnlimitedSearches(): bool
    {
        return $this->search_limit === -1;
    }

    /**
     * Get formatted price string.
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->price == 0) {
            return 'Free';
        }

        return '$' . number_format($this->price, 2) . '/' . $this->interval;
    }

    /**
     * Get the Paddle price ID for the given billing cycle.
     */
    public function getPaddlePriceId(string $billingCycle = 'month'): ?string
    {
        return $billingCycle === 'year'
            ? $this->paddle_price_id_yearly
            : $this->paddle_price_id_monthly;
    }

    /**
     * Check if this plan has Paddle integration configured.
     */
    public function hasPaddleIntegration(): bool
    {
        return !empty($this->paddle_price_id_monthly) || !empty($this->paddle_price_id_yearly);
    }

    /**
     * Check if this plan supports the given billing cycle.
     */
    public function supportsBillingCycle(string $billingCycle): bool
    {
        return !empty($this->getPaddlePriceId($billingCycle));
    }

    /**
     * Check if online payments are enabled for this plan.
     */
    public function hasOnlinePaymentEnabled(): bool
    {
        return $this->online_payment_enabled;
    }

    /**
     * Check if offline payments are enabled for this plan.
     */
    public function hasOfflinePaymentEnabled(): bool
    {
        return $this->offline_payment_enabled;
    }

    /**
     * Check if online payments are available (enabled and configured).
     */
    public function supportsOnlinePayment(): bool
    {
        return $this->hasOnlinePaymentEnabled() && $this->hasPaddleIntegration();
    }

    /**
     * Get the ShurjoPay price ID for the given billing cycle.
     */
    public function getShurjoPayPriceId(string $billingCycle = 'month'): ?string
    {
        return $billingCycle === 'year'
            ? $this->shurjopay_price_id_yearly
            : $this->shurjopay_price_id_monthly;
    }

    /**
     * Check if this plan has ShurjoPay integration configured.
     */
    public function hasShurjoPayIntegration(): bool
    {
        return !empty($this->shurjopay_price_id_monthly) || !empty($this->shurjopay_price_id_yearly);
    }

    /**
     * Check if any payment method is available for this plan.
     */
    public function hasAnyPaymentMethod(): bool
    {
        return $this->hasOnlinePaymentEnabled() || $this->hasOfflinePaymentEnabled() || $this->hasCryptoPaymentEnabled();
    }

    /**
     * Get the Coinbase Commerce price ID for the given billing cycle.
     */
    public function getCoinbaseCommercePriceId(string $billingCycle = 'month'): ?string
    {
        return $billingCycle === 'year'
            ? $this->coinbase_commerce_price_id_yearly
            : $this->coinbase_commerce_price_id_monthly;
    }

    /**
     * Check if this plan has Coinbase Commerce integration configured.
     */
    public function hasCoinbaseCommerceIntegration(): bool
    {
        return !empty($this->coinbase_commerce_price_id_monthly) || !empty($this->coinbase_commerce_price_id_yearly);
    }

    /**
     * Check if crypto payments are enabled for this plan.
     */
    public function hasCryptoPaymentEnabled(): bool
    {
        return $this->crypto_payment_enabled;
    }

    /**
     * Check if crypto payments are available (enabled and configured).
     */
    public function supportsCryptoPayment(): bool
    {
        return $this->hasCryptoPaymentEnabled() && $this->hasCoinbaseCommerceIntegration();
    }

    /**
     * Check if this plan supports the given billing cycle for Coinbase Commerce.
     */
    public function supportsCoinbaseCommerceBillingCycle(string $billingCycle): bool
    {
        return !empty($this->getCoinbaseCommercePriceId($billingCycle));
    }

    /**
     * Get the plan configuration for frontend.
     */
    public function toArray(): array
    {
        $array = parent::toArray();
        $array['formatted_price'] = $this->formatted_price;
        $array['has_paddle_integration'] = $this->hasPaddleIntegration();
        $array['has_shurjopay_integration'] = $this->hasShurjoPayIntegration();
        $array['has_coinbase_commerce_integration'] = $this->hasCoinbaseCommerceIntegration();
        $array['supports_monthly'] = $this->supportsBillingCycle('month');
        $array['supports_yearly'] = $this->supportsBillingCycle('year');
        $array['supports_coinbase_commerce_monthly'] = $this->supportsCoinbaseCommerceBillingCycle('month');
        $array['supports_coinbase_commerce_yearly'] = $this->supportsCoinbaseCommerceBillingCycle('year');
        $array['supports_online_payment'] = $this->supportsOnlinePayment();
        $array['supports_crypto_payment'] = $this->supportsCryptoPayment();
        $array['has_online_payment_enabled'] = $this->hasOnlinePaymentEnabled();
        $array['has_offline_payment_enabled'] = $this->hasOfflinePaymentEnabled();
        $array['has_crypto_payment_enabled'] = $this->hasCryptoPaymentEnabled();
        $array['has_any_payment_method'] = $this->hasAnyPaymentMethod();
        return $array;
    }
}
