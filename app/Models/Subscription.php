<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Subscription extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'plan_name',
        'pricing_plan_id',
        'status',
        'current_period_start',
        'current_period_end',
        'stripe_subscription_id',
        'paddle_subscription_id',
        'shurjopay_subscription_id',
        'coinbase_commerce_subscription_id',
        'payment_gateway',
    ];

    protected $casts = [
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
    ];

    /**
     * Get the user that owns this subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the pricing plan for this subscription.
     */
    public function pricingPlan(): BelongsTo
    {
        return $this->belongsTo(PricingPlan::class);
    }

    /**
     * Get the Paddle transactions for this subscription.
     */
    public function paddleTransactions(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(PaddleTransaction::class);
    }

    /**
     * Get the ShurjoPay transactions for this subscription.
     */
    public function shurjoPayTransactions(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ShurjoPayTransaction::class);
    }

    /**
     * Get the Coinbase Commerce transactions for this subscription.
     */
    public function coinbaseCommerceTransactions(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(CoinbaseCommerceTransaction::class);
    }

    /**
     * Scope to get only active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' &&
               $this->current_period_end &&
               $this->current_period_end->isFuture();
    }
}
