<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Media extends Model
{
    use HasFactory;

    protected $fillable = [
        'filename',
        'original_filename',
        'mime_type',
        'size',
        'path',
        'disk',
        'alt_text',
        'title',
        'description',
        'width',
        'height',
        'uploaded_by',
    ];

    protected $casts = [
        'size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
    ];

    protected $appends = [
        'url',
        'thumbnail_url',
        'formatted_size',
    ];

    /**
     * Get the user who uploaded this media.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the full URL for this media file.
     */
    public function getUrlAttribute(): string
    {
        $url = $this->disk === 'public'
            ? Storage::disk('public')->url($this->path)
            : Storage::disk($this->disk)->url($this->path);

        return $this->normalizeUrl($url);
    }

    /**
     * Normalize URL for frontend consistency.
     */
    private function normalizeUrl(string $url): string
    {
        // In development, normalize 127.0.0.1 to localhost for frontend consistency
        if (app()->environment('local')) {
            $url = str_replace('127.0.0.1', 'localhost', $url);
        }

        return $url;
    }

    /**
     * Get the full file path.
     */
    public function getFullPathAttribute(): string
    {
        return Storage::disk($this->disk)->path($this->path);
    }

    /**
     * Check if the media file is an image.
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get thumbnail URL for images.
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        if (!$this->isImage()) {
            return null;
        }

        // For now, return the original image URL (already normalized)
        // In the future, you could implement thumbnail generation
        return $this->url;
    }

    /**
     * Scope to get only images.
     */
    public function scopeImages($query)
    {
        return $query->where('mime_type', 'like', 'image/%');
    }

    /**
     * Scope to get media by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('mime_type', 'like', $type . '/%');
    }

    /**
     * Delete the media file from storage when the model is deleted.
     */
    protected static function booted()
    {
        static::deleting(function (Media $media) {
            if (Storage::disk($media->disk)->exists($media->path)) {
                Storage::disk($media->disk)->delete($media->path);
            }
        });
    }
}
