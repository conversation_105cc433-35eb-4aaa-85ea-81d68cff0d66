<?php

namespace App\Plugins\Simple;

use App\Contracts\BasePlugin;

class SimplePlugin extends BasePlugin
{
    public function getName(): string
    {
        return 'Simple Plugin';
    }

    public function getVersion(): string
    {
        return '1.0.0';
    }

    public function getDescription(): string
    {
        return 'A simple plugin for testing the plugin system';
    }

    public function getAuthor(): string
    {
        return 'Mobile Parts DB Team';
    }

    public function register(): void
    {
        // Register any services here
        logger()->info('Simple plugin registered');
    }

    public function boot(): void
    {
        // Boot the plugin
        logger()->info('Simple plugin booted');
    }

    public function install(): void
    {
        logger()->info('Simple plugin installed');
    }

    public function uninstall(): void
    {
        logger()->info('Simple plugin uninstalled');
    }
}
