<?php

/**
 * Paddle Integration Test Script
 * 
 * Run this script after configuring real Paddle price IDs to test the integration.
 * Usage: php test-paddle-integration.php
 */

require_once 'vendor/autoload.php';

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔧 Testing Paddle Integration...\n\n";

// Test 1: Check Paddle Service Configuration
echo "1. Testing Paddle Service Configuration:\n";
$paddleService = app(App\Services\PaddleService::class);
echo "   ✅ Is Configured: " . ($paddleService->isConfigured() ? 'YES' : 'NO') . "\n";
echo "   ✅ Development Mode: " . ($paddleService->isDevelopmentMode() ? 'YES' : 'NO') . "\n";
echo "   ✅ Environment: " . config('paddle.environment') . "\n\n";

// Test 2: Check Pricing Plan Configuration
echo "2. Testing Pricing Plan Configuration:\n";
$plan = App\Models\PricingPlan::find(2);
if ($plan) {
    echo "   ✅ Plan Name: " . $plan->name . "\n";
    echo "   ✅ Monthly Price ID: " . $plan->paddle_price_id_monthly . "\n";
    echo "   ✅ Yearly Price ID: " . $plan->paddle_price_id_yearly . "\n";
    
    // Check if price IDs are still placeholders
    $monthlyIsPlaceholder = $paddleService->isPlaceholderValue($plan->paddle_price_id_monthly);
    $yearlyIsPlaceholder = $paddleService->isPlaceholderValue($plan->paddle_price_id_yearly);
    
    echo "   " . ($monthlyIsPlaceholder ? "❌" : "✅") . " Monthly Price ID: " . ($monthlyIsPlaceholder ? "PLACEHOLDER" : "REAL") . "\n";
    echo "   " . ($yearlyIsPlaceholder ? "❌" : "✅") . " Yearly Price ID: " . ($yearlyIsPlaceholder ? "PLACEHOLDER" : "REAL") . "\n\n";
} else {
    echo "   ❌ Pricing plan not found\n\n";
}

// Test 3: Test Checkout Session Creation
echo "3. Testing Checkout Session Creation:\n";
$user = App\Models\User::find(6); // Adjust user ID as needed
if ($user && $plan) {
    echo "   ✅ Test User: " . $user->email . "\n";
    
    try {
        $result = $paddleService->createCheckoutSession($user, $plan, 'month');
        
        if ($result && !isset($result['error'])) {
            echo "   ✅ Checkout Session: CREATED SUCCESSFULLY\n";
            echo "   ✅ Transaction ID: " . $result['transaction_id'] . "\n";
            echo "   ✅ Checkout URL: " . $result['checkout_url'] . "\n";
        } elseif (isset($result['error'])) {
            echo "   ❌ Checkout Session: FAILED\n";
            echo "   ❌ Error: " . $result['message'] . "\n";
            echo "   💡 Help: " . $result['help'] . "\n";
        } else {
            echo "   ❌ Checkout Session: FAILED (Unknown error)\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Checkout Session: EXCEPTION\n";
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ❌ Test user or plan not found\n";
}

echo "\n🎯 Integration Test Complete!\n\n";

// Provide next steps based on results
if ($paddleService->isConfigured() && !$paddleService->isDevelopmentMode()) {
    if ($plan && !$paddleService->isPlaceholderValue($plan->paddle_price_id_monthly)) {
        echo "🎉 SUCCESS: Your Paddle integration is fully configured and ready!\n";
        echo "   You can now process real payments through Paddle.\n\n";
    } else {
        echo "⚠️  NEXT STEP: Update your pricing plan with real Paddle price IDs.\n";
        echo "   1. Create products in your Paddle dashboard\n";
        echo "   2. Copy the price IDs\n";
        echo "   3. Update the database with: php artisan tinker --execute=\"App\\Models\\PricingPlan::find(2)->update(['paddle_price_id_monthly' => 'your_price_id', 'paddle_price_id_yearly' => 'your_price_id']);\"\n\n";
    }
} else {
    echo "⚠️  CONFIGURATION NEEDED: Please configure real Paddle credentials.\n\n";
}
